/**
 * Database logger for Fademail
 *
 * This module provides functions for logging events and errors to the database
 */
import { createServerSupabaseClient } from '../supabase';
import NodeCache from 'node-cache';

// Cache for recent logs to avoid duplicate logging
export const recentLogsCache = new NodeCache({ stdTTL: 60, checkperiod: 30 });

// Logger interface
export const logger = {
  debug: (category: string, message: string, metadata?: Record<string, any>) =>
    logToDatabase('DEBUG', category, message, metadata),
  info: (category: string, message: string, metadata?: Record<string, any>) =>
    logToDatabase('INFO', category, message, metadata),
  warn: (category: string, message: string, metadata?: Record<string, any>) =>
    logToDatabase('WARN', category, message, metadata),
  error: (category: string, message: string, metadata?: Record<string, any>) =>
    logToDatabase('ERROR', category, message, metadata)
};

/**
 * Log to the database
 *
 * @param level The log level
 * @param category The log category
 * @param message The log message
 * @param metadata Additional metadata
 */
export async function logToDatabase(
  level: string,
  category: string,
  message: string,
  metadata?: Record<string, any>
): Promise<void> {
  try {
    // Only log INFO, WARN, and ERROR levels to the database
    if (level === 'DEBUG') {
      return;
    }

    // Check for duplicate logs
    const cacheKey = `${level}:${category}:${message}`;
    if (recentLogsCache.get(cacheKey)) {
      return;
    }

    // Cache this log to prevent duplicates
    recentLogsCache.set(cacheKey, true);

    const supabase = createServerSupabaseClient();

    await supabase
      .from('system_logs')
      .insert({
        level: level.toLowerCase(),
        category,
        message,
        metadata: metadata || null,
        timestamp: new Date().toISOString()
      });
  } catch (error) {
    console.error(`Error logging to database: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Get logs from the database
 *
 * @param options Options for filtering logs
 * @returns Array of logs
 */
export async function getLogs(options: {
  level?: string;
  category?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}): Promise<any[]> {
  try {
    const supabase = createServerSupabaseClient();

    let query = supabase
      .from('system_logs')
      .select('*');

    // Apply filters
    if (options.level) {
      query = query.eq('level', options.level.toLowerCase());
    }

    if (options.category) {
      query = query.ilike('category', `%${options.category}%`);
    }

    if (options.startDate) {
      query = query.gte('timestamp', options.startDate.toISOString());
    }

    if (options.endDate) {
      query = query.lte('timestamp', options.endDate.toISOString());
    }

    // Apply pagination
    query = query
      .order('timestamp', { ascending: false })
      .limit(options.limit || 100)
      .range(
        options.offset || 0,
        (options.offset || 0) + (options.limit || 100) - 1
      );

    const { data, error } = await query;

    if (error) {
      console.error('Error getting logs:', error);
      return [];
    }

    return data;
  } catch (error) {
    console.error('Error getting logs:', error);
    return [];
  }
}

/**
 * Clear logs from the database
 *
 * @param options Options for filtering logs to clear
 * @returns True if successful, false otherwise
 */
export async function clearLogs(options: {
  level?: string;
  category?: string;
  olderThan?: Date;
}): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();

    let query = supabase
      .from('system_logs')
      .delete();

    // Apply filters
    if (options.level) {
      query = query.eq('level', options.level.toLowerCase());
    }

    if (options.category) {
      query = query.ilike('category', `%${options.category}%`);
    }

    if (options.olderThan) {
      query = query.lt('timestamp', options.olderThan.toISOString());
    }

    const { error } = await query;

    if (error) {
      console.error('Error clearing logs:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error clearing logs:', error);
    return false;
  }
}
