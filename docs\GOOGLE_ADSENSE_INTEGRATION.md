# Google AdSense Integration in Fademail

This document explains how Google AdSense is integrated into the Fademail application, focusing on the strategic placement of ads and the configuration system.

## Overview

Fademail uses Google AdSense to monetize the application through strategically placed advertisements. The implementation includes:

1. **Strategic Ad Placements**: Ads are placed in non-intrusive locations that don't interfere with the core functionality.
2. **Database-Driven Configuration**: All ad settings are stored in the database and managed through the admin interface.
3. **Device-Specific Targeting**: Ads can be configured to appear on specific device types (desktop, tablet, mobile).
4. **Domain-Specific Settings**: Different domains can have different ad configurations.

## Ad Placement Locations

The application has two main ad placement locations:

1. **Top Ad**: Located between the navbar and email controls
   - Placement ID: `top`
   - Visible on all device types
   - Horizontal banner format

2. **Middle Ad**: Located between the email controls and inbox/email viewer
   - Placement ID: `middle`
   - Visible on desktop and tablet
   - Horizontal banner format

## Technical Implementation

### 1. AdContainer Component

The `AdContainer` component is responsible for rendering ads. It:

- Fetches configuration from the database
- Respects device type and domain settings
- Handles ad loading and error states
- Implements responsive design for different screen sizes

```tsx
<AdContainer
  placementId="top"
  className="w-full max-w-5xl mx-auto my-2 px-4 sm:px-6"
  style={{ minHeight: '90px', maxHeight: '100px' }}
/>
```

### 2. Database Schema

Ads are configured in the `ad_config` table with the following structure:

```sql
CREATE TABLE ad_config (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  placement_id VARCHAR(50) NOT NULL,
  domain VARCHAR(255) NOT NULL,
  ad_unit_id VARCHAR(255) NOT NULL,
  ad_client_id VARCHAR(255) NOT NULL,
  is_enabled BOOLEAN DEFAULT true,
  device_types JSONB DEFAULT '["desktop", "tablet", "mobile"]'::jsonb,
  format VARCHAR(50) DEFAULT 'auto',
  display_options JSONB DEFAULT '{}'::jsonb,
  schedule JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(placement_id, domain)
);
```

### 3. Admin Interface

The ad configuration can be managed through the admin interface at:
- `/management-portal-x7z9y2/ads`

This interface allows administrators to:
- Create new ad placements
- Edit existing ad configurations
- Enable/disable specific ad placements
- Configure device targeting
- Set up scheduling for ads

### 4. AdSense Script Integration

The Google AdSense script is loaded in the application's root layout:

```tsx
<Script
  id="adsense-init"
  async
  src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXXXXXXXX"
  crossOrigin="anonymous"
  strategy="afterInteractive"
/>
```

## Configuration Guide

### Setting Up a New Ad Placement

1. Go to `/management-portal-x7z9y2/ads`
2. Click "Add Ad Placement"
3. Fill in the required fields:
   - Placement ID: A unique identifier for the placement location
   - Domain: The domain this configuration applies to (use * for all domains)
   - Ad Unit ID: Your Google AdSense ad unit ID
   - Ad Client ID: Your Google AdSense publisher ID
   - Enabled: Whether this ad should be displayed
   - Device Types: Which devices should show this ad
   - Format: The ad format (auto, rectangle, vertical, etc.)
   - Display Options: JSON configuration for additional display settings

### Troubleshooting

If ads are not displaying:

1. Check that the ad configuration exists in the database
2. Verify that the ad is enabled
3. Confirm that the current device type is included in the configuration
4. Check the browser console for any AdSense-related errors
5. Verify that your AdSense account is approved and active

## Performance Considerations

- Ads are loaded asynchronously to prevent blocking the main thread
- Minimum height containers are used to reduce layout shifts
- The sidebar ad is only loaded on large screens to avoid performance issues on mobile
- Ad containers are not rendered at all if they're disabled in the configuration

## Future Enhancements

Planned enhancements for the ad system include:

1. A/B testing capabilities
2. Performance analytics integration
3. More granular scheduling options
4. User segment targeting
5. Integration with additional ad networks

## Related Files

- `src/components/AdContainer.tsx`: The main component for displaying ads
- `src/hooks/useAdConfig.ts`: Hook for fetching ad configuration
- `src/lib/deviceDetection.ts`: Utility for detecting device types
- `src/app/api/management-portal-x7z9y2/ad-config/route.ts`: API routes for ad configuration
- `src/db/ad_config_schema.sql`: Database schema for ad configuration
