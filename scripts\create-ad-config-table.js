require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Anon Key:', supabaseAnonKey ? 'Found' : 'Not found');

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createAdConfigTable() {
  console.log('Creating ad_config table...');

  try {
    // Check if the table already exists
    const { data: checkData, error: checkError } = await supabase
      .from('ad_config')
      .select('*')
      .limit(1);

    if (!checkError) {
      console.log('ad_config table already exists');
      console.log('Sample data:', checkData);
      return;
    }

    // Try to create the table using SQL
    try {
      const { error: createError } = await supabase.rpc('execute_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS ad_config (
            placement_id VARCHAR(50) NOT NULL,
            domain VARCHAR(100) NOT NULL,
            ad_unit_id VARCHAR(100) NOT NULL,
            is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
            device_types JSONB NOT NULL,
            display_options JSONB,
            schedule JSONB,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            PRIMARY KEY (placement_id, domain)
          );
        `
      });

      if (createError) {
        console.error('Failed to create ad_config table using RPC:', createError.message);
        console.log('Trying alternative method...');

        // Try to create the table by inserting a record
        const { error: insertError } = await supabase
          .from('ad_config')
          .insert({
            placement_id: 'test-placement',
            domain: 'test-domain',
            ad_unit_id: 'test-ad-unit',
            is_enabled: true,
            device_types: ['desktop', 'tablet', 'mobile'],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError && insertError.code !== '23505') {
          console.error('Failed to create ad_config table by insertion:', insertError.message);
          console.log('Please create the ad_config table manually with the following SQL:');
          console.log(`
            CREATE TABLE IF NOT EXISTS ad_config (
              placement_id VARCHAR(50) NOT NULL,
              domain VARCHAR(100) NOT NULL,
              ad_unit_id VARCHAR(100) NOT NULL,
              is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
              device_types JSONB NOT NULL,
              display_options JSONB,
              schedule JSONB,
              created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
              updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
              PRIMARY KEY (placement_id, domain)
            );
          `);
          return;
        }
      }

      // Insert default ad placements
      const defaultPlacements = [
        {
          placement_id: 'sidebar-top',
          domain: 'fademail.site',
          ad_unit_id: 'ca-pub-XXXXXXXXXXXXXXXX',
          is_enabled: true,
          device_types: ['desktop', 'tablet']
        },
        {
          placement_id: 'inbox-bottom',
          domain: 'fademail.site',
          ad_unit_id: 'ca-pub-XXXXXXXXXXXXXXXX',
          is_enabled: true,
          device_types: ['desktop', 'tablet', 'mobile']
        },
        {
          placement_id: 'email-view-right',
          domain: 'fademail.site',
          ad_unit_id: 'ca-pub-XXXXXXXXXXXXXXXX',
          is_enabled: true,
          device_types: ['desktop']
        }
      ];

      for (const placement of defaultPlacements) {
        const { error: insertError } = await supabase
          .from('ad_config')
          .upsert({
            ...placement,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error(`Failed to insert placement ${placement.placement_id}:`, insertError.message);
        } else {
          console.log(`Inserted placement ${placement.placement_id} for domain ${placement.domain}`);
        }
      }

      // Verify the table was created
      const { data: verifyData, error: verifyError } = await supabase
        .from('ad_config')
        .select('*');

      if (verifyError) {
        console.error('Failed to verify ad_config table creation:', verifyError.message);
      } else {
        console.log('ad_config table created successfully!');
        console.log('Current data:', verifyData);
      }
    } catch (error) {
      console.error('Exception occurred while creating ad_config table:', error.message);
      console.log('Please create the ad_config table manually with the following SQL:');
      console.log(`
        CREATE TABLE IF NOT EXISTS ad_config (
          placement_id VARCHAR(50) NOT NULL,
          domain VARCHAR(100) NOT NULL,
          ad_unit_id VARCHAR(100) NOT NULL,
          is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
          device_types JSONB NOT NULL,
          display_options JSONB,
          schedule JSONB,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          PRIMARY KEY (placement_id, domain)
        );
      `);
    }
  } catch (error) {
    console.error('Error checking ad_config table:', error.message);
  }
}

createAdConfigTable();
