'use client';

import { useEffect, useRef, useState } from 'react';
import { useAdConfig } from '@/hooks/useAdConfig';
import { getDeviceType } from '@/lib/deviceDetection';
import { ClientOnly } from '@/lib/utils/suppressHydrationWarning';

interface StickyAdContainerProps {
  placementId: string; // e.g., 'left-rail', 'right-rail'
  className?: string;
  style?: React.CSSProperties;
  position: 'left' | 'right';
}

/**
 * StickyAdContainer Component
 *
 * Renders a sticky advertisement container that sticks to the side of the screen
 * as the user scrolls. This component is specifically designed for side rail ads.
 * It integrates with the existing ad management system.
 */
export default function StickyAdContainer({
  placementId,
  className,
  style,
  position
}: StickyAdContainerProps) {
  const adRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize position styles based on position prop
  const [positionStyles, setPositionStyles] = useState(
    position === 'left'
      ? { left: '0', borderTopRightRadius: '8px', borderBottomRightRadius: '8px' }
      : { right: '0', borderTopLeftRadius: '8px', borderBottomLeftRadius: '8px' }
  );

  // State for display options from ad config
  const [displayStyles, setDisplayStyles] = useState<React.CSSProperties>({});

  // State for ad label
  const [showLabel, setShowLabel] = useState(false);
  const [labelText, setLabelText] = useState('Advertisement');

  // Use the existing ad configuration hook
  const { adConfig, isLoading, error: configError } = useAdConfig(placementId);

  // Update position based on viewport width
  useEffect(() => {
    if (!isVisible || typeof window === 'undefined') return;

    const updatePosition = () => {
      const viewportWidth = window.innerWidth;
      const contentWidth = 1280; // xl breakpoint in Tailwind (matches the xl:block class)
      const adWidth = 160; // Width of the ad
      const margin = 16; // Margin between content and ad (16px = 1rem)

      // Calculate the space available on each side
      const availableSpace = Math.max(0, (viewportWidth - contentWidth) / 2);

      // Only show if there's enough space
      const shouldShow = availableSpace >= (adWidth + margin);

      // Calculate position
      if (position === 'left') {
        setPositionStyles({
          left: shouldShow ? `${(availableSpace - adWidth) / 2}px` : '-9999px', // Hide off-screen if not enough space
          borderTopRightRadius: '8px',
          borderBottomRightRadius: '8px'
        });
      } else {
        setPositionStyles({
          right: shouldShow ? `${(availableSpace - adWidth) / 2}px` : '-9999px', // Hide off-screen if not enough space
          borderTopLeftRadius: '8px',
          borderBottomLeftRadius: '8px'
        });
      }
    };

    // Initial position
    updatePosition();

    // Update position on window resize
    window.addEventListener('resize', updatePosition);

    // Cleanup
    return () => window.removeEventListener('resize', updatePosition);
  }, [position, isVisible]);

  // Handle ad configuration and creation
  useEffect(() => {
    // Only proceed if we have ad configuration and we're on the client
    if (typeof window === 'undefined' || isLoading) return;

    // Handle configuration errors
    if (configError) {
      console.error(`Ad configuration error for ${placementId}:`, configError);
      setError(configError);
      setIsVisible(false);
      return;
    }

    // If no ad config found, don't display anything
    if (!adConfig) {
      setIsVisible(false);
      return;
    }

    // Check if this ad should be displayed based on existing rules
    const currentDomain = window.location.hostname;
    const deviceType = getDeviceType();

    // Skip if ad is disabled, domain doesn't match, or device type is excluded
    if (
      !adConfig.isEnabled ||
      (adConfig.domain !== '*' && adConfig.domain !== currentDomain) ||
      !adConfig.deviceTypes.includes(deviceType)
    ) {
      setIsVisible(false);
      return;
    }

    setIsVisible(true);
    setError(null);

    // Process display options from ad configuration
    const displayOptions = adConfig.displayOptions || {};
    const newDisplayStyles: React.CSSProperties = {};

    // Apply display options if they exist
    if (displayOptions) {
      // Cast displayOptions to any to access all properties
      const displayOpts = displayOptions as any;

      // Apply background color if specified
      if (displayOpts.backgroundColor) {
        newDisplayStyles.backgroundColor = displayOpts.backgroundColor;
      }

      // Apply padding if specified
      if (displayOpts.padding) {
        newDisplayStyles.padding = displayOpts.padding;
      }

      // Apply margin if specified
      if (displayOpts.margin) {
        newDisplayStyles.margin = displayOpts.margin;
      }

      // Apply border radius if specified
      if (displayOpts.borderRadius) {
        newDisplayStyles.borderRadius = displayOpts.borderRadius;
      }

      // Apply border if specified
      if (displayOpts.showBorder) {
        newDisplayStyles.border = `1px solid ${displayOpts.borderColor || '#e5e7eb'}`;
      }

      // Apply max width if specified
      if (displayOpts.maxWidth) {
        newDisplayStyles.maxWidth = displayOpts.maxWidth;
      }

      // Apply overflow if specified
      if (displayOpts.overflow) {
        newDisplayStyles.overflow = displayOpts.overflow;
      }

      // Set label options
      setShowLabel(displayOpts.showLabel || false);
      if (displayOpts.labelText) {
        setLabelText(displayOpts.labelText);
      }
    }

    // Update display styles
    setDisplayStyles(newDisplayStyles);

    // Create an ad slot using the existing configuration
    const adSlot = document.createElement('ins');
    adSlot.className = 'adsbygoogle';
    adSlot.style.display = 'block';
    adSlot.setAttribute('data-ad-client', adConfig.adClientId || 'ca-pub-8397529755029714');
    adSlot.setAttribute('data-ad-slot', adConfig.adUnitId);
    adSlot.setAttribute('data-ad-format', 'vertical');
    adSlot.setAttribute('data-full-width-responsive', 'false');

    // Clear the container and append the ad
    if (adRef.current) {
      adRef.current.innerHTML = '';
      adRef.current.appendChild(adSlot);

      // Push the ad to AdSense
      try {
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      } catch (err) {
        console.error('AdSense error:', err);
        setError('Failed to load advertisement');
      }
    }

    // Check if ads are blocked after a short delay
    const checkAdBlocker = setTimeout(() => {
      if (adRef.current && adRef.current.clientHeight <= 10) {
        console.log('Ad blocker detected for', placementId);
        // We don't show any message to avoid disrupting the user experience
      }
    }, 2000);

    // Cleanup function
    return () => {
      clearTimeout(checkAdBlocker);
      if (adRef.current) {
        adRef.current.innerHTML = '';
      }
    };
  }, [placementId, adConfig, isLoading, configError, position]);

  // Use ClientOnly to prevent hydration errors with AdSense
  return (
    <ClientOnly>
      {isVisible && (
        <div
          ref={adRef}
          id={`ad-container-${placementId}`}
          className={`sticky-ad-container ${className || ''}`}
          style={{
            position: 'fixed',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 10,
            minHeight: '600px',
            width: '160px',
            background: 'transparent',
            overflow: 'hidden',
            padding: '8px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
            transition: 'all 0.3s ease',
            ...positionStyles,
            ...displayStyles,
            ...style
          }}
          data-testid={`ad-container-${placementId}`}
          aria-label="Advertisement"
          role="complementary"
        >
          {showLabel && (
            <div className="text-xs text-gray-500 text-center py-1 bg-gray-100 rounded mb-1">
              {labelText}
            </div>
          )}
          {error && (
            <div className="text-xs text-gray-400 text-center py-1">
              Ad loading error
            </div>
          )}
        </div>
      )}
    </ClientOnly>
  );
}
