// DMARC Generator Types - Isolated from existing types

export type DmarcPolicy = 'none' | 'quarantine' | 'reject';
export type DmarcAlignment = 'r' | 's'; // relaxed or strict

export interface DmarcPolicyConfig {
  domain: string;
  policy: DmarcPolicy;
  rua: string; // Reporting email address
  pct: number; // Percentage (0-100)
  sp?: DmarcPolicy; // Subdomain policy
  adkim?: DmarcAlignment; // DKIM alignment
  aspf?: DmarcAlignment; // SPF alignment
  ruf?: string; // Failure reporting email address
  fo?: string; // Failure reporting options
  rf?: string; // Report format
  ri?: number; // Report interval
}

export interface DmarcRecord {
  domain: string;
  policy: DmarcPolicyConfig;
  dnsRecord: string;
  recordName: string;
}

export interface DmarcGenerateRequest {
  domain: string;
  policy: DmarcPolicy;
  rua: string;
  ruf?: string;
  pct: number;
  sp?: DmarcPolicy;
  adkim?: DmarcAlignment;
  aspf?: DmarcAlignment;
  sessionId?: string;
}

export interface DmarcGenerateResponse {
  success: boolean;
  data?: {
    dnsRecord: string;
    recordName: string;
    domain: string;
    policy: DmarcPolicyConfig;
    instructions: string[];
    generatedAt: string;
  };
  message?: string;
  error?: string;
}

export interface DmarcValidationRequest {
  domain: string;
  expectedPolicy?: DmarcPolicy;
}

export interface DmarcValidationResponse {
  success: boolean;
  data?: {
    exists: boolean;
    records: string[];
    isValid: boolean;
    errors: string[];
    lastChecked: string;
    parsedPolicy?: Partial<DmarcPolicyConfig>;
    policyMatch?: boolean;
  };
  message?: string;
  error?: string;
}

export interface DmarcFormData {
  domain: string;
  policy: DmarcPolicy;
  rua: string;
  pct: number;
  sp?: DmarcPolicy;
  adkim?: DmarcAlignment;
  aspf?: DmarcAlignment;
}

export interface DmarcFormErrors {
  domain?: string;
  policy?: string;
  rua?: string;
  pct?: string;
  sp?: string;
  adkim?: string;
  aspf?: string;
}

// Database record type for DMARC
export interface DmarcDatabaseRecord {
  id: string;
  session_id: string;
  user_id?: string;
  record_type: 'dmarc';
  domain: string;
  selector?: null; // Not used for DMARC
  dns_record: string;
  record_name: string;
  private_key_encrypted?: null; // Not used for DMARC
  metadata: {
    policy: DmarcPolicyConfig;
    instructions: string[];
  };
  created_at: string;
  expires_at: string;
  validated_at?: string;
  validation_status: 'pending' | 'valid' | 'invalid' | 'error';
  validation_errors: string[];
}
