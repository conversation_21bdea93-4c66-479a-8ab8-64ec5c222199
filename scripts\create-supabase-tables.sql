-- Create temp_emails table
CREATE TABLE temp_emails (
  id SERIAL PRIMARY KEY,
  email_address VARCHAR(255) NOT NULL UNIQUE,
  creation_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  expiration_date TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create index on expiration_date for cleanup queries
CREATE INDEX idx_expiration_date ON temp_emails(expiration_date);

-- Analytics is now handled by PostHog
-- No additional tables needed for analytics
