/**
 * SMTP Loading Overlay Component
 * 
 * Loading state component for SMTP testing operations
 */

'use client';

import React from 'react';

interface SmtpLoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  testMode?: 'auto' | 'custom';
}

export default function SmtpLoadingOverlay({ 
  isVisible, 
  message = 'Testing SMTP connection...', 
  testMode = 'auto' 
}: SmtpLoadingOverlayProps) {
  if (!isVisible) return null;

  const getLoadingSteps = () => {
    if (testMode === 'auto') {
      return [
        'Validating SMTP configuration',
        'Establishing connection to server',
        'Authenticating with credentials',
        'Sending test email',
        'Analyzing email authentication',
        'Generating results'
      ];
    } else {
      return [
        'Validating SMTP configuration',
        'Establishing connection to server',
        'Authenticating with credentials',
        'Sending test email',
        'Confirming delivery'
      ];
    }
  };

  const steps = getLoadingSteps();

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white/95 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-6 max-w-md w-full">
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-[#66b077] to-[#07880e] flex items-center justify-center">
            <svg className="w-6 h-6 text-white animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-bold" style={{ color: '#1b130e' }}>SMTP Testing</h3>
            <p className="text-sm" style={{ color: '#4a3728' }}>{message}</p>
          </div>
        </div>

        {/* Loading Animation */}
        <div className="flex justify-center mb-6">
          <div className="relative">
            {/* Outer spinning ring */}
            <div className="w-16 h-16 border-4 border-[#4a3728]/20 rounded-full animate-spin border-t-[#66b077]"></div>
            {/* Inner pulsing dot */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-4 h-4 bg-[#66b077] rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="space-y-3">
          <h4 className="text-sm font-semibold" style={{ color: '#1b130e' }}>Testing Progress:</h4>
          <div className="space-y-2">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="flex-shrink-0">
                  <div className="w-4 h-4 rounded-full bg-[#66b077]/20 flex items-center justify-center">
                    <div className="w-2 h-2 rounded-full bg-[#66b077] animate-pulse" style={{ animationDelay: `${index * 200}ms` }}></div>
                  </div>
                </div>
                <span className="text-sm" style={{ color: '#4a3728' }}>{step}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Test Mode Info */}
        <div className="mt-6 p-3 rounded-lg" style={{ backgroundColor: '#f3ece8' }}>
          <div className="flex items-center gap-2 mb-1">
            <svg className="w-4 h-4" style={{ color: '#f59e0b' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm font-medium" style={{ color: '#1b130e' }}>
              {testMode === 'auto' ? 'Auto Mode' : 'Custom Mode'}
            </span>
          </div>
          <p className="text-xs" style={{ color: '#4a3728' }}>
            {testMode === 'auto' 
              ? 'Testing SMTP connectivity and analyzing email authentication (SPF, DKIM, DMARC)'
              : 'Testing SMTP connectivity with your specified recipient'
            }
          </p>
        </div>

        {/* Estimated Time */}
        <div className="mt-4 text-center">
          <p className="text-xs" style={{ color: '#956b50' }}>
            Estimated time: {testMode === 'auto' ? '10-30 seconds' : '5-15 seconds'}
          </p>
        </div>
      </div>
    </div>
  );
}
