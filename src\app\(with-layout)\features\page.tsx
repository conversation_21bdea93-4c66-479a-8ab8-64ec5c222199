import React from 'react';
import Link from 'next/link';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Features | VanishPost',
  description: 'Discover the powerful features of VanishPost, your secure temporary email service.',
};

export default function FeaturesPage() {
  return (
    <div className="max-w-5xl mx-auto px-4 py-12 sm:py-16">
      <div className="text-center mb-12">
        <h1 className="text-3xl sm:text-4xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>VanishPost Features</h1>
        <p className="text-lg max-w-3xl mx-auto" style={{ color: 'var(--earth-brown-medium)' }}>
          Discover how VanishPost helps protect your privacy with powerful features designed for security and convenience.
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 mb-16">
        {/* Feature 1 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full mr-4" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold" style={{ color: 'var(--earth-brown-dark)' }}>Temporary Email Addresses</h2>
          </div>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            Generate disposable email addresses that automatically expire after 15 minutes, protecting your real inbox from spam and unwanted communications.
          </p>
        </div>

        {/* Feature 2 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full mr-4" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold" style={{ color: 'var(--earth-brown-dark)' }}>No Registration Required</h2>
          </div>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            Start using VanishPost instantly with no sign-up, account creation, or personal information required. Complete anonymity at your fingertips.
          </p>
        </div>

        {/* Feature 3 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full mr-4" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold" style={{ color: 'var(--earth-brown-dark)' }}>Auto-Refresh Inbox</h2>
          </div>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            Emails are automatically checked every 14 seconds, ensuring you never miss important messages. Manual refresh is also available for immediate updates.
          </p>
        </div>

        {/* Feature 4 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full mr-4" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold" style={{ color: 'var(--earth-brown-dark)' }}>Secure Email Viewing</h2>
          </div>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            View emails in a secure, isolated environment that prevents tracking pixels and scripts from compromising your privacy or device security.
          </p>
        </div>

        {/* Feature 5 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full mr-4" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold" style={{ color: 'var(--earth-brown-dark)' }}>Email Management</h2>
          </div>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            Easily delete unwanted emails with a single click. All emails are automatically purged after 15 minutes, leaving no trace of your communications.
          </p>
        </div>

        {/* Feature 6 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full mr-4" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold" style={{ color: 'var(--earth-brown-dark)' }}>Attachment Support</h2>
          </div>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            Receive and view email attachments securely. VanishPost supports all common file types and displays them in a safe, controlled environment.
          </p>
        </div>
      </div>

      <div className="p-8 rounded-md text-center" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
        <h2 className="text-2xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Ready to protect your privacy?</h2>
        <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
          Start using VanishPost today - no registration, no personal information, just instant privacy protection.
        </p>
        <Link href="/" className="inline-block bg-[#ce601c] text-white px-6 py-3 rounded-md font-medium hover:bg-[#b85518] transition-colors">
          Try VanishPost Now
        </Link>
      </div>
    </div>
  );
}
