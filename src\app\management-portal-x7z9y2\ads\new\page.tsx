'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeftIcon } from '@heroicons/react/24/solid';
import AdPlacementForm from '@/components/admin/AdPlacementForm';
import { AdDisplayOptions } from '@/lib/config/types';
import { Button } from '@/components/ui/Button';
import { Card, CardHeader, CardContent, CardTitle, CardDescription } from '@/components/ui/Card';
import { PageHeader } from '@/components/ui/PageHeader';
import { Alert } from '@/components/ui/Alert';

/**
 * New Ad Placement Page
 */
export default function NewAdPlacementPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [domains, setDomains] = useState<string[]>([]);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch domains
  useEffect(() => {
    const fetchDomains = async () => {
      try {
        const response = await fetch('/api/management-portal-x7z9y2/domains');

        if (!response.ok) {
          throw new Error(`Failed to fetch domains: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
          // Extract domain names from domain configs
          const domainNames = data.data.map((domain: any) => domain.domain);
          setDomains(domainNames);
        } else {
          throw new Error(data.error || 'Failed to fetch domains');
        }
      } catch (err) {
        console.error('Error fetching domains:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      }
    };

    fetchDomains();
  }, []);

  // Handle add ad placement
  const handleAddAdPlacement = async (
    placementId: string,
    domain: string,
    adUnitId: string,
    adClientId: string,
    isEnabled: boolean,
    deviceTypes: string[],
    displayOptions?: AdDisplayOptions
  ) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/ads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          placementId,
          domain,
          adUnitId,
          adClientId,
          isEnabled,
          deviceTypes,
          displayOptions
        })
      });

      let responseData;
      try {
        responseData = await response.json();
      } catch (jsonError) {
        console.error('Error parsing response JSON:', jsonError);
        responseData = { success: false };
      }

      if (response.ok && responseData.success) {
        // Success case - API returned success
        setSuccessMessage('Ad placement added successfully');

        // Redirect to the ad management page after 2 seconds
        setTimeout(() => {
          router.push('/management-portal-x7z9y2/ads');
        }, 2000);
      } else if (response.status === 409) {
        // Conflict - duplicate key
        setError(`Ad placement with ID "${placementId}" already exists for domain "${domain}"`);
      } else {
        // Other error cases
        setError(responseData.error || 'Failed to add ad placement');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      console.error('Error adding ad placement:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <PageHeader
        title="Add New Ad Placement"
        description="Create a new ad placement for your Fademail domains."
        icon={
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
        }
        actions={
          <Link href="/management-portal-x7z9y2/ads">
            <Button variant="secondary" className="flex items-center">
              <ArrowLeftIcon className="h-4 w-4 mr-1.5" />
              Back to Ad Management
            </Button>
          </Link>
        }
      />

      {error && (
        <Alert variant="error" title="Error" onDismiss={() => setError(null)}>
          <p>{error}</p>
        </Alert>
      )}

      {successMessage && (
        <Alert variant="success" title="Success" onDismiss={() => setSuccessMessage(null)}>
          <p>{successMessage}</p>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Ad Placement Details</CardTitle>
          <CardDescription>
            Fill in the details for your new ad placement.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AdPlacementForm
            onSubmit={handleAddAdPlacement}
            onCancel={() => router.push('/management-portal-x7z9y2/ads')}
            domains={domains}
          />
        </CardContent>
      </Card>
    </div>
  );
}
