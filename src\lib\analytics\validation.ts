/**
 * Request Validation and Sanitization for Analytics API
 * 
 * Provides comprehensive input validation, data sanitization, and security checks
 * for all analytics endpoints to prevent malicious data and ensure data quality.
 */

import { z } from 'zod';

/**
 * Maximum lengths for various fields to prevent DoS attacks
 */
const MAX_LENGTHS = {
  sessionId: 100,
  eventType: 50,
  userAgent: 500,
  referrer: 500,
  url: 1000,
  message: 1000,
  category: 50,
  level: 20,
  metadata: 5000, // JSON string
} as const;

/**
 * Allowed event types for analytics
 */
const ALLOWED_EVENT_TYPES = [
  'email_generated',
  'email_copied',
  'email_opened',
  'email_deleted',
  'manual_refresh',
  'session_start',
  'session_end',
  'page_view',
  'heartbeat',
  'email_received',
  'emails_received_count',
] as const;

/**
 * Allowed log levels
 */
const ALLOWED_LOG_LEVELS = [
  'debug',
  'info',
  'warning',
  'error',
  'critical',
] as const;

/**
 * Allowed log categories
 */
const ALLOWED_LOG_CATEGORIES = [
  'API',
  'DATABASE',
  'AUTH',
  'EMAIL',
  'CLEANUP',
  'CONFIG',
  'MONITOR',
  'ALERT',
  'CACHE_INIT',
] as const;

/**
 * Base validation schema for common fields
 */
const baseValidationSchema = {
  sessionId: z.string()
    .min(1, 'Session ID is required')
    .max(MAX_LENGTHS.sessionId, `Session ID must be less than ${MAX_LENGTHS.sessionId} characters`)
    .regex(/^[a-zA-Z0-9_-]+$/, 'Session ID contains invalid characters'),
    
  timestamp: z.string()
    .datetime('Invalid timestamp format')
    .optional()
    .or(z.number().int().positive('Timestamp must be a positive integer').optional()),
    
  userAgent: z.string()
    .max(MAX_LENGTHS.userAgent, `User agent must be less than ${MAX_LENGTHS.userAgent} characters`)
    .optional(),
    
  referrer: z.string()
    .max(MAX_LENGTHS.referrer, `Referrer must be less than ${MAX_LENGTHS.referrer} characters`)
    .url('Invalid referrer URL format')
    .optional()
    .or(z.literal('')),
    
  url: z.string()
    .max(MAX_LENGTHS.url, `URL must be less than ${MAX_LENGTHS.url} characters`)
    .url('Invalid URL format')
    .optional(),
};

/**
 * Analytics event validation schema
 */
export const analyticsEventSchema = z.object({
  eventType: z.enum(ALLOWED_EVENT_TYPES, {
    errorMap: () => ({ message: 'Invalid event type' })
  }),
  sessionId: baseValidationSchema.sessionId,
  timestamp: baseValidationSchema.timestamp,
  userAgent: baseValidationSchema.userAgent,
  referrer: baseValidationSchema.referrer,
  url: baseValidationSchema.url,
  metadata: z.record(z.any())
    .optional()
    .refine(
      (data) => {
        if (!data) return true;
        const jsonString = JSON.stringify(data);
        return jsonString.length <= MAX_LENGTHS.metadata;
      },
      `Metadata must be less than ${MAX_LENGTHS.metadata} characters when serialized`
    ),
});

/**
 * Session analytics validation schema
 */
export const sessionAnalyticsSchema = z.object({
  sessionId: baseValidationSchema.sessionId,
  startTime: z.string().datetime('Invalid start time format'),
  endTime: z.string().datetime('Invalid end time format').optional(),
  duration: z.number().int().min(0, 'Duration must be non-negative').optional(),
  eventCount: z.number().int().min(0, 'Event count must be non-negative').optional(),
  userAgent: baseValidationSchema.userAgent,
  referrer: baseValidationSchema.referrer,
  lastActiveTime: z.string().datetime('Invalid last active time format').optional(),
});

/**
 * Heartbeat validation schema
 */
export const heartbeatSchema = z.object({
  sessionId: baseValidationSchema.sessionId,
  timestamp: baseValidationSchema.timestamp,
  isActive: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * Query parameter validation schemas
 */
export const timeRangeSchema = z.object({
  timeRange: z.enum(['1h', '24h', '7d', '30d', 'custom'], {
    errorMap: () => ({ message: 'Invalid time range' })
  }).optional(),
  startDate: z.string().datetime('Invalid start date format').optional(),
  endDate: z.string().datetime('Invalid end date format').optional(),
  eventType: z.enum(ALLOWED_EVENT_TYPES).optional(),
  limit: z.string()
    .regex(/^\d+$/, 'Limit must be a number')
    .transform(Number)
    .refine(n => n > 0 && n <= 1000, 'Limit must be between 1 and 1000')
    .optional(),
  offset: z.string()
    .regex(/^\d+$/, 'Offset must be a number')
    .transform(Number)
    .refine(n => n >= 0, 'Offset must be non-negative')
    .optional(),
});

/**
 * Log filtering validation schema
 */
export const logFilterSchema = z.object({
  level: z.enum(ALLOWED_LOG_LEVELS).optional(),
  category: z.enum(ALLOWED_LOG_CATEGORIES).optional(),
  startDate: z.string().datetime('Invalid start date format').optional(),
  endDate: z.string().datetime('Invalid end date format').optional(),
  limit: z.string()
    .regex(/^\d+$/, 'Limit must be a number')
    .transform(Number)
    .refine(n => n > 0 && n <= 1000, 'Limit must be between 1 and 1000')
    .optional(),
});

/**
 * Sanitize string input to prevent XSS and injection attacks
 */
export function sanitizeString(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .trim();
}

/**
 * Sanitize metadata object
 */
export function sanitizeMetadata(metadata: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(metadata)) {
    // Sanitize key
    const sanitizedKey = sanitizeString(key);
    if (sanitizedKey.length === 0) continue;
    
    // Sanitize value based on type
    if (typeof value === 'string') {
      sanitized[sanitizedKey] = sanitizeString(value);
    } else if (typeof value === 'number' || typeof value === 'boolean') {
      sanitized[sanitizedKey] = value;
    } else if (value === null || value === undefined) {
      sanitized[sanitizedKey] = null;
    } else if (Array.isArray(value)) {
      // Sanitize array elements (only primitives)
      sanitized[sanitizedKey] = value
        .filter(item => typeof item === 'string' || typeof item === 'number' || typeof item === 'boolean')
        .map(item => typeof item === 'string' ? sanitizeString(item) : item);
    } else if (typeof value === 'object') {
      // Recursively sanitize nested objects (max depth 2)
      sanitized[sanitizedKey] = sanitizeMetadata(value);
    }
  }
  
  return sanitized;
}

/**
 * Validate and sanitize analytics event
 */
export function validateAnalyticsEvent(data: unknown): {
  success: boolean;
  data?: z.infer<typeof analyticsEventSchema>;
  error?: string;
} {
  try {
    const parsed = analyticsEventSchema.parse(data);
    
    // Sanitize metadata if present
    if (parsed.metadata) {
      parsed.metadata = sanitizeMetadata(parsed.metadata);
    }
    
    return { success: true, data: parsed };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
      };
    }
    return { success: false, error: 'Invalid data format' };
  }
}

/**
 * Validate query parameters
 */
export function validateQueryParams(
  params: Record<string, string | string[] | undefined>,
  schema: z.ZodSchema
): {
  success: boolean;
  data?: any;
  error?: string;
} {
  try {
    // Convert array values to single values (take first)
    const normalizedParams: Record<string, string | undefined> = {};
    for (const [key, value] of Object.entries(params)) {
      if (Array.isArray(value)) {
        normalizedParams[key] = value[0];
      } else {
        normalizedParams[key] = value;
      }
    }
    
    const parsed = schema.parse(normalizedParams);
    return { success: true, data: parsed };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
      };
    }
    return { success: false, error: 'Invalid query parameters' };
  }
}

/**
 * Check if request size is within limits
 */
export function validateRequestSize(contentLength: number | null): {
  valid: boolean;
  error?: string;
} {
  const MAX_REQUEST_SIZE = 10 * 1024; // 10KB
  
  if (contentLength === null) {
    return { valid: false, error: 'Content-Length header is required' };
  }
  
  if (contentLength > MAX_REQUEST_SIZE) {
    return {
      valid: false,
      error: `Request size ${contentLength} bytes exceeds maximum allowed size of ${MAX_REQUEST_SIZE} bytes`
    };
  }
  
  return { valid: true };
}

/**
 * Validate timestamp is within reasonable bounds
 */
export function validateTimestamp(timestamp: string | number): {
  valid: boolean;
  error?: string;
} {
  const now = Date.now();
  const timestampMs = typeof timestamp === 'string' ? new Date(timestamp).getTime() : timestamp;
  
  // Allow timestamps from 1 hour ago to 5 minutes in the future
  const oneHourAgo = now - (60 * 60 * 1000);
  const fiveMinutesFromNow = now + (5 * 60 * 1000);
  
  if (isNaN(timestampMs)) {
    return { valid: false, error: 'Invalid timestamp format' };
  }
  
  if (timestampMs < oneHourAgo) {
    return { valid: false, error: 'Timestamp is too old (more than 1 hour ago)' };
  }
  
  if (timestampMs > fiveMinutesFromNow) {
    return { valid: false, error: 'Timestamp is too far in the future (more than 5 minutes)' };
  }
  
  return { valid: true };
}
