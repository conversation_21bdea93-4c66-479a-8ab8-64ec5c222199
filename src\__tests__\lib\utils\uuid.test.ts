/**
 * Tests for browser-compatible UUID generation utilities
 */

import { generateUUID, generateSessionId, isValidUUID, generateShortId } from '@/lib/utils/uuid';

// Mock window.crypto for testing
const mockCrypto = {
  getRandomValues: jest.fn((array: Uint8Array) => {
    // Fill with predictable values for testing
    for (let i = 0; i < array.length; i++) {
      array[i] = i % 256;
    }
    return array;
  })
};

describe('UUID Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateUUID', () => {
    it('should generate a valid UUID v4 format', () => {
      const uuid = generateUUID();
      expect(uuid).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    it('should generate different UUIDs on multiple calls', () => {
      const uuid1 = generateUUID();
      const uuid2 = generateUUID();
      expect(uuid1).not.toBe(uuid2);
    });

    it('should use crypto.getRandomValues when available', () => {
      // Mock window and crypto
      const originalWindow = global.window;
      global.window = {
        crypto: mockCrypto
      } as any;

      const uuid = generateUUID();
      expect(uuid).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);

      // Restore original window
      global.window = originalWindow;
    });

    it('should fallback to Math.random when crypto is not available', () => {
      // Ensure window is undefined
      const originalWindow = global.window;
      delete (global as any).window;

      const uuid = generateUUID();
      expect(uuid).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);

      // Restore original window
      global.window = originalWindow;
    });
  });

  describe('generateSessionId', () => {
    it('should generate a valid UUID v4 format', () => {
      const sessionId = generateSessionId();
      expect(sessionId).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    it('should generate different session IDs on multiple calls', () => {
      const sessionId1 = generateSessionId();
      const sessionId2 = generateSessionId();
      expect(sessionId1).not.toBe(sessionId2);
    });
  });

  describe('isValidUUID', () => {
    it('should return true for valid UUID v4', () => {
      const validUUIDs = [
        '550e8400-e29b-41d4-a716-************',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
        '6ba7b811-9dad-11d1-80b4-00c04fd430c8'
      ];

      validUUIDs.forEach(uuid => {
        expect(isValidUUID(uuid)).toBe(true);
      });
    });

    it('should return false for invalid UUIDs', () => {
      const invalidUUIDs = [
        '',
        'not-a-uuid',
        '550e8400-e29b-41d4-a716',
        '550e8400-e29b-41d4-a716-************-extra',
        '550e8400-e29b-41d4-a716-44665544000g', // invalid character
        '550e8400e29b41d4a716************', // missing dashes
        '550e8400-e29b-41d4-a716-************-', // extra dash
        'gggggggg-gggg-gggg-gggg-gggggggggggg' // invalid characters
      ];

      invalidUUIDs.forEach(uuid => {
        expect(isValidUUID(uuid)).toBe(false);
      });
    });
  });

  describe('generateShortId', () => {
    it('should generate an 8-character string', () => {
      const shortId = generateShortId();
      expect(shortId).toHaveLength(8);
      expect(shortId).toMatch(/^[0-9a-f]{8}$/i);
    });

    it('should generate different short IDs on multiple calls', () => {
      const shortId1 = generateShortId();
      const shortId2 = generateShortId();
      expect(shortId1).not.toBe(shortId2);
    });
  });

  describe('Browser compatibility', () => {
    it('should work in Node.js environment (no window)', () => {
      const originalWindow = global.window;
      delete (global as any).window;

      const uuid = generateUUID();
      expect(isValidUUID(uuid)).toBe(true);

      global.window = originalWindow;
    });

    it('should work in browser environment with crypto', () => {
      const originalWindow = global.window;
      global.window = {
        crypto: mockCrypto
      } as any;

      const uuid = generateUUID();
      expect(isValidUUID(uuid)).toBe(true);

      global.window = originalWindow;
    });

    it('should work in browser environment without crypto', () => {
      const originalWindow = global.window;
      global.window = {} as any;

      const uuid = generateUUID();
      expect(isValidUUID(uuid)).toBe(true);

      global.window = originalWindow;
    });
  });
});
