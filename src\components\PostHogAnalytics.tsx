'use client'

import { useEffect } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'
import { trackPageView } from '@/lib/analytics/posthog'

/**
 * PostHogAnalytics component for tracking page views
 * This component should be included in the root layout to track all page views
 */
export default function PostHogAnalytics() {
  const pathname = usePathname() || ''
  const searchParams = useSearchParams()

  useEffect(() => {
    // Skip tracking for admin and management portal pages
    if (pathname.startsWith('/admin') || pathname.startsWith('/management-portal-x7z9y2')) {
      return
    }

    // Track page view
    trackPageView(pathname)
  }, [pathname, searchParams])

  // This component doesn't render anything
  return null
}
