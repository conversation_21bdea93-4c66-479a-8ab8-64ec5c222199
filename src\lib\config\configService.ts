/**
 * Configuration service for Fademail
 *
 * This service provides functions to get and set configuration values
 * stored in the Supabase database.
 */
import { createServerSupabaseClient } from '@/lib/supabase';
import NodeCache from 'node-cache';
import { AppConfig, DomainConfig, AdConfig } from './types';
import { logInfo, logError } from '@/lib/logging';
import { Json } from '@/lib/database.types';

// Cache configuration values for 5 minutes
export const configCache = new NodeCache({ stdTTL: 300, checkperiod: 60 });

// Default configuration values
export const defaultAppConfig: AppConfig = {
  emailExpirationMinutes: 30,
  autoRefreshInterval: 11,  // 11 seconds base + 3 seconds post-fetch delay = 14 seconds effective
  maxEmailsPerAddress: 100,
  maintenanceMode: false,
  cleanupIntervalMinutes: 15,
  cleanupBatchSize: 1000,
  maintenanceIntervalHours: 24,  // Run maintenance once a day by default
  analyticsEnabled: true  // Default to enabled, PostHog handles user opt-in/out separately
};

/**
 * Get a configuration value
 *
 * @param key The configuration key
 * @returns The configuration value
 */
export async function getConfig<K extends keyof AppConfig>(key: K): Promise<AppConfig[K]> {
  try {
    // Check cache first
    const cacheKey = `config:${key}`;
    const cachedValue = configCache.get<AppConfig[K]>(cacheKey);
    if (cachedValue !== undefined) {
      return cachedValue;
    }

    // Get from database
    const supabase = createServerSupabaseClient();

    try {
      const { data, error } = await supabase
        .from('app_config')
        .select('value')
        .eq('key', key)
        .single();

      if (error) {
        // If the table doesn't exist, return default
        if (error.code === 'PGRST116') {
          logInfo('config', `app_config table does not exist, using default value for ${String(key)}`);
          const defaultValue = defaultAppConfig[key];
          configCache.set(cacheKey, defaultValue);
          return defaultValue;
        }

        // If the key doesn't exist, return default
        if (error.code === 'PGRST104') {
          logInfo('config', `Configuration key ${String(key)} not found, using default value`);
          const defaultValue = defaultAppConfig[key];
          configCache.set(cacheKey, defaultValue);
          return defaultValue;
        }

        // For other errors, log and return default
        logError('config', `Error querying app_config table for key ${String(key)}`, { error });
        const defaultValue = defaultAppConfig[key];
        configCache.set(cacheKey, defaultValue);
        return defaultValue;
      }

      if (!data) {
        // No data found, return default
        logInfo('config', `Configuration key ${String(key)} not found, using default value`);
        const defaultValue = defaultAppConfig[key];
        configCache.set(cacheKey, defaultValue);
        return defaultValue;
      }

      // Parse the value based on the expected type
      let parsedValue: AppConfig[K];

      switch (typeof defaultAppConfig[key]) {
        case 'number':
          parsedValue = Number(data.value) as AppConfig[K];
          break;
        case 'boolean':
          parsedValue = (data.value === true || data.value === 'true') as unknown as AppConfig[K];
          break;
        default:
          parsedValue = data.value as AppConfig[K];
      }

      // Cache the value
      configCache.set(cacheKey, parsedValue);
      return parsedValue;
    } catch (dbError) {
      // Handle database errors
      logError('config', `Error accessing database for key ${String(key)}`, { dbError });
      const defaultValue = defaultAppConfig[key];
      configCache.set(cacheKey, defaultValue);
      return defaultValue;
    }
  } catch (error) {
    logError('config', `Error getting config value for ${String(key)}`, { error });
    return defaultAppConfig[key];
  }
}

/**
 * Get all configuration values
 *
 * @returns All configuration values
 */
export async function getAllConfig(): Promise<AppConfig> {
  try {
    // Check cache first
    const cacheKey = 'config:all';
    const cachedValue = configCache.get<AppConfig>(cacheKey);
    if (cachedValue !== undefined) {
      return cachedValue;
    }

    // Get from database
    const supabase = createServerSupabaseClient();

    // First check if the table exists
    try {
      const { data, error } = await supabase
        .from('app_config')
        .select('key, value');

      if (error) {
        // If the table doesn't exist, return defaults
        if (error.code === 'PGRST116') {
          logInfo('config', 'app_config table does not exist, using default values');
          configCache.set(cacheKey, defaultAppConfig);
          return { ...defaultAppConfig };
        }

        // For other errors, log and return defaults
        logError('config', 'Error querying app_config table', { error });
        configCache.set(cacheKey, defaultAppConfig);
        return { ...defaultAppConfig };
      }

      if (!data || data.length === 0) {
        // No data found, return defaults
        logInfo('config', 'No configuration found in database, using default values');
        configCache.set(cacheKey, defaultAppConfig);
        return { ...defaultAppConfig };
      }

      // Build config object from database values
      const config: Partial<AppConfig> = { ...defaultAppConfig };

      for (const item of data) {
        const key = item.key as keyof AppConfig;

        // Parse the value based on the expected type
        switch (typeof defaultAppConfig[key]) {
          case 'number':
            config[key] = Number(item.value) as any;
            break;
          case 'boolean':
            config[key] = (item.value === true || item.value === 'true') as any;
            break;
          default:
            config[key] = item.value as any;
        }
      }

      const fullConfig = config as AppConfig;

      // Cache the value
      configCache.set(cacheKey, fullConfig);
      return fullConfig;
    } catch (dbError) {
      // Handle database errors
      logError('config', 'Error accessing database', { dbError });
      configCache.set(cacheKey, defaultAppConfig);
      return { ...defaultAppConfig };
    }
  } catch (error) {
    logError('config', 'Error getting all config values', { error });
    return { ...defaultAppConfig };
  }
}

/**
 * Update a configuration value
 *
 * @param key The configuration key
 * @param value The new value
 * @returns True if successful, false otherwise
 */
export async function updateConfig<K extends keyof AppConfig>(key: K, value: AppConfig[K]): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();

    // Ensure value is not undefined (Json type doesn't allow undefined)
    const jsonValue = value === undefined ? null : value;

    const { error } = await supabase
      .from('app_config')
      .upsert({
        key,
        value: jsonValue as Json,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'key'
      });

    if (error) {
      logError('config', `Error updating config value for ${String(key)}`, { error });
      return false;
    }

    // Update cache
    const cacheKey = `config:${key}`;
    configCache.set(cacheKey, value);

    // Invalidate the all config cache
    configCache.del('config:all');

    logInfo('config', `Updated config value for ${String(key)}`, { value });
    return true;
  } catch (error) {
    logError('config', `Error updating config value for ${String(key)}`, { error });
    return false;
  }
}

/**
 * Update multiple configuration values
 *
 * @param updates The configuration updates
 * @returns True if successful, false otherwise
 */
export async function updateMultipleConfig(updates: Partial<AppConfig>): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();

    // Convert updates to array of upsert objects
    // Ensure values are not undefined (Json type doesn't allow undefined)
    const upserts = Object.entries(updates).map(([key, value]) => ({
      key,
      value: value === undefined ? null : value as Json,
      updated_at: new Date().toISOString()
    }));

    const { error } = await supabase
      .from('app_config')
      .upsert(upserts, {
        onConflict: 'key'
      });

    if (error) {
      logError('config', 'Error updating multiple config values', { error });
      return false;
    }

    // Update cache for each key
    for (const [key, value] of Object.entries(updates)) {
      const cacheKey = `config:${key}`;
      configCache.set(cacheKey, value);
    }

    // Invalidate the all config cache
    configCache.del('config:all');

    logInfo('config', 'Updated multiple config values', { updates });
    return true;
  } catch (error) {
    logError('config', 'Error updating multiple config values', { error });
    return false;
  }
}

/**
 * Clear the configuration cache
 */
export function clearConfigCache(): void {
  configCache.flushAll();
  logInfo('config', 'Cleared configuration cache');
}

/**
 * Get a configuration value with any key (for custom configurations)
 *
 * @param key The configuration key (any string)
 * @returns The configuration value or null if not found
 */
export async function getCustomConfig(key: string): Promise<any> {
  try {
    // Check cache first
    const cacheKey = `config:${key}`;
    const cachedValue = configCache.get(cacheKey);
    if (cachedValue !== undefined) {
      return cachedValue;
    }

    // Get from database
    const supabase = createServerSupabaseClient();

    try {
      const { data, error } = await supabase
        .from('app_config')
        .select('value')
        .eq('key', key)
        .single();

      if (error || !data) {
        // Key not found or error
        logInfo('config', `Custom configuration key ${key} not found or error occurred`);
        return null;
      }

      // Cache the value
      configCache.set(cacheKey, data.value);
      return data.value;
    } catch (dbError) {
      // Handle database errors
      logError('config', `Error accessing database for custom key ${key}`, { dbError });
      return null;
    }
  } catch (error) {
    logError('config', `Error getting custom config value for ${key}`, { error });
    return null;
  }
}

/**
 * Set a configuration value with any key (for custom configurations)
 *
 * @param key The configuration key (any string)
 * @param value The value to set
 * @returns True if successful, false otherwise
 */
export async function setCustomConfig(key: string, value: any): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();

    // Ensure value is not undefined (Json type doesn't allow undefined)
    const jsonValue = value === undefined ? null : value;

    const { error } = await supabase
      .from('app_config')
      .upsert({
        key,
        value: jsonValue as Json,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'key'
      });

    if (error) {
      logError('config', `Error setting custom config value for ${key}`, { error });
      return false;
    }

    // Update cache
    const cacheKey = `config:${key}`;
    configCache.set(cacheKey, value);

    logInfo('config', `Set custom config value for ${key}`, { value });
    return true;
  } catch (error) {
    logError('config', `Error setting custom config value for ${key}`, { error });
    return false;
  }
}

// Export all functions as a configService object
export const configService = {
  getConfig,
  getAllConfig,
  setConfig: updateConfig,  // Alias for updateConfig
  updateConfig,
  updateMultipleConfig,
  clearConfigCache,
  getCustomConfig,
  setCustomConfig
};
