/**
 * API route for managing individual ad placements
 */
import { NextRequest, NextResponse } from 'next/server';
import { getAdPlacement, updateAdPlacement, deleteAdPlacement } from '@/lib/config/adService';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/admin/ads/[placementId]/[domain]
 *
 * Get a specific ad placement
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ placementId: string; domain: string }> }
) {
  try {
    const { placementId, domain } = await params;

    // Get ad placement
    const adPlacement = await getAdPlacement(placementId, domain);

    if (!adPlacement) {
      return NextResponse.json(
        { success: false, error: 'Ad placement not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: adPlacement
    });
  } catch (error) {
    logError('api', 'Error getting ad placement', { error, params });

    return NextResponse.json(
      { success: false, error: 'Failed to get ad placement' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/ads/[placementId]/[domain]
 *
 * Update an ad placement
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ placementId: string; domain: string }> }
) {
  try {
    const { placementId, domain } = await params;

    // Get update data from request body
    const updates = await request.json();

    // Update ad placement
    const updatedAd = await updateAdPlacement(placementId, domain, updates);

    if (!updatedAd) {
      return NextResponse.json(
        { success: false, error: 'Failed to update ad placement' },
        { status: 500 }
      );
    }

    logInfo('api', 'Updated ad placement', { placementId, domain, updates });

    return NextResponse.json({
      success: true,
      data: updatedAd,
      message: 'Ad placement updated successfully'
    });
  } catch (error) {
    logError('api', 'Error updating ad placement', { error, params });

    return NextResponse.json(
      { success: false, error: 'Failed to update ad placement' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/ads/[placementId]/[domain]
 *
 * Delete an ad placement
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ placementId: string; domain: string }> }
) {
  try {
    const { placementId, domain } = await params;

    // Delete ad placement
    const success = await deleteAdPlacement(placementId, domain);

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to delete ad placement' },
        { status: 500 }
      );
    }

    logInfo('api', 'Deleted ad placement', { placementId, domain });

    return NextResponse.json({
      success: true,
      message: 'Ad placement deleted successfully'
    });
  } catch (error) {
    logError('api', 'Error deleting ad placement', { error, params });

    return NextResponse.json(
      { success: false, error: 'Failed to delete ad placement' },
      { status: 500 }
    );
  }
}
