/**
 * This is a placeholder file to replace the original analytics toggle route
 * that was causing deployment errors.
 * 
 * The original file was trying to update the 'analyticsEnabled' property
 * which is not part of the AppConfig interface.
 */
import { NextRequest, NextResponse } from 'next/server';

/**
 * POST handler for analytics toggle
 * This is a placeholder that returns a success response
 */
export async function POST(request: NextRequest) {
  return NextResponse.json({
    success: true,
    message: 'Analytics is now handled by PostHog and configured via environment variables'
  });
}
