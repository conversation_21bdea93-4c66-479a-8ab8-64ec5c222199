'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

/**
 * Admin index page that redirects to the monitoring dashboard
 */
export default function AdminIndexPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the monitoring dashboard
    router.push('/admin/monitoring');
  }, [router]);

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-md">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mb-4"></div>
          <p className="text-gray-600">Redirecting to monitoring dashboard...</p>
        </div>
      </div>
    </div>
  );
}
