/**
 * Helper functions for admin path management
 */
import { ADMIN } from '@/lib/constants';

/**
 * Get the secure admin path from constants
 * @returns The secure admin path
 */
export function getSecureAdminPath(): string {
  return ADMIN.SECURE_PATH;
}

/**
 * Convert an old admin path to the new secure admin path
 * @param oldPath The old admin path (e.g., '/admin/analytics')
 * @returns The new secure admin path (e.g., '/management-portal-x7z9y2/analytics')
 */
export function convertToSecurePath(oldPath: string): string {
  if (!oldPath.startsWith('/admin')) {
    return oldPath;
  }
  
  return oldPath.replace('/admin', `/${getSecureAdminPath()}`);
}

/**
 * Check if a path is an admin path
 * @param path The path to check
 * @returns True if the path is an admin path
 */
export function isAdminPath(path: string): boolean {
  return path.startsWith('/admin') || path.startsWith(`/${getSecureAdminPath()}`);
}

/**
 * Get the login path for the secure admin area
 * @returns The login path
 */
export function getSecureLoginPath(): string {
  return `/${getSecureAdminPath()}/login`;
}

/**
 * Get the dashboard path for the secure admin area
 * @returns The dashboard path
 */
export function getSecureDashboardPath(): string {
  return `/${getSecureAdminPath()}/analytics`;
}
