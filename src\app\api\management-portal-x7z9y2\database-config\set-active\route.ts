import { NextRequest, NextResponse } from 'next/server';
import { setActiveConfiguration } from '@/lib/services/databaseConfigService';
import { resetConnections } from '@/lib/db/connectionManager';

/**
 * POST /api/management-portal-x7z9y2/database-config/set-active
 * 
 * Set a database configuration as active
 */
export async function POST(request: NextRequest) {
  try {
    const { id } = await request.json();
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'ID is required' },
        { status: 400 }
      );
    }
    
    const result = await setActiveConfiguration(id);
    
    if (result.success) {
      // Reset connections to use the new configuration
      await resetConnections();
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error setting active configuration:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
