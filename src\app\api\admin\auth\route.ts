import { NextRequest, NextResponse } from 'next/server';
import { sign, verify } from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { checkRateLimit, resetRateLimit } from '../../../../lib/auth/rate-limit';
import { ADMIN } from '../../../../lib/constants';
import { verifyAdminCredentials, logAdminActivity } from '../../../../lib/admin/user-service';
import bcrypt from 'bcrypt';

// Rate limiting is now handled by the imported utility

/**
 * Verify admin login credentials using environment variables
 * Consolidated function to avoid module resolution issues in deployment
 */
async function verifyAdminLogin(username: string, password: string): Promise<boolean> {
  try {
    const envUsername = process.env.ADMIN_USERNAME;
    const envPasswordHash = process.env.ADMIN_PASSWORD_HASH;
    const envPassword = process.env.ADMIN_PASSWORD;

    if (!envUsername || (!envPasswordHash && !envPassword)) {
      console.error('Admin credentials not configured in environment variables');
      return false;
    }

    if (envUsername !== username) {
      return false;
    }

    // Use hashed password if available
    if (envPasswordHash) {
      return await bcrypt.compare(password, envPasswordHash);
    }

    // Fallback to plain password comparison (not recommended for production)
    if (envPassword) {
      console.warn('Using plain-text password comparison. Please migrate to ADMIN_PASSWORD_HASH.');
      return password === envPassword;
    }

    return false;
  } catch (error) {
    console.error('Error verifying admin login:', error);
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Check rate limit
    if (!checkRateLimit(ip)) {
      return NextResponse.json(
        { success: false, message: 'Too many login attempts. Please try again later.' },
        { status: 429 }
      );
    }

    const { username, password } = await request.json();

    // First try to verify credentials against the database
    const { user, valid } = await verifyAdminCredentials(username, password);

    // If database authentication fails, try secure credential manager
    let isSecureAuth = false;
    if (!valid) {
      isSecureAuth = await verifyAdminLogin(username, password);
    }

    // If both database and secure authentication fail
    if ((!valid || !user) && !isSecureAuth) {
      // Add delay to prevent timing attacks
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Log failed login attempt
      console.warn(`Failed login attempt for username: ${username} from IP: ${ip}`);

      return NextResponse.json(
        { success: false, message: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Create admin user object for token generation
    const adminUser = user || {
      id: 1, // Default admin ID
      username: username, // Use the authenticated username
      email: '<EMAIL>', // Default admin email
      fullName: 'Administrator',
      role: 'admin' as const,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    };

    // Ensure JWT_SECRET is configured
    if (!process.env.JWT_SECRET) {
      console.error('Missing required environment variable: JWT_SECRET');
      return NextResponse.json(
        { success: false, message: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create a JWT token with expiration
    const token = sign(
      {
        userId: adminUser.id,
        username: adminUser.username,
        role: adminUser.role
      },
      process.env.JWT_SECRET,
      { expiresIn: `${ADMIN.TOKEN_EXPIRATION_HOURS}h` }
    );

    // Create a response object
    const response = NextResponse.json({ success: true });

    // Set HTTP-only cookie on the response
    response.cookies.set({
      name: 'admin_token',
      value: token,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60 * ADMIN.TOKEN_EXPIRATION_HOURS, // Convert hours to seconds
      path: '/'
    });

    // We don't need to use the cookies API here since we're already setting the cookie in the response
    // The response.cookies.set is sufficient to set the cookie

    // Reset rate limit after successful login
    resetRateLimit(ip);

    // Log successful login
    console.info(`Successful login for username: ${adminUser.username} from IP: ${ip}`);

    // Log the activity in the database if using database user
    if (valid && user) {
      await logAdminActivity(
        adminUser.id,
        'login',
        null,
        null,
        { success: true },
        ip,
        userAgent
      );
    }

    return response;
  } catch (error) {
    console.error('Authentication error:', error);
    return NextResponse.json(
      { success: false, message: 'Authentication failed' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  // Endpoint to check if user is authenticated
  const token = request.cookies.get('admin_token')?.value;

  if (!token) {
    return NextResponse.json({ authenticated: false });
  }

  // Check if JWT_SECRET is set
  if (!process.env.JWT_SECRET) {
    console.error('Missing required environment variable: JWT_SECRET');
    return NextResponse.json({ authenticated: false });
  }

  try {
    // Verify the token
    const decoded = verify(token, process.env.JWT_SECRET);

    // Type guard to check if decoded is a JwtPayload with our expected properties
    if (typeof decoded === 'object' && decoded !== null && 'userId' in decoded && 'username' in decoded && 'role' in decoded) {
      // Return user information including role
      return NextResponse.json({
        authenticated: true,
        user: {
          userId: decoded.userId,
          username: decoded.username,
          role: decoded.role
        }
      });
    } else {
      console.error('Invalid token format');
      return NextResponse.json({ authenticated: false });
    }
  } catch (error) {
    // Token is invalid or expired
    console.error('Token verification error:', error);
    return NextResponse.json({ authenticated: false });
  }
}


