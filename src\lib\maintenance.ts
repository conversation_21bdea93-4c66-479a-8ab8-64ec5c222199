/**
 * Maintenance mode utilities
 * 
 * These functions help detect and handle maintenance mode on the client side
 */

// Cache for maintenance mode status to avoid repeated checks
let maintenanceModeCache: {
  status: boolean | null;
  timestamp: number;
} = {
  status: null,
  timestamp: 0
};

// Cache TTL in milliseconds (30 seconds)
const CACHE_TTL = 30000;

/**
 * Check if the site is in maintenance mode
 * 
 * This function checks if the site is in maintenance mode by making a lightweight
 * request to the server. It caches the result to avoid repeated requests.
 * 
 * @returns {Promise<boolean>} True if the site is in maintenance mode
 */
export async function isInMaintenanceMode(): Promise<boolean> {
  const now = Date.now();
  
  // Return cached value if still valid
  if (maintenanceModeCache.status !== null && maintenanceModeCache.timestamp > now - CACHE_TTL) {
    return maintenanceModeCache.status;
  }
  
  try {
    // Make a lightweight request to check maintenance mode
    const response = await fetch('/api/status', {
      method: 'HEAD',
      cache: 'no-store'
    });
    
    // Check if the response has the maintenance mode header or status code
    const isMaintenanceMode = 
      response.headers.get('X-Maintenance-Mode') === 'true' || 
      response.status === 503;
    
    // Update cache
    maintenanceModeCache = {
      status: isMaintenanceMode,
      timestamp: now
    };
    
    return isMaintenanceMode;
  } catch (error) {
    // On error, assume not in maintenance mode
    console.error('Error checking maintenance mode:', error);
    return false;
  }
}

/**
 * Wrapper for fetch that handles maintenance mode
 * 
 * This function checks if the site is in maintenance mode before making a fetch request.
 * If the site is in maintenance mode, it returns a mock response with a 503 status code.
 * 
 * @param {RequestInfo | URL} input The URL to fetch
 * @param {RequestInit} [init] The fetch options
 * @returns {Promise<Response>} The fetch response
 */
export async function maintenanceAwareFetch(
  input: RequestInfo | URL,
  init?: RequestInit
): Promise<Response> {
  // Check if we're in maintenance mode
  if (await isInMaintenanceMode()) {
    // Return a mock response for maintenance mode
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Service is currently under maintenance',
        maintenanceMode: true
      }),
      {
        status: 503,
        headers: {
          'Content-Type': 'application/json',
          'X-Maintenance-Mode': 'true'
        }
      }
    );
  }
  
  // Otherwise, proceed with the normal fetch
  return fetch(input, init);
}
