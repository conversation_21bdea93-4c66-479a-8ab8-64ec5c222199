/**
 * <PERSON><PERSON>t to set up database maintenance functions in Supabase
 */
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service key not found in environment variables');
  process.exit(1);
}

// Create Supabase client with service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupMaintenanceFunctions() {
  try {
    console.log('Setting up maintenance functions in Supabase...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'create-maintenance-functions.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the SQL content into individual statements
    const statements = sqlContent.split(';').filter(stmt => stmt.trim() !== '');
    
    // Execute each statement
    for (const statement of statements) {
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        console.error('Error executing SQL statement:', error);
        console.log('Statement:', statement);
      }
    }
    
    console.log('Maintenance functions set up successfully');
    
    // Update app_config with maintenance interval
    const { error: configError } = await supabase
      .from('app_config')
      .upsert([
        { key: 'maintenanceIntervalHours', value: 24 } // Default: run maintenance once a day
      ]);
    
    if (configError) {
      console.error('Error updating app_config:', configError);
    } else {
      console.log('Added maintenanceIntervalHours to app_config');
    }
    
    // Test the maintenance function
    const { data, error } = await supabase.rpc(
      'perform_maintenance',
      { table_name: 'temp_emails' }
    );
    
    if (error) {
      console.error('Error testing maintenance function:', error);
    } else {
      console.log('Maintenance function test result:', data);
    }
    
  } catch (error) {
    console.error('Error setting up maintenance functions:', error);
  }
}

// Run the setup
setupMaintenanceFunctions()
  .then(() => {
    console.log('Setup complete');
    process.exit(0);
  })
  .catch(err => {
    console.error('Setup failed:', err);
    process.exit(1);
  });
