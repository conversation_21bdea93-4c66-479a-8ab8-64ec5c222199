// DMARC Record Generation Utilities - Isolated implementation
import { <PERSON>mar<PERSON><PERSON><PERSON><PERSON>, DmarcPolicyConfig, DmarcPolicy, DmarcAlignment } from '@/types/dmarc';

/**
 * Generate DMARC policy record
 * @param config - DMARC policy configuration
 * @returns DmarcRecord - Generated DMARC record
 */
export function generateDmarcRecord(config: DmarcPolicyConfig): DmarcRecord {
  try {
    // Create DMARC DNS record value
    const dnsRecord = createDmarcDnsRecord(config);

    // Create record name (_dmarc.domain)
    const recordName = `_dmarc.${config.domain}`;

    return {
      domain: config.domain,
      policy: config,
      dnsRecord,
      recordName,
    };
  } catch (error) {
    console.error('DMARC record generation error:', error);
    throw new Error(`Failed to generate DMARC record: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create DMARC DNS TXT record value
 * @param config - DMARC policy configuration
 * @returns Formatted DNS TXT record value
 */
export function createDmarcDnsRecord(config: DmarcPolicyConfig): string {
  try {
    const components = [
      'v=DMARC1', // Version (required)
      `p=${config.policy}`, // Policy (required)
    ];

    // Add reporting email address (required)
    if (config.rua) {
      // Handle both formats: with and without mailto: prefix
      const ruaValue = config.rua.startsWith('mailto:') ? config.rua : `mailto:${config.rua}`;
      components.push(`rua=${ruaValue}`);
    }

    // Add percentage if not 100%
    if (config.pct !== undefined && config.pct !== 100) {
      components.push(`pct=${config.pct}`);
    }

    // Add subdomain policy if specified
    if (config.sp && config.sp !== config.policy) {
      components.push(`sp=${config.sp}`);
    }

    // Add DKIM alignment if specified
    if (config.adkim && config.adkim !== 'r') {
      components.push(`adkim=${config.adkim}`);
    }

    // Add SPF alignment if specified
    if (config.aspf && config.aspf !== 'r') {
      components.push(`aspf=${config.aspf}`);
    }

    // Add failure reporting email address if specified
    if (config.ruf) {
      // Handle both formats: with and without mailto: prefix
      const rufValue = config.ruf.startsWith('mailto:') ? config.ruf : `mailto:${config.ruf}`;
      components.push(`ruf=${rufValue}`);
    }

    // Add failure reporting options if specified
    if (config.fo) {
      components.push(`fo=${config.fo}`);
    }

    // Add report format if specified
    if (config.rf && config.rf !== 'afrf') {
      components.push(`rf=${config.rf}`);
    }

    // Add report interval if specified
    if (config.ri && config.ri !== 86400) {
      components.push(`ri=${config.ri}`);
    }

    return components.join('; ');
  } catch (error) {
    console.error('DMARC DNS record creation error:', error);
    throw new Error(`Failed to create DMARC DNS record: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate setup instructions for DMARC implementation
 * @param dmarcRecord - DMARC record information
 * @returns Array of instruction steps
 */
export function generateDmarcInstructions(dmarcRecord: DmarcRecord): string[] {
  const { recordName, dnsRecord, domain, policy } = dmarcRecord;

  return [
    `Add a TXT record to your DNS settings for domain: ${domain}`,
    `Record Name: ${recordName}`,
    `Record Type: TXT`,
    `Record Value: ${dnsRecord}`,
    `TTL: 3600 (or your DNS provider's default)`,
    '',
    'DNS Provider Specific Instructions:',
    '• For Cloudflare: Go to DNS > Records > Add record',
    '• For GoDaddy: Go to DNS Management > Add new record',
    '• For Namecheap: Go to Advanced DNS > Add new record',
    '• For Route 53: Go to Hosted zones > Create record',
    '',
    'Policy Implementation:',
    `• Current policy: ${policy.policy.toUpperCase()}`,
    getPolicyExplanation(policy.policy),
    `• Percentage applied: ${policy.pct}%`,
    '',
    'Monitoring and Reporting:',
    `• Reports will be sent to: ${policy.rua}`,
    '• Monitor DMARC reports for authentication failures',
    '• Gradually increase policy strictness based on reports',
    '',
    'Next Steps:',
    '• Ensure SPF and DKIM records are properly configured',
    '• Monitor DMARC reports for 1-2 weeks',
    '• Consider upgrading policy from "none" to "quarantine" then "reject"',
    '• Use the DNS validation tool to verify record propagation',
  ];
}

/**
 * Get explanation for DMARC policy
 * @param policy - DMARC policy type
 * @returns Policy explanation string
 */
function getPolicyExplanation(policy: DmarcPolicy): string {
  switch (policy) {
    case 'none':
      return '  - Monitor mode: No action taken on failed emails, only reporting';
    case 'quarantine':
      return '  - Quarantine mode: Failed emails sent to spam/junk folder';
    case 'reject':
      return '  - Reject mode: Failed emails are rejected and not delivered';
    default:
      return '  - Unknown policy';
  }
}

/**
 * Validate DMARC record format
 * @param dnsRecord - DNS record value to validate
 * @returns boolean - True if valid DMARC record format
 */
export function validateDmarcRecordFormat(dnsRecord: string): boolean {
  try {
    // Check for required DMARC components
    const requiredComponents = ['v=DMARC1', 'p='];

    return requiredComponents.every(component =>
      dnsRecord.includes(component)
    );
  } catch (error) {
    console.error('DMARC record validation error:', error);
    return false;
  }
}

/**
 * Parse DMARC DNS record to extract components
 * @param dnsRecord - DNS record value to parse
 * @returns Parsed DMARC policy configuration
 */
export function parseDmarcRecord(dnsRecord: string): Partial<DmarcPolicyConfig> {
  try {
    const config: Partial<DmarcPolicyConfig> = {};

    // Split by semicolon and parse key-value pairs
    const parts = dnsRecord.split(';').map(part => part.trim());

    for (const part of parts) {
      const [key, value] = part.split('=', 2);
      if (key && value) {
        const trimmedKey = key.trim();
        const trimmedValue = value.trim();

        switch (trimmedKey) {
          case 'p':
            if (['none', 'quarantine', 'reject'].includes(trimmedValue)) {
              config.policy = trimmedValue as DmarcPolicy;
            }
            break;
          case 'rua':
            config.rua = trimmedValue.replace('mailto:', '');
            break;
          case 'ruf':
            config.ruf = trimmedValue.replace('mailto:', '');
            break;
          case 'pct':
            const pct = parseInt(trimmedValue, 10);
            if (!isNaN(pct) && pct >= 0 && pct <= 100) {
              config.pct = pct;
            }
            break;
          case 'sp':
            if (['none', 'quarantine', 'reject'].includes(trimmedValue)) {
              config.sp = trimmedValue as DmarcPolicy;
            }
            break;
          case 'adkim':
            if (['r', 's'].includes(trimmedValue)) {
              config.adkim = trimmedValue as DmarcAlignment;
            }
            break;
          case 'aspf':
            if (['r', 's'].includes(trimmedValue)) {
              config.aspf = trimmedValue as DmarcAlignment;
            }
            break;
          case 'fo':
            config.fo = trimmedValue;
            break;
          case 'rf':
            config.rf = trimmedValue;
            break;
          case 'ri':
            const ri = parseInt(trimmedValue, 10);
            if (!isNaN(ri) && ri > 0) {
              config.ri = ri;
            }
            break;
        }
      }
    }

    return config;
  } catch (error) {
    console.error('DMARC record parsing error:', error);
    return {};
  }
}

/**
 * Format record name for display
 * @param domain - Domain name
 * @returns Formatted DMARC record name
 */
export function formatDmarcRecordName(domain: string): string {
  return `_dmarc.${domain}`;
}
