'use client';

import { useEffect } from 'react';
import { logInfo, logError } from '@/lib/logging';
import { ADMIN } from '@/lib/constants';

/**
 * Component to initialize the cleanup scheduler
 * This component is used in the app layout to start the cleanup scheduler when the application starts
 */
export default function CleanupSchedulerInitializer() {
  useEffect(() => {
    // Use direct admin API path instead of relying on rewrites
    const initializeCleanupScheduler = async () => {
      try {
        // Check if the scheduler is already running - use direct admin API path
        const statusResponse = await fetch('/api/admin/cleanup/status');

        // Check if the response is a redirect (which would indicate auth issues)
        if (statusResponse.redirected) {
          // If we're being redirected, we're likely not authenticated
          // Just log a debug message and exit gracefully
          console.debug('Cleanup scheduler check skipped - authentication required');
          return;
        }

        // Check if the response is successful
        if (!statusResponse.ok) {
          console.debug('Cleanup scheduler check failed with status:', statusResponse.status);
          return;
        }

        const statusData = await statusResponse.json();

        // Check if auto-start is enabled in configuration
        if (statusData.success && !statusData.status.running && statusData.status.autoStart !== false) {
          try {
            // Start the scheduler - this requires authentication - use direct admin API path
            const startResponse = await fetch('/api/admin/cleanup/scheduler', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'start',
                intervalMinutes: statusData.status.intervalMinutes || 15
              })
            });

            // Check if the response is a redirect (which would indicate auth issues)
            if (startResponse.redirected) {
              // If we're being redirected, we're likely not authenticated
              // Just log a debug message and exit gracefully
              console.debug('Cleanup scheduler start skipped - authentication required');
              return;
            }

            // Check if the response is successful
            if (!startResponse.ok) {
              console.debug('Cleanup scheduler start failed with status:', startResponse.status);
              return;
            }

            const startData = await startResponse.json();

            if (startData.success) {
              logInfo('cleanup', 'Cleanup scheduler initialized on application start');
            } else {
              logError('cleanup', 'Failed to initialize cleanup scheduler', { error: startData.error });
            }
          } catch (startError) {
            // Only log as debug to avoid filling error logs with auth-related errors
            console.debug('Could not start cleanup scheduler:', startError);
          }
        }
      } catch (error) {
        // Check if this is an authentication error (typically happens after logout)
        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
          // This is likely due to a redirect after authentication failure
          // Just log a debug message instead of an error
          console.debug('Cleanup scheduler check skipped - possible authentication issue');
        } else {
          // For other errors, log as usual
          logError('cleanup', 'Error initializing cleanup scheduler', { error });
        }
      }
    };

    // Initialize the cleanup scheduler
    initializeCleanupScheduler();

    // Cleanup function
    return () => {
      // We don't stop the scheduler when the component unmounts
      // because we want it to continue running in the background
    };
  }, []);

  // This component doesn't render anything
  return null;
}
