'use client';

import React from 'react';

interface ImageLoadingIndicatorProps {
  /**
   * Total number of images
   */
  totalImages: number;

  /**
   * Number of loaded images
   */
  loadedImages: number;

  /**
   * Loading progress percentage (0-100)
   */
  progress: number;

  /**
   * Optional className for the container
   */
  className?: string;
}

/**
 * ImageLoadingIndicator Component
 *
 * Displays image loading progress with a progress bar and text
 */
export function ImageLoadingIndicator({
  totalImages,
  loadedImages,
  progress,
  className = ''
}: ImageLoadingIndicatorProps) {
  // Don't render anything if there are no images
  if (totalImages === 0) {
    return null;
  }

  return (
    <div className={className}>
      {/* Text indicator with icon */}
      <div className="flex items-center text-xs text-gray-600 mb-1">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <span className="font-medium">{loadedImages}/{totalImages}</span>
        <span className="mx-1 text-gray-400">•</span>
        <span className={`font-medium ${progress === 100 ? 'text-[#28c08e]' : 'text-[#ce601c]'}`}>
          {progress}%
        </span>
      </div>

      {/* Progress bar - only show if not all images are loaded */}
      {loadedImages < totalImages && (
        <div className="w-full bg-gray-200 rounded-full h-1.5 mb-2 overflow-hidden">
          <div
            className="bg-gradient-to-r from-[#ce601c] to-[#b85518] h-1.5 rounded-full transition-all duration-300 ease-out shadow-sm"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </div>
  );
}
