/**
 * Application-wide constants for VanishPost
 */

/**
 * Admin-related constants
 */
export const ADMIN = {
  /** Secure admin path - this should match the environment variable SECURE_ADMIN_PATH */
  SECURE_PATH: process.env.SECURE_ADMIN_PATH || 'management-portal-x7z9y2',

  /** Public admin path used in client-side code - matches NEXT_PUBLIC_ADMIN_UI_PATH */
  PUBLIC_PATH: process.env.NEXT_PUBLIC_ADMIN_UI_PATH || 'admin-portal',

  /** JWT token expiration time in hours */
  TOKEN_EXPIRATION_HOURS: 4,

  /** Maximum failed login attempts before rate limiting */
  MAX_LOGIN_ATTEMPTS: 5,

  /** Rate limit window in minutes */
  RATE_LIMIT_WINDOW_MINUTES: 15,
};

/**
 * Time-related constants in milliseconds
 */
export const TIMEOUTS = {
  /** Auto-refresh interval (11 seconds base + 3 seconds post-fetch delay = 14 seconds effective) */
  AUTO_REFRESH_INTERVAL: 11000,

  /** Initial delay before starting auto-refresh (15 seconds) */
  INITIAL_REFRESH_DELAY: 15000,

  /** Delay after a fetch before the next auto-refresh cycle (3 seconds) */
  POST_FETCH_DELAY: 3000,

  /** Delay after generating a new address before the first GET API call (5 seconds) */
  NEW_ADDRESS_DELAY: 5000,

  /** Delay after deleting an email before resuming auto-refresh (2 seconds) */
  EMAIL_DELETION_DELAY: 2000,

  /** Database connection timeout (10 seconds) */
  CONNECTION_TIMEOUT: 10000,

  /** Default email expiration time in hours (24 hours) */
  DEFAULT_EMAIL_EXPIRATION_HOURS: 24,
};

/**
 * UI-related constants
 */
export const UI = {
  /** Maximum length of email preview text */
  MAX_PREVIEW_LENGTH: 100,

  /** Time format for display (hours and minutes) */
  TIME_FORMAT: { hour: '2-digit', minute: '2-digit' } as const,

  /** Time format with seconds for display */
  TIME_FORMAT_WITH_SECONDS: { hour: '2-digit', minute: '2-digit', second: '2-digit' } as const,

  /** CSS class for unread emails */
  UNREAD_EMAIL_CLASS: 'bg-neutral-50/30 border-l-4 border-transparent',

  /** CSS class for read emails */
  READ_EMAIL_CLASS: 'border-l-4 border-transparent',

  /** CSS class for selected emails */
  SELECTED_EMAIL_CLASS: 'bg-neutral-100 border-l-4 border-primary-500',
};

/**
 * API-related constants
 */
export const API = {
  /** Default page size for pagination */
  DEFAULT_PAGE_SIZE: 10,

  /** Default sort field */
  DEFAULT_SORT_BY: 'date',

  /** Default sort order */
  DEFAULT_SORT_ORDER: 'desc',

  /** Valid sort columns for emails */
  VALID_SORT_COLUMNS: ['date', 'from', 'subject', 'mail_id'] as const,

  /** Valid sort orders */
  VALID_SORT_ORDERS: ['asc', 'desc'] as const,
};

/**
 * Storage-related constants
 */
export const STORAGE = {
  /** LocalStorage key for read status */
  READ_STATUS_KEY: 'vanishpost_read_status',

  /** LocalStorage key for email address */
  EMAIL_ADDRESS_KEY: 'vanishpost_email_address',

  /** LocalStorage key for auto-refresh active state */
  AUTO_REFRESH_ACTIVE_KEY: 'vanishpost_auto_refresh_active',

  /** LocalStorage key for auto-refresh last start time */
  AUTO_REFRESH_LAST_START_KEY: 'vanishpost_auto_refresh_last_start',

  /** LocalStorage key for auto-refresh interval */
  AUTO_REFRESH_INTERVAL_KEY: 'vanishpost_auto_refresh_interval',

  /** LocalStorage key for auto-refresh email address */
  AUTO_REFRESH_EMAIL_KEY: 'vanishpost_auto_refresh_email',

  /** LocalStorage key for email expiration date */
  EMAIL_EXPIRATION_KEY: 'vanishpost_email_expiration',
};

/**
 * Guide email constants
 */
export const GUIDE = {
  /** Number of guide emails to show */
  GUIDE_EMAIL_COUNT: 3,
};

/**
 * Database configuration constants
 */
export const DATABASE = {
  /** Default local database host */
  DEFAULT_LOCAL_HOST: 'localhost',

  /** Default local database user */
  DEFAULT_LOCAL_USER: 'tempmail_web_user',

  /** Default local database name */
  DEFAULT_LOCAL_DB_NAME: 'tempmail_web',

  /** Default guerrilla database host */
  DEFAULT_GUERRILLA_HOST: process.env.GUERRILLA_DB_HOST || 'localhost',

  /** Default guerrilla database user */
  DEFAULT_GUERRILLA_USER: process.env.GUERRILLA_DB_USER || 'user',

  /** Default guerrilla database name */
  DEFAULT_GUERRILLA_DB_NAME: process.env.GUERRILLA_DB_NAME || 'guerrilla_db',

  /** Default database port */
  DEFAULT_PORT: 3306,

  /** Default connection limit */
  DEFAULT_CONNECTION_LIMIT: 20,

  /** Default queue limit (0 means unlimited) */
  DEFAULT_QUEUE_LIMIT: 0,

  /** Default connection idle timeout in milliseconds (60 seconds) */
  CONNECTION_IDLE_TIMEOUT: 60000,

  /** Default connection acquire timeout in milliseconds (30 seconds) */
  CONNECTION_ACQUIRE_TIMEOUT: 30000,

  /**
   * Default SSL configuration
   * Note: We use undefined instead of false to match the expected type in mysql2
   * The mysql2 library expects ssl to be either undefined, a string, or an SslOptions object
   */
  DEFAULT_SSL: undefined,
};
