'use client';

import { useEffect } from 'react';
import { logInfo, logError } from '@/lib/logging';

/**
 * Component to initialize the maintenance scheduler
 * This component is used in the app layout to start the maintenance scheduler when the application starts
 */
export default function MaintenanceSchedulerInitializer() {
  useEffect(() => {
    const initializeMaintenanceScheduler = async () => {
      try {
        // Check if the scheduler is already running
        const statusResponse = await fetch('/api/management-portal-x7z9y2/maintenance/scheduler');

        // Check if the response is a redirect (which would indicate auth issues)
        if (statusResponse.redirected) {
          // If we're being redirected, we're likely not authenticated
          // Just log a debug message and exit gracefully
          console.debug('Maintenance scheduler check skipped - authentication required');
          return;
        }

        // Check if the response is successful
        if (!statusResponse.ok) {
          console.debug('Maintenance scheduler check failed with status:', statusResponse.status);
          return;
        }

        const statusData = await statusResponse.json();

        if (statusData.success && !statusData.status.running) {
          try {
            // Start the scheduler - this requires authentication
            const startResponse = await fetch('/api/management-portal-x7z9y2/maintenance/scheduler', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'start',
                intervalHours: statusData.status.intervalHours || 24
              })
            });

            // Check if the response is a redirect (which would indicate auth issues)
            if (startResponse.redirected) {
              // If we're being redirected, we're likely not authenticated
              // Just log a debug message and exit gracefully
              console.debug('Maintenance scheduler start skipped - authentication required');
              return;
            }

            // Check if the response is successful
            if (!startResponse.ok) {
              console.debug('Maintenance scheduler start failed with status:', startResponse.status);
              return;
            }

            const startData = await startResponse.json();

            if (startData.success) {
              logInfo('maintenance', 'Maintenance scheduler initialized on application start');
            } else {
              logError('maintenance', 'Failed to initialize maintenance scheduler', { error: startData.error });
            }
          } catch (startError) {
            // Only log as debug to avoid filling error logs with auth-related errors
            console.debug('Could not start maintenance scheduler:', startError);
          }
        }
      } catch (error) {
        // Check if this is an authentication error (typically happens after logout)
        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
          // This is likely due to a redirect after authentication failure
          // Just log a debug message instead of an error
          console.debug('Maintenance scheduler check skipped - possible authentication issue');
        } else {
          // For other errors, log as usual
          logError('maintenance', 'Error initializing maintenance scheduler', { error });
        }
      }
    };

    // Initialize the maintenance scheduler
    initializeMaintenanceScheduler();

    // Cleanup function
    return () => {
      // We don't stop the scheduler when the component unmounts
      // because we want it to continue running in the background
    };
  }, []);

  // This component doesn't render anything
  return null;
}
