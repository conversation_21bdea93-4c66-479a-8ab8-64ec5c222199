import { simplePars<PERSON>, ParsedMail, Attachment } from 'mailparser';
import { createHash } from 'crypto';
import NodeCache from 'node-cache';
import DOMPurify from 'dompurify';
import { <PERSON><PERSON><PERSON> } from 'jsdom';
import { ParsedEmail } from './types';

// Create a cache for parsed emails with 30-minute TTL
export const emailCache = new NodeCache({ stdTTL: 1800, checkperiod: 600 });

/**
 * Parse an email from raw content
 * @param rawEmail The raw email content
 * @param mailId The ID of the email
 * @returns The parsed email
 */
export async function parseEmail(rawEmail: string, mailId: string): Promise<ParsedEmail> {
  // Generate a cache key based on the email content
  const cacheKey = createHash('md5').update(rawEmail).digest('hex');

  // Check if the email is already in the cache
  const cachedEmail = emailCache.get<ParsedEmail>(cacheKey);
  if (cachedEmail) {
    return cachedEmail;
  }

  // Parse the email
  const parsed: ParsedMail = await simpleParser(rawEmail);

  // Extract sender details
  let fromName = '';
  let fromEmail = '';

  if (parsed.from && parsed.from.value.length > 0) {
    const sender = parsed.from.value[0];
    fromName = sender.name || '';
    fromEmail = sender.address || '';
  }

  // Process HTML content to replace CID references with base64 data
  let htmlContent = parsed.html || '';

  if (htmlContent && parsed.attachments && parsed.attachments.length > 0) {
    htmlContent = await replaceInlineAttachments(htmlContent, parsed.attachments);
  }

  // Process HTML content to preserve text alignment
  htmlContent = preserveTextAlignment(htmlContent);

  // Sanitize HTML content
  const sanitizedHtml = sanitizeHtml(htmlContent);

  // Create the parsed email object
  const parsedEmail: ParsedEmail = {
    mail_id: mailId,
    from: parsed.from ? parsed.from.text : '',
    fromName,
    fromEmail,
    to: parsed.to ? (Array.isArray(parsed.to) ? parsed.to.map(t => t.text).join(', ') : parsed.to.text) : '',
    subject: parsed.subject || '',
    date: parsed.date ? parsed.date.toISOString() : '',
    text: parsed.text || '',
    html: sanitizedHtml,
    attachments: parsed.attachments.map(attachment => ({
      filename: attachment.filename || 'unnamed-attachment',
      contentType: attachment.contentType || 'application/octet-stream',
      content: attachment.content,
      size: attachment.size || 0
    }))
  };

  // Store the parsed email in the cache
  emailCache.set(cacheKey, parsedEmail);

  return parsedEmail;
}

/**
 * Process HTML content to preserve text alignment
 * @param html The HTML content
 * @returns The HTML content with preserved text alignment
 */
function preserveTextAlignment(html: string): string {
  if (!html) {
    return '';
  }

  // Create a DOM to manipulate the HTML
  const dom = new JSDOM(html);
  const document = dom.window.document;

  // Find all elements with text-align style
  const elementsWithStyle = document.querySelectorAll('[style*="text-align"]');
  for (const element of Array.from(elementsWithStyle)) {
    const style = element.getAttribute('style') || '';
    if (style.includes('text-align: center') || style.includes('text-align:center')) {
      element.setAttribute('align', 'center');
      element.setAttribute('data-original-align', 'center');
    } else if (style.includes('text-align: right') || style.includes('text-align:right')) {
      element.setAttribute('align', 'right');
      element.setAttribute('data-original-align', 'right');
    } else if (style.includes('text-align: left') || style.includes('text-align:left')) {
      element.setAttribute('align', 'left');
      element.setAttribute('data-original-align', 'left');
    }
  }

  // Find all table elements with margin auto style (centered tables)
  const tables = document.querySelectorAll('table');
  for (const table of Array.from(tables)) {
    const style = table.getAttribute('style') || '';
    if (
      style.includes('margin: auto') ||
      style.includes('margin:auto') ||
      style.includes('margin: 0 auto') ||
      style.includes('margin:0 auto') ||
      (style.includes('margin-left: auto') && style.includes('margin-right: auto')) ||
      (style.includes('margin-left:auto') && style.includes('margin-right:auto'))
    ) {
      if (!table.hasAttribute('align')) {
        table.setAttribute('align', 'center');
        table.setAttribute('data-original-align', 'center');
      }
    }
  }

  // Find all table rows and cells with align attribute
  const tableElements = document.querySelectorAll('tr, td, th');
  for (const element of Array.from(tableElements)) {
    const align = element.getAttribute('align');
    if (align) {
      element.setAttribute('data-original-align', align);
    }
  }

  // Find all images with margin auto style (centered images)
  const images = document.querySelectorAll('img');
  for (const img of Array.from(images)) {
    const style = img.getAttribute('style') || '';
    if (
      style.includes('margin: 0 auto') ||
      style.includes('margin:0 auto') ||
      style.includes('margin-left: auto') && style.includes('margin-right: auto') ||
      style.includes('margin-left:auto') && style.includes('margin-right:auto')
    ) {
      img.setAttribute('align', 'center');
      img.setAttribute('data-original-align', 'center');
    }

    // Check if the image is inside a center tag
    const parentElement = img.parentElement;
    if (parentElement && parentElement.tagName.toLowerCase() === 'center') {
      img.setAttribute('align', 'center');
      img.setAttribute('data-original-align', 'center');
    }
  }

  // Find all list elements with text-align style or align attribute
  const listElements = document.querySelectorAll('ul, ol');
  for (const list of Array.from(listElements)) {
    const style = list.getAttribute('style') || '';
    if (style.includes('text-align: center') || style.includes('text-align:center')) {
      list.setAttribute('align', 'center');
      list.setAttribute('data-original-align', 'center');

      // For centered lists, ensure list-style-position is inside
      if (!style.includes('list-style-position')) {
        const newStyle = `${style}; list-style-position: inside;`;
        list.setAttribute('style', newStyle);
      }
    }

    const align = list.getAttribute('align');
    if (align) {
      list.setAttribute('data-original-align', align);

      // For centered lists, ensure list-style-position is inside
      if (align === 'center' && !style.includes('list-style-position')) {
        const newStyle = `${style}; list-style-position: inside;`;
        list.setAttribute('style', newStyle);
      }
    }
  }

  // Find all remaining elements with align attribute
  const elementsWithAlign = document.querySelectorAll('[align]');
  for (const element of Array.from(elementsWithAlign)) {
    const align = element.getAttribute('align');
    if (align) {
      element.setAttribute('data-original-align', align);
    }
  }

  // Return the modified HTML
  return dom.serialize();
}

/**
 * Replace CID references in HTML with base64 data URLs
 * @param html The HTML content
 * @param attachments The email attachments
 * @returns The HTML content with CID references replaced
 */
async function replaceInlineAttachments(html: string, attachments: Attachment[]): Promise<string> {
  // Create a map of CID to attachment
  const cidMap = new Map<string, Attachment>();

  // Populate the map
  for (const attachment of attachments) {
    if (attachment.contentId) {
      // Remove angle brackets from CID if present
      const cid = attachment.contentId.replace(/[<>]/g, '');
      cidMap.set(cid, attachment);
    }
  }

  // If no CIDs found, return the original HTML
  if (cidMap.size === 0) {
    return html;
  }

  // Create a DOM to manipulate the HTML
  const dom = new JSDOM(html);
  const document = dom.window.document;

  // Find all images with CID sources
  const images = document.querySelectorAll('img[src^="cid:"]');

  // Replace each CID reference with a base64 data URL
  for (const img of Array.from(images)) {
    const src = img.getAttribute('src');
    if (src) {
      const cid = src.replace('cid:', '');
      const attachment = cidMap.get(cid);

      if (attachment && attachment.content) {
        // Create a base64 data URL
        const base64Data = attachment.content.toString('base64');
        const dataUrl = `data:${attachment.contentType};base64,${base64Data}`;

        // Replace the src attribute
        img.setAttribute('src', dataUrl);

        // Ensure alignment attributes are preserved
        const parentElement = img.parentElement;
        if (parentElement) {
          const parentTagName = parentElement.tagName.toLowerCase();

          // If the image is wrapped in a CENTER tag, add align attribute
          if (parentTagName === 'center') {
            if (!img.hasAttribute('align')) {
              img.setAttribute('align', 'center');
              img.setAttribute('data-original-align', 'center');
            }
          }

          // Check if parent has text-align: center style
          const parentStyle = parentElement.getAttribute('style') || '';
          if (
            parentStyle.includes('text-align: center') ||
            parentStyle.includes('text-align:center') ||
            parentElement.getAttribute('align') === 'center'
          ) {
            if (!img.hasAttribute('align')) {
              img.setAttribute('align', 'center');
              img.setAttribute('data-original-align', 'center');
            }
          }
        }

        // Check if the image itself has style for centering
        const imgStyle = img.getAttribute('style') || '';
        if (
          imgStyle.includes('margin: 0 auto') ||
          imgStyle.includes('margin:0 auto') ||
          imgStyle.includes('margin-left: auto') && imgStyle.includes('margin-right: auto') ||
          imgStyle.includes('margin-left:auto') && imgStyle.includes('margin-right:auto')
        ) {
          img.setAttribute('align', 'center');
          img.setAttribute('data-original-align', 'center');
        }
      }
    }
  }

  // Return the modified HTML
  return dom.serialize();
}

/**
 * Sanitize HTML content to prevent XSS
 * @param html The HTML content to sanitize
 * @returns The sanitized HTML
 */
function sanitizeHtml(html: string): string {
  if (!html) {
    return '';
  }

  // Create a new JSDOM instance
  const window = new JSDOM('').window;
  // Use proper type casting for DOMPurify
  const purify = DOMPurify(window);

  // Configure DOMPurify to allow certain tags and attributes
  const sanitized = purify.sanitize(html, {
    ALLOWED_TAGS: [
      'a', 'b', 'br', 'center', 'div', 'em', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'i', 'img', 'li', 'ol', 'p', 'span', 'strong', 'table', 'tbody',
      'td', 'th', 'thead', 'tr', 'u', 'ul'
    ],
    ALLOWED_ATTR: [
      'href', 'src', 'alt', 'title', 'style', 'class', 'id', 'name',
      'width', 'height', 'target', 'align', 'data-original-align',
      'cellpadding', 'cellspacing', 'border', 'bgcolor', 'valign',
      'colspan', 'rowspan', 'scope', 'headers', 'abbr', 'summary',
      'list-style-position', 'list-style-type', 'start', 'type', 'value'
    ],
    ALLOW_DATA_ATTR: true, // Allow data attributes for our alignment markers
    ADD_ATTR: ['target'],
    WHOLE_DOCUMENT: false,
    SANITIZE_DOM: true,
    ADD_TAGS: ['center'],
    ALLOW_UNKNOWN_PROTOCOLS: true,
    FORBID_TAGS: ['script', 'style', 'iframe'],
    FORBID_ATTR: ['onerror', 'onload', 'onclick'],
    ALLOW_ARIA_ATTR: true
  });

  return sanitized;
}

/**
 * Clear the email cache
 */
export function clearEmailCache(): void {
  emailCache.flushAll();
}

/**
 * Get cache statistics
 */
export function getEmailCacheStats(): { keys: number, hits: number, misses: number } {
  return {
    keys: emailCache.keys().length,
    hits: emailCache.getStats().hits,
    misses: emailCache.getStats().misses
  };
}
