/**
 * API route for system health
 *
 * This API provides comprehensive system health information for monitoring purposes.
 * It checks database connections, email service, and overall system status.
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { getGuerrillaDbConnection } from '@/lib/db';
import { logInfo, logError } from '@/lib/logging';
import { getAllConfig } from '@/lib/config/configService';
import { getAllDomains } from '@/lib/config/domainService';
import { getCacheStats, getAllCacheNames } from '@/lib/cacheRegistry';
import os from 'os';

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  components: {
    supabase: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      responseTime?: number;
      message?: string;
    };
    guerrilla: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      responseTime?: number;
      message?: string;
    };
    emailService: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      message?: string;
    };
    config: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      message?: string;
    };
  };
  metrics: {
    activeEmails: number;
    emailsReceivedLast24Hours: number;
    totalLogs: number;
    errorLogs: number;
    activeDomains?: number;
  };
  system?: {
    uptime: number;
    memory: {
      total: number;
      free: number;
      used: number;
    };
    cpu: {
      cores: number;
      load: number[];
    };
  };
}

/**
 * GET /api/admin/health
 *
 * Get comprehensive system health information
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Initialize health status
    const healthStatus: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.APP_VERSION || '1.0.0',
      components: {
        supabase: {
          status: 'healthy'
        },
        guerrilla: {
          status: 'healthy'
        },
        emailService: {
          status: 'healthy'
        },
        config: {
          status: 'healthy'
        }
      },
      metrics: {
        activeEmails: 0,
        emailsReceivedLast24Hours: 0,
        totalLogs: 0,
        errorLogs: 0,
        activeDomains: 0
      }
    };

    // Check Supabase connection
    const supabaseStartTime = Date.now();
    const supabase = createServerSupabaseClient();
    let supabaseResult;

    try {
      // Use a simple query to check connection
      supabaseResult = await supabase.from('app_config').select('key').limit(1);
      const supabaseResponseTime = Date.now() - supabaseStartTime;
      healthStatus.components.supabase.responseTime = supabaseResponseTime;

      if (supabaseResult.error) {
        healthStatus.components.supabase.status = 'unhealthy';
        healthStatus.components.supabase.message = `Error: ${supabaseResult.error.message}`;
        healthStatus.status = 'degraded';
      } else {
        healthStatus.components.supabase.message = 'Connection successful';
      }
    } catch (error) {
      healthStatus.components.supabase.status = 'unhealthy';
      healthStatus.components.supabase.message = `Exception: ${error instanceof Error ? error.message : String(error)}`;
      healthStatus.status = 'degraded';
    }

    // Check Guerrilla connection
    const guerrillaStartTime = Date.now();
    let guerrillaConnected = false;

    try {
      const connection = await getGuerrillaDbConnection();
      guerrillaConnected = true;
      connection.release();

      const guerrillaResponseTime = Date.now() - guerrillaStartTime;
      healthStatus.components.guerrilla.responseTime = guerrillaResponseTime;
      healthStatus.components.guerrilla.message = 'Connection successful';
    } catch (error) {
      healthStatus.components.guerrilla.status = 'unhealthy';
      healthStatus.components.guerrilla.message = `Error: ${error instanceof Error ? error.message : String(error)}`;
      healthStatus.status = 'degraded';
      console.error('Guerrilla connection error:', error);
    }

    // Check configuration service
    try {
      const config = await getAllConfig();
      healthStatus.components.config.message = 'Configuration service operational';

      // Get active domains count
      const domains = await getAllDomains();
      healthStatus.metrics.activeDomains = domains.filter(d => d.isActive).length;
    } catch (error) {
      healthStatus.components.config.status = 'degraded';
      healthStatus.components.config.message = `Error: ${error instanceof Error ? error.message : String(error)}`;
      healthStatus.status = 'degraded';
    }

    // Check email service status based on Guerrilla connection
    if (guerrillaConnected) {
      healthStatus.components.emailService.message = 'Email service operational';
    } else {
      healthStatus.components.emailService.status = 'unhealthy';
      healthStatus.components.emailService.message = 'Email service unavailable - database connection failed';
      healthStatus.status = 'unhealthy';
    }

    // Get active email addresses count
    try {
      // First check if the temp_emails table exists
      const { data: tableInfo, error: tableError } = await supabase
        .from('temp_emails')
        .select('email_address')
        .limit(1);

      if (!tableError) {
        // Table exists, get active emails count
        // Use the correct count syntax and filter by expiration_date
        const now = new Date().toISOString();
        logInfo('health', `Current time for comparison: ${now}`);

        const { count, error: countError } = await supabase
          .from('temp_emails')
          .select('*', { count: 'exact', head: true })
          .gt('expiration_date', now);

        if (!countError) {
          healthStatus.metrics.activeEmails = count || 0;
          logInfo('health', `Active emails count: ${healthStatus.metrics.activeEmails}`);

          // Log the total count of emails for reference
          const { count: totalCount, error: allEmailsError } = await supabase
            .from('temp_emails')
            .select('*', { count: 'exact', head: true });

          if (!allEmailsError) {
            logInfo('health', `Total emails in database: ${totalCount || 0}`);
          }
        } else {
          logError('health', 'Error getting active emails count', { error: countError });
        }
      } else {
        logError('health', 'Error checking temp_emails table', { error: tableError });
      }
    } catch (error) {
      console.error('Error getting active emails count:', error);
    }

    // Get emails received in last 24 hours
    if (guerrillaConnected) {
      try {
        const connection = await getGuerrillaDbConnection();

        // First check if the guerrilla_mail table exists
        const [tables] = await connection.execute(
          "SHOW TABLES LIKE 'guerrilla_mail'"
        );

        // If the table exists, get the count
        if (Array.isArray(tables) && tables.length > 0) {
          // Get all emails regardless of time
          // Since we don't know the exact timestamp column name, just count all emails
          const [rows] = await connection.execute(`
            SELECT COUNT(*) as count
            FROM guerrilla_mail
          `);

          // Store the total count
          healthStatus.metrics.emailsReceivedLast24Hours = (rows as any[])[0]?.count || 0;
          logInfo('health', `Total emails in guerrilla_mail: ${healthStatus.metrics.emailsReceivedLast24Hours}`);
        } else {
          // Table doesn't exist, set count to 0
          logInfo('health', "Guerrilla 'guerrilla_mail' table doesn't exist, setting emails received to 0");
          healthStatus.metrics.emailsReceivedLast24Hours = 0;
        }

        connection.release();
      } catch (error) {
        console.error('Error getting emails received in last 24 hours:', error);
        healthStatus.metrics.emailsReceivedLast24Hours = 0;
      }
    }

    // Get system logs count
    try {
      const { count: totalLogs, error: logsError } = await supabase
        .from('system_logs')
        .select('*', { count: 'exact', head: true });

      if (!logsError) {
        healthStatus.metrics.totalLogs = totalLogs || 0;
      }

      // Get error logs count
      const { count: errorLogs, error: errorLogsError } = await supabase
        .from('system_logs')
        .select('*', { count: 'exact', head: true })
        .eq('level', 'error');

      if (!errorLogsError) {
        healthStatus.metrics.errorLogs = errorLogs || 0;
      }
    } catch (error) {
      console.error('Error getting logs count:', error);
    }

    // Add system information
    healthStatus.system = {
      uptime: os.uptime(),
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem()
      },
      cpu: {
        cores: os.cpus().length,
        load: os.loadavg()
      }
    };

    // Add cache statistics
    const cacheStats = getCacheStats();
    (healthStatus as any).caches = cacheStats;

    // Calculate overall status
    const componentStatuses = Object.values(healthStatus.components).map(c => c.status);
    if (componentStatuses.includes('unhealthy')) {
      healthStatus.status = 'unhealthy';
    } else if (componentStatuses.includes('degraded')) {
      healthStatus.status = 'degraded';
    }

    // Log health check result
    logInfo('health', `Health check completed: ${healthStatus.status}`, {
      responseTime: Date.now() - startTime,
      components: Object.entries(healthStatus.components).reduce((acc, [key, value]) => {
        acc[key] = value.status;
        return acc;
      }, {} as Record<string, string>)
    });

    return NextResponse.json({
      success: true,
      data: healthStatus
    });
  } catch (error) {
    logError('health', 'Error getting system health', { error });

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get system health',
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
