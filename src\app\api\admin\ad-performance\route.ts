/**
 * API route for ad performance metrics
 */
import { NextRequest, NextResponse } from 'next/server';
import { getCacheStats } from '@/lib/config/adCacheService';
import { logInfo, logError } from '@/lib/logging';

// Store load times in memory (would be in a database in production)
const adLoadTimes: number[] = [];

/**
 * Add a load time measurement
 *
 * @param loadTime Load time in milliseconds
 */
function addAdLoadTime(loadTime: number): void {
  adLoadTimes.push(loadTime);

  // Keep only the last 1000 measurements
  if (adLoadTimes.length > 1000) {
    adLoadTimes.shift();
  }
}

/**
 * GET /api/admin/ad-performance
 *
 * Get ad performance metrics
 */
export async function GET(request: NextRequest) {
  try {
    // Get cache stats
    const cacheStats = getCacheStats();

    // Return performance metrics
    return NextResponse.json({
      success: true,
      data: {
        ...cacheStats,
        loadTimes: adLoadTimes
      }
    });
  } catch (error) {
    logError('api', 'Error getting ad performance metrics', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to get ad performance metrics' },
      { status: 500 }
    );
  }
}
