/**
 * Detects the current device type based on window size and user agent
 * 
 * @returns The device type: 'mobile', 'tablet', or 'desktop'
 */
export function getDeviceType(): string {
  // Only run on client side
  if (typeof window === 'undefined') return 'desktop';
  
  // First check screen width for responsive design
  const width = window.innerWidth;
  
  if (width < 768) {
    return 'mobile';
  } else if (width < 1024) {
    return 'tablet';
  }
  
  // For more accurate detection, also check user agent
  const userAgent = navigator.userAgent.toLowerCase();
  
  // Check for mobile devices
  if (
    /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile|mobile safari|crios/.test(userAgent)
  ) {
    // Further distinguish between tablets and phones
    if (
      /ipad|tablet/.test(userAgent) || 
      (width >= 768 && width < 1024)
    ) {
      return 'tablet';
    }
    return 'mobile';
  }
  
  // Default to desktop
  return 'desktop';
}
