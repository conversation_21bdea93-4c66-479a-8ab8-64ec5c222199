import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '../../../../lib/auth';
import { realTimeMonitor } from '../../../../lib/logging/realTimeMonitor';
import { logger } from '../../../../lib/logging/Logger';

/**
 * GET /api/management-portal-x7z9y2/monitoring
 * Get monitoring status and alert thresholds
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get alert thresholds and counts
    const thresholds = realTimeMonitor.getAlertThresholds();
    const counts = realTimeMonitor.getAlertCounts();

    return NextResponse.json({
      thresholds,
      counts,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    await logger.error('API_MONITORING_GET', `Error getting monitoring status: ${error}`);
    return NextResponse.json({ error: 'Failed to get monitoring status' }, { status: 500 });
  }
}

/**
 * PUT /api/management-portal-x7z9y2/monitoring
 * Update alert thresholds
 */
export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { thresholds } = body;

    if (!thresholds || typeof thresholds !== 'object') {
      return NextResponse.json({ error: 'Invalid thresholds' }, { status: 400 });
    }

    // Validate thresholds
    for (const [category, threshold] of Object.entries(thresholds)) {
      if (typeof threshold !== 'number' || threshold < 1) {
        return NextResponse.json({
          error: `Invalid threshold for ${category}: must be a positive number`
        }, { status: 400 });
      }
    }

    // Update thresholds
    await realTimeMonitor.updateAlertThresholds(thresholds);

    return NextResponse.json({
      success: true,
      message: 'Alert thresholds updated successfully',
      thresholds: realTimeMonitor.getAlertThresholds()
    });
  } catch (error) {
    await logger.error('API_MONITORING_UPDATE', `Error updating alert thresholds: ${error}`);
    return NextResponse.json({ error: 'Failed to update alert thresholds' }, { status: 500 });
  }
}
