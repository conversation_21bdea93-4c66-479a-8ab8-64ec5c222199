import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '../../../../lib/auth';
import productionSafeguards from '../../../../lib/config/productionSafeguards';
import { logger } from '../../../../lib/logging/dbLogger';

/**
 * GET /api/admin/safeguards/backups
 * List all configuration backups
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // List all backups
    const backups = await productionSafeguards.listConfigBackups();

    return NextResponse.json({ backups });
  } catch (error) {
    await logger.error('API_SAFEGUARDS_LIST_BACKUPS', `Error listing backups: ${error}`);
    return NextResponse.json({ error: 'Failed to list backups' }, { status: 500 });
  }
}

/**
 * POST /api/admin/safeguards/backup
 * Create a new configuration backup
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create a new backup
    const backupId = await productionSafeguards.createConfigBackup();

    return NextResponse.json({ success: true, backupId });
  } catch (error) {
    await logger.error('API_SAFEGUARDS_CREATE_BACKUP', `Error creating backup: ${error}`);
    return NextResponse.json({ error: 'Failed to create backup' }, { status: 500 });
  }
}

/**
 * PUT /api/admin/safeguards/restore
 * Restore configuration from a backup
 */
export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { backupId } = body;

    if (!backupId) {
      return NextResponse.json({ error: 'Backup ID is required' }, { status: 400 });
    }

    // Restore from backup
    const success = await productionSafeguards.restoreConfigBackup(backupId);

    if (!success) {
      return NextResponse.json({ error: 'Failed to restore from backup' }, { status: 400 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    await logger.error('API_SAFEGUARDS_RESTORE_BACKUP', `Error restoring from backup: ${error}`);
    return NextResponse.json({ error: 'Failed to restore from backup' }, { status: 500 });
  }
}
