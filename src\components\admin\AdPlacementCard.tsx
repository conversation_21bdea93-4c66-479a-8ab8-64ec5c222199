'use client';

import { useState } from 'react';
import { PencilIcon, TrashIcon, CheckIcon, XMarkIcon, AdjustmentsHorizontalIcon, CalendarIcon } from '@heroicons/react/24/solid';
import { AdConfig, AdDisplayOptions as AdDisplayOptionsType } from '@/lib/config/types';
import AdPlacementForm from './AdPlacementForm';
import AdDisplayOptionsComponent from './AdDisplayOptions';
import AdScheduler from './AdScheduler';

interface AdPlacementCardProps {
  adPlacement: AdConfig;
  onUpdate: (
    placementId: string,
    domain: string,
    updates: {
      adUnitId?: string;
      adClientId?: string;
      isEnabled?: boolean;
      deviceTypes?: string[];
      displayOptions?: AdDisplayOptionsType;
    }
  ) => Promise<void>;
  onDelete: (placementId: string, domain: string) => Promise<void>;
}

export default function AdPlacementCard({ adPlacement, onUpdate, onDelete }: AdPlacementCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showDisplayOptions, setShowDisplayOptions] = useState(false);
  const [showScheduler, setShowScheduler] = useState(false);

  // Toggle ad enabled status
  const toggleEnabled = async () => {
    try {
      console.log(`Toggling enabled status for ad placement: ${adPlacement.placementId}, domain: ${adPlacement.domain}`);
      console.log(`Current enabled status: ${adPlacement.isEnabled}, toggling to: ${!adPlacement.isEnabled}`);

      await onUpdate(adPlacement.placementId, adPlacement.domain, {
        isEnabled: !adPlacement.isEnabled
      });
    } catch (error) {
      console.error('Error toggling enabled status:', error);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Handle edit form submission
  const handleEditSubmit = async (
    placementId: string,
    domain: string,
    adUnitId: string,
    adClientId: string,
    isEnabled: boolean,
    deviceTypes: string[],
    displayOptions?: AdDisplayOptionsType
  ) => {
    await onUpdate(placementId, domain, {
      adUnitId,
      adClientId,
      isEnabled,
      deviceTypes,
      displayOptions
    });
    setIsEditing(false);
  };

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg border border-gray-200">
      {isEditing ? (
        <div className="p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Edit Ad Placement</h3>
          <AdPlacementForm
            onSubmit={handleEditSubmit}
            onCancel={() => setIsEditing(false)}
            initialValues={adPlacement}
            isEditMode={true}
          />
        </div>
      ) : (
        <>
          <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-medium text-gray-900 truncate" title={adPlacement.placementId}>
                  {adPlacement.placementId}
                </h3>
                <p className="mt-1 text-sm text-gray-500">{adPlacement.domain}</p>
              </div>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={toggleEnabled}
                  className={`inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white ${
                    adPlacement.isEnabled ? 'bg-[#28c08e] hover:bg-[#1fa678]' : 'bg-[#605f5f] hover:bg-[#505050]'
                  } focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                    adPlacement.isEnabled ? 'focus:ring-[#28c08e]' : 'focus:ring-[#605f5f]'
                  }`}
                  title={adPlacement.isEnabled ? 'Disable' : 'Enable'}
                >
                  {adPlacement.isEnabled ? (
                    <CheckIcon className="h-4 w-4" />
                  ) : (
                    <XMarkIcon className="h-4 w-4" />
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => setShowScheduler(true)}
                  className="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  title="Schedule"
                >
                  <CalendarIcon className="h-4 w-4" />
                </button>
                <button
                  type="button"
                  onClick={() => setShowDisplayOptions(true)}
                  className="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  title="Display Options"
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4" />
                </button>
                <button
                  type="button"
                  onClick={() => setIsEditing(true)}
                  className="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  title="Edit"
                >
                  <PencilIcon className="h-4 w-4" />
                </button>
                <button
                  type="button"
                  onClick={() => onDelete(adPlacement.placementId, adPlacement.domain)}
                  className="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  title="Delete"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
            <div className="mt-2 flex flex-wrap gap-2">
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  adPlacement.isEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}
              >
                {adPlacement.isEnabled ? 'Enabled' : 'Disabled'}
              </span>
              {adPlacement.schedule && (
                <span
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                  title="This ad has a custom schedule"
                >
                  <CalendarIcon className="h-3 w-3 mr-1" />
                  Scheduled
                </span>
              )}
            </div>
          </div>
          <div className="px-4 py-5 sm:p-6 space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500">Ad Client ID</h4>
              <p className="mt-1 text-sm text-gray-900 break-all">{adPlacement.adClientId}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">Ad Unit ID</h4>
              <p className="mt-1 text-sm text-gray-900 break-all">{adPlacement.adUnitId}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">Device Types</h4>
              <div className="mt-1 flex flex-wrap gap-1">
                {adPlacement.deviceTypes.map((deviceType) => (
                  <span
                    key={deviceType}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {deviceType}
                  </span>
                ))}
              </div>
            </div>
            {/* Display Options */}
            {adPlacement.displayOptions && Object.keys(adPlacement.displayOptions).length > 0 && (
              <div>
                <button
                  type="button"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="text-sm font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none"
                >
                  {isExpanded ? 'Hide Display Options' : 'Show Display Options'}
                </button>
                {isExpanded && (
                  <div className="mt-2 text-sm text-gray-900">
                    <pre className="bg-gray-50 p-2 rounded overflow-auto max-h-40">
                      {JSON.stringify(adPlacement.displayOptions, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}

            {/* Schedule Information */}
            {adPlacement.schedule && (
              <div className="border-t border-gray-200 pt-4">
                <h4 className="text-sm font-medium text-gray-500">Schedule</h4>
                <div className="mt-2 text-sm text-gray-600 space-y-2">
                  {adPlacement.schedule.startDate && (
                    <div className="flex items-center">
                      <span className="font-medium mr-2">Start Date:</span>
                      {new Date(adPlacement.schedule.startDate).toLocaleDateString()}
                    </div>
                  )}
                  {adPlacement.schedule.endDate && (
                    <div className="flex items-center">
                      <span className="font-medium mr-2">End Date:</span>
                      {new Date(adPlacement.schedule.endDate).toLocaleDateString()}
                    </div>
                  )}
                  {adPlacement.schedule.startTime && (
                    <div className="flex items-center">
                      <span className="font-medium mr-2">Start Time:</span>
                      {adPlacement.schedule.startTime}
                    </div>
                  )}
                  {adPlacement.schedule.endTime && (
                    <div className="flex items-center">
                      <span className="font-medium mr-2">End Time:</span>
                      {adPlacement.schedule.endTime}
                    </div>
                  )}
                  {adPlacement.schedule.daysOfWeek && adPlacement.schedule.daysOfWeek.length > 0 && (
                    <div className="flex items-center">
                      <span className="font-medium mr-2">Days:</span>
                      {adPlacement.schedule.daysOfWeek
                        .map(day => ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][day])
                        .join(', ')}
                    </div>
                  )}
                  {adPlacement.schedule.isRecurring && (
                    <div className="flex items-center">
                      <span className="font-medium mr-2">Recurring:</span>
                      Every {adPlacement.schedule.recurringInterval || 1} {adPlacement.schedule.recurringPattern || 'day'}(s)
                    </div>
                  )}
                </div>
              </div>
            )}
            <div className="border-t border-gray-200 pt-4">
              <div className="flex justify-between text-xs text-gray-500">
                <span>Created: {formatDate(adPlacement.createdAt)}</span>
                <span>Updated: {formatDate(adPlacement.updatedAt)}</span>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Display Options Modal */}
      {showDisplayOptions && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Display Options for {adPlacement.placementId}
                  </h3>
                  <div className="mt-2">
                    <AdDisplayOptionsComponent adPlacement={adPlacement} onUpdate={onUpdate} />
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
                  onClick={() => setShowDisplayOptions(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Scheduler Modal */}
      {showScheduler && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Schedule for {adPlacement.placementId}
                  </h3>
                  <div className="mt-2">
                    <AdScheduler adPlacement={adPlacement} onUpdate={onUpdate} />
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
                  onClick={() => setShowScheduler(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
