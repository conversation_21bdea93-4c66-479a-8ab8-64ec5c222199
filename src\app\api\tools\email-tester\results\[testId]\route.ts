/**
 * API Route for Retrieving Email Test Results
 *
 * This route retrieves the results of a specific email test.
 *
 * GET /api/tools/email-tester/results/[testId]
 */

import { NextRequest, NextResponse } from 'next/server';
import { getEmailTesterResult } from '@/lib/tools/email-tester/database';
import { getCurrentSessionId } from '@/lib/tools/email-tester/sessionManager';
import { logError } from '@/lib/logging';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ testId: string }> }
) {
  try {
    // In Next.js 15, we need to await params before accessing its properties
    const resolvedParams = await params;
    const testId = resolvedParams.testId;

    // Check for share token in query parameters
    const shareToken = request.nextUrl.searchParams.get('share') || undefined;

    // Get current session ID for access control (if not using share token)
    const sessionId = shareToken ? undefined : (await getCurrentSessionId()) || undefined;

    if (!shareToken && !sessionId) {
      return NextResponse.json(
        { success: false, message: 'Access denied - no valid session' },
        { status: 403 }
      );
    }

    // Get the test result from Supabase with access control
    const { result, recommendations } = await getEmailTesterResult(testId, sessionId, shareToken);

    return NextResponse.json({
      success: true,
      result,
      recommendations
    });
  } catch (error) {
    logError('API', 'Failed to retrieve test results', error);

    // Check if it's an access control error
    if (error instanceof Error && error.message.includes('access denied')) {
      return NextResponse.json(
        { success: false, message: 'Test result not found or access denied' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { success: false, message: 'Failed to retrieve test results' },
      { status: 500 }
    );
  }
}
