/**
 * Parse user agent string to extract browser and device information
 * This is a simple implementation - in production you might use a more robust library
 */
export function parseUserAgent(userAgent: string = '') {
  // Normalize to lowercase for easier matching
  const ua = userAgent.toLowerCase();
  
  // Detect device type
  const isMobile = /mobile|android|iphone|ipad|ipod|windows phone/i.test(ua);
  const isTablet = /ipad|android(?!.*mobile)/i.test(ua);
  
  let deviceType = 'desktop';
  if (isTablet) deviceType = 'tablet';
  else if (isMobile) deviceType = 'mobile';
  
  // Detect browser
  let browser = 'unknown';
  
  if (/edge|edg/i.test(ua)) {
    browser = 'Edge';
  } else if (/firefox/i.test(ua)) {
    browser = 'Firefox';
  } else if (/chrome/i.test(ua)) {
    browser = 'Chrome';
  } else if (/safari/i.test(ua)) {
    browser = 'Safari';
  } else if (/msie|trident/i.test(ua)) {
    browser = 'Internet Explorer';
  } else if (/opera|opr/i.test(ua)) {
    browser = 'Opera';
  }
  
  return {
    browser,
    deviceType
  };
}

/**
 * Get country from IP address
 * This is a placeholder implementation - in production you would use a GeoIP database
 * For privacy reasons, you might want to only store country or region information
 */
export async function getCountryFromIP(ip: string): Promise<string> {
  // For now, just return 'unknown' to avoid making external API calls
  // In production, you could use a local GeoIP database or a privacy-friendly service
  return 'unknown';
  
  // Example implementation with a GeoIP service (not used here for privacy reasons)
  /*
  try {
    const response = await fetch(`https://ipapi.co/${ip}/json/`);
    const data = await response.json();
    return data.country_name || 'unknown';
  } catch (error) {
    console.error('Error getting country from IP:', error);
    return 'unknown';
  }
  */
}
