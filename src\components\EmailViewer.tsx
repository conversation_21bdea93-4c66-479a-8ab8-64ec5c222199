'use client';

import { useState, useEffect } from 'react';
import { Email } from '@/lib/emailProcessing';
import { EmailContentSkeleton } from './EmailSkeleton';
import OpenIframeRenderer from './OpenIframeRenderer';
import '@/styles/envelope-animation.css';

interface EmailViewerProps {
  /** The selected email to display */
  selectedEmail: Email | null;

  /** The current email address */
  emailAddress: string;

  /** Whether emails are currently loading */
  isLoading: boolean;

  /** Function to format file size */
  formatFileSize: (bytes: number) => string;

  /** Function to go back to the inbox (mobile only) */
  onBackToInbox: () => void;

  /** Whether to show the inbox on mobile */
  showMobileInbox: boolean;
}

/**
 * EmailViewer Component
 *
 * Displays the content of a selected email, including sender information,
 * subject, body, and attachments. Adapts to mobile and desktop views.
 *
 * @param {Email|null} props.selectedEmail - The email to display
 * @param {string} props.emailAddress - The current email address
 * @param {boolean} props.isLoading - Whether emails are currently loading
 * @param {Function} props.formatFileSize - Function to format file size
 * @param {Function} props.onBackToInbox - Function to go back to the inbox (mobile only)
 * @param {boolean} props.showMobileInbox - Whether to show the inbox on mobile
 * @returns {JSX.Element} The rendered component
 */
export default function EmailViewer({
  selectedEmail,
  emailAddress,
  isLoading,
  formatFileSize,
  onBackToInbox,
  showMobileInbox
}: EmailViewerProps) {
  // State to track if we're on mobile
  const [isMobile, setIsMobile] = useState(false);

  // Effect to check window size on mount and resize
  useEffect(() => {
    // Function to check if we're on mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
    };

    // Check initially
    checkMobile();

    // Add resize listener
    window.addEventListener('resize', checkMobile);

    // Clean up
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  return (
    <div
      className={`${!selectedEmail || showMobileInbox ? 'hidden' : 'block'} md:block w-full md:w-2/3 lg:w-2/3 bg-white md:border-l border-neutral-100 rounded-md md:rounded-l-none overflow-hidden shadow-sm`}
      style={{
        overflow: 'hidden',
        minHeight: '600px',
        position: 'relative',
        transform: 'translateZ(0)', // Force hardware acceleration
        willChange: 'contents' // Hint to browser that contents will change
      }}
    >
      {/* Mobile Indicator - Only visible on small screens */}
      <div className="md:hidden bg-neutral-100 py-3 px-4 text-neutral-800 text-sm font-medium border-b border-neutral-200">
        <div className="flex items-center justify-between">
          <button
            onClick={onBackToInbox}
            className="flex items-center px-4 py-1.5 rounded-full bg-white text-[#ce601c] border border-[#ce601c] shadow-sm hover:bg-neutral-50 transition-all duration-200 active:scale-95"
            aria-label="Back to inbox"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span className="font-medium">Back</span>
          </button>
          <span className="truncate max-w-[60%] text-center font-medium">{selectedEmail?.subject || 'View Email'}</span>
          <div className="w-[72px]"></div> {/* Empty div for balance */}
        </div>
      </div>
      {isLoading && !selectedEmail?.id.startsWith('guide-') ? (
        <EmailContentSkeleton />
      ) : selectedEmail ? (
        <div
          className={`p-6 sm:p-8 rounded-md overflow-hidden ${
            selectedEmail.id.startsWith('guide-')
              ? 'animate-guide-email'
              : 'animate-email-content'
          } will-change-transform will-change-opacity`}
          style={{
            overflow: 'hidden',
            width: '100%', // Ensure consistent width
            boxSizing: 'border-box', // Include padding in width calculation
            transform: 'translateZ(0)' // Force hardware acceleration
          }}
          data-email-id={selectedEmail.id} // Add data attribute for debugging
          data-email-type={selectedEmail.id.startsWith('guide-') ? 'guide' : 'regular'} // Add type for debugging
        >
          <div className="border-b border-neutral-100 pb-5">
            <h2 className="text-lg sm:text-xl font-semibold text-neutral-800">{selectedEmail.subject}</h2>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mt-3">
              <div>
                <p className="text-xs sm:text-sm text-neutral-600 flex flex-col sm:flex-row sm:items-center">
                  <span className="text-neutral-500 mr-1.5">From:</span>
                  <span className="font-medium">{selectedEmail.fromName}</span>
                  <span className="hidden sm:inline text-neutral-400 ml-1">&lt;{selectedEmail.fromEmail}&gt;</span>
                </p>
                <p className="text-xs sm:text-sm text-neutral-600 mt-1.5 flex flex-col sm:flex-row sm:items-center">
                  <span className="text-neutral-500 mr-1.5">To:</span>
                  <span className="font-medium">{emailAddress}</span>
                </p>
              </div>
              <span className="text-xs sm:text-sm text-neutral-500 mt-3 sm:mt-0 flex items-center sm:justify-end">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1.5 text-neutral-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10" />
                  <polyline points="12 6 12 12 16 14" />
                </svg>
                {(() => {
                  // Format the date as "Oct 1, 2024, 4:05 PM"
                  const date = new Date(selectedEmail.date);
                  return date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                  }) + ', ' + date.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                  });
                })()}
              </span>
            </div>
          </div>

          {/* Email Body */}
          <div
            className="py-5 sm:py-6 overflow-visible will-change-contents"
            style={{
              minHeight: '200px', // Consistent minimum height for all emails
              maxHeight: selectedEmail.id.startsWith('guide-') ? (isMobile ? 'none' : '550px') : 'none', // Responsive height for guide emails
              position: 'relative', // Create a positioning context
              width: '100%', // Ensure consistent width
              transition: 'height 0.3s cubic-bezier(0.16, 1, 0.3, 1)', // Smooth height transitions with nice easing
              overflowX: 'auto', // Allow horizontal scrolling if needed
              overflowY: selectedEmail.id.startsWith('guide-') ? (isMobile ? 'auto' : 'hidden') : 'visible', // Allow scrolling on mobile for guide emails
              WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS
              msOverflowStyle: '-ms-autohiding-scrollbar' // Better scrolling on Windows
            }}
          >
            <OpenIframeRenderer
              html={selectedEmail.html}
              className="w-full"
              style={{
                minHeight: '600px', // Ensure minimum height
                width: '100%',
                display: 'block', // Ensure proper rendering
                maxWidth: '100%', // Prevent horizontal overflow
                borderRadius: '0.375rem' // Rounded corners (0.375rem = 6px)
              }}
            />
          </div>

          {/* Attachments */}
          {selectedEmail.attachments.length > 0 && (
            <EmailAttachments
              attachments={selectedEmail.attachments}
              formatFileSize={formatFileSize}
            />
          )}
        </div>
      ) : (
        <NoEmailSelected />
      )}
    </div>
  );
}

interface EmailAttachmentsProps {
  /** List of attachments */
  attachments: {
    filename: string;
    size: number;
    contentType: string;
  }[];

  /** Function to format file size */
  formatFileSize: (bytes: number) => string;
}

/**
 * EmailAttachments Component
 *
 * Displays the attachments for an email
 */
function EmailAttachments({ attachments, formatFileSize }: EmailAttachmentsProps) {
  return (
    <div className="mt-5 sm:mt-6 border-t border-neutral-100 pt-5 sm:pt-6">
      <h3 className="text-base font-semibold text-neutral-800 mb-3 sm:mb-4 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-[#605f5f]" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M21.44 11.05l-9.19 9.19a6 6 0 01-8.49-8.49l9.19-9.19a4 4 0 015.66 5.66l-9.2 9.19a2 2 0 01-2.83-2.83l8.49-8.48" />
        </svg>
        Attachments
      </h3>
      <div className="space-y-2.5 sm:space-y-3">
        {attachments.map((attachment, index) => (
          <div key={index} className="flex items-center p-3 sm:p-4 border border-neutral-200 rounded-md hover:border-[#605f5f] transition-all duration-200 shadow-sm bg-neutral-50/50">
            <div className="bg-neutral-100 p-2 rounded-md mr-3 sm:mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6 text-[#605f5f]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="flex-1 min-w-0"> {/* min-width: 0 helps with text truncation */}
              <p className="text-sm font-medium text-neutral-700 truncate">{attachment.filename}</p>
              <p className="text-xs text-neutral-500 mt-0.5">{formatFileSize(attachment.size)}</p>
            </div>
            <button
              className="ml-2 px-3 sm:px-4 py-1.5 sm:py-2 bg-white text-[#605f5f] border border-neutral-200 text-xs sm:text-sm rounded-md hover:bg-neutral-100 hover:border-[#605f5f] transition-all duration-200 whitespace-nowrap active:scale-95 font-medium"
              aria-label={`Download ${attachment.filename}`}
            >
              Download
            </button>
          </div>
        ))}
      </div>

      {attachments.length > 1 && (
        <div className="mt-3 sm:mt-4 text-right">
          <button
            className="text-[#605f5f] hover:text-[#505050] text-xs sm:text-sm font-medium inline-flex items-center transition-colors duration-200 hover:bg-neutral-100 px-3 py-1.5 rounded-md active:scale-95"
            aria-label="Download all attachments"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" />
              <polyline points="7 10 12 15 17 10" />
              <line x1="12" y1="15" x2="12" y2="3" />
            </svg>
            Download All
          </button>
        </div>
      )}
    </div>
  );
}

/**
 * NoEmailSelected Component
 *
 * Displays a message when no email is selected
 */
function NoEmailSelected() {
  return (
    <div
      className="flex items-center justify-center h-full"
      style={{
        minHeight: '600px', // Match the container's minHeight
        transform: 'translateZ(0)', // Force hardware acceleration
        width: '100%' // Ensure consistent width
      }}
    >
      <div className="text-center p-6 sm:p-8 max-w-md">
        <div className="relative mx-auto w-fit mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-[#ce601c] animate-spin-slow" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </div>
        <h3 className="text-lg sm:text-xl font-semibold text-neutral-800 mb-2">Inbox Ready</h3>
        <p className="text-sm text-neutral-500">Select an email from the list to view its contents</p>
        <p className="text-xs text-neutral-400 mt-3">Your emails will appear here when selected</p>
      </div>
    </div>
  );
}
