/**
 * IP Rotation Detection System
 * Detects when sessions use multiple IP addresses to bypass rate limiting
 * Automatically blocks sessions that exhibit IP rotation patterns
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface IPRotationAnalysis {
  sessionId: string;
  ipAddresses: string[];
  ipCount: number;
  timeSpan: number; // minutes
  rotationRate: number; // IPs per hour
  isRotating: boolean;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  confidence: number; // 0-1
}

interface IPRotationConfig {
  maxIPsPerSession: number;
  timeWindowMinutes: number;
  rotationRateThreshold: number; // IPs per hour
  autoBlockThreshold: number; // confidence level for auto-blocking
}

// Default configuration
const DEFAULT_CONFIG: IPRotationConfig = {
  maxIPsPerSession: 3,
  timeWindowMinutes: 60,
  rotationRateThreshold: 2.0, // More than 2 IPs per hour is suspicious
  autoBlockThreshold: 0.8 // 80% confidence for auto-blocking
};

/**
 * Analyze a session for IP rotation patterns
 */
export async function analyzeIPRotation(
  sessionId: string,
  config: IPRotationConfig = DEFAULT_CONFIG
): Promise<IPRotationAnalysis> {
  try {
    const timeWindow = new Date(Date.now() - config.timeWindowMinutes * 60 * 1000);

    // Get all IP addresses used by this session in the time window
    const { data: events, error } = await supabase
      .from('analytics_events')
      .select('additional_data, timestamp')
      .eq('session_id', sessionId)
      .gte('timestamp', timeWindow.toISOString())
      .not('additional_data->clientIP', 'is', null)
      .order('timestamp', { ascending: true });

    if (error) {
      console.error('Error fetching session events:', error);
      return createSafeAnalysis(sessionId);
    }

    if (!events || events.length === 0) {
      return createSafeAnalysis(sessionId);
    }

    // Extract unique IP addresses and timing
    const ipData: Array<{ ip: string; timestamp: Date }> = [];
    const uniqueIPs = new Set<string>();

    events.forEach(event => {
      const clientIP = event.additional_data?.clientIP;
      if (clientIP && typeof clientIP === 'string') {
        uniqueIPs.add(clientIP);
        ipData.push({
          ip: clientIP,
          timestamp: new Date(event.timestamp)
        });
      }
    });

    const ipAddresses = Array.from(uniqueIPs);
    const ipCount = ipAddresses.length;

    // Calculate time span
    const firstEvent = new Date(events[0].timestamp);
    const lastEvent = new Date(events[events.length - 1].timestamp);
    const timeSpanMs = lastEvent.getTime() - firstEvent.getTime();
    const timeSpanMinutes = timeSpanMs / (1000 * 60);

    // Calculate rotation rate (IPs per hour)
    const rotationRate = timeSpanMinutes > 0 ? (ipCount / timeSpanMinutes) * 60 : 0;

    // Determine if rotation is occurring
    const isRotating = ipCount > config.maxIPsPerSession;

    // Calculate risk level and confidence
    const { riskLevel, confidence } = calculateRiskLevel(
      ipCount,
      rotationRate,
      timeSpanMinutes,
      config
    );

    const analysis: IPRotationAnalysis = {
      sessionId,
      ipAddresses,
      ipCount,
      timeSpan: timeSpanMinutes,
      rotationRate,
      isRotating,
      riskLevel,
      confidence
    };

    // Log the analysis
    await logIPRotationAnalysis(analysis);

    // Auto-block if confidence is high enough
    if (confidence >= config.autoBlockThreshold && riskLevel === 'critical') {
      await autoBlockSession(sessionId, analysis);
    }

    return analysis;
  } catch (error) {
    console.error('Error in analyzeIPRotation:', error);
    return createSafeAnalysis(sessionId);
  }
}

/**
 * Calculate risk level and confidence based on rotation patterns
 */
function calculateRiskLevel(
  ipCount: number,
  rotationRate: number,
  timeSpanMinutes: number,
  config: IPRotationConfig
): { riskLevel: 'low' | 'medium' | 'high' | 'critical'; confidence: number } {
  let riskScore = 0;
  let confidence = 0;

  // Factor 1: Number of IPs
  if (ipCount <= 2) {
    riskScore += 0;
    confidence += 0.9; // High confidence in low risk
  } else if (ipCount <= 5) {
    riskScore += 0.3;
    confidence += 0.7;
  } else if (ipCount <= 10) {
    riskScore += 0.6;
    confidence += 0.8;
  } else {
    riskScore += 1.0;
    confidence += 0.95; // Very high confidence in high risk
  }

  // Factor 2: Rotation rate
  if (rotationRate > config.rotationRateThreshold * 3) {
    riskScore += 0.4;
    confidence += 0.1;
  } else if (rotationRate > config.rotationRateThreshold) {
    riskScore += 0.2;
    confidence += 0.05;
  }

  // Factor 3: Time span (rapid rotation is more suspicious)
  if (timeSpanMinutes < 10 && ipCount > 3) {
    riskScore += 0.3;
    confidence += 0.1;
  }

  // Normalize confidence (max 1.0)
  confidence = Math.min(confidence, 1.0);

  // Determine risk level
  let riskLevel: 'low' | 'medium' | 'high' | 'critical';
  if (riskScore < 0.3) {
    riskLevel = 'low';
  } else if (riskScore < 0.6) {
    riskLevel = 'medium';
  } else if (riskScore < 0.9) {
    riskLevel = 'high';
  } else {
    riskLevel = 'critical';
  }

  return { riskLevel, confidence };
}

/**
 * Create a safe analysis result for error cases
 */
function createSafeAnalysis(sessionId: string): IPRotationAnalysis {
  return {
    sessionId,
    ipAddresses: [],
    ipCount: 0,
    timeSpan: 0,
    rotationRate: 0,
    isRotating: false,
    riskLevel: 'low',
    confidence: 0
  };
}

/**
 * Log IP rotation analysis to database
 */
async function logIPRotationAnalysis(analysis: IPRotationAnalysis): Promise<void> {
  try {
    await supabase
      .from('abuse_patterns')
      .insert({
        session_id: analysis.sessionId,
        pattern_type: 'ip_rotation',
        confidence_score: analysis.confidence,
        evidence: {
          ip_count: analysis.ipCount,
          ip_addresses: analysis.ipAddresses,
          rotation_rate: analysis.rotationRate,
          time_span_minutes: analysis.timeSpan,
          risk_level: analysis.riskLevel
        },
        action_taken: analysis.confidence >= 0.8 ? 'auto_blocked' : 'logged'
      });

    // Also log as security event
    await supabase
      .from('security_events')
      .insert({
        event_type: 'ip_rotation_detected',
        session_id: analysis.sessionId,
        severity: analysis.riskLevel === 'critical' ? 'high' : 'medium',
        description: `IP rotation detected: ${analysis.ipCount} IPs in ${analysis.timeSpan.toFixed(1)} minutes`,
        metadata: {
          analysis,
          detection_time: new Date().toISOString()
        },
        action_taken: analysis.confidence >= 0.8 ? 'session_blocked' : 'monitored'
      });
  } catch (error) {
    console.error('Error logging IP rotation analysis:', error);
  }
}

/**
 * Automatically block a session for IP rotation abuse
 */
async function autoBlockSession(sessionId: string, analysis: IPRotationAnalysis): Promise<void> {
  try {
    const blockDuration = 24 * 60 * 60 * 1000; // 24 hours
    const expiresAt = new Date(Date.now() + blockDuration);

    await supabase
      .from('blocked_sessions')
      .insert({
        session_id: sessionId,
        reason: `Automatic block for IP rotation abuse: ${analysis.ipCount} IPs detected`,
        blocked_by: 'IP Rotation Detector',
        expires_at: expiresAt.toISOString(),
        is_active: true
      });

    console.log(`🚨 AUTO-BLOCKED: Session ${sessionId} for IP rotation (${analysis.ipCount} IPs)`);
  } catch (error) {
    console.error('Error auto-blocking session:', error);
  }
}

/**
 * Monitor all active sessions for IP rotation
 */
export async function monitorAllSessions(
  config: IPRotationConfig = DEFAULT_CONFIG
): Promise<IPRotationAnalysis[]> {
  try {
    // Get all active sessions from the last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    const { data: activeSessions, error } = await supabase
      .from('analytics_events')
      .select('session_id')
      .gte('timestamp', oneHourAgo.toISOString())
      .not('additional_data->clientIP', 'is', null);

    if (error || !activeSessions) {
      console.error('Error fetching active sessions:', error);
      return [];
    }

    // Get unique session IDs
    const uniqueSessions = [...new Set(activeSessions.map(s => s.session_id))];

    // Analyze each session
    const analyses: IPRotationAnalysis[] = [];
    for (const sessionId of uniqueSessions) {
      const analysis = await analyzeIPRotation(sessionId, config);
      if (analysis.isRotating) {
        analyses.push(analysis);
      }
    }

    return analyses;
  } catch (error) {
    console.error('Error in monitorAllSessions:', error);
    return [];
  }
}

/**
 * Middleware function to check IP rotation for incoming requests
 */
export async function checkIPRotationMiddleware(
  sessionId: string,
  currentIP: string
): Promise<{
  allowed: boolean;
  analysis?: IPRotationAnalysis;
  reason?: string;
}> {
  try {
    if (!sessionId || sessionId === 'anonymous') {
      return { allowed: true };
    }

    const analysis = await analyzeIPRotation(sessionId);

    if (analysis.riskLevel === 'critical' && analysis.confidence >= 0.8) {
      return {
        allowed: false,
        analysis,
        reason: `IP rotation detected: ${analysis.ipCount} IPs used in ${analysis.timeSpan.toFixed(1)} minutes`
      };
    }

    return { allowed: true, analysis };
  } catch (error) {
    console.error('Error in IP rotation middleware:', error);
    return { allowed: true }; // Fail open to avoid breaking service
  }
}

export default {
  analyzeIPRotation,
  monitorAllSessions,
  checkIPRotationMiddleware,
  DEFAULT_CONFIG
};
