/**
 * RecommendationCard Component
 *
 * This component displays a recommendation card for improving email deliverability.
 */

'use client';

interface RecommendationCardProps {
  recommendation: {
    id: string;
    category: string;
    issue_description: string;
    recommendation: string;
    dns_record_template?: string;
    implementation_steps?: string;
    priority: number;
  };
}

export default function RecommendationCard({ recommendation }: RecommendationCardProps) {
  // Helper function to determine category color
  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'spf':
        return 'text-white';
      case 'dkim':
        return 'text-white';
      case 'dmarc':
        return 'text-white';
      case 'ip':
      case 'reputation':
        return 'text-white';
      case 'content':
        return 'text-white';
      default:
        return 'text-white';
    }
  };

  // Helper function to determine category background color
  const getCategoryBgColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'spf':
        return '#956b50';
      case 'dkim':
        return '#66b077';
      case 'dmarc':
        return '#1b130e';
      case 'ip':
      case 'reputation':
        return '#f59e0b';
      case 'content':
        return '#4a3728';
      default:
        return '#4a3728';
    }
  };

  // Helper function to determine priority label
  const getPriorityLabel = (priority: number) => {
    switch (priority) {
      case 1:
        return 'Critical';
      case 2:
        return 'High';
      case 3:
        return 'Medium';
      case 4:
        return 'Low';
      default:
        return 'Info';
    }
  };

  // Helper function to determine priority color
  const getPriorityColor = (priority: number) => {
    switch (priority) {
      case 1:
        return 'text-white';
      case 2:
        return 'text-white';
      case 3:
        return 'text-white';
      case 4:
        return 'text-white';
      default:
        return 'text-white';
    }
  };

  // Helper function to determine priority background color
  const getPriorityBgColor = (priority: number) => {
    switch (priority) {
      case 1:
        return '#f59e0b';
      case 2:
        return '#f59e0b';
      case 3:
        return '#956b50';
      case 4:
        return '#66b077';
      default:
        return '#4a3728';
    }
  };

  return (
    <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl overflow-hidden shadow-sm transition-all duration-300 hover:bg-white/90 hover:shadow-md">
      <div className="p-6" style={{ borderBottom: '1px solid #f3ece8' }}>
        <div className="flex flex-wrap gap-2 mb-2">
          <span className={`px-2 py-1 rounded-md text-xs font-medium shadow-sm ${getCategoryColor(recommendation.category)}`} style={{
            backgroundColor: getCategoryBgColor(recommendation.category)
          }}>
            {recommendation.category.toUpperCase()}
          </span>
          <span className={`px-2 py-1 rounded-md text-xs font-medium shadow-sm ${getPriorityColor(recommendation.priority)}`} style={{
            backgroundColor: getPriorityBgColor(recommendation.priority)
          }}>
            {getPriorityLabel(recommendation.priority)}
          </span>
        </div>
        <h3 className="text-lg font-medium" style={{ color: '#1b130e' }}>{recommendation.issue_description}</h3>
      </div>

      <div className="p-6">
        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>Recommendation</h4>
          <p style={{ color: '#4a3728' }}>{recommendation.recommendation}</p>
        </div>

        {recommendation.dns_record_template && (
          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>DNS Record Template</h4>
            <div className="p-3 rounded-md font-mono text-sm overflow-x-auto" style={{ backgroundColor: '#f3ece8', color: '#1b130e' }}>
              {recommendation.dns_record_template}
            </div>
          </div>
        )}

        {recommendation.implementation_steps && (
          <div>
            <h4 className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>Implementation Steps</h4>
            <div>
              {recommendation.implementation_steps.split('\n').map((step, index) => {
                // Trim whitespace and check if step is not empty
                const trimmedStep = step.trim();
                if (!trimmedStep) {
                  return null; // Skip empty lines
                }

                // Enhanced regex to match various number formats at the start of a line
                // Matches: "1.", "1", "1. ", "1 ", "10.", "10", etc.
                const numberMatch = trimmedStep.match(/^(\d+\.?\s*)(.*)/);
                if (numberMatch) {
                  const [, numberPart, restOfStep] = numberMatch;
                  return (
                    <p key={`step-${index}`} className="mb-2" style={{ margin: '0 0 8px 0' }}>
                      <span
                        style={{
                          fontWeight: 'bold',
                          color: '#1b130e',
                          fontFamily: 'inherit'
                        }}
                      >
                        {numberPart}
                      </span>
                      <span style={{ color: '#4a3728', fontWeight: 'normal' }}>
                        {restOfStep}
                      </span>
                    </p>
                  );
                }
                // If no number match, render the step as-is
                return (
                  <p key={`text-${index}`} className="mb-2" style={{ color: '#4a3728', margin: '0 0 8px 0' }}>
                    {trimmedStep}
                  </p>
                );
              }).filter(Boolean)}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
