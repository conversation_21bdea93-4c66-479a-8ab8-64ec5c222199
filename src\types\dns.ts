// DNS Validation Types - Isolated from existing types

export interface DnsValidationResult {
  exists: boolean;
  records: string[];
  isValid: boolean;
  errors: string[];
  lastChecked: string;
  recordType: 'TXT' | 'MX' | 'A' | 'AAAA' | 'CNAME';
  domain: string;
  recordName: string;
}

export interface DnsValidationRequest {
  domain: string;
  recordName: string;
  recordType: 'TXT' | 'MX' | 'A' | 'AAAA' | 'CNAME';
  expectedValue?: string;
  timeout?: number;
}

export interface DnsValidationResponse {
  success: boolean;
  data?: DnsValidationResult;
  message?: string;
  error?: string;
}

export interface DnsValidationHistory {
  id: string;
  record_id: string;
  validation_type: string;
  domain: string;
  record_name: string;
  expected_value?: string;
  actual_values: string[];
  is_valid: boolean;
  errors: string[];
  validated_at: string;
}

export interface DnsValidationOptions {
  timeout: number;
  retries: number;
  retryDelay: number;
  dnsServers?: string[];
}

export interface DnsRecordDisplay {
  type: 'TXT' | 'MX' | 'A' | 'AAAA' | 'CNAME';
  name: string;
  value: string;
  ttl?: number;
  priority?: number; // For MX records
}

export interface DnsValidationError {
  code: string;
  message: string;
  details?: string;
  timestamp: string;
}

// Shared DNS validation utilities
export interface DnsValidatorConfig {
  defaultTimeout: number;
  defaultRetries: number;
  defaultRetryDelay: number;
  defaultDnsServers: string[];
  maxRecordLength: number;
  supportedRecordTypes: string[];
}
