# Fademail Deployment Guide

This guide provides instructions for deploying the Fademail application to Vercel.

## Prerequisites

- A Vercel account
- A GitHub account with access to the fademail-production repository
- Supabase account with a project set up
- MySQL database for Guerrilla Mail (already set up on Digital Ocean)

## Deployment Steps

### 1. Prepare Your Environment Variables

The following environment variables need to be set in Vercel:

#### Database Configuration
```
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

GUERRILLA_DB_HOST=your-mysql-host
GUERRILLA_DB_USER=your-mysql-user
GUERRILLA_DB_PASSWORD=your-mysql-password
GUERRILLA_DB_NAME=your-mysql-database
GUERRILLA_DB_PORT=3306
```

#### Admin Configuration
```
ADMIN_USERNAME=your-admin-username
ADMIN_PASSWORD_HASH=your-hashed-password
JWT_SECRET=your-jwt-secret
SECURE_ADMIN_PATH=management-portal-x7z9y2
```

#### Email Configuration
```
EMAIL_EXPIRATION_HOURS=0.5
CLEANUP_API_KEY=your-cleanup-api-key
```

### 2. Generate Password Hash

Before deploying, generate a hashed password for the admin user:

1. Run the script locally:
   ```
   node scripts/generate-password-hash.js your-password
   ```
2. Copy the generated hash to your Vercel environment variables as `ADMIN_PASSWORD_HASH`

### 3. Deploy to Vercel

1. Log in to your Vercel dashboard
2. Click "New Project"
3. Import your GitHub repository (connect your GitHub account if needed)
4. Select the "fademail-production" repository
5. Configure your project settings:
   - Framework Preset: Next.js
   - Build Command: `npm run build` or `pnpm build` (depending on what you use)
   - Output Directory: `.next` (default for Next.js)
   - Environment Variables: Add all the variables listed above
6. Click "Deploy"

### 4. Configure Custom Domain (Optional)

1. In your Vercel dashboard, go to your project settings
2. Click on "Domains"
3. Add your custom domain (e.g., fademail.com)
4. Follow Vercel's instructions to set up DNS records

## Security Notes

### Environment Variables

- **NEVER** use the `NEXT_PUBLIC_` prefix for sensitive information like admin credentials or API keys
- Variables with the `NEXT_PUBLIC_` prefix are:
  - Exposed to the browser/client-side
  - Visible in your JavaScript bundle
  - Accessible to anyone who visits your site

### Authentication

- Admin authentication is implemented server-side
- Passwords are hashed using bcrypt
- JWT tokens are stored in HTTP-only cookies
- Rate limiting is implemented to prevent brute force attacks

### Supabase Security

- Ensure your Supabase Row Level Security (RLS) policies are properly configured
- The `NEXT_PUBLIC_SUPABASE_ANON_KEY` has limited permissions by design
- The `SUPABASE_SERVICE_ROLE_KEY` has full access and should never be exposed to the client

## Troubleshooting

### Build Errors

If you encounter build errors:

1. Check the build logs in Vercel
2. Ensure all required environment variables are set
3. Verify that the production repository has been properly synced from the main repository

### Authentication Issues

If you can't log in to the admin portal:

1. Verify that the `ADMIN_USERNAME` and `ADMIN_PASSWORD_HASH` are correctly set
2. Check that the `JWT_SECRET` is set and matches the one used to generate tokens
3. Clear your browser cookies and try again

## Maintenance

### Updating the Application

1. Make changes to the main repository
2. Run the sync workflow to update the production repository
3. Vercel will automatically deploy the updated production repository

### Rotating Credentials

For security, periodically rotate:

1. Admin password (generate a new hash)
2. JWT secret
3. API keys

## Support

For additional help, contact the development team or refer to the project documentation.
