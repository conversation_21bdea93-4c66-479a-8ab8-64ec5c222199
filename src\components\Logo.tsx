'use client';

import Link from 'next/link';
import Image from 'next/image';

/**
 * Logo Component
 *
 * Displays the VanishPost logo with optimized image loading
 */
export default function Logo({ showText = true }: { showText?: boolean }) {
  return (
    <Link
      href="/"
      className="flex items-center hover:opacity-80 transition-all duration-200 group"
      aria-label="VanishPost Home"
    >
      <div className="transition-transform duration-300 ease-in-out group-hover:scale-110 relative w-10 h-10 flex items-center justify-center">
        <Image
          src="/vanishpost-temporary-email-logo.svg"
          alt="VanishPost Secure Temporary Email Service Logo"
          width={40}
          height={40}
          priority
          className="w-10 h-10"
        />
      </div>
      {showText && (
        <span className="ml-2.5 text-xl font-semibold text-[#605f5f] hidden sm:inline tracking-tight">
          VanishPost
        </span>
      )}
    </Link>
  );
}
