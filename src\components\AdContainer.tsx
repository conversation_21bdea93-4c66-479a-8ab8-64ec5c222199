'use client';

import { useEffect, useRef, useState } from 'react';
import { useAdConfig } from '@/hooks/useAdConfig';
import { getDeviceType } from '@/lib/deviceDetection';
import { AdDisplayOptions } from '@/lib/config/types';
import { ClientOnly } from '@/lib/utils/suppressHydrationWarning';

interface AdContainerProps {
  placementId: string; // e.g., 'top', 'middle', 'sidebar'
  className?: string;
  style?: React.CSSProperties;
}

/**
 * AdContainer Component
 *
 * Renders an advertisement container that integrates with the existing ad management system.
 * The component will only display ads that are enabled in the admin panel and match the
 * current domain and device type settings.
 */
export default function AdContainer({ placementId, className, style }: AdContainerProps) {
  const adRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for display options from ad config
  const [displayStyles, setDisplayStyles] = useState<React.CSSProperties>({});

  // State for ad label
  const [showLabel, setShowLabel] = useState(false);
  const [labelText, setLabelText] = useState('Advertisement');

  // Use the existing ad configuration hook
  const { adConfig, isLoading, error: configError } = useAdConfig(placementId);

  useEffect(() => {
    // Only proceed if we have ad configuration and we're on the client
    if (typeof window === 'undefined' || isLoading) return;

    // Handle configuration errors
    if (configError) {
      console.error(`Ad configuration error for ${placementId}:`, configError);
      setError(configError);
      setIsVisible(false);
      return;
    }

    // If no ad config found, don't display anything
    if (!adConfig) {
      setIsVisible(false);
      return;
    }

    // Check if this ad should be displayed based on existing rules
    const currentDomain = window.location.hostname;
    const deviceType = getDeviceType();

    // Skip if ad is disabled, domain doesn't match, or device type is excluded
    if (
      !adConfig.isEnabled ||
      (adConfig.domain !== '*' && adConfig.domain !== currentDomain) ||
      !adConfig.deviceTypes.includes(deviceType)
    ) {
      setIsVisible(false);
      return;
    }

    setIsVisible(true);
    setError(null);

    // Process display options from ad configuration
    const displayOptions = adConfig.displayOptions as AdDisplayOptions || {};
    const newDisplayStyles: React.CSSProperties = {};

    // Apply display options if they exist
    if (displayOptions) {
      // Apply background color if specified
      if (displayOptions.backgroundColor) {
        newDisplayStyles.backgroundColor = displayOptions.backgroundColor;
      }

      // Apply padding if specified
      if (displayOptions.padding) {
        newDisplayStyles.padding = displayOptions.padding;
      }

      // Apply margin if specified
      if (displayOptions.margin) {
        newDisplayStyles.margin = displayOptions.margin;
      }

      // Apply border radius if specified
      if (displayOptions.borderRadius) {
        newDisplayStyles.borderRadius = displayOptions.borderRadius;
      }

      // Apply border if specified
      if (displayOptions.showBorder) {
        newDisplayStyles.border = `1px solid ${displayOptions.borderColor || '#e5e7eb'}`;
      }

      // Apply max width if specified
      if (displayOptions.maxWidth) {
        newDisplayStyles.maxWidth = displayOptions.maxWidth;
      }

      // Apply overflow if specified
      if (displayOptions.overflow) {
        newDisplayStyles.overflow = displayOptions.overflow;
      }

      // Set label options
      setShowLabel(displayOptions.showLabel || false);
      if (displayOptions.labelText) {
        setLabelText(displayOptions.labelText);
      }
    }

    // Update display styles
    setDisplayStyles(newDisplayStyles);

    // Create an ad slot using the existing configuration
    const adSlot = document.createElement('ins');
    adSlot.className = 'adsbygoogle';
    adSlot.style.display = 'block';
    adSlot.setAttribute('data-ad-client', adConfig.adClientId || 'ca-pub-8397529755029714');
    adSlot.setAttribute('data-ad-slot', adConfig.adUnitId);
    adSlot.setAttribute('data-ad-format', adConfig.format || 'auto');
    adSlot.setAttribute('data-full-width-responsive', 'true');

    // Clear the container and append the ad
    if (adRef.current) {
      adRef.current.innerHTML = '';
      adRef.current.appendChild(adSlot);

      // Push the ad to AdSense
      try {
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      } catch (err) {
        console.error('AdSense error:', err);
        setError('Failed to load advertisement');
      }
    }

    // Check if ads are blocked after a short delay
    const checkAdBlocker = setTimeout(() => {
      if (adRef.current && adRef.current.clientHeight <= 10) {
        console.log('Ad blocker detected for', placementId);
        // We don't show any message to avoid disrupting the user experience
      }
    }, 2000);

    // Cleanup function
    return () => {
      clearTimeout(checkAdBlocker);
      if (adRef.current) {
        adRef.current.innerHTML = '';
      }
    };
  }, [placementId, adConfig, isLoading, configError]);

  // Use ClientOnly to prevent hydration errors with AdSense
  return (
    <ClientOnly>
      {isVisible && (
        <div
          ref={adRef}
          id={`ad-container-${placementId}`}
          className={`ad-container ${className || ''}`}
          style={{
            minHeight: '50px',
            background: 'transparent',
            overflow: 'hidden',
            ...displayStyles,
            ...style
          }}
          data-testid={`ad-container-${placementId}`}
          aria-label="Advertisement"
          role="complementary"
        >
          {showLabel && (
            <div className="text-xs text-gray-500 text-center py-1 bg-gray-100 rounded mb-1">
              {labelText}
            </div>
          )}
          {error && (
            <div className="text-xs text-gray-400 text-center py-1">
              Ad loading error
            </div>
          )}
        </div>
      )}
    </ClientOnly>
  );
}
