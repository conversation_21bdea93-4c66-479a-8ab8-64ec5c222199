/**
 * Optimized Ad Service
 *
 * This service provides functions to manage ad placements with optimized caching
 */
import { createServerSupabaseClient } from '@/lib/supabase';
import { AdConfig, AdDisplayOptions, AdSchedule } from './types';
import { logInfo, logError } from '@/lib/logging';
import { shouldDisplayAd } from './adScheduleChecker';
import {
  getAdFromCache,
  setAdInCache,
  getDomainAdsFromCache,
  setDomainAdsInCache,
  invalidateAdCache,
  invalidateDomainCache,
  flushAllCaches
} from './adCacheService';
// Define the database record type
interface AdConfigDB {
  placement_id: string;
  domain: string;
  ad_unit_id: string;
  ad_client_id: string; // Required field with default value in database
  is_enabled: boolean;
  device_types: string[];
  display_options: any;
  schedule: any;
  created_at: string;
  updated_at: string;
}

/**
 * Get ad placement by ID and domain with caching
 *
 * @param placementId Ad placement ID
 * @param domain Domain
 * @returns Promise<AdConfig | null> Ad configuration or null if not found
 */
export async function getAdPlacement(
  placementId: string,
  domain: string
): Promise<AdConfig | null> {
  try {
    // Check cache first
    const cachedAd = getAdFromCache(placementId, domain);
    if (cachedAd) {
      return cachedAd;
    }

    // Get from database
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ad_config')
      .select('*')
      .eq('placement_id', placementId)
      .eq('domain', domain)
      .single();

    if (error || !data) {
      logError('ad', `Error getting ad placement: ${placementId} for domain: ${domain}`, { error });
      return null;
    }

    // Cast the data to ensure proper type safety
    const dbData = data as AdConfigDB;

    // Map database result to AdConfig object
    const adConfig: AdConfig = {
      placementId: dbData.placement_id,
      domain: dbData.domain,
      adUnitId: dbData.ad_unit_id,
      adClientId: dbData.ad_client_id,
      isEnabled: dbData.is_enabled,
      deviceTypes: dbData.device_types,
      displayOptions: dbData.display_options as AdDisplayOptions | undefined,
      schedule: dbData.schedule as AdSchedule | undefined,
      createdAt: dbData.created_at,
      updatedAt: dbData.updated_at
    };

    // Cache the ad
    setAdInCache(adConfig);
    return adConfig;
  } catch (error) {
    logError('ad', `Error getting ad placement: ${placementId} for domain: ${domain}`, { error });
    return null;
  }
}

/**
 * Get all ad placements for a domain with caching
 *
 * @param domain Domain
 * @returns Promise<AdConfig[]> Array of ad configurations
 */
export async function getDomainAdPlacements(domain: string): Promise<AdConfig[]> {
  try {
    // Check cache first
    const cachedAds = getDomainAdsFromCache(domain);
    if (cachedAds) {
      return cachedAds;
    }

    // Get from database
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ad_config')
      .select('*')
      .eq('domain', domain)
      .order('placement_id');

    if (error || !data) {
      logError('ad', `Error getting ad placements for domain: ${domain}`, { error });
      return [];
    }

    // Map database results to AdConfig objects
    const ads: AdConfig[] = data.map(item => {
      // Cast the item to AdConfigDB to ensure type safety
      const dbItem = item as AdConfigDB;
      return {
        placementId: dbItem.placement_id,
        domain: dbItem.domain,
        adUnitId: dbItem.ad_unit_id,
        adClientId: dbItem.ad_client_id,
        isEnabled: dbItem.is_enabled,
        deviceTypes: dbItem.device_types,
        displayOptions: dbItem.display_options as AdDisplayOptions | undefined,
        schedule: dbItem.schedule as AdSchedule | undefined,
        createdAt: dbItem.created_at,
        updatedAt: dbItem.updated_at
      };
    });

    // Cache the ads
    setDomainAdsInCache(domain, ads);
    return ads;
  } catch (error) {
    logError('ad', `Error getting ad placements for domain: ${domain}`, { error });
    return [];
  }
}

/**
 * Get enabled ad placements for a domain with caching and schedule checking
 *
 * @param domain Domain
 * @param deviceType Device type (optional)
 * @returns Promise<AdConfig[]> Array of enabled ad configurations
 */
export async function getEnabledAdPlacements(
  domain: string,
  deviceType?: string
): Promise<AdConfig[]> {
  try {
    // Get all domain ads
    const allAds = await getDomainAdPlacements(domain);

    // Filter by enabled status, device type, and schedule
    return allAds.filter(ad => {
      // Check if enabled
      if (!ad.isEnabled) return false;

      // Check device type
      if (deviceType && !ad.deviceTypes.includes('all') && !ad.deviceTypes.includes(deviceType)) {
        return false;
      }

      // Check schedule
      return shouldDisplayAd(ad.schedule);
    });
  } catch (error) {
    logError('ad', `Error getting enabled ad placements for domain: ${domain}`, { error });
    return [];
  }
}

/**
 * Update ad placement with caching
 *
 * @param placementId Ad placement ID
 * @param domain Domain
 * @param updates Updates to apply
 * @returns Promise<AdConfig | null> Updated ad configuration or null if error
 */
export async function updateAdPlacement(
  placementId: string,
  domain: string,
  updates: {
    adUnitId?: string;
    isEnabled?: boolean;
    deviceTypes?: string[];
    displayOptions?: AdDisplayOptions;
    schedule?: AdSchedule;
  }
): Promise<AdConfig | null> {
  try {
    // Prepare update object
    const updateObj: any = {
      updated_at: new Date().toISOString()
    };

    if (updates.adUnitId !== undefined) {
      updateObj.ad_unit_id = updates.adUnitId;
    }

    if (updates.isEnabled !== undefined) {
      updateObj.is_enabled = updates.isEnabled;
    }

    if (updates.deviceTypes !== undefined) {
      updateObj.device_types = updates.deviceTypes;
    }

    if (updates.displayOptions !== undefined) {
      updateObj.display_options = updates.displayOptions;
    }

    if (updates.schedule !== undefined) {
      updateObj.schedule = updates.schedule;
    }

    // Update in database
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ad_config')
      .update(updateObj)
      .eq('placement_id', placementId)
      .eq('domain', domain)
      .select('*')
      .single();

    if (error || !data) {
      logError('ad', `Error updating ad placement: ${placementId} for domain: ${domain}`, { error });
      return null;
    }

    // Cast the data to ensure proper type safety
    const dbData = data as AdConfigDB;

    // Map database result to AdConfig object
    const adConfig: AdConfig = {
      placementId: dbData.placement_id,
      domain: dbData.domain,
      adUnitId: dbData.ad_unit_id,
      adClientId: dbData.ad_client_id,
      isEnabled: dbData.is_enabled,
      deviceTypes: dbData.device_types,
      displayOptions: dbData.display_options as AdDisplayOptions | undefined,
      schedule: dbData.schedule as AdSchedule | undefined,
      createdAt: dbData.created_at,
      updatedAt: dbData.updated_at
    };

    // Invalidate caches
    invalidateAdCache(placementId, domain);
    invalidateDomainCache(domain);

    return adConfig;
  } catch (error) {
    logError('ad', `Error updating ad placement: ${placementId} for domain: ${domain}`, { error });
    return null;
  }
}

/**
 * Get client-side ad placements with schedule checking
 *
 * @param domain Domain
 * @param deviceType Device type
 * @returns Promise<Pick<AdConfig, 'placementId' | 'adUnitId' | 'displayOptions' | 'schedule'>[]>
 */
export async function getClientAdPlacements(
  domain: string,
  deviceType: 'desktop' | 'tablet' | 'mobile'
): Promise<Pick<AdConfig, 'placementId' | 'adUnitId' | 'displayOptions' | 'schedule'>[]> {
  try {
    // Get enabled ads for the domain and device type
    const ads = await getEnabledAdPlacements(domain, deviceType);

    // Return only the necessary information
    return ads.map(ad => ({
      placementId: ad.placementId,
      adUnitId: ad.adUnitId,
      displayOptions: ad.displayOptions,
      schedule: ad.schedule
    }));
  } catch (error) {
    logError('ad', `Error getting client-side ad placements for domain: ${domain}`, { error });
    return [];
  }
}
