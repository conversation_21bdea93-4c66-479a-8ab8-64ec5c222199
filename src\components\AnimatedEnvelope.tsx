import React, { useEffect, useState } from 'react';

/**
 * AnimatedEnvelope Component
 *
 * Displays an animated envelope icon with a subtle floating animation.
 * The animation is tasteful and not distracting, suitable for empty states.
 */
export default function AnimatedEnvelope() {
  // State to track if we're on the client-side (for SSR compatibility)
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Mark as mounted for SSR
    setIsMounted(true);
  }, []);

  // Don't render anything during SSR
  if (!isMounted) return null;

  return (
    <div className="animate-float">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-16 w-16 text-[#ce601c]"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        {/* Envelope body */}
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={1.5}
          d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
        />

        {/* Envelope flap */}
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={1.5}
          d="M3 8V7a2 2 0 012-2h14a2 2 0 012 2v1l-9 5.5L3 8z"
        />
      </svg>
    </div>
  );
}
