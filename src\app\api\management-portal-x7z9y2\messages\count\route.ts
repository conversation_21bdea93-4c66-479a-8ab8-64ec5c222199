import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { logError } from '@/lib/logging';
import { MessageStatus } from '@/lib/types/contact';

/**
 * API route for counting messages by status
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as MessageStatus || 'unread';
    
    // Get Supabase client
    const supabase = await createServerSupabaseClient();
    
    // Count messages with the specified status
    const { count, error } = await supabase
      .from('contact_messages')
      .select('*', { count: 'exact', head: true })
      .eq('status', status)
      .eq('is_admin_reply', false);
    
    if (error) {
      logError('messages', 'Failed to count messages', { status, error });
      return NextResponse.json({
        success: false,
        message: 'Failed to count messages'
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      count: count || 0
    });
  } catch (error) {
    logError('messages', 'Error counting messages', { error });
    return NextResponse.json({
      success: false,
      message: 'An error occurred while counting messages'
    }, { status: 500 });
  }
}
