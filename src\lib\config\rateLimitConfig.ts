/**
 * Dynamic Rate Limit Configuration Management
 * 
 * Allows runtime modification of rate limiting parameters through admin interface
 */

import { createServerSupabaseClient } from '@/lib/supabase';
import { logger } from '@/lib/logging/Logger';

export interface RateLimitRule {
  id: string;
  endpoint: string;
  name: string;
  description: string | null;
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests: boolean | null;
  skipFailedRequests: boolean | null;
  isActive: boolean | null;
  createdAt: string | null;
  updatedAt: string | null;
  updatedBy: string;
}

export interface RateLimitConfigUpdate {
  endpoint: string;
  windowMs?: number;
  maxRequests?: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  isActive?: boolean;
  updatedBy: string;
}

// Default rate limit configurations
const DEFAULT_RATE_LIMITS: Array<{
  endpoint: string;
  name: string;
  description: string | null;
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
  isActive: boolean;
}> = [
  {
    endpoint: 'emailGeneration',
    name: 'Email Generation',
    description: 'Rate limit for temporary email generation',
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 15,
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    isActive: true
  },
  {
    endpoint: 'analytics',
    name: 'Analytics Collection',
    description: 'Rate limit for analytics event collection',
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    isActive: true
  },
  {
    endpoint: 'adminAnalytics',
    name: 'Admin Analytics',
    description: 'Rate limit for admin analytics dashboard',
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60,
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
    isActive: true
  },
  {
    endpoint: 'sessionAnalytics',
    name: 'Session Analytics',
    description: 'Rate limit for session analytics tracking',
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30,
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
    isActive: true
  },
  {
    endpoint: 'heartbeat',
    name: 'Heartbeat',
    description: 'Rate limit for heartbeat endpoint',
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 200,
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
    isActive: true
  }
];

/**
 * Get all rate limit configurations
 */
export async function getRateLimitConfigs(): Promise<RateLimitRule[]> {
  try {
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('rate_limit_configs')
      .select('*')
      .order('endpoint');

    if (error) {
      await logger.error('RATE_LIMIT_CONFIG', 'Error fetching rate limit configs', { error });
      throw error;
    }

    // Map database columns (snake_case) to frontend format (camelCase)
    const mappedData = (data || []).map((row: any) => ({
      id: row.id,
      endpoint: row.endpoint,
      name: row.name,
      description: row.description,
      windowMs: row.window_ms,
      maxRequests: row.max_requests,
      skipSuccessfulRequests: row.skip_successful_requests,
      skipFailedRequests: row.skip_failed_requests,
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      updatedBy: row.updated_by
    }));

    return mappedData;
  } catch (error) {
    await logger.error('RATE_LIMIT_CONFIG', 'Error in getRateLimitConfigs', { error });
    throw error;
  }
}

/**
 * Get rate limit configuration for a specific endpoint
 */
export async function getRateLimitConfig(endpoint: string): Promise<RateLimitRule | null> {
  try {
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('rate_limit_configs')
      .select('*')
      .eq('endpoint', endpoint)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      await logger.error('RATE_LIMIT_CONFIG', 'Error fetching rate limit config', { endpoint, error });
      throw error;
    }

    if (!data) {
      return null;
    }

    // Map database columns (snake_case) to frontend format (camelCase)
    return {
      id: data.id,
      endpoint: data.endpoint,
      name: data.name,
      description: data.description,
      windowMs: data.window_ms,
      maxRequests: data.max_requests,
      skipSuccessfulRequests: data.skip_successful_requests,
      skipFailedRequests: data.skip_failed_requests,
      isActive: data.is_active,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      updatedBy: data.updated_by
    };
  } catch (error) {
    await logger.error('RATE_LIMIT_CONFIG', 'Error in getRateLimitConfig', { endpoint, error });
    throw error;
  }
}

/**
 * Update rate limit configuration
 */
export async function updateRateLimitConfig(
  configUpdate: RateLimitConfigUpdate
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('rate_limit_configs')
      .update({
        window_ms: configUpdate.windowMs,
        max_requests: configUpdate.maxRequests,
        skip_successful_requests: configUpdate.skipSuccessfulRequests,
        skip_failed_requests: configUpdate.skipFailedRequests,
        is_active: configUpdate.isActive,
        updated_by: configUpdate.updatedBy,
        updated_at: new Date().toISOString()
      })
      .eq('endpoint', configUpdate.endpoint)
      .select()
      .single();

    if (error) {
      await logger.error('RATE_LIMIT_CONFIG', 'Error updating rate limit config', { 
        endpoint: configUpdate.endpoint, 
        error 
      });
      return { success: false, error: error.message };
    }

    await logger.info('RATE_LIMIT_CONFIG', 'Rate limit config updated', { 
      endpoint: configUpdate.endpoint,
      updatedBy: configUpdate.updatedBy,
      changes: configUpdate
    });

    // Clear any cached rate limit data for this endpoint
    await clearRateLimitCache(configUpdate.endpoint);

    return { success: true };
  } catch (error) {
    await logger.error('RATE_LIMIT_CONFIG', 'Error in updateRateLimitConfig', { 
      endpoint: configUpdate.endpoint, 
      error 
    });
    return { success: false, error: 'Failed to update rate limit configuration' };
  }
}

/**
 * Create a new rate limit configuration
 */
export async function createRateLimitConfig(
  config: Omit<RateLimitRule, 'id' | 'createdAt' | 'updatedAt'> & { updatedBy: string }
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('rate_limit_configs')
      .insert({
        endpoint: config.endpoint,
        name: config.name,
        description: config.description,
        window_ms: config.windowMs,
        max_requests: config.maxRequests,
        skip_successful_requests: config.skipSuccessfulRequests,
        skip_failed_requests: config.skipFailedRequests,
        is_active: config.isActive,
        updated_by: config.updatedBy
      })
      .select()
      .single();

    if (error) {
      await logger.error('RATE_LIMIT_CONFIG', 'Error creating rate limit config', { 
        endpoint: config.endpoint, 
        error 
      });
      return { success: false, error: error.message };
    }

    await logger.info('RATE_LIMIT_CONFIG', 'Rate limit config created', { 
      endpoint: config.endpoint,
      createdBy: config.updatedBy
    });

    return { success: true };
  } catch (error) {
    await logger.error('RATE_LIMIT_CONFIG', 'Error in createRateLimitConfig', { 
      endpoint: config.endpoint, 
      error 
    });
    return { success: false, error: 'Failed to create rate limit configuration' };
  }
}

/**
 * Initialize default rate limit configurations
 */
export async function initializeDefaultRateLimitConfigs(): Promise<void> {
  try {
    for (const defaultConfig of DEFAULT_RATE_LIMITS) {
      const existing = await getRateLimitConfig(defaultConfig.endpoint);
      
      if (!existing) {
        await createRateLimitConfig({
          ...defaultConfig,
          updatedBy: 'system_initialization'
        });
      }
    }

    await logger.info('RATE_LIMIT_CONFIG', 'Default rate limit configurations initialized');
  } catch (error) {
    await logger.error('RATE_LIMIT_CONFIG', 'Error initializing default configs', { error });
  }
}

/**
 * Clear rate limit cache for an endpoint (implementation depends on your caching strategy)
 */
async function clearRateLimitCache(endpoint: string): Promise<void> {
  try {
    // If you're using a cache like Redis or in-memory cache, clear it here
    // For now, we'll just log the action
    await logger.info('RATE_LIMIT_CONFIG', 'Rate limit cache cleared', { endpoint });
  } catch (error) {
    await logger.error('RATE_LIMIT_CONFIG', 'Error clearing rate limit cache', { endpoint, error });
  }
}

/**
 * Get rate limit configuration with fallback to defaults
 */
export async function getRateLimitConfigWithFallback(endpoint: string): Promise<{
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
}> {
  try {
    const config = await getRateLimitConfig(endpoint);
    
    if (config) {
      return {
        windowMs: config.windowMs,
        maxRequests: config.maxRequests,
        skipSuccessfulRequests: config.skipSuccessfulRequests ?? false,
        skipFailedRequests: config.skipFailedRequests ?? false
      };
    }

    // Fallback to default configuration
    const defaultConfig = DEFAULT_RATE_LIMITS.find(c => c.endpoint === endpoint);
    if (defaultConfig) {
      return {
        windowMs: defaultConfig.windowMs,
        maxRequests: defaultConfig.maxRequests,
        skipSuccessfulRequests: defaultConfig.skipSuccessfulRequests,
        skipFailedRequests: defaultConfig.skipFailedRequests
      };
    }

    // Ultimate fallback
    return {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 100,
      skipSuccessfulRequests: false,
      skipFailedRequests: false
    };
  } catch (error) {
    await logger.error('RATE_LIMIT_CONFIG', 'Error getting rate limit config with fallback', { 
      endpoint, 
      error 
    });
    
    // Return safe defaults on error
    return {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 100,
      skipSuccessfulRequests: false,
      skipFailedRequests: false
    };
  }
}
