/**
 * Supabase client for browser-side operations
 *
 * This module provides a Supabase client for browser-side operations
 * using the environment variables.
 */

import { createBrowserClient } from '@supabase/ssr';
import { Database } from '@/lib/database.types';

// Singleton instance for browser client
let browserClientInstance: ReturnType<typeof createBrowserClient<Database>> | null = null;

/**
 * Create a Supabase client for use in browser environments
 * Uses a singleton pattern to prevent multiple instances
 *
 * Note: Browser clients always use environment variables since
 * they don't have access to the database configuration service.
 */
export function createBrowserSupabaseClient() {
  if (typeof window !== 'undefined' && browserClientInstance) {
    return browserClientInstance;
  }

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

  const client = createBrowserClient<Database>(supabaseUrl, supabase<PERSON>nonKey, {
    global: {
      headers: {
        apikey: supabase<PERSON><PERSON><PERSON>ey,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }
  });

  if (typeof window !== 'undefined') {
    browserClientInstance = client;
  }

  return client;
}
