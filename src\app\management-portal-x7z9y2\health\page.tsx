'use client';

import HealthDashboard from '@/components/admin/HealthDashboard';
import { Card, CardContent } from '@/components/ui/Card';

/**
 * Admin Health Dashboard Page
 */
export default function HealthPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
          </svg>
          <h1 className="text-2xl font-bold text-gray-900">System Health</h1>
        </div>
        <p className="mt-2 text-sm text-gray-600 ml-11">
          Monitor the health and performance of the Fademail system in real-time.
        </p>
      </div>

      <Card>
        <CardContent className="p-0">
          <HealthDashboard />
        </CardContent>
      </Card>
    </div>
  );
}
