'use client';

import React from 'react';
import * as ProgressPrimitive from '@radix-ui/react-progress';
import { cn } from '@/lib/utils';

/**
 * shadcn/ui Progress Component adapted for VanishPost
 * 
 * A modern progress indicator component for showing completion status
 * of long-running operations in the admin interface.
 */
const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({ className, value, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(
      "relative h-2 w-full overflow-hidden rounded-full bg-gray-200",
      "dark:bg-gray-700", // Dark mode support for VanishPost theme
      className
    )}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className={cn(
        "h-full w-full flex-1 bg-gray-900 transition-all",
        "dark:bg-gray-100", // Dark mode support
        // VanishPost brand color variants
        "data-[state=complete]:bg-green-600",
        "data-[state=loading]:bg-blue-600"
      )}
      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
    />
  </ProgressPrimitive.Root>
));

Progress.displayName = ProgressPrimitive.Root.displayName;

/**
 * Enhanced Progress variants for VanishPost admin use cases
 */

// Progress with label and percentage
interface ProgressWithLabelProps extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> {
  label?: string;
  showPercentage?: boolean;
  variant?: 'default' | 'success' | 'warning' | 'error';
}

export function ProgressWithLabel({ 
  className, 
  value = 0, 
  label, 
  showPercentage = true,
  variant = 'default',
  ...props 
}: ProgressWithLabelProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'success':
        return 'bg-green-600';
      case 'warning':
        return 'bg-yellow-600';
      case 'error':
        return 'bg-red-600';
      default:
        return 'bg-blue-600'; // VanishPost primary color
    }
  };

  return (
    <div className="space-y-2">
      {(label || showPercentage) && (
        <div className="flex justify-between items-center text-sm">
          {label && <span className="font-medium text-gray-700">{label}</span>}
          {showPercentage && <span className="text-gray-500">{Math.round(value)}%</span>}
        </div>
      )}
      <ProgressPrimitive.Root
        className={cn(
          "relative h-2 w-full overflow-hidden rounded-full bg-gray-200",
          className
        )}
        value={value}
        {...props}
      >
        <ProgressPrimitive.Indicator
          className={cn(
            "h-full w-full flex-1 transition-all duration-300 ease-in-out",
            getVariantClasses()
          )}
          style={{ transform: `translateX(-${100 - value}%)` }}
        />
      </ProgressPrimitive.Root>
    </div>
  );
}

// Circular progress for compact spaces
interface CircularProgressProps {
  value: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'error';
}

export function CircularProgress({ 
  value, 
  size = 40, 
  strokeWidth = 4, 
  className,
  variant = 'default'
}: CircularProgressProps) {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (value / 100) * circumference;

  const getStrokeColor = () => {
    switch (variant) {
      case 'success':
        return 'stroke-green-600';
      case 'warning':
        return 'stroke-yellow-600';
      case 'error':
        return 'stroke-red-600';
      default:
        return 'stroke-blue-600';
    }
  };

  return (
    <div className={cn("relative", className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-gray-200"
        />
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn("transition-all duration-300 ease-in-out", getStrokeColor())}
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className="text-xs font-medium text-gray-700">
          {Math.round(value)}%
        </span>
      </div>
    </div>
  );
}

// Progress for file uploads with file info
interface FileUploadProgressProps {
  fileName: string;
  progress: number;
  fileSize?: string;
  uploadSpeed?: string;
  className?: string;
}

export function FileUploadProgress({
  fileName,
  progress,
  fileSize,
  uploadSpeed,
  className
}: FileUploadProgressProps) {
  return (
    <div className={cn("space-y-2 p-3 border rounded-lg bg-gray-50", className)}>
      <div className="flex justify-between items-start">
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">{fileName}</p>
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            {fileSize && <span>{fileSize}</span>}
            {uploadSpeed && <span>• {uploadSpeed}</span>}
          </div>
        </div>
        <span className="text-sm font-medium text-gray-700 ml-2">
          {Math.round(progress)}%
        </span>
      </div>
      <Progress value={progress} className="h-1.5" />
    </div>
  );
}

export { Progress };
