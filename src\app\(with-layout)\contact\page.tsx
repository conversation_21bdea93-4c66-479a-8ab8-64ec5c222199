import React from 'react';
import type { Metadata } from 'next';
import ContactForm from '@/components/ContactForm';

export const metadata: Metadata = {
  title: 'Contact Us | VanishPost',
  description: 'Get in touch with the VanishPost team for support, feedback, or partnership inquiries.',
};

export default function ContactPage() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-12 sm:py-16">
      <div className="text-center mb-12">
        <h1 className="text-3xl sm:text-4xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Contact Us</h1>
        <p className="text-lg max-w-3xl mx-auto" style={{ color: 'var(--earth-brown-medium)' }}>
          Have questions, feedback, or need assistance? We're here to help!
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 mb-12">
        {/* Contact Form */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Send Us a Message</h2>
          <ContactForm />
        </div>

        {/* Contact Information */}
        <div>
          <div className="p-6 rounded-md shadow-sm mb-6" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
            <h2 className="text-xl font-semibold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Contact Information</h2>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="p-2 rounded-full mr-3 mt-1" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium" style={{ color: 'var(--earth-brown-dark)' }}>Email</h3>
                  <p style={{ color: 'var(--earth-brown-medium)' }}><EMAIL></p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="p-2 rounded-full mr-3 mt-1" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium" style={{ color: 'var(--earth-brown-dark)' }}>Response Time</h3>
                  <p style={{ color: 'var(--earth-brown-medium)' }}>We typically respond within 24-48 hours during business days.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
            <h2 className="text-xl font-semibold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Connect With Us</h2>
            <p className="mb-4" style={{ color: 'var(--earth-brown-medium)' }}>
              Follow us on social media for updates, privacy tips, and more.
            </p>
            <div className="flex space-x-4">
              {/* Twitter hidden until page is created */}
              <a href="https://www.facebook.com/vanishpost" className="p-3 rounded-full transition-colors social-icon"
                 style={{ backgroundColor: 'var(--earth-beige-secondary)', color: 'var(--earth-brown-medium)' }}
                 aria-label="Facebook"
                 target="_blank"
                 rel="noopener noreferrer">
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                </svg>
              </a>
              <a href="https://www.instagram.com/vanishpost_temporary_email/" className="p-3 rounded-full transition-colors social-icon"
                 style={{ backgroundColor: 'var(--earth-beige-secondary)', color: 'var(--earth-brown-medium)' }}
                 aria-label="Instagram"
                 target="_blank"
                 rel="noopener noreferrer">
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" />
                </svg>
              </a>
              {/* LinkedIn hidden until page is created */}
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="p-8 rounded-md text-center" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
        <h2 className="text-2xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Frequently Asked Questions</h2>
        <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
          Find quick answers to common questions about VanishPost.
        </p>
        <a href="/faq" className="inline-block bg-[#ce601c] text-white px-6 py-3 rounded-md font-medium hover:bg-[#b85518] transition-colors">
          Visit FAQ Page
        </a>
      </div>
    </div>
  );
}
