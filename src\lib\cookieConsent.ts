import { optIn, optOut } from './analytics/posthog';

export type CookiePreferences = {
  necessary: boolean;
  functional: boolean;
  analytics: boolean;
  marketing: boolean;
  preferences_set: boolean;
  consent_date: string;
};

export const defaultPreferences: CookiePreferences = {
  necessary: true, // Always required
  functional: false,
  analytics: false,
  marketing: false,
  preferences_set: false,
  consent_date: '',
};

/**
 * Get cookie preferences from localStorage
 */
export function getCookiePreferences(): CookiePreferences {
  if (typeof window === 'undefined') {
    return defaultPreferences;
  }

  try {
    const storedPreferences = localStorage.getItem('cookie_preferences');
    if (storedPreferences) {
      return JSON.parse(storedPreferences) as CookiePreferences;
    }
  } catch (error) {
    console.error('Error getting cookie preferences:', error);
  }

  return defaultPreferences;
}

/**
 * Save cookie preferences to localStorage
 */
export function saveCookiePreferences(preferences: Partial<CookiePreferences>): CookiePreferences {
  if (typeof window === 'undefined') {
    return defaultPreferences;
  }

  try {
    const currentPreferences = getCookiePreferences();
    const updatedPreferences: CookiePreferences = {
      ...currentPreferences,
      ...preferences,
      preferences_set: true,
      consent_date: new Date().toISOString(),
    };

    localStorage.setItem('cookie_preferences', JSON.stringify(updatedPreferences));

    // Apply analytics preference
    if (updatedPreferences.analytics) {
      optIn();
    } else {
      optOut();
    }

    return updatedPreferences;
  } catch (error) {
    console.error('Error saving cookie preferences:', error);
    return defaultPreferences;
  }
}

/**
 * Check if cookie preferences have been set
 */
export function hasSetCookiePreferences(): boolean {
  const preferences = getCookiePreferences();
  return preferences.preferences_set;
}

/**
 * Accept all cookies
 */
export function acceptAllCookies(): CookiePreferences {
  return saveCookiePreferences({
    necessary: true,
    functional: true,
    analytics: true,
    marketing: false, // Marketing cookies removed from UI but kept in type for backward compatibility
  });
}

/**
 * Accept only necessary cookies
 */
export function acceptNecessaryCookies(): CookiePreferences {
  return saveCookiePreferences({
    necessary: true,
    functional: false,
    analytics: false,
    marketing: false,
  });
}

/**
 * Reset cookie preferences
 */
export function resetCookiePreferences(): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.removeItem('cookie_preferences');
  optOut(); // Opt out of analytics by default
}
