#13 72.96 Failed to compile.
2025-May-21 13:30:51.524796
#13 72.96
2025-May-21 13:30:51.524796
#13 72.96 ./src/app/api/admin/analytics/toggle/route.ts:47:45
2025-May-21 13:30:51.524796
#13 72.96 Type error: Argument of type '"analyticsEnabled"' is not assignable to parameter of type 'keyof AppConfig'.
2025-May-21 13:30:51.524796
#13 72.96
2025-May-21 13:30:51.524796
#13 72.96   45 |
2025-May-21 13:30:51.524796
#13 72.96   46 |     // Update the configuration
2025-May-21 13:30:51.524796
#13 72.96 > 47 |     const updateResult = await updateConfig('analyticsEnabled', enabled);
2025-May-21 13:30:51.524796
#13 72.96      |                                             ^
2025-May-21 13:30:51.524796
#13 72.96   48 |     if (!updateResult) {
2025-May-21 13:30:51.524796
#13 72.96   49 |       return NextResponse.json(
2025-May-21 13:30:51.524796
#13 72.96   50 |         { success: false, error: 'Failed to update configuration' },
2025-May-21 13:30:51.524796
#13 73.19 Next.js build worker exited with code: 1 and signal: null
2025-May-21 13:30:51.524796
#13 ERROR: process "/bin/bash -ol pipefail -c npm run build" did not complete successfully: exit code: 1
2025-May-21 13:30:51.524796
------
2025-May-21 13:30:51.524796
> [stage-0  9/11] RUN --mount=type=cache,id=lgwc0ow4w08wsowkgcs40cgw-next/cache,target=/app/.next/cache --mount=type=cache,id=lgwc0ow4w08wsowkgcs40cgw-node_modules/cache,target=/app/node_modules/.cache npm run build:
2025-May-21 13:30:51.524796
72.96 Type error: Argument of type '"analyticsEnabled"' is not assignable to parameter of type 'keyof AppConfig'.
2025-May-21 13:30:51.524796
72.96
2025-May-21 13:30:51.524796
72.96   45 |
2025-May-21 13:30:51.524796
72.96   46 |     // Update the configuration
2025-May-21 13:30:51.524796
72.96 > 47 |     const updateResult = await updateConfig('analyticsEnabled', enabled);
2025-May-21 13:30:51.524796
72.96      |                                             ^
2025-May-21 13:30:51.524796
72.96   48 |     if (!updateResult) {
2025-May-21 13:30:51.524796
72.96   49 |       return NextResponse.json(
2025-May-21 13:30:51.524796
72.96   50 |         { success: false, error: 'Failed to update configuration' },
2025-May-21 13:30:51.524796
73.19 Next.js build worker exited with code: 1 and signal: null
2025-May-21 13:30:51.524796
------
2025-May-21 13:30:51.524796
2025-May-21 13:30:51.524796
1 warning found (use docker --debug to expand):
2025-May-21 13:30:51.524796
- UndefinedVar: Usage of undefined variable '$NIXPACKS_PATH' (line 18)
2025-May-21 13:30:51.524796
Dockerfile:24
2025-May-21 13:30:51.524796
--------------------
2025-May-21 13:30:51.524796
22 |     # build phase
2025-May-21 13:30:51.524796
23 |     COPY . /app/.
2025-May-21 13:30:51.524796
24 | >>> RUN --mount=type=cache,id=lgwc0ow4w08wsowkgcs40cgw-next/cache,target=/app/.next/cache --mount=type=cache,id=lgwc0ow4w08wsowkgcs40cgw-node_modules/cache,target=/app/node_modules/.cache npm run build
2025-May-21 13:30:51.524796
25 |
2025-May-21 13:30:51.524796
26 |
2025-May-21 13:30:51.524796
--------------------
2025-May-21 13:30:51.524796
ERROR: failed to solve: process "/bin/bash -ol pipefail -c npm run build" did not complete successfully: exit code: 1
2025-May-21 13:30:51.524796
exit status 1
2025-May-21 13:30:51.546313
Deployment failed. Removing the new version of your application.