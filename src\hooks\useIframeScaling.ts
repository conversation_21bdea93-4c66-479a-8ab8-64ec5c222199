import { useCallback, useEffect, useState, useRef } from 'react';

interface UseIframeScalingProps {
  initialScale?: number;
  minScale?: number;
  maxScale?: number;
  onChange?: (scale: number) => void;
}

/**
 * Simplified hook for managing iframe content scaling
 *
 * This hook provides a simple, reliable way to:
 * - Set and track scale factor for iframe content
 * - Enforce min/max scale constraints
 * - Trigger callbacks when scale changes
 */
export function useIframeScaling({
  initialScale = 1.0,
  minScale = 0.5,
  maxScale = 2.0,
  onChange
}: UseIframeScalingProps = {}) {
  // State for current scale factor
  const [scaleFactor, setScaleFactor] = useState<number>(initialScale);

  // Ref to track previous scale for comparison
  const prevScaleRef = useRef<number>(initialScale);

  // Effect to ensure scale is within bounds on initialization and when initialScale changes
  useEffect(() => {
    // Ensure initial scale is within bounds
    const boundedScale = Math.min(Math.max(initialScale, minScale), maxScale);

    // Only update if the scale has actually changed
    if (boundedScale !== scaleFactor) {
      // Update scale factor when initialScale changes
      setScaleFactor(boundedScale);

      // Call onChange if provided
      if (onChange) {
        onChange(boundedScale);
      }
    }
  }, [initialScale, minScale, maxScale, onChange, scaleFactor]);

  // Function to set scale with constraints
  const setScale = useCallback((newScale: number) => {
    // Clean number with 2 decimal places for consistent scaling
    const cleanScale = parseFloat(newScale.toFixed(2));

    // Enforce min/max constraints
    const boundedScale = Math.min(Math.max(cleanScale, minScale), maxScale);

    // Only update if the scale has actually changed
    if (boundedScale !== scaleFactor) {
      // Update state
      setScaleFactor(boundedScale);

      // Call onChange if provided
      if (onChange) {
        onChange(boundedScale);
      }

      // Update previous scale ref
      prevScaleRef.current = boundedScale;
    }

    return boundedScale;
  }, [minScale, maxScale, onChange, scaleFactor]);

  // Function to increase scale by a fixed amount
  const zoomIn = useCallback(() => {
    setScale(scaleFactor + 0.1);
  }, [scaleFactor, setScale]);

  // Function to decrease scale by a fixed amount
  const zoomOut = useCallback(() => {
    setScale(scaleFactor - 0.1);
  }, [scaleFactor, setScale]);

  // Function to reset scale to 100%
  const resetZoom = useCallback(() => {
    setScale(1.0);
  }, [setScale]);

  return {
    scaleFactor,
    prevScale: prevScaleRef.current,
    setScale,
    zoomIn,
    zoomOut,
    resetZoom
  };
}
