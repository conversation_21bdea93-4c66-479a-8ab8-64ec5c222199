import { NextRequest, NextResponse } from 'next/server';
import NodeCache from 'node-cache';
import { getEmailsForRecipient, deleteEmailFromGuerrilla } from '@/lib/db';
import { getTempEmailByAddress } from '@/lib/supabase-db';
import { EmailRetrievalResponse, ParsedEmail, GuerrillaEmail } from '@/lib/types';
import { API } from '@/lib/constants';
import { handleApiError, logError } from '@/lib/errorHandling';
import { parseEmail } from '@/lib/emailParser';
import { logger } from '@/lib/logging/Logger';

// Create a cache for parsed emails with a TTL of 5 minutes
// Note: We're using the emailCache from emailParser.ts instead
// This is kept for backward compatibility
import { emailCache } from '@/lib/emailParser';

/**
 * Get emails for a specific email address
 *
 * @route GET /api/emails/[address]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ address: string }> }
): Promise<NextResponse> {
  try {
    // Get the email address from the params
    const resolvedParams = await params;
    const emailAddress = decodeURIComponent(resolvedParams.address);

    await logger.info('EMAIL_FETCH', `Processing request for email address: ${emailAddress}`);

    // Check if the email address exists and is valid
    let tempEmail;
    try {
      tempEmail = await getTempEmailByAddress(emailAddress);
    } catch (dbError) {
      await logger.error('EMAIL_FETCH', `Database error while checking email address: ${emailAddress}`, { error: dbError });
      return NextResponse.json({
        success: false,
        message: 'Database error while checking email address',
        emails: [],
        totalCount: 0,
        page: 1,
        pageSize: API.DEFAULT_PAGE_SIZE
      }, { status: 500 });
    }

    if (!tempEmail) {
      await logger.warning('EMAIL_FETCH', `Email address not found: ${emailAddress}`);
      return NextResponse.json({
        success: false,
        message: 'Email address not found',
        emails: [],
        totalCount: 0,
        page: 1,
        pageSize: API.DEFAULT_PAGE_SIZE
      }, { status: 404 });
    }

    // Check if the email address has expired
    const now = new Date();
    if (tempEmail.expirationDate < now) {
      await logger.warning('EMAIL_FETCH', `Email address has expired: ${emailAddress}`);
      return NextResponse.json({
        success: false,
        message: 'Email address has expired',
        emails: [],
        totalCount: 0,
        page: 1,
        pageSize: API.DEFAULT_PAGE_SIZE
      }, { status: 410 });
    }

    // Get query parameters for pagination, sorting, and filtering
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const pageSize = parseInt(searchParams.get('pageSize') || String(API.DEFAULT_PAGE_SIZE), 10);
    const sortBy = searchParams.get('sortBy') || API.DEFAULT_SORT_BY;
    const sortOrder = searchParams.get('sortOrder') || API.DEFAULT_SORT_ORDER;

    await logger.info('EMAIL_FETCH', `Fetching emails for: ${emailAddress}`);

    // Get emails from the database
    // We need to ensure we only get emails for this specific email address
    // and not any previous addresses that might have similar patterns
    let emails: GuerrillaEmail[] = [];
    let totalCount = 0;

    try {
      const result = await getEmailsForRecipient(
        emailAddress,
        page,
        pageSize,
        sortBy,
        sortOrder
      );

      emails = result.emails;
      totalCount = result.totalCount;

      await logger.info('EMAIL_FETCH', `Emails found: ${emails.length}`);
    } catch (emailError) {
      await logger.error('EMAIL_FETCH', `Error fetching emails for ${emailAddress}`, { error: emailError });
      // Continue with empty emails array instead of failing
      await logger.info('EMAIL_FETCH', 'Continuing with empty emails array');
    }

    // Parse and sanitize emails
    const parsedEmails: ParsedEmail[] = await Promise.all(
      emails.map(async (email) => {
        // Check if the email is already in the cache
        const cacheKey = `email_${email.mail_id}`;
        const cachedEmail = emailCache.get<ParsedEmail>(cacheKey);

        if (cachedEmail) {
          return cachedEmail;
        }

        // Use the parseEmail function from emailParser module
        const parsedEmail = await parseEmail(email.mail, email.mail_id.toString());

        // Update some fields that are specific to the guerrilla email
        const updatedEmail: ParsedEmail = {
          ...parsedEmail,
          date: email.date.toISOString(), // Use the date from the database record
          to: email.to // Use the recipient from the database record
        };

        // Store the parsed email in the cache
        emailCache.set(cacheKey, updatedEmail);

        return updatedEmail;
      })
    );

    // Return the parsed emails
    const response: EmailRetrievalResponse = {
      emails: parsedEmails,
      totalCount,
      page,
      pageSize,
      success: true
    };

    return NextResponse.json(response);
  } catch (error) {
    await logger.error('EMAIL_FETCH', `Error retrieving emails: ${error instanceof Error ? error.message : String(error)}`, { error });

    // Always return a 200 status with empty emails to prevent frontend errors
    // This is more user-friendly than showing an error message
    return NextResponse.json({
      emails: [],
      totalCount: 0,
      page: 1,
      pageSize: API.DEFAULT_PAGE_SIZE,
      success: true, // Return success: true to prevent frontend errors
      message: 'No emails found' // User-friendly message
    }, { status: 200 });
  }
}

// The GET_EMAIL_BY_ID function has been moved to src/app/api/emails/[address]/[id]/route.ts

/**
 * Delete a specific email
 *
 * @route DELETE /api/emails/[address]/[id]
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ address: string }> }
): Promise<NextResponse> {
  try {
    // Get the email address from the params
    const resolvedParams = await params;
    const emailAddress = decodeURIComponent(resolvedParams.address);

    // Get the email ID from the request body
    const { emailId } = await request.json();

    if (!emailId) {
      return NextResponse.json({
        success: false,
        message: 'Email ID is required'
      }, { status: 400 });
    }

    // Check if the email address exists and is valid
    const tempEmail = await getTempEmailByAddress(emailAddress);

    if (!tempEmail) {
      return NextResponse.json({
        success: false,
        message: 'Email address not found'
      }, { status: 404 });
    }

    // Check if the email address has expired
    const now = new Date();
    if (tempEmail.expirationDate < now) {
      return NextResponse.json({
        success: false,
        message: 'Email address has expired'
      }, { status: 410 });
    }

    // Delete the email from the guerrilla database
    const deleted = await deleteEmailFromGuerrilla(emailId, emailAddress);

    if (!deleted) {
      await logger.warning('EMAIL_DELETE', `Email not found or already deleted: ID ${emailId} for ${emailAddress}`);
      return NextResponse.json({
        success: false,
        message: 'Email not found or already deleted'
      }, { status: 404 });
    }

    // Clear the email from the cache if it exists
    const cacheKey = `email_${emailId}`;
    emailCache.del(cacheKey);

    await logger.info('EMAIL_DELETE', `Email deleted successfully: ID ${emailId} for ${emailAddress}`);

    return NextResponse.json({
      success: true,
      message: 'Email deleted successfully'
    });
  } catch (error) {
    await logger.error('EMAIL_DELETE', `Error deleting email: ${error instanceof Error ? error.message : String(error)}`, { error });

    const { message, status } = handleApiError(error);

    return NextResponse.json({
      success: false,
      message: message || 'Failed to delete email'
    }, { status });
  }
}
