import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { jwtVerify } from 'jose';
import { ADMIN } from './lib/constants';
import { createServerSupabaseClient } from './lib/supabase';
import {
  checkRateLimit,
  getRateLimitHeaders
} from './lib/analytics/rateLimiting';
import {
  validateQueryParams,
  timeRangeSchema,
  validateRequestSize
} from './lib/analytics/validation';

// Simple in-memory cache with expiration
interface MaintenanceModeCache {
  value: boolean | null;
  timestamp: number;
}

// Cache TTL in milliseconds (10 seconds)
const CACHE_TTL = 10000;

// Initialize cache
let maintenanceModeCache: MaintenanceModeCache = {
  value: null,
  timestamp: 0
};

// Function to verify JWT token
async function verifyToken(token: string) {
  try {
    // Check if JWT_SECRET is set
    if (!process.env.JWT_SECRET) {
      console.error('Missing required environment variable: JWT_SECRET');
      return null;
    }

    const secret = new TextEncoder().encode(process.env.JWT_SECRET);

    const { payload } = await jwtVerify(token, secret);
    return payload;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// Get the secure admin path from constants
const SECURE_ADMIN_PATH = ADMIN.SECURE_PATH;
// Get the public-facing admin path that's exposed to clients
const PUBLIC_ADMIN_PATH = process.env.NEXT_PUBLIC_ADMIN_UI_PATH || 'admin-portal';

/**
 * Handle analytics endpoints with rate limiting and security
 */
async function handleAnalyticsEndpoint(request: NextRequest): Promise<NextResponse> {
  // Determine endpoint type for rate limiting
  let endpointType: 'analytics' | 'adminAnalytics' | 'sessionAnalytics' | 'heartbeat' = 'analytics';

  if (request.nextUrl.pathname.includes('/heartbeat')) {
    endpointType = 'heartbeat';
  } else if (request.nextUrl.pathname.includes('/sessions')) {
    endpointType = 'sessionAnalytics';
  } else if (request.nextUrl.pathname.includes(`/${SECURE_ADMIN_PATH}/analytics`) ||
             request.nextUrl.pathname.includes(`/${PUBLIC_ADMIN_PATH}/analytics`)) {
    endpointType = 'adminAnalytics';
  }

  // Check rate limit
  const rateLimitResult = checkRateLimit(request, endpointType);

  // Create response with rate limit headers
  const response = NextResponse.next();
  const rateLimitHeaders = getRateLimitHeaders(rateLimitResult);

  // Add rate limit headers
  Object.entries(rateLimitHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Add security headers for analytics endpoints
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // If rate limit exceeded, return 429
  if (!rateLimitResult.allowed) {
    return new NextResponse(
      JSON.stringify({
        success: false,
        error: 'Rate limit exceeded',
        retryAfter: rateLimitResult.retryAfter
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          ...rateLimitHeaders,
          'X-Content-Type-Options': 'nosniff',
          'X-Frame-Options': 'DENY',
          'X-XSS-Protection': '1; mode=block',
          'Referrer-Policy': 'strict-origin-when-cross-origin',
        }
      }
    );
  }

  // Validate request size for POST requests
  if (request.method === 'POST') {
    const contentLength = parseInt(request.headers.get('content-length') || '0');
    const sizeValidation = validateRequestSize(contentLength);

    if (!sizeValidation.valid) {
      return new NextResponse(
        JSON.stringify({
          success: false,
          error: sizeValidation.error
        }),
        {
          status: 413,
          headers: {
            'Content-Type': 'application/json',
            ...rateLimitHeaders,
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
          }
        }
      );
    }
  }

  // Validate query parameters for GET requests
  if (request.method === 'GET' && request.nextUrl.searchParams.size > 0) {
    const params = Object.fromEntries(request.nextUrl.searchParams.entries());
    const validation = validateQueryParams(params, timeRangeSchema);

    if (!validation.success) {
      return new NextResponse(
        JSON.stringify({
          success: false,
          error: `Invalid query parameters: ${validation.error}`
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...rateLimitHeaders,
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
          }
        }
      );
    }
  }

  return response;
}

/**
 * Get maintenance mode status with caching
 * This reduces database queries by caching the result for a short period
 */
async function getMaintenanceMode(): Promise<boolean> {
  const now = Date.now();

  // Return cached value if still valid
  if (maintenanceModeCache.value !== null && maintenanceModeCache.timestamp > now - CACHE_TTL) {
    return maintenanceModeCache.value;
  }

  // Otherwise fetch from database
  try {
    const supabase = createServerSupabaseClient();
    const { data } = await supabase
      .from('app_config')
      .select('value')
      .eq('key', 'maintenanceMode')
      .single();

    const isMaintenanceMode = data?.value === true || data?.value === 'true';

    // Update cache
    maintenanceModeCache = {
      value: isMaintenanceMode,
      timestamp: now
    };

    return isMaintenanceMode;
  } catch (error) {
    // On error, return false and don't update cache
    console.error('Error checking maintenance mode:', error);
    return false;
  }
}

export async function middleware(request: NextRequest) {
  // Skip debug endpoints entirely
  if (request.nextUrl.pathname.startsWith('/api/debug/') ||
      request.nextUrl.pathname.startsWith('/debug/')) {
    return NextResponse.next();
  }

  // Handle analytics endpoints with rate limiting and security
  if (request.nextUrl.pathname.startsWith('/api/analytics')) {
    return handleAnalyticsEndpoint(request);
  }

  // Skip certain API routes for admin protection - SECURITY: Minimize bypasses
  if (
    // Only allow health checks without authentication (read-only status)
    request.nextUrl.pathname.startsWith('/api/admin/health') ||
    request.nextUrl.pathname.startsWith(`/api/${SECURE_ADMIN_PATH}/health`) ||
    request.nextUrl.pathname.startsWith(`/api/${PUBLIC_ADMIN_PATH}/health`)
    // SECURITY: Removed maintenance scheduler and cleanup status bypasses
    // These should require authentication to prevent information disclosure
  ) {
    return NextResponse.next();
  }

  try {
    // Check if the site is in maintenance mode using our cached function
    const isMaintenanceMode = await getMaintenanceMode();

    if (isMaintenanceMode) {
      // Skip maintenance mode check for admin paths and the maintenance page itself
      const isAdminPath =
        request.nextUrl.pathname.startsWith(`/${SECURE_ADMIN_PATH}`) ||
        request.nextUrl.pathname.startsWith(`/${PUBLIC_ADMIN_PATH}`) ||
        request.nextUrl.pathname.startsWith(`/api/${SECURE_ADMIN_PATH}`) ||
        request.nextUrl.pathname.startsWith(`/api/${PUBLIC_ADMIN_PATH}`) ||
        request.nextUrl.pathname.startsWith('/api/admin');

      const isMaintenancePath = request.nextUrl.pathname === '/maintenance';
      const isStaticAsset = request.nextUrl.pathname.startsWith('/_next') ||
                            request.nextUrl.pathname.includes('.') ||
                            request.nextUrl.pathname === '/favicon.ico';

      const isApiRequest = request.nextUrl.pathname.startsWith('/api/');

      // If maintenance mode is enabled and not accessing admin paths, maintenance page, or static assets
      if (!isAdminPath && !isMaintenancePath && !isStaticAsset) {
        // For API requests, return a 503 Service Unavailable instead of redirecting
        if (isApiRequest) {
          return new NextResponse(
            JSON.stringify({
              success: false,
              error: 'Service is currently under maintenance',
              maintenanceMode: true
            }),
            {
              status: 503,
              headers: {
                'Content-Type': 'application/json',
                'Retry-After': '3600',
                'X-Maintenance-Mode': 'true'
              }
            }
          );
        }

        // For regular page requests, redirect to the maintenance page
        return NextResponse.redirect(new URL('/maintenance', request.url));
      }

      // For allowed paths during maintenance, add a header to indicate maintenance mode
      const response = NextResponse.next();
      response.headers.set('X-Maintenance-Mode', 'true');
      return response;
    }
  } catch (error) {
    console.error('Error in middleware maintenance mode check:', error);
    // In case of error, continue with normal middleware processing
  }

  // Handle old admin route - return 404 to hide the existence of admin functionality
  if (request.nextUrl.pathname.startsWith('/admin')) {
    // Return 404 Not Found for the old admin path
    return new NextResponse('Not Found', { status: 404 });
  }

  // Check for authentication token
  const token = request.cookies.get('admin_token')?.value;
  const isAuthenticated = token ? await verifyToken(token) : null;

  // Block direct access to the secure admin path - only allow access through the public path
  // But allow access to the login page or if the user is authenticated
  if (request.nextUrl.pathname.startsWith(`/${SECURE_ADMIN_PATH}`) &&
      !request.nextUrl.pathname.endsWith('/login') &&
      !isAuthenticated &&
      SECURE_ADMIN_PATH !== PUBLIC_ADMIN_PATH) {
    // Return 404 Not Found for direct access to the secure path
    return new NextResponse('Not Found', { status: 404 });
  }

  // Handle secure admin route protection for both secure and public paths
  if ((request.nextUrl.pathname.startsWith(`/${SECURE_ADMIN_PATH}`) ||
       request.nextUrl.pathname.startsWith(`/api/${SECURE_ADMIN_PATH}`) ||
       request.nextUrl.pathname.startsWith(`/${PUBLIC_ADMIN_PATH}`) ||
       request.nextUrl.pathname.startsWith(`/api/${PUBLIC_ADMIN_PATH}`)) &&
      request.nextUrl.pathname !== `/${SECURE_ADMIN_PATH}/login` &&
      request.nextUrl.pathname !== `/${PUBLIC_ADMIN_PATH}/login`) {

    // We already verified the token above, so we can reuse the result
    if (!isAuthenticated) {
      // Determine which path the user is trying to access
      const isSecurePath = request.nextUrl.pathname.startsWith(`/${SECURE_ADMIN_PATH}`) ||
                          request.nextUrl.pathname.startsWith(`/api/${SECURE_ADMIN_PATH}`);

      // Redirect to the login page using the same path format
      const loginPath = isSecurePath ? `/${SECURE_ADMIN_PATH}/login` : `/${PUBLIC_ADMIN_PATH}/login`;

      return NextResponse.redirect(new URL(loginPath, request.url));
    }

    // Token is valid, proceed
    return NextResponse.next();
  }

  // Check if we're in maintenance mode before doing analytics
  const isMaintenanceMode = await getMaintenanceMode();

  // Skip analytics tracking if in maintenance mode or for certain paths
  if (
    isMaintenanceMode ||
    request.nextUrl.pathname.startsWith('/api') ||
    request.nextUrl.pathname.startsWith('/_next') ||
    request.nextUrl.pathname.includes('.') ||
    // Skip admin routes to avoid tracking admin usage - both secure and public paths
    request.nextUrl.pathname.startsWith('/admin') ||
    request.nextUrl.pathname.startsWith(`/${SECURE_ADMIN_PATH}`) ||
    request.nextUrl.pathname.startsWith(`/${PUBLIC_ADMIN_PATH}`) ||
    request.nextUrl.pathname.startsWith(`/api/${SECURE_ADMIN_PATH}`) ||
    request.nextUrl.pathname.startsWith(`/api/${PUBLIC_ADMIN_PATH}`) ||
    // Skip home page - we'll track it manually on initial load only
    request.nextUrl.pathname === '/' ||
    // Skip maintenance page
    request.nextUrl.pathname === '/maintenance'
  ) {
    return NextResponse.next();
  }

  // For middleware, we'll continue to use the API route for analytics
  // This is because we can't use server actions directly in middleware
  // and we want to avoid blocking the response
  const pageViewData = {
    eventType: 'page_view',
    pagePath: request.nextUrl.pathname || '/',
  };

  // Send to your analytics API
  // We use a dynamic origin to ensure it works in all environments
  const origin = request.nextUrl.origin;
  fetch(`${origin}/api/analytics`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': request.headers.get('user-agent') || '',
      'Referer': request.headers.get('referer') || '',
      'x-forwarded-for': request.headers.get('x-forwarded-for') || '',
    },
    body: JSON.stringify(pageViewData),
  }).catch(err => {
    // Only log errors in development to reduce noise
    if (process.env.NODE_ENV === 'development') {
      console.error('Analytics error:', err);
    }
  });

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Apply to all paths
    '/((?!_next/static|_next/image|favicon.ico).*)',
    // Make sure to include admin paths explicitly for auth protection
    '/admin/:path*',
    // Include admin API routes
    '/api/admin/:path*',
    // We use a wildcard pattern for API routes that start with /api/ followed by any segment
    // This ensures protection for any admin path defined in environment variables
    '/api/:path*/:path*',
    // We use a regex-based matcher for the admin UI routes
    // This ensures protection regardless of what SECURE_ADMIN_PATH is set to
    '/:path*/:path*',
    // Include maintenance page
    '/maintenance',
  ],
};
