/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: false, // Disable dark mode
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        mono: ['SF Mono', 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
      },
      fontSize: {
        '2xs': '0.625rem', // 10px
      },
      letterSpacing: {
        'wider': '0.05em',
        'widest': '0.1em',
      },
      animation: {
        fadeIn: 'fadeIn 0.3s ease-in-out',
        slideIn: 'slideIn 0.3s ease-in-out',
        pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'loading-bar': 'loadingBar 2s ease-in-out infinite',
        'loading-dot-1': 'loadingDot 1.4s ease-in-out infinite',
        'loading-dot-2': 'loadingDot 1.4s ease-in-out 0.2s infinite',
        'loading-dot-3': 'loadingDot 1.4s ease-in-out 0.4s infinite',
        'fade-out': 'fadeOut 0.5s ease-in-out forwards',
        'fade-in-scale': 'fadeInScale 0.6s ease-out forwards',
        'slide-in-right': 'slideInRight 0.5s ease-out forwards',
        'slide-in-left': 'slideInLeft 0.5s ease-out forwards',
        'slideInBottom': 'slideInBottom 0.2s ease-out forwards',
        'spin-slow': 'spin 3s linear infinite',
        'click': 'click 0.3s ease-in-out',
        'fade-in': 'fadeIn 0.3s ease-in-out forwards',
        'float': 'float 6s ease-in-out infinite',
        'subtle-bounce': 'subtleBounce 2s ease-in-out infinite',
        'scale-click': 'scaleClick 0.3s ease-in-out',
        'pulse-soft': 'pulseSoft 2s ease-in-out infinite',
        'shimmer': 'shimmer 2.5s ease-in-out infinite',
        'grow-shadow': 'growShadow 0.3s ease-out forwards',
        'envelope-bounce': 'envelopeBounce 2s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite',
        'email-select': 'emailSelect 0.25s cubic-bezier(0.2, 0, 0.2, 1) forwards',
        'email-deselect': 'emailDeselect 0.25s cubic-bezier(0.2, 0, 0.2, 1) forwards',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        pulse: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '.5' },
        },
        loadingBar: {
          '0%': { width: '0%', left: '0%' },
          '50%': { width: '30%', left: '30%' },
          '100%': { width: '0%', left: '100%' },
        },
        loadingDot: {
          '0%, 100%': { transform: 'scale(0.5)', opacity: '0.5' },
          '50%': { transform: 'scale(1)', opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        fadeInScale: {
          '0%': { opacity: '0', transform: 'scale(0.95)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(-20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideInBottom: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        click: {
          '0%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(0.95)' },
          '100%': { transform: 'scale(1)' },
        },
        float: {
          '0%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
          '100%': { transform: 'translateY(0)' },
        },
        subtleBounce: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-3px)' },
        },
        scaleClick: {
          '0%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(0.95)' },
          '100%': { transform: 'scale(1)' },
        },
        pulseSoft: {
          '0%, 100%': { opacity: '1', transform: 'scale(1)' },
          '50%': { opacity: '0.85', transform: 'scale(1.03)' },
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        growShadow: {
          '0%': { boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)' },
          '100%': { boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.05)' },
        },
        envelopeBounce: {
          '0%': { transform: 'translateY(0) rotate(0deg)' },
          '25%': { transform: 'translateY(-15px) rotate(-5deg)' },
          '50%': { transform: 'translateY(0) rotate(0deg)' },
          '75%': { transform: 'translateY(-10px) rotate(5deg)' },
          '100%': { transform: 'translateY(0) rotate(0deg)' },
        },
        emailSelect: {
          '0%': {
            transform: 'translateX(0)',
            opacity: '0.95'
          },
          '30%': {
            transform: 'translateX(3px)'
          },
          '100%': {
            transform: 'translateX(0)',
            opacity: '1'
          }
        },
        emailDeselect: {
          '0%': {
            opacity: '0.95'
          },
          '100%': {
            opacity: '1'
          }
        },

      },
      colors: {
        // Modern UI color palette based on new-fademail-color-palette.html
        background: '#fafafa',
        surface: '#ffffff',
        primary: {
          50: '#f8f8f8',
          100: '#e8e8e8',
          200: '#d0d0d0',
          300: '#b0b0b0',
          400: '#888888',
          500: '#605f5f', // Primary accent color
          600: '#505050', // Button hover state
          700: '#404040',
          800: '#303030',
          900: '#1c1b1a',
          950: '#0c0b0a',
        },
        neutral: {
          50: '#f8f7f7',
          100: '#e8e7e6', // Selected email background
          200: '#d5d3d2',
          300: '#b8b6b4',
          400: '#9c9a98',
          500: '#7d7b79', // Email preview text
          600: '#605f5d', // Tertiary text
          700: '#4a4846', // Secondary text / Email subject
          800: '#1c1b1a', // Primary text / Logo color
          900: '#0c0b0a',
          950: '#060605',
        },
        success: {
          50: '#eafaf4',
          100: '#d5f5e9',
          500: '#24ad80', // Updated to match the countdown timer color
          600: '#1c9068',
        },
        error: {
          50: '#feecec',
          100: '#fddada',
          500: '#f15757',
          600: '#e13a3a',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          500: '#f59e0b',
          600: '#d97706',
        },
        info: {
          50: '#eff6ff',
          100: '#dbeafe',
          500: '#3b82f6',
          600: '#2563eb',
        },
        svg: {
          background: '#f5dfd2',
        },
        // Keep the brand colors for backward compatibility but they should not be used for new components
        brand: {
          50: '#f5f7ff',
          100: '#ebf0fe',
          200: '#d6e0fd',
          300: '#b3c5fb',
          400: '#8aa2f8',
          500: '#6b82f5',
          600: '#4c5feb',
          700: '#3a4cd6',
          800: '#3040ae',
          900: '#2c3a8c',
          950: '#1e2456',
        },
      },
      boxShadow: {
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.03)',
        'DEFAULT': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.05)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.04), 0 4px 6px -4px rgba(0, 0, 0, 0.02)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.03), 0 8px 10px -6px rgba(0, 0, 0, 0.01)',
        'card': '0 1px 3px 0 rgba(0, 0, 0, 0.04), 0 1px 2px -1px rgba(0, 0, 0, 0.02)',
        'card-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03)',
        'dropdown': '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03)',
        'button': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      },
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
        '4xl': '2rem',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms')({
      strategy: 'class',
    }),
  ],
}
