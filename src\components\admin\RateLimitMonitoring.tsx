'use client';

/**
 * Rate Limit Monitoring Dashboard
 * 
 * Real-time monitoring of rate limits, violations, and IP activity
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { Separator } from '@/components/ui/Separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/Tooltip';
import { Progress } from '@/components/ui/Progress';
import {
  Card as ShadcnCard,
  CardContent as ShadcnCardContent,
  CardHeader as ShadcnCardHeader,
  CardTitle as ShadcnCardTitle,
} from '@/components/ui/ShadcnCard';
import { 
  AlertTriangle, 
  Activity, 
  Shield, 
  TrendingUp,
  Clock,
  Globe,
  BarChart3
} from 'lucide-react';

interface RateLimitStats {
  totalRequests: number;
  blockedRequests: number;
  violationsByEndpoint: Record<string, number>;
  topViolatingIPs: Array<{
    ip: string;
    violations: number;
    lastViolation: string;
  }>;
  recentActivity: Array<{
    timestamp: string;
    ip: string;
    endpoint: string;
    action: 'blocked' | 'rate_limited' | 'violation';
    details?: string;
  }>;
}

export default function RateLimitMonitoring() {
  const [stats, setStats] = useState<RateLimitStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    fetchStats();
    
    if (autoRefresh) {
      const interval = setInterval(fetchStats, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/rate-limit-stats');
      const result = await response.json();
      
      if (result.success) {
        setStats(result.data);
      }
    } catch (error) {
      console.error('Error fetching rate limit stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'blocked': return 'destructive';
      case 'rate_limited': return 'secondary';
      case 'violation': return 'outline';
      default: return 'default';
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'blocked': return <Shield className="w-4 h-4" />;
      case 'rate_limited': return <Clock className="w-4 h-4" />;
      case 'violation': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  if (loading) {
    return <div className="p-6">Loading rate limit monitoring data...</div>;
  }

  if (!stats) {
    return <div className="p-6">Failed to load monitoring data</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Rate Limit Monitoring</h1>
        <div className="flex gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={autoRefresh ? "default" : "outline"}
                onClick={() => setAutoRefresh(!autoRefresh)}
                size="sm"
              >
                <Activity className="w-4 h-4 mr-2" />
                Auto Refresh {autoRefresh ? 'On' : 'Off'}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Toggle automatic refresh every 30 seconds</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button onClick={fetchStats} variant="outline" size="sm">
                Refresh Now
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Manually refresh monitoring data</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Tooltip>
          <TooltipTrigger asChild>
            <ShadcnCard className="hover:shadow-md transition-all duration-200 cursor-help">
              <ShadcnCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <ShadcnCardTitle className="text-sm font-medium">Total Requests</ShadcnCardTitle>
                <BarChart3 className="h-4 w-4 text-blue-600" />
              </ShadcnCardHeader>
              <ShadcnCardContent>
                <div className="text-2xl font-bold text-blue-700">{stats.totalRequests.toLocaleString()}</div>
                <p className="text-xs text-gray-500">Last 24 hours</p>
              </ShadcnCardContent>
            </ShadcnCard>
          </TooltipTrigger>
          <TooltipContent>
            <p>Total number of API requests processed in the last 24 hours</p>
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <ShadcnCard className="hover:shadow-md transition-all duration-200 cursor-help">
              <ShadcnCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <ShadcnCardTitle className="text-sm font-medium">Blocked Requests</ShadcnCardTitle>
                <Shield className="h-4 w-4 text-red-600" />
              </ShadcnCardHeader>
              <ShadcnCardContent>
                <div className="text-2xl font-bold text-red-600">{stats.blockedRequests.toLocaleString()}</div>
                <p className="text-xs text-gray-500">
                  {stats.totalRequests > 0
                    ? `${((stats.blockedRequests / stats.totalRequests) * 100).toFixed(1)}% of total`
                    : '0% of total'
                  }
                </p>
              </ShadcnCardContent>
            </ShadcnCard>
          </TooltipTrigger>
          <TooltipContent>
            <p>Requests blocked due to rate limiting violations</p>
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <ShadcnCard className="hover:shadow-md transition-all duration-200 cursor-help">
              <ShadcnCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <ShadcnCardTitle className="text-sm font-medium">Active Violations</ShadcnCardTitle>
                <AlertTriangle className="h-4 w-4 text-orange-600" />
              </ShadcnCardHeader>
              <ShadcnCardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {Object.values(stats.violationsByEndpoint).reduce((a, b) => a + b, 0)}
                </div>
                <p className="text-xs text-gray-500">Across all endpoints</p>
              </ShadcnCardContent>
            </ShadcnCard>
          </TooltipTrigger>
          <TooltipContent>
            <p>Current rate limit violations across all API endpoints</p>
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <ShadcnCard className="hover:shadow-md transition-all duration-200 cursor-help">
              <ShadcnCardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <ShadcnCardTitle className="text-sm font-medium">Top Violating IPs</ShadcnCardTitle>
                <Globe className="h-4 w-4 text-purple-600" />
              </ShadcnCardHeader>
              <ShadcnCardContent>
                <div className="text-2xl font-bold text-purple-700">{stats.topViolatingIPs.length}</div>
                <p className="text-xs text-gray-500">Unique IP addresses</p>
              </ShadcnCardContent>
            </ShadcnCard>
          </TooltipTrigger>
          <TooltipContent>
            <p>Number of unique IP addresses with rate limit violations</p>
          </TooltipContent>
        </Tooltip>
      </div>

      {/* Violations by Endpoint */}
      <ShadcnCard className="shadow-sm">
        <ShadcnCardHeader>
          <ShadcnCardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            Violations by Endpoint
            {Object.keys(stats.violationsByEndpoint).length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {Object.keys(stats.violationsByEndpoint).length} endpoints
              </Badge>
            )}
          </ShadcnCardTitle>
        </ShadcnCardHeader>

        <Separator />

        <ShadcnCardContent>
          <div className="space-y-4">
            {Object.entries(stats.violationsByEndpoint).map(([endpoint, count]) => (
              <div key={endpoint} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{endpoint}</Badge>
                  </div>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="text-sm font-medium cursor-help">{count} violations</span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Total violations for {endpoint} endpoint</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <Progress
                  value={Math.min(100, (count / Math.max(...Object.values(stats.violationsByEndpoint))) * 100)}
                  className="h-2"
                />
              </div>
            ))}
            {Object.keys(stats.violationsByEndpoint).length === 0 && (
              <div className="text-center py-8">
                <AlertTriangle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No violations recorded</p>
                <p className="text-sm text-gray-400 mt-1">All endpoints are operating within limits</p>
              </div>
            )}
          </div>
        </ShadcnCardContent>
      </ShadcnCard>

      {/* Top Violating IPs */}
      <ShadcnCard className="shadow-sm">
        <ShadcnCardHeader>
          <ShadcnCardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5 text-red-600" />
            Top Violating IP Addresses
            {stats.topViolatingIPs.length > 0 && (
              <Badge variant="destructive" className="ml-2">
                {stats.topViolatingIPs.length} IPs
              </Badge>
            )}
          </ShadcnCardTitle>
        </ShadcnCardHeader>

        <Separator />

        <ShadcnCardContent className="p-0">
          <ScrollArea className="h-80 w-full">
            <div className="space-y-3 p-4">
              {stats.topViolatingIPs.map((ip, index) => (
                <ShadcnCard key={ip.ip} className="py-3 hover:shadow-md transition-all duration-200">
                  <ShadcnCardContent className="px-4 py-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="text-xs">
                          #{index + 1}
                        </Badge>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <code className="bg-red-100 px-2 py-1 rounded text-sm cursor-help">
                              {ip.ip}
                            </code>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>IP Address: {ip.ip}</p>
                          </TooltipContent>
                        </Tooltip>

                        <Badge variant="destructive">
                          {ip.violations} violations
                        </Badge>
                      </div>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="text-xs text-gray-500 cursor-help">
                            Last: {formatTime(ip.lastViolation)}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Time of most recent violation</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </ShadcnCardContent>
                </ShadcnCard>
              ))}
              {stats.topViolatingIPs.length === 0 && (
                <div className="text-center py-8">
                  <Globe className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No violating IPs</p>
                  <p className="text-sm text-gray-400 mt-1">All traffic is within rate limits</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </ShadcnCardContent>
      </ShadcnCard>

      {/* Recent Activity */}
      <ShadcnCard className="shadow-sm">
        <ShadcnCardHeader>
          <ShadcnCardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-green-600" />
            Recent Activity
            {stats.recentActivity.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {stats.recentActivity.length} events
              </Badge>
            )}
          </ShadcnCardTitle>
        </ShadcnCardHeader>

        <Separator />

        <ShadcnCardContent className="p-0">
          <ScrollArea className="h-96 w-full">
            <div className="space-y-3 p-4">
              {stats.recentActivity.map((activity, index) => (
                <ShadcnCard key={index} className="py-3 hover:shadow-md transition-all duration-200">
                  <ShadcnCardContent className="px-4 py-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {getActionIcon(activity.action)}
                          <Badge variant={getActionColor(activity.action) as any}>
                            {activity.action.replace('_', ' ')}
                          </Badge>
                        </div>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <code className="bg-gray-100 px-2 py-1 rounded text-xs cursor-help">
                              {activity.ip}
                            </code>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Source IP: {activity.ip}</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="text-sm text-gray-600 cursor-help">
                              {activity.endpoint}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>API Endpoint: {activity.endpoint}</p>
                          </TooltipContent>
                        </Tooltip>

                        {activity.details && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="text-xs text-gray-500 cursor-help">
                                - {activity.details}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Additional details: {activity.details}</p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </div>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="text-xs text-gray-500 cursor-help flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {formatTime(activity.timestamp)}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Event timestamp: {activity.timestamp}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </ShadcnCardContent>
                </ShadcnCard>
              ))}
              {stats.recentActivity.length === 0 && (
                <div className="text-center py-8">
                  <Activity className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No recent activity</p>
                  <p className="text-sm text-gray-400 mt-1">Rate limit events will appear here</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </ShadcnCardContent>
      </ShadcnCard>
    </div>
  );
}
