'use client';

/**
 * Rate Limit Monitoring Dashboard
 * 
 * Real-time monitoring of rate limits, violations, and IP activity
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { 
  AlertTriangle, 
  Activity, 
  Shield, 
  TrendingUp,
  Clock,
  Globe,
  BarChart3
} from 'lucide-react';

interface RateLimitStats {
  totalRequests: number;
  blockedRequests: number;
  violationsByEndpoint: Record<string, number>;
  topViolatingIPs: Array<{
    ip: string;
    violations: number;
    lastViolation: string;
  }>;
  recentActivity: Array<{
    timestamp: string;
    ip: string;
    endpoint: string;
    action: 'blocked' | 'rate_limited' | 'violation';
    details?: string;
  }>;
}

export default function RateLimitMonitoring() {
  const [stats, setStats] = useState<RateLimitStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    fetchStats();
    
    if (autoRefresh) {
      const interval = setInterval(fetchStats, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/rate-limit-stats');
      const result = await response.json();
      
      if (result.success) {
        setStats(result.data);
      }
    } catch (error) {
      console.error('Error fetching rate limit stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'blocked': return 'destructive';
      case 'rate_limited': return 'secondary';
      case 'violation': return 'outline';
      default: return 'default';
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'blocked': return <Shield className="w-4 h-4" />;
      case 'rate_limited': return <Clock className="w-4 h-4" />;
      case 'violation': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  if (loading) {
    return <div className="p-6">Loading rate limit monitoring data...</div>;
  }

  if (!stats) {
    return <div className="p-6">Failed to load monitoring data</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Rate Limit Monitoring</h1>
        <div className="flex gap-2">
          <Button
            variant={autoRefresh ? "default" : "outline"}
            onClick={() => setAutoRefresh(!autoRefresh)}
            size="sm"
          >
            <Activity className="w-4 h-4 mr-2" />
            Auto Refresh {autoRefresh ? 'On' : 'Off'}
          </Button>
          <Button onClick={fetchStats} variant="outline" size="sm">
            Refresh Now
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalRequests.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Last 24 hours</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Blocked Requests</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.blockedRequests.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalRequests > 0 
                ? `${((stats.blockedRequests / stats.totalRequests) * 100).toFixed(1)}% of total`
                : '0% of total'
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Violations</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {Object.values(stats.violationsByEndpoint).reduce((a, b) => a + b, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Across all endpoints</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Violating IPs</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.topViolatingIPs.length}</div>
            <p className="text-xs text-muted-foreground">Unique IP addresses</p>
          </CardContent>
        </Card>
      </div>

      {/* Violations by Endpoint */}
      <Card>
        <CardHeader>
          <CardTitle>Violations by Endpoint</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(stats.violationsByEndpoint).map(([endpoint, count]) => (
              <div key={endpoint} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{endpoint}</Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">{count} violations</span>
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-red-500 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.min(100, (count / Math.max(...Object.values(stats.violationsByEndpoint))) * 100)}%` 
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
            {Object.keys(stats.violationsByEndpoint).length === 0 && (
              <p className="text-gray-500 text-center py-4">No violations recorded</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Top Violating IPs */}
      <Card>
        <CardHeader>
          <CardTitle>Top Violating IP Addresses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats.topViolatingIPs.map((ip, index) => (
              <div key={ip.ip} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                  <code className="bg-red-100 px-2 py-1 rounded text-sm">{ip.ip}</code>
                  <Badge variant="destructive">{ip.violations} violations</Badge>
                </div>
                <div className="text-xs text-gray-500">
                  Last: {formatTime(ip.lastViolation)}
                </div>
              </div>
            ))}
            {stats.topViolatingIPs.length === 0 && (
              <p className="text-gray-500 text-center py-4">No violating IPs</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {stats.recentActivity.map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-2 border-l-4 border-l-gray-200 pl-4">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    {getActionIcon(activity.action)}
                    <Badge variant={getActionColor(activity.action) as any}>
                      {activity.action.replace('_', ' ')}
                    </Badge>
                  </div>
                  <code className="bg-gray-100 px-2 py-1 rounded text-xs">{activity.ip}</code>
                  <span className="text-sm text-gray-600">{activity.endpoint}</span>
                  {activity.details && (
                    <span className="text-xs text-gray-500">- {activity.details}</span>
                  )}
                </div>
                <span className="text-xs text-gray-500">
                  {formatTime(activity.timestamp)}
                </span>
              </div>
            ))}
            {stats.recentActivity.length === 0 && (
              <p className="text-gray-500 text-center py-4">No recent activity</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
