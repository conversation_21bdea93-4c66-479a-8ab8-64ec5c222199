# Responsive Email Rendering Implementation

This document explains the implementation of the responsive iframe email renderer in the Fademail application.

## Overview

The `ResponsiveIframeEmailRenderer` component provides a solution for rendering HTML emails in an iframe while ensuring they display properly on mobile devices. It uses a combination of techniques to achieve this:

1. **Dynamic height adjustment** using the `@iframe-resizer/react` library
2. **Proportional scaling** for fixed-width emails on mobile devices
3. **Server-side rendering compatibility** with Next.js

## Key Features

- **Preserves original email styling** while making it mobile-friendly
- **No modifications to the original HTML content** of the email
- **Gmail-like scaling approach** for fixed-width emails
- **Responsive on all devices** without horizontal scrolling
- **Handles complex email layouts** with nested tables and fixed widths
- **Smooth transitions** when scaling content

## Implementation Details

### 1. Iframe Resizer Integration

The component uses `@iframe-resizer/react` to dynamically adjust the iframe height based on its content:

```tsx
<IframeResizer
  srcDoc={htmlContent}
  style={{ width: '100%', border: 'none', overflow: 'hidden' }}
  checkOrigin={false}
  scrolling={false}
  inPageLinks
  resizeFrom="child"
  heightCalculationMethod="lowestElement"
  sizeWidth
/>
```

### 2. Mobile Scaling Approach

For fixed-width emails, the component applies CSS transforms to scale down the content proportionally:

```javascript
function handleEmailScaling() {
  var wrapper = document.querySelector('.email-wrapper');
  var content = wrapper.firstElementChild;
  var viewportWidth = window.innerWidth;
  
  // Find all fixed-width elements
  var fixedWidthElements = document.querySelectorAll('table[width], table[style*="width"], div[style*="width"]');
  var maxContentWidth = 0;
  
  // Find the maximum content width
  fixedWidthElements.forEach(function(el) {
    var elWidth = el.getAttribute('width') || 
                 (el.style.width ? parseInt(el.style.width, 10) : 0) || 
                 el.offsetWidth;
    
    // Convert to number
    elWidth = parseInt(elWidth, 10);
    
    if (elWidth > maxContentWidth) {
      maxContentWidth = elWidth;
    }
  });
  
  // If no fixed width elements found, use the content width
  if (maxContentWidth === 0) {
    maxContentWidth = content.offsetWidth;
  }
  
  // Only scale if content is wider than viewport and we're on mobile
  if (maxContentWidth > viewportWidth && viewportWidth < 600) {
    var scale = viewportWidth / maxContentWidth;
    
    // Apply minimum scale to prevent too small content (65% minimum)
    scale = Math.max(scale, 0.65);
    
    // Apply the scale transform
    content.classList.add('email-mobile-scale');
    content.style.transform = 'scale(' + scale + ')';
    content.style.width = (maxContentWidth) + 'px';
    content.style.transformOrigin = 'top left';
    
    // Set wrapper height to match scaled content
    wrapper.style.height = (content.offsetHeight * scale) + 'px';
    
    // Notify parent about height change
    if (window.parentIFrame) {
      window.parentIFrame.size();
    }
  }
}
```

### 3. Server-Side Rendering Compatibility

The component uses Next.js's `dynamic` import to handle server-side rendering:

```tsx
import dynamic from 'next/dynamic';

// Dynamically import the IframeResizer component with no SSR
const IframeResizer = dynamic(
  () => import('@iframe-resizer/react').then(mod => mod.IframeResizer),
  { ssr: false }
);

// Create a fallback component for SSR
function FallbackIframe({ className = '' }: { className?: string }) {
  return (
    <div className={`iframe-fallback ${className}`} style={{ width: '100%', minHeight: '200px' }}>
      <div className="animate-pulse flex flex-col space-y-4 p-4">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
    </div>
  );
}
```

## Usage

To use the responsive iframe email renderer in your components:

```tsx
import ResponsiveIframeEmailRenderer from '@/components/ResponsiveIframeEmailRenderer';

function EmailViewer({ email }) {
  return (
    <div className="email-container">
      <ResponsiveIframeEmailRenderer html={email.html} />
    </div>
  );
}
```

## Testing

A test page is available at `/test-responsive-iframe` that demonstrates the responsive behavior with different email templates:

1. **Fixed Width Table Email** - A simple email with a fixed width of 600px
2. **Nested Tables Email** - An email with nested tables and columns
3. **Complex Layout Email** - A complex email with multiple sections and formatting

To test the responsive behavior:
1. Open the test page in a browser
2. Resize the browser window to simulate different device sizes
3. Observe how the emails scale down proportionally on smaller screens

## Browser Compatibility

The implementation has been tested and works well in:
- Chrome
- Firefox
- Safari
- Edge

## Mobile Device Testing

For optimal results, test the implementation on actual mobile devices or using browser developer tools to simulate various device sizes.

## Performance Considerations

- Content hashing is used to prevent unnecessary re-renders
- Dynamic imports reduce the initial bundle size
- Memoization optimizes the HTML content generation

## Accessibility

The implementation maintains accessibility by:
- Preserving text scaling
- Ensuring links remain clickable
- Maintaining proper contrast ratios

## Future Improvements

Potential future improvements to consider:
- Add support for dark mode in emails
- Implement print-specific styling
- Add options for customizing the scaling behavior
- Optimize performance for very large emails
