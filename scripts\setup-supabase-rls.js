/**
 * <PERSON><PERSON><PERSON> to set up Row-Level Security (RLS) policies for Supabase tables
 * 
 * This script enables RLS on the tables and creates policies to control access
 */
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://pfxzwsjgnpycqopkfyuh.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY is required to set up RLS policies');
  console.error('Please add it to your .env.local file');
  process.exit(1);
}

// Create a Supabase client with the service role key
// This is required to create RLS policies
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Execute a SQL query using the Supabase client
 */
async function executeSQL(query) {
  try {
    const { data, error } = await supabase.rpc('execute_sql', { query });
    
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    // If the execute_sql function is not available, try using the REST API
    console.warn('Warning: execute_sql function not available, using REST API instead');
    console.warn('This may not work for all SQL statements');
    
    // For simple queries, we can use the REST API
    // This is a workaround and may not work for all SQL statements
    if (query.toLowerCase().includes('select')) {
      const { data, error } = await supabase.from('temp_emails').select('*').limit(1);
      if (error) {
        throw error;
      }
      return data;
    } else {
      throw new Error('Cannot execute SQL statement: execute_sql function not available');
    }
  }
}

/**
 * Set up RLS policies for the temp_emails table
 */
async function setupTempEmailsRLS() {
  try {
    console.log('Setting up RLS policies for temp_emails table...');
    
    // Enable RLS on the temp_emails table
    await supabase.rpc('execute_sql', {
      query: `
        -- Enable RLS on the temp_emails table
        ALTER TABLE temp_emails ENABLE ROW LEVEL SECURITY;
      `
    }).catch(error => {
      console.warn('Warning: Could not enable RLS on temp_emails table using execute_sql');
      console.warn('Error:', error.message);
      console.warn('Trying alternative method...');
      
      // Try using the REST API to check if the table exists
      return supabase.from('temp_emails').select('*').limit(1);
    });
    
    // Create policies for the temp_emails table
    // These policies control who can read, insert, update, and delete rows
    
    // Create a policy to allow anyone to read temp_emails
    // This is needed for the public API endpoints
    await supabase.rpc('execute_sql', {
      query: `
        -- Allow anyone to read temp_emails
        CREATE POLICY "Allow anyone to read temp_emails"
        ON temp_emails
        FOR SELECT
        USING (true);
      `
    }).catch(error => {
      console.warn('Warning: Could not create read policy for temp_emails table');
      console.warn('Error:', error.message);
    });
    
    // Create a policy to allow authenticated users to insert into temp_emails
    await supabase.rpc('execute_sql', {
      query: `
        -- Allow authenticated users to insert into temp_emails
        CREATE POLICY "Allow authenticated users to insert into temp_emails"
        ON temp_emails
        FOR INSERT
        WITH CHECK (auth.role() = 'authenticated' OR auth.role() = 'service_role');
      `
    }).catch(error => {
      console.warn('Warning: Could not create insert policy for temp_emails table');
      console.warn('Error:', error.message);
    });
    
    // Create a policy to allow authenticated users to update temp_emails
    await supabase.rpc('execute_sql', {
      query: `
        -- Allow authenticated users to update temp_emails
        CREATE POLICY "Allow authenticated users to update temp_emails"
        ON temp_emails
        FOR UPDATE
        USING (auth.role() = 'authenticated' OR auth.role() = 'service_role')
        WITH CHECK (auth.role() = 'authenticated' OR auth.role() = 'service_role');
      `
    }).catch(error => {
      console.warn('Warning: Could not create update policy for temp_emails table');
      console.warn('Error:', error.message);
    });
    
    // Create a policy to allow authenticated users to delete from temp_emails
    await supabase.rpc('execute_sql', {
      query: `
        -- Allow authenticated users to delete from temp_emails
        CREATE POLICY "Allow authenticated users to delete from temp_emails"
        ON temp_emails
        FOR DELETE
        USING (auth.role() = 'authenticated' OR auth.role() = 'service_role');
      `
    }).catch(error => {
      console.warn('Warning: Could not create delete policy for temp_emails table');
      console.warn('Error:', error.message);
    });
    
    console.log('RLS policies for temp_emails table set up successfully');
  } catch (error) {
    console.error('Error setting up RLS policies for temp_emails table:', error);
  }
}

/**
 * Set up RLS policies for the analytics_events table
 */
async function setupAnalyticsEventsRLS() {
  try {
    console.log('Setting up RLS policies for analytics_events table...');
    
    // Enable RLS on the analytics_events table
    await supabase.rpc('execute_sql', {
      query: `
        -- Enable RLS on the analytics_events table
        ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
      `
    }).catch(error => {
      console.warn('Warning: Could not enable RLS on analytics_events table using execute_sql');
      console.warn('Error:', error.message);
      console.warn('Trying alternative method...');
      
      // Try using the REST API to check if the table exists
      return supabase.from('analytics_events').select('*').limit(1);
    });
    
    // Create policies for the analytics_events table
    
    // Create a policy to allow anyone to insert into analytics_events
    // This is needed for the public API endpoints that record analytics events
    await supabase.rpc('execute_sql', {
      query: `
        -- Allow anyone to insert into analytics_events
        CREATE POLICY "Allow anyone to insert into analytics_events"
        ON analytics_events
        FOR INSERT
        WITH CHECK (true);
      `
    }).catch(error => {
      console.warn('Warning: Could not create insert policy for analytics_events table');
      console.warn('Error:', error.message);
    });
    
    // Create a policy to allow only authenticated users to read analytics_events
    await supabase.rpc('execute_sql', {
      query: `
        -- Allow only authenticated users to read analytics_events
        CREATE POLICY "Allow only authenticated users to read analytics_events"
        ON analytics_events
        FOR SELECT
        USING (auth.role() = 'authenticated' OR auth.role() = 'service_role');
      `
    }).catch(error => {
      console.warn('Warning: Could not create read policy for analytics_events table');
      console.warn('Error:', error.message);
    });
    
    // Create a policy to allow only authenticated users to update analytics_events
    await supabase.rpc('execute_sql', {
      query: `
        -- Allow only authenticated users to update analytics_events
        CREATE POLICY "Allow only authenticated users to update analytics_events"
        ON analytics_events
        FOR UPDATE
        USING (auth.role() = 'authenticated' OR auth.role() = 'service_role')
        WITH CHECK (auth.role() = 'authenticated' OR auth.role() = 'service_role');
      `
    }).catch(error => {
      console.warn('Warning: Could not create update policy for analytics_events table');
      console.warn('Error:', error.message);
    });
    
    // Create a policy to allow only authenticated users to delete from analytics_events
    await supabase.rpc('execute_sql', {
      query: `
        -- Allow only authenticated users to delete from analytics_events
        CREATE POLICY "Allow only authenticated users to delete from analytics_events"
        ON analytics_events
        FOR DELETE
        USING (auth.role() = 'authenticated' OR auth.role() = 'service_role');
      `
    }).catch(error => {
      console.warn('Warning: Could not create delete policy for analytics_events table');
      console.warn('Error:', error.message);
    });
    
    console.log('RLS policies for analytics_events table set up successfully');
  } catch (error) {
    console.error('Error setting up RLS policies for analytics_events table:', error);
  }
}

/**
 * Main function to set up RLS policies
 */
async function setupRLSPolicies() {
  try {
    console.log('Setting up RLS policies for Supabase tables...');
    
    // Set up RLS policies for each table
    await setupTempEmailsRLS();
    await setupAnalyticsEventsRLS();
    
    console.log('RLS policies set up successfully');
  } catch (error) {
    console.error('Error setting up RLS policies:', error);
    process.exit(1);
  }
}

// Run the main function
setupRLSPolicies();
