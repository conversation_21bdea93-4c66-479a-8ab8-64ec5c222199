import { ADMIN } from '../constants';

// Simple in-memory rate limiting
// In production, you would use a more robust solution like Redis
interface RateLimitEntry {
  count: number;
  resetAt: number;
}

const rateLimitMap = new Map<string, RateLimitEntry>();

/**
 * Check if a request is rate limited
 * @param ip The IP address to check
 * @returns true if the request is allowed, false if it's rate limited
 */
export function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const windowMs = ADMIN.RATE_LIMIT_WINDOW_MINUTES * 60 * 1000;
  const maxAttempts = ADMIN.MAX_LOGIN_ATTEMPTS;
  
  // Clean up expired entries
  for (const [key, entry] of rateLimitMap.entries()) {
    if (entry.resetAt < now) {
      rateLimitMap.delete(key);
    }
  }
  
  // Get or create entry for this IP
  let entry = rateLimitMap.get(ip);
  if (!entry) {
    entry = {
      count: 0,
      resetAt: now + windowMs
    };
    rateLimitMap.set(ip, entry);
  }
  
  // Check if rate limited
  if (entry.count >= maxAttempts) {
    return false;
  }
  
  // Increment count
  entry.count++;
  return true;
}

/**
 * Reset rate limit for an IP (e.g., after successful login)
 * @param ip The IP address to reset
 */
export function resetRateLimit(ip: string): void {
  rateLimitMap.delete(ip);
}
