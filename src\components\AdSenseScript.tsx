'use client';

import { useEffect, useState } from 'react';

interface AdSenseScriptProps {
  clientId: string;
}

/**
 * AdSenseScript Component
 *
 * A component that safely loads the Google AdSense script on the client side
 * to prevent hydration errors and avoid the data-nscript attribute issue.
 *
 * This component:
 * 1. Only loads the script on the client side
 * 2. Uses dangerouslySetInnerHTML to inject the script tag directly without Next.js modifications
 * 3. Provides a cleanup mechanism to remove AdSense elements on unmount
 *
 * @param clientId The Google AdSense publisher ID (ca-pub-XXXXXXXXXXXXXXXX)
 */
export default function AdSenseScript({ clientId }: AdSenseScriptProps) {
  const [isClient, setIsClient] = useState(false);

  // Only run on the client side
  useEffect(() => {
    setIsClient(true);

    // Create and inject the AdSense script directly to avoid data-nscript attribute
    if (typeof document !== 'undefined' && !document.getElementById('google-adsense-script')) {
      const script = document.createElement('script');
      script.id = 'google-adsense-script';
      script.async = true;
      script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${clientId}`;
      script.crossOrigin = 'anonymous';

      // Add error handler
      script.onerror = (e) => {
        console.error('Error loading AdSense script:', e);
      };

      // Append to head
      document.head.appendChild(script);
    }

    // Cleanup function to remove any adsbygoogle-noablate elements on unmount
    return () => {
      if (typeof document !== 'undefined') {
        // Remove the script if component unmounts
        const scriptElement = document.getElementById('google-adsense-script');
        if (scriptElement) {
          scriptElement.remove();
        }

        // Remove any adsbygoogle-noablate elements that might cause hydration issues
        const noablateElements = document.querySelectorAll('.adsbygoogle-noablate');
        noablateElements.forEach(element => {
          element.remove();
        });
      }
    };
  }, [clientId]);

  // Don't render anything visible - the script is injected via useEffect
  return null;
}
