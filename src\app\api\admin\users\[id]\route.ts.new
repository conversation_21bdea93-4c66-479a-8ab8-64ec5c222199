/**
 * API routes for individual admin user operations
 */
import { NextRequest, NextResponse } from 'next/server';
import {
  getAdminUserById,
  updateAdminUser,
  deleteAdminUser,
  logAdminActivity
} from '@/lib/admin/user-service';
import { getCurrentUser } from '@/lib/auth';
import { AdminUserUpdate } from '@/lib/types/admin-users';

/**
 * GET /api/admin/users/[id] - Get admin user by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin privileges
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, message: 'Invalid user ID' },
        { status: 400 }
      );
    }

    const user = await getAdminUserById(id);

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true, data: user });
  } catch (error) {
    console.error(`Error getting admin user:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to get admin user' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/users/[id] - Update admin user
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin privileges
    const currentUser = await getCurrentUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, message: 'Invalid user ID' },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Check if user exists
    const existingUser = await getAdminUserById(id);
    if (!existingUser) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Update user
    const userData: AdminUserUpdate = {
      id,
      email: body.email,
      fullName: body.fullName,
      role: body.role,
      isActive: body.isActive,
      password: body.password
    };

    const updatedUser = await updateAdminUser(userData);

    // Log activity
    await logAdminActivity(
      currentUser.userId,
      'update',
      'user',
      id.toString(),
      { username: existingUser.username, changes: { ...userData, password: undefined } },
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json({ success: true, data: updatedUser });
  } catch (error) {
    console.error(`Error updating admin user:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to update admin user' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/users/[id] - Delete admin user
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin privileges
    const currentUser = await getCurrentUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, message: 'Invalid user ID' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await getAdminUserById(id);
    if (!existingUser) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Prevent deleting yourself
    if (id === currentUser.userId) {
      return NextResponse.json(
        { success: false, message: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Delete user
    await deleteAdminUser(id);

    // Log activity
    await logAdminActivity(
      currentUser.userId,
      'delete',
      'user',
      id.toString(),
      { username: existingUser.username },
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`Error deleting admin user:`, error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete admin user' },
      { status: 500 }
    );
  }
}
