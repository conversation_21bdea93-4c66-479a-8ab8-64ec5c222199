import nodemailer from 'nodemailer';
import { logInfo, logError } from '@/lib/logging';

/**
 * Email service configuration interface
 */
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: string;
}

/**
 * Get email configuration from environment variables
 */
export function getEmailConfig(): EmailConfig | null {
  // Check if SMTP is configured
  if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASSWORD) {
    console.log('Missing SMTP configuration:', {
      host: !!process.env.SMTP_HOST,
      user: !!process.env.SMTP_USER,
      password: !!process.env.SMTP_PASSWORD
    });
    return null;
  }

  // Log the SMTP configuration (without sensitive data)
  console.log('SMTP configuration found:', {
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT || '587',
    secure: process.env.SMTP_SECURE === 'true',
    user: process.env.SMTP_USER?.substring(0, 3) + '...',
    from: process.env.SMTP_FROM || '<EMAIL>'
  });

  return {
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587', 10),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    },
    from: process.env.SMTP_FROM || '<EMAIL>',
  };
}

/**
 * Create a nodemailer transporter
 */
export function createTransporter() {
  const config = getEmailConfig();

  if (!config) {
    return null;
  }

  return nodemailer.createTransport({
    host: config.host,
    port: config.port,
    secure: config.secure,
    auth: config.auth,
  });
}

/**
 * Email options interface
 */
interface EmailOptions {
  to: string;
  subject: string;
  text: string;
  html: string;
}

/**
 * Send an email
 *
 * @param options Email options
 * @returns Promise resolving to success status and message
 */
export async function sendEmail(options: EmailOptions): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Sending email to:', options.to);

    const config = getEmailConfig();

    if (!config) {
      console.log('SMTP not configured, skipping email sending');
      logInfo('email', 'SMTP not configured, skipping email sending');
      return {
        success: false,
        message: 'SMTP not configured'
      };
    }

    console.log('SMTP config found:', {
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: { user: config.auth.user.substring(0, 3) + '...' }
    });

    const transporter = createTransporter();

    if (!transporter) {
      console.error('Failed to create email transporter');
      return {
        success: false,
        message: 'Failed to create email transporter'
      };
    }

    const mailOptions = {
      from: `"VanishPost Support" <${config.from}>`,
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html,
    };

    console.log('Sending mail with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject
    });

    try {
      const info = await transporter.sendMail(mailOptions);

      console.log('Email sent successfully:', info.messageId);
      logInfo('email', 'Email sent successfully', {
        messageId: info.messageId,
        recipient: options.to,
        subject: options.subject
      });

      return {
        success: true,
        message: `Email sent: ${info.messageId}`
      };
    } catch (mailError) {
      console.error('Error sending mail:', mailError);
      throw mailError; // Re-throw to be caught by the outer try-catch
    }
  } catch (error) {
    console.error('Failed to send email:', error);
    logError('email', 'Failed to send email', { error });
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Send a contact reply notification email
 *
 * @param to Recipient email address
 * @param name Recipient name
 * @param subject Original message subject
 * @param originalMessage Original message content
 * @param replyMessage Admin reply message
 * @returns Promise resolving to success status and message
 */
export async function sendContactReplyNotification(
  to: string,
  name: string,
  subject: string,
  originalMessage: string,
  replyMessage: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Validate inputs
    if (!to || typeof to !== 'string') {
      console.error('Invalid recipient email:', to);
      return { success: false, message: 'Invalid recipient email address' };
    }

    if (!replyMessage || typeof replyMessage !== 'string') {
      console.error('Invalid reply message:', replyMessage);
      return { success: false, message: 'Invalid reply message' };
    }

    // Use safe defaults for other parameters if they're missing
    const safeName = name || 'Customer';
    const safeSubject = subject || 'Your Message';
    const safeOriginalMessage = originalMessage || '(No original message content)';

    const emailSubject = `Re: ${safeSubject} - VanishPost Support`;

  // Text version of the email
  const text = `
Hello ${safeName},

Thank you for contacting VanishPost Support. We have received your message and our team has provided a response.

YOUR MESSAGE:
${safeOriginalMessage}

OUR RESPONSE:
${replyMessage}

If you have any further questions, please feel free to reply to this email or submit another contact form on our website.

Best regards,
The VanishPost Team
<EMAIL>
https://vanishpost.com
  `.trim();

  // HTML version of the email with VanishPost branding
  const html = `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="format-detection" content="telephone=no" />
  <title>VanishPost Support Response</title>
  <!--[if mso]>
  <style type="text/css">
    body, table, td {font-family: Arial, Helvetica, sans-serif !important;}
  </style>
  <![endif]-->
  <style>
    /* Base styles */
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Container styles */
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 0;
      background-color: #ffffff;
    }

    /* Header styles */
    .header {
      background-color: #605f5f;
      padding: 25px 20px;
      text-align: center;
      color: white;
    }

    /* Header title */
    .header h1 {
      margin: 0;
      padding: 0;
      font-size: 28px;
      font-weight: 500;
      color: #ffffff;
      font-family: Arial, sans-serif;
      line-height: 1.3;
    }

    /* Fix for Outlook */
    table {
      border-collapse: collapse;
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
    }

    /* Content area */
    .content {
      padding: 30px 20px;
      background-color: #f9f9f9;
    }

    /* Message boxes */
    .message-box {
      background-color: #fff;
      border-left: 4px solid #605f5f;
      padding: 15px;
      margin-bottom: 20px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .reply-box {
      background-color: #faefe8;
      border-left: 4px solid #ce601c;
      padding: 15px;
      margin-bottom: 20px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    /* Typography */
    h3 {
      color: #333;
      margin-top: 25px;
      margin-bottom: 10px;
    }

    p {
      margin: 0 0 15px 0;
    }

    /* Button styles - optimized for email clients */
    .button {
      display: inline-block;
      background-color: #ce601c;
      color: white !important;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 4px;
      margin-top: 20px;
      font-weight: bold;
      mso-padding-alt: 12px 24px;
      text-align: center;
    }

    /* MSO button fallback */
    .button-fallback {
      mso-hide: all;
      visibility: hidden;
      display: none;
    }

    /* Footer styles */
    .footer {
      text-align: center;
      padding: 20px;
      font-size: 12px;
      color: #666;
      border-top: 1px solid #eee;
    }

    .footer a {
      color: #666;
      text-decoration: underline;
    }

    /* Responsive adjustments */
    @media only screen and (max-width: 480px) {
      .content {
        padding: 20px 15px;
      }

      .message-box, .reply-box {
        padding: 12px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!--[if mso]>
    <table role="presentation" width="100%" style="background-color: #605f5f;">
      <tr>
        <td align="center" style="padding: 25px 20px;">
          <h1 style="margin: 0; padding: 0; font-size: 28px; font-weight: 500; color: #ffffff; font-family: Arial, sans-serif; line-height: 1.3;">VanishPost Support</h1>
        </td>
      </tr>
    </table>
    <![endif]-->

    <!--[if !mso]><!-->
    <div class="header">
      <h1>VanishPost Support</h1>
    </div>
    <!--<![endif]-->

    <div class="content">
      <p>Hello ${safeName},</p>

      <p>Thank you for contacting VanishPost Support. We have received your message and our team has provided a response.</p>

      <h3>Your Message:</h3>
      <div class="message-box">
        <p>${safeOriginalMessage.replace(/\n/g, '<br>')}</p>
      </div>

      <h3>Our Response:</h3>
      <div class="reply-box">
        <p>${replyMessage.replace(/\n/g, '<br>')}</p>
      </div>

      <p>If you have any further questions, please feel free to reply to this email or submit another contact form on our website.</p>

      <!-- Button with MSO fallback for Outlook -->
      <!--[if mso]>
      <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="https://vanishpost.com/contact" style="height:40px;v-text-anchor:middle;width:200px;" arcsize="10%" stroke="f" fillcolor="#ce601c">
        <w:anchorlock/>
        <center>
      <![endif]-->
      <a href="https://vanishpost.com/contact" class="button">Contact Us Again</a>
      <!--[if mso]>
        </center>
      </v:roundrect>
      <![endif]-->
    </div>

    <div class="footer">
      <p>© ${new Date().getFullYear()} VanishPost. All rights reserved.</p>
      <table role="presentation" width="100%" border="0" cellpadding="0" cellspacing="0">
        <tr>
          <td align="center" style="padding: 10px 0;">
            <a href="https://vanishpost.com" style="color: #666; text-decoration: underline; padding: 0 5px;">Website</a> |
            <a href="https://vanishpost.com/privacy" style="color: #666; text-decoration: underline; padding: 0 5px;">Privacy Policy</a> |
            <a href="https://vanishpost.com/terms" style="color: #666; text-decoration: underline; padding: 0 5px;">Terms of Service</a>
          </td>
        </tr>
      </table>
    </div>
  </div>
</body>
</html>
  `.trim();

  return sendEmail({
    to,
    subject: emailSubject,
    text,
    html
  });
  } catch (error) {
    console.error('Error in sendContactReplyNotification:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error in email notification'
    };
  }
}

/**
 * Test SMTP connection
 *
 * @returns Promise resolving to connection status
 */
export async function testSmtpConnection(): Promise<{
  success: boolean;
  message: string;
  config?: {
    host: string;
    port: string | number;
    secure: boolean;
    user: string;
    from: string;
  };
}> {
  try {
    const config = getEmailConfig();

    if (!config) {
      return {
        success: false,
        message: 'SMTP not configured'
      };
    }

    const transporter = createTransporter();

    if (!transporter) {
      return {
        success: false,
        message: 'Failed to create email transporter'
      };
    }

    // Verify SMTP connection
    await transporter.verify();

    logInfo('email', 'SMTP connection verified successfully');

    return {
      success: true,
      message: 'SMTP connection verified successfully',
      config: {
        host: config.host,
        port: config.port,
        secure: config.secure,
        user: `${config.auth.user.substring(0, 3)}...`,
        from: config.from
      }
    };
  } catch (error) {
    logError('email', 'SMTP connection test failed', { error });

    return {
      success: false,
      message: `SMTP connection test failed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}
