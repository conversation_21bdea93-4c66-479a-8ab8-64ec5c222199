import EmailApp from "@/components/EmailApp";
import MutationObserverFix from "@/components/MutationObserverFix";
import Footer from '@/components/Footer';
import StructuredData from '@/components/StructuredData';

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen bg-background">
      <StructuredData type="website" />
      <main className="flex-grow min-h-screen bg-background flex flex-col">
        {/* Add MutationObserverFix component to fix console errors */}
        <MutationObserverFix />
        <div className="flex-1">
          <EmailApp />
        </div>
      </main>
      <Footer />
    </div>
  );
}
