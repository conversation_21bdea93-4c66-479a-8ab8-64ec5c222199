/**
 * Ad Schedule Checker
 *
 * This module provides functions for checking if an ad should be displayed based on its schedule.
 */
import { AdSchedule } from './types';
import { logInfo } from '@/lib/logging';

/**
 * Check if an ad should be displayed based on its schedule
 *
 * @param schedule The ad schedule to check
 * @returns boolean True if the ad should be displayed, false otherwise
 */
export function shouldDisplayAd(schedule?: AdSchedule): boolean {
  // If no schedule, always display
  if (!schedule) {
    return true;
  }

  const now = new Date();
  const currentTime = now.toTimeString().substring(0, 5); // HH:MM
  const currentDay = now.getDay(); // 0 = Sunday, 6 = Saturday
  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  const currentDayName = dayNames[currentDay];

  // Check if using the new weekly schedule format
  if (schedule.useSchedule) {
    // Schedule is enabled, check the day schedule

    // Get the current day's schedule
    const daySchedule = schedule[currentDayName as keyof AdSchedule];

    // If no schedule for this day, don't display
    if (!daySchedule) {
      logInfo('adSchedule', `Ad not displayed: no schedule for ${currentDayName}`);
      return false;
    }

    // If day is not enabled, don't display
    // Check if daySchedule is a DaySchedule object with the enabled property
    if (typeof daySchedule === 'object' && daySchedule !== null && 'enabled' in daySchedule && !daySchedule.enabled) {
      logInfo('adSchedule', `Ad not displayed: ${currentDayName} is disabled`);
      return false;
    }

    // Check time range
    if (typeof daySchedule === 'object' && daySchedule !== null && 'startTime' in daySchedule &&
        daySchedule.startTime && daySchedule.startTime > currentTime) {
      logInfo('adSchedule', `Ad not displayed: before start time (${daySchedule.startTime}) on ${currentDayName}`);
      return false;
    }

    if (typeof daySchedule === 'object' && daySchedule !== null && 'endTime' in daySchedule &&
        daySchedule.endTime && daySchedule.endTime < currentTime) {
      logInfo('adSchedule', `Ad not displayed: after end time (${daySchedule.endTime}) on ${currentDayName}`);
      return false;
    }

    // If all checks pass for the new format, display the ad
    return true;
  }

  // Legacy format checks

  // Check date range
  if (schedule.startDate && new Date(schedule.startDate) > now) {
    logInfo('adSchedule', `Ad not displayed: before start date (${schedule.startDate})`);
    return false;
  }

  if (schedule.endDate && new Date(schedule.endDate) < now) {
    logInfo('adSchedule', `Ad not displayed: after end date (${schedule.endDate})`);
    return false;
  }

  // Check time range
  if (schedule.startTime && schedule.startTime > currentTime) {
    logInfo('adSchedule', `Ad not displayed: before start time (${schedule.startTime})`);
    return false;
  }

  if (schedule.endTime && schedule.endTime < currentTime) {
    logInfo('adSchedule', `Ad not displayed: after end time (${schedule.endTime})`);
    return false;
  }

  // Check days of week
  if (schedule.daysOfWeek && schedule.daysOfWeek.length > 0 && !schedule.daysOfWeek.includes(currentDay)) {
    logInfo('adSchedule', `Ad not displayed: not scheduled for current day (${['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][currentDay]})`);
    return false;
  }

  // Check special dates
  if (schedule.specialDates && schedule.specialDates.length > 0) {
    const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD

    const specialDate = schedule.specialDates.find(sd => sd.date === currentDate);
    if (specialDate) {
      if (!specialDate.isEnabled) {
        logInfo('adSchedule', `Ad not displayed: disabled for special date (${currentDate})`);
        return false;
      }
      // If enabled for this special date, continue with other checks
    }
  }

  // Check recurring schedule
  if (schedule.isRecurring && schedule.recurringPattern && schedule.recurringInterval) {
    // This would require more complex logic based on the recurring pattern
    // For now, we'll assume that if it's recurring, it's valid for today
    // A more complete implementation would check if today falls within the recurring pattern
  }

  // If all checks pass, display the ad
  return true;
}
