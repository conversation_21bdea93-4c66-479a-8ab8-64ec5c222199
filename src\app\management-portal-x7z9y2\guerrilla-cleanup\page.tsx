/**
 * Guerrilla Email Cleanup Management Page
 * 
 * Dedicated page for managing Guerrilla email cleanup operations
 * Part of the VanishPost management portal
 */

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import GuerrillaCleanupManager from '@/components/admin/GuerrillaCleanupManager';

export default function GuerrillaCleanupPage() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/management-portal-x7z9y2/auth', {
          method: 'GET',
          credentials: 'include'
        });

        if (response.ok) {
          setIsAuthenticated(true);
        } else {
          router.push('/management-portal-x7z9y2/login');
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        router.push('/management-portal-x7z9y2/login');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="py-10">
        <header>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold leading-tight text-gray-900">
                  Guerrilla Email Cleanup
                </h1>
                <p className="mt-2 text-sm text-gray-600">
                  Dedicated cleanup system for old Guerrilla emails stored in MySQL database
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/management-portal-x7z9y2')}
                  className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Back to Dashboard
                </button>
              </div>
            </div>
          </div>
        </header>
        <main>
          <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-8 sm:px-0">
              <GuerrillaCleanupManager />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
