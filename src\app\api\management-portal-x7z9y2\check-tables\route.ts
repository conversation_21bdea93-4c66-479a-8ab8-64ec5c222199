/**
 * API route to check database tables
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/management-portal-x7z9y2/check-tables
 * 
 * Check if required tables exist
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();
    const results: Record<string, any> = {};
    
    // Check app_config table
    try {
      const { data: appConfigData, error: appConfigError } = await supabase
        .from('app_config')
        .select('*')
        .limit(1);
      
      results.app_config = {
        exists: !appConfigError,
        error: appConfigError ? appConfigError.message : null,
        sample: appConfigData
      };
    } catch (error) {
      results.app_config = {
        exists: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sample: null
      };
    }
    
    // Check domain_config table
    try {
      const { data: domainConfigData, error: domainConfigError } = await supabase
        .from('domain_config')
        .select('*')
        .limit(1);
      
      results.domain_config = {
        exists: !domainConfigError,
        error: domainConfigError ? domainConfigError.message : null,
        sample: domainConfigData
      };
    } catch (error) {
      results.domain_config = {
        exists: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sample: null
      };
    }
    
    // Check ad_config table
    try {
      const { data: adConfigData, error: adConfigError } = await supabase
        .from('ad_config')
        .select('*')
        .limit(1);
      
      results.ad_config = {
        exists: !adConfigError,
        error: adConfigError ? adConfigError.message : null,
        sample: adConfigData
      };
    } catch (error) {
      results.ad_config = {
        exists: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sample: null
      };
    }
    
    // Check system_logs table
    try {
      const { data: systemLogsData, error: systemLogsError } = await supabase
        .from('system_logs')
        .select('*')
        .limit(1);
      
      results.system_logs = {
        exists: !systemLogsError,
        error: systemLogsError ? systemLogsError.message : null,
        sample: systemLogsData
      };
    } catch (error) {
      results.system_logs = {
        exists: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sample: null
      };
    }
    
    return NextResponse.json({
      success: true,
      results
    });
  } catch (error) {
    logError('api', 'Error checking tables', { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to check tables' },
      { status: 500 }
    );
  }
}
