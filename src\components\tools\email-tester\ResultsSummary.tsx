/**
 * ResultsSummary Component
 *
 * This component displays a summary of the email deliverability test results.
 */

'use client';

interface ResultsSummaryProps {
  result: any;
}

export default function ResultsSummary({ result }: ResultsSummaryProps) {
  // Helper function to determine status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return 'text-white';
      case 'fail':
        return 'text-white';
      case 'warning':
        return 'text-white';
      case 'neutral':
        return 'text-white';
      default:
        return 'text-white';
    }
  };

  // Calculate overall score color
  const getScoreColor = (score: number) => {
    if (score >= 8) return '#66b077';
    if (score >= 5) return '#f59e0b';
    return '#f59e0b';
  };

  return (
    <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 sm:p-6 lg:p-8 shadow-sm transition-all duration-300">
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4 sm:mb-6 lg:mb-8">
        <div className="flex items-center space-x-4">
          <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-[#1b130e] to-[#4a3728] rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
            <svg className="w-7 h-7 sm:w-8 sm:h-8 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div>
            <h2 className="text-2xl font-bold" style={{ color: '#1b130e' }}>Deliverability Score</h2>
            <p style={{ color: '#4a3728' }}>
              Based on email authentication analysis
            </p>
          </div>
        </div>
        <div className="mt-6 lg:mt-0">
          <div className="text-center">
            <div className={`inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 rounded-2xl shadow-lg flex-shrink-0`} style={{
              backgroundColor: getScoreColor(result.overall_score),
              aspectRatio: '1'
            }}>
              <span className="text-2xl sm:text-3xl font-bold text-white">
                {result.overall_score}
              </span>
            </div>
            <p className="text-sm mt-2" style={{ color: '#4a3728' }}>out of 10</p>
            <p className={`text-sm font-medium mt-1`} style={{
              color: result.overall_score >= 8 ? '#66b077' :
                     result.overall_score >= 5 ? '#f59e0b' :
                     '#f59e0b'
            }}>
              {result.overall_score >= 8 ? 'Excellent' :
               result.overall_score >= 5 ? 'Good' :
               'Needs Improvement'}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* SPF Card */}
        <div className="group bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 sm:p-6 transition-all duration-300 hover:bg-white/80 hover:shadow-md">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className={`flex items-center justify-center w-10 h-10 rounded-xl shadow-sm`} style={{
                backgroundColor: result.spf_result === 'pass' ? '#66b077' : '#f59e0b'
              }}>
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.586-3.414A2 2 0 0018 7.414V6a2 2 0 00-2-2H8a2 2 0 00-2 2v1.414a2 2 0 00.586 1.414L9 11.414V15a2 2 0 002 2h2a2 2 0 002-2v-3.586l2.414-2.414z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold" style={{ color: '#1b130e' }}>SPF</h3>
            </div>
            <span className={`px-3 py-1 rounded-full text-xs font-bold uppercase tracking-wide text-white`} style={{
              backgroundColor: result.spf_result === 'pass' ? '#66b077' :
                             result.spf_result === 'fail' ? '#f59e0b' :
                             result.spf_result === 'softfail' ? '#f59e0b' :
                             '#4a3728'
            }}>
              {result.spf_result === 'pass' ? 'Pass' :
               result.spf_result === 'fail' ? 'Fail' :
               result.spf_result === 'softfail' ? 'Warning' :
               result.spf_result}
            </span>
          </div>
          <p className="leading-relaxed" style={{ color: '#4a3728' }}>
            {result.spf_result === 'pass'
              ? 'SPF record is properly configured and validates sender authorization'
              : 'SPF authentication failed or is missing - this may affect deliverability'}
          </p>
        </div>

        {/* DKIM Card */}
        <div className="group bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 sm:p-6 transition-all duration-300 hover:bg-white/80 hover:shadow-md">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className={`flex items-center justify-center w-10 h-10 rounded-xl shadow-sm`} style={{
                backgroundColor: result.dkim_result === 'pass' ? '#66b077' : '#f59e0b'
              }}>
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 1 21 9z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold" style={{ color: '#1b130e' }}>DKIM</h3>
            </div>
            <span className={`px-3 py-1 rounded-full text-xs font-bold uppercase tracking-wide text-white`} style={{
              backgroundColor: result.dkim_result === 'pass' ? '#66b077' : '#f59e0b'
            }}>
              {result.dkim_result === 'pass' ? 'Pass' : 'Fail'}
            </span>
          </div>
          <p className="leading-relaxed" style={{ color: '#4a3728' }}>
            {result.dkim_result === 'pass'
              ? 'DKIM signature is valid and ensures message integrity'
              : 'DKIM signature failed or is missing - consider implementing DKIM signing'}
          </p>
        </div>

        {/* DMARC Card */}
        <div className="group bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 sm:p-6 transition-all duration-300 hover:bg-white/80 hover:shadow-md">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className={`flex items-center justify-center w-10 h-10 rounded-xl shadow-sm`} style={{
                backgroundColor: result.dmarc_result === 'pass' ? '#66b077' : '#f59e0b'
              }}>
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold" style={{ color: '#1b130e' }}>DMARC</h3>
            </div>
            <span className={`px-3 py-1 rounded-full text-xs font-bold uppercase tracking-wide text-white`} style={{
              backgroundColor: result.dmarc_result === 'pass' ? '#66b077' : '#f59e0b'
            }}>
              {result.dmarc_result === 'pass' ? 'Pass' : 'Fail'}
            </span>
          </div>
          <p className="leading-relaxed" style={{ color: '#4a3728' }}>
            {result.dmarc_result === 'pass'
              ? 'DMARC policy is properly enforced and protects against spoofing'
              : 'DMARC policy failed or is missing - implement DMARC for better security'}
          </p>
        </div>

        {/* MX Records Card */}
        <div className="group bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 sm:p-6 transition-all duration-300 hover:bg-white/80 hover:shadow-md">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className={`flex items-center justify-center w-10 h-10 rounded-xl shadow-sm`} style={{
                backgroundColor: result.mx_result === 'pass' ? '#66b077' :
                               result.mx_result === 'warning' ? '#f59e0b' :
                               '#f59e0b'
              }}>
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                </svg>
              </div>
              <h3 className="text-lg font-bold" style={{ color: '#1b130e' }}>MX Records</h3>
            </div>
            <span className={`px-3 py-1 rounded-full text-xs font-bold uppercase tracking-wide text-white`} style={{
              backgroundColor: result.mx_result === 'pass' ? '#66b077' :
                             result.mx_result === 'warning' ? '#f59e0b' :
                             result.mx_result === 'fail' ? '#f59e0b' :
                             '#4a3728'
            }}>
              {result.mx_result === 'pass' ? 'Pass' :
               result.mx_result === 'warning' ? 'Warning' :
               result.mx_result === 'fail' ? 'Fail' :
               'None'}
            </span>
          </div>
          <p className="leading-relaxed" style={{ color: '#4a3728' }}>
            {result.mx_result === 'pass'
              ? 'MX records are properly configured for reliable email delivery'
              : result.mx_result === 'warning'
              ? 'MX records work but have configuration issues that should be addressed'
              : result.mx_result === 'fail'
              ? 'MX records have critical issues that may prevent email delivery'
              : 'MX record validation not available for this test'}
          </p>
          {result.analysis_json?.mx?.totalRecords && (
            <div className="mt-3 flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full" style={{ backgroundColor: '#956b50' }}></div>
              <p className="text-sm" style={{ color: '#4a3728' }}>
                {result.analysis_json.mx.totalRecords} record(s) found
              </p>
            </div>
          )}
        </div>

        {/* PTR Card */}
        <div className="group bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 sm:p-6 transition-all duration-300 hover:bg-white/80 hover:shadow-md">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className={`flex items-center justify-center w-10 h-10 rounded-xl shadow-sm`} style={{
                backgroundColor: result.analysis_json?.reverseDns?.result === 'pass' ? '#66b077' :
                               result.analysis_json?.reverseDns?.result === 'warning' ? '#f59e0b' :
                               result.analysis_json?.reverseDns?.result === 'fail' ? '#f59e0b' :
                               '#4a3728'
              }}>
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              <h3 className="text-lg font-bold" style={{ color: '#1b130e' }}>PTR</h3>
            </div>
            <span className={`px-3 py-1 rounded-full text-xs font-bold uppercase tracking-wide text-white`} style={{
              backgroundColor: result.analysis_json?.reverseDns?.result === 'pass' ? '#66b077' :
                             result.analysis_json?.reverseDns?.result === 'warning' ? '#f59e0b' :
                             result.analysis_json?.reverseDns?.result === 'fail' ? '#f59e0b' :
                             '#4a3728'
            }}>
              {result.analysis_json?.reverseDns?.result === 'pass' ? 'Pass' :
               result.analysis_json?.reverseDns?.result === 'warning' ? 'Warning' :
               result.analysis_json?.reverseDns?.result === 'fail' ? 'Fail' :
               'None'}
            </span>
          </div>
          <p className="leading-relaxed" style={{ color: '#4a3728' }}>
            {result.analysis_json?.reverseDns?.result === 'pass'
              ? 'Reverse DNS is properly configured and matches the sender domain'
              : result.analysis_json?.reverseDns?.result === 'warning'
              ? 'Reverse DNS works check for more information in the details below'
              : result.analysis_json?.reverseDns?.result === 'fail'
              ? 'Reverse DNS has critical issues that may affect email reputation'
              : 'Reverse DNS validation not available for this test'}
          </p>
          {result.analysis_json?.reverseDns?.ipAddress && (
            <div className="mt-3 flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full" style={{ backgroundColor: '#956b50' }}></div>
              <p className="text-sm" style={{ color: '#4a3728' }}>
                IP: {result.analysis_json.reverseDns.ipAddress}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
