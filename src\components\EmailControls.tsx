'use client';

import { useState, Suspense } from 'react';
import dynamic from 'next/dynamic';

// Import the client-only component with no SSR
const ClientEmailInput = dynamic(() => import('./ClientEmailInput'), {
  ssr: false,
});

interface EmailControlsProps {
  /** The current email address */
  emailAddress: string;

  /** Whether a new address is being generated */
  isGeneratingAddress: boolean;

  /** Function to generate a new email address */
  onGenerateNewAddress: () => void;

  /** Function to copy the email address to clipboard */
  onCopyEmailAddress: () => void;

  /** Text to display on the copy button */
  copyButtonText: string;
}

/**
 * EmailControls Component
 *
 * Displays the email address controls including the email input field,
 * copy button, and generate new address button. Handles copying the email
 * address to clipboard and generating a new email address.
 *
 * @param {string} props.emailAddress - The current email address
 * @param {boolean} props.isGeneratingAddress - Whether a new address is being generated
 * @param {Function} props.onGenerateNewAddress - Function to generate a new email address
 * @param {Function} props.onCopyEmailAddress - Function to copy the email address to clipboard
 * @param {string} props.copyButtonText - Text to display on the copy button
 * @returns {JSX.Element} The rendered component
 */
export default function EmailControls({
  emailAddress,
  isGeneratingAddress,
  onGenerateNewAddress,
  onCopyEmailAddress,
  copyButtonText
}: EmailControlsProps) {
  return (
    <div className="shadow-md max-w-5xl w-full mx-auto mt-6 p-5 rounded-md bg-white"
         style={{ border: '1px solid rgba(206, 96, 28, 0.3)' }}>
      <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4">
        {/* Email Address Field */}
        <div className="w-full md:flex-1 relative">
          <Suspense fallback={<div className="w-full px-4 rounded-md h-[56px] animate-pulse"
                                           style={{ border: '1px solid var(--earth-beige-secondary)', backgroundColor: 'var(--earth-beige-secondary)' }}></div>}>
            <ClientEmailInput emailAddress={emailAddress} />
          </Suspense>


        </div>

        {/* Copy Button (Secondary) */}
        <button
          onClick={onCopyEmailAddress}
          className="w-full md:w-auto px-6 bg-white text-[#ce601c] border border-[#ce601c] rounded-md hover:bg-neutral-50 hover:shadow-md hover:-translate-y-0.5 transition-all duration-300 shadow-sm font-medium flex items-center justify-center active:scale-98 relative overflow-hidden group h-[56px]"
          aria-label="Copy email address to clipboard"
        >
          <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-neutral-100/0 via-neutral-100/40 to-neutral-100/0 transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out"></span>
          <div className="relative flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2.5 text-[#ce601c] group-hover:animate-subtle-bounce" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
              <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" />
            </svg>
            <span className="text-base font-semibold">{copyButtonText}</span>
          </div>
        </button>

        {/* Generate New Address Button (Primary) */}
        <button
          onClick={onGenerateNewAddress}
          disabled={isGeneratingAddress}
          className="w-full md:w-auto px-6 text-white rounded-md hover:shadow-md hover:-translate-y-0.5 transition-all duration-300 shadow-sm font-medium flex items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed active:scale-98 relative overflow-hidden group h-[56px]"
          style={{
            backgroundColor: '#505050',
            border: '1px solid #505050'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#404040';
            e.currentTarget.style.borderColor = '#404040';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#505050';
            e.currentTarget.style.borderColor = '#505050';
          }}
          aria-label="Generate new email address"
        >
          <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-white/10 to-white/0 transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out"></span>
          <div className="relative flex items-center">
            {isGeneratingAddress ? (
              <svg className="animate-spin h-5 w-5 mr-2.5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2.5 text-white group-hover:animate-subtle-bounce" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 22v-5" />
                <path d="M9 8V2" />
                <path d="M15 8V2" />
                <path d="M12 8a4 4 0 100 8 4 4 0 000-8z" />
              </svg>
            )}
            <span className="text-base font-semibold">{isGeneratingAddress ? 'Generating...' : 'New Address'}</span>
          </div>
        </button>
      </div>
    </div>
  );
}
