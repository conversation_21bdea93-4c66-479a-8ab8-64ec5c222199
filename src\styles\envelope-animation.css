/* Custom envelope animation */
@keyframes bounce-envelope {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  15% {
    transform: translateY(-12px) rotate(-4deg);
  }
  30% {
    transform: translateY(0) rotate(0deg);
  }
  /* Long pause between bounces */
  65% {
    transform: translateY(0) rotate(0deg);
  }
  80% {
    transform: translateY(-8px) rotate(4deg);
  }
  95% {
    transform: translateY(0) rotate(0deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

.envelope-bounce {
  animation: bounce-envelope 4s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite;
  transform-origin: center;
  will-change: transform;
  transform: translateZ(0);
}

/* Simplified envelope animation - just floating */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration for smoother animation */
}

/* Slower spinning animation for email viewer */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration for smoother animation */
}
