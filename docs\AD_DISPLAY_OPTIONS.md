# Ad Display Options

This document explains the display options available for all ad placements in Fademail, including top, middle, and side rail ads.

## Overview

All ad placements in Fademail support customizable display options that can be configured through the admin interface. These options allow you to control the appearance and behavior of ads to better match your site's design.

## Available Display Options

The following display options are available for all ad placements:

```json
{
  "backgroundColor": "#f9f9f9",
  "padding": "10px",
  "margin": "5px",
  "borderRadius": "8px",
  "showBorder": true,
  "borderColor": "#e5e7eb",
  "maxWidth": "100%",
  "overflow": "hidden",
  "showLabel": true,
  "labelText": "Advertisement"
}
```

### Option Descriptions

- **backgroundColor**: Sets the background color of the ad container
- **padding**: Sets the internal padding of the ad container
- **margin**: Sets the external margin around the ad container
- **borderRadius**: Rounds the corners of the ad container
- **showBorder**: Adds a border around the ad container when true
- **borderColor**: Sets the color of the border (only used when showBorder is true)
- **maxWidth**: Limits the maximum width of the ad container
- **overflow**: Controls how content that exceeds the container dimensions is handled
- **showLabel**: Shows a label above the ad when true
- **labelText**: The text to display in the label (only used when showLabel is true)

## Ad Placement Types

### Top and Middle Ads

The top and middle ads appear in the main content flow and can be styled to match the surrounding content.

Example configuration for top ad:

```json
{
  "backgroundColor": "transparent",
  "padding": "8px",
  "margin": "10px auto",
  "maxWidth": "100%",
  "showLabel": true,
  "labelText": "Advertisement"
}
```

### Side Rail Ads

Side rail ads stick to the sides of the screen as the user scrolls and have some additional positioning logic.

Example configuration for side rail ads:

```json
{
  "backgroundColor": "#f9f9f9",
  "padding": "10px",
  "borderRadius": "8px",
  "showBorder": true,
  "borderColor": "#e5e7eb",
  "maxWidth": "160px",
  "showLabel": true,
  "labelText": "Sponsored"
}
```

## How to Configure

1. Go to `/management-portal-x7z9y2/ads`
2. Click "Add Ad Placement" or edit an existing ad
3. In the Display Options (JSON) field, add your configuration
4. Save the changes

The display options will be immediately applied to the ads on the main Fademail interface.

## How to Remove Display Options

If you want to remove all display options from an ad placement:

1. Go to `/management-portal-x7z9y2/ads`
2. Click "Edit" on the ad placement you want to modify
3. Click the "Clear" button next to the Display Options (JSON) field
4. Save the changes

This will remove all display options and revert the ad to its default appearance.

## Best Practices

1. Use transparent backgrounds for ads that blend with your site's design
2. Add labels to clearly identify sponsored content
3. Use consistent styling across similar ad placements
4. Test different configurations to find what works best for your site
5. Consider using borders or background colors to subtly distinguish ads from content
6. Keep ad containers clean and minimal to avoid distracting from the main content
