# Next.js 15.3.0 Migration Guide for VanishPost

This guide provides a comprehensive, step-by-step approach for updating the VanishPost application to fully comply with Next.js 15.3.0 best practices. It focuses on addressing breaking changes and implementing recommended patterns.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Running the Upgrade Codemod](#running-the-upgrade-codemod)
3. [Async API Changes](#async-api-changes)
   - [Cookies API](#cookies-api)
   - [Headers API](#headers-api)
   - [DraftMode API](#draftmode-api)
   - [Params and SearchParams](#params-and-searchparams)
4. [Fetch Caching Strategy](#fetch-caching-strategy)
5. [Runtime Configuration](#runtime-configuration)
6. [TypeScript Type Updates](#typescript-type-updates)
7. [Testing Your Changes](#testing-your-changes)
8. [Additional Considerations](#additional-considerations)

## Prerequisites

Before starting the migration process, ensure you have:

1. Updated to the latest versions of Next.js and React:

```bash
npm install next@latest react@latest react-dom@latest eslint-config-next@latest
```

2. If using TypeScript, update the type definitions:

```bash
npm install @types/react@latest @types/react-dom@latest
```

3. Create a backup of your codebase or ensure you're working in a separate branch.

## Running the Upgrade Codemod

Next.js provides a codemod to automate many of the migration tasks:

```bash
npx @next/codemod@canary upgrade latest
```

This will help with some of the changes, but manual updates will still be required, especially for the async API changes.

## Async API Changes

### Cookies API

The `cookies()` function is now asynchronous. Update all instances where it's used.

**Current Implementation:**

```typescript
import { cookies } from 'next/headers';

export default function MyComponent() {
  const cookieStore = cookies();
  const token = cookieStore.get('token');

  // Rest of the component
}
```

**Recommended Implementation:**

```typescript
import { cookies } from 'next/headers';

export default async function MyComponent() {
  const cookieStore = await cookies();
  const token = cookieStore.get('token');

  // Rest of the component
}
```

For client components where you can't use `async/await` directly:

```typescript
'use client';

import { use } from 'react';
import { cookies } from 'next/headers';

export default function MyClientComponent({ cookiesPromise }) {
  const cookieStore = use(cookiesPromise);
  const token = cookieStore.get('token');

  // Rest of the component
}
```

### Headers API

The `headers()` function is now asynchronous. Update all instances where it's used.

**Current Implementation:**

```typescript
import { headers } from 'next/headers';

export default function MyComponent() {
  const headersList = headers();
  const userAgent = headersList.get('user-agent');

  // Rest of the component
}
```

**Recommended Implementation:**

```typescript
import { headers } from 'next/headers';

export default async function MyComponent() {
  const headersList = await headers();
  const userAgent = headersList.get('user-agent');

  // Rest of the component
}
```

### DraftMode API

The `draftMode()` function is now asynchronous. Update all instances where it's used.

**Current Implementation:**

```typescript
import { draftMode } from 'next/headers';

export default function MyComponent() {
  const { isEnabled } = draftMode();

  // Rest of the component
}
```

**Recommended Implementation:**

```typescript
import { draftMode } from 'next/headers';

export default async function MyComponent() {
  const { isEnabled } = await draftMode();

  // Rest of the component
}
```

### Params and SearchParams

The `params` and `searchParams` objects in page and layout components are now asynchronous.

**Current Implementation (Page):**

```typescript
export default function Page({ params, searchParams }) {
  const { slug } = params;
  const { query } = searchParams;

  // Rest of the component
}
```

**Recommended Implementation (Async Page):**

```typescript
export default async function Page(props) {
  const params = await props.params;
  const searchParams = await props.searchParams;

  const { slug } = params;
  const { query } = searchParams;

  // Rest of the component
}
```

**Recommended Implementation (Client Component):**

```typescript
'use client';

import { use } from 'react';

export default function Page(props) {
  const params = use(props.params);
  const searchParams = use(props.searchParams);

  const { slug } = params;
  const { query } = searchParams;

  // Rest of the component
}
```

For TypeScript, update your type definitions:

```typescript
type Params = Promise<{ slug: string }>;
type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>;

export default async function Page(props: {
  params: Params;
  searchParams: SearchParams;
}) {
  const params = await props.params;
  const searchParams = await props.searchParams;

  // Rest of the component
}
```

## Fetch Caching Strategy

In Next.js 15, fetch requests are no longer cached by default. You need to explicitly opt into caching.

**Current Implementation:**

```typescript
export default async function MyComponent() {
  const data = await fetch('https://api.example.com/data');
  const json = await data.json();

  // Rest of the component
}
```

**Recommended Implementation:**

```typescript
export default async function MyComponent() {
  // Not cached
  const dataUncached = await fetch('https://api.example.com/data');

  // Cached
  const dataCached = await fetch('https://api.example.com/data', {
    cache: 'force-cache'
  });

  // Rest of the component
}
```

To opt all fetch requests in a layout or page into caching, use the segment config option:

```typescript
// Since this is the root layout, all fetch requests in the app
// that don't set their own cache option will be cached.
export const fetchCache = 'default-cache';

export default async function RootLayout() {
  const a = await fetch('https://...'); // Cached
  const b = await fetch('https://...', { cache: 'no-store' }); // Not cached

  // ...
}
```

## Runtime Configuration

If you're using the `runtime` segment configuration with `experimental-edge`, update it to `edge`:

**Current Implementation:**

```typescript
export const runtime = 'experimental-edge';
```

**Recommended Implementation:**

```typescript
export const runtime = 'edge';
```

## TypeScript Type Updates

Update your TypeScript types to reflect the async nature of the APIs:

```typescript
// For cookies
import { cookies } from 'next/headers';
type CookieStore = Awaited<ReturnType<typeof cookies>>;

// For headers
import { headers } from 'next/headers';
type HeadersList = Awaited<ReturnType<typeof headers>>;

// For params and searchParams
type Params = Promise<{ slug: string }>;
type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>;
```

## Testing Your Changes

After implementing the changes, follow these steps to test your application:

1. Run the development server:

```bash
npm run dev
```

2. Check the console for any warnings related to the async APIs.

3. Test all pages and functionality that use cookies, headers, params, or searchParams.

4. Run a production build to ensure everything compiles correctly:

```bash
npm run build
```

5. Test the production build locally:

```bash
npm run start
```

6. Create automated tests for critical paths that were updated.

## Additional Considerations

### NextRequest Geolocation

The `geo` and `ip` properties on `NextRequest` have been removed. If you're using Vercel, use the `geolocation` and `ipAddress` functions from `@vercel/functions` instead:

```typescript
import { geolocation, ipAddress } from '@vercel/functions';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { city } = geolocation(request);
  const ip = ipAddress(request);

  // Rest of the middleware
}
```

### Client-side Router Cache

Page segments are no longer reused from the client-side router cache when navigating between pages via `<Link>` or `useRouter`. To opt page segments into caching, use the `staleTimes` config option:

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    staleTimes: {
      dynamic: 30,
      static: 180,
    },
  },
};

module.exports = nextConfig;
```

### Route Handlers

`GET` functions in Route Handlers are no longer cached by default. To opt `GET` methods into caching, use a route config option:

```typescript
// app/api/route.ts
export const dynamic = 'force-static';

export async function GET() {
  // This route handler will be cached
}
```

## VanishPost-Specific Migration Checklist

### 1. Update Components Using Cookies API

- [ ] `CookieConsent.tsx` - Update to use async cookies API
- [ ] `AuthContext.tsx` - Update authentication functions to handle async cookies

### 2. Update Components Using Headers API

- [ ] `middleware.ts` - Update to handle async headers
- [ ] Any API routes that use the headers function

### 3. Update Components with Params/SearchParams

- [ ] `/app/(with-layout)/cookies/page.tsx`
- [ ] All dynamic routes under `/app/management-portal-x7z9y2/`
- [ ] All dynamic routes under `/app/admin/`

### 4. Update Fetch Caching Strategy

- [ ] Review and update fetch calls in `CacheInitializer.tsx`
- [ ] Review and update fetch calls in `CleanupSchedulerInitializer.tsx`
- [ ] Add appropriate caching strategies to API data fetching in admin components

### 5. Update API Routes

- [ ] Review all route handlers in `/app/api/` to ensure they handle async params correctly
- [ ] Update caching strategies for GET handlers where appropriate

### 6. Update TypeScript Types

- [ ] Create or update type definitions for async APIs
- [ ] Update component props types to reflect Promise-based params and searchParams

## Systematic Migration Approach

To minimize disruption during the migration process, follow this systematic approach:

1. **Start with Infrastructure Components**:
   - First update middleware, layout components, and other infrastructure code
   - Test thoroughly before proceeding

2. **Update API Routes**:
   - Update route handlers to use async APIs
   - Test API endpoints independently

3. **Update Page Components**:
   - Start with simpler pages and gradually move to more complex ones
   - Convert each page and test before moving to the next

4. **Update Client Components**:
   - Update client components to use the `use()` hook with promises
   - Ensure proper error handling for promise rejections

5. **Update Fetch Caching**:
   - Implement appropriate caching strategies based on data requirements
   - Test performance and behavior

## VanishPost Component Examples

### Example 1: Updating CacheInitializer.tsx

**Current Implementation:**

```typescript
// src/components/CacheInitializer.tsx
'use client';

import { useEffect } from 'react';
import { ADMIN } from '@/lib/constants';

export default function CacheInitializer() {
  useEffect(() => {
    // Use direct admin API path instead of relying on rewrites
    console.log('Using direct admin API path for cache initialization');

    // Call the API to initialize caches server-side (using direct admin path)
    fetch('/api/admin/cache/init', { method: 'POST' })
      .then(response => {
        if (!response.ok) {
          console.error(`Cache initialization failed with status: ${response.status} ${response.statusText}`);
          return;
        }

        console.log('Cache initialization complete');
        return response.json();
      })
      .then(data => {
        if (data) {
          console.log('Cache initialization details:', data);
        }
      })
      .catch(error => {
        console.error('Cache initialization error:', error);
      });
  }, []);

  return null;
}
```

**Updated Implementation with Fetch Caching:**

```typescript
// src/components/CacheInitializer.tsx
'use client';

import { useEffect } from 'react';

export default function CacheInitializer() {
  useEffect(() => {
    // Use direct admin API path instead of relying on rewrites
    console.log('Using direct admin API path for cache initialization');

    const initializeCache = async () => {
      try {
        // Call the API to initialize caches server-side with no caching
        const response = await fetch('/api/admin/cache/init', {
          method: 'POST',
          cache: 'no-store', // Explicitly set no caching for this admin operation
          next: { revalidate: 0 } // Ensure fresh data
        });

        if (!response.ok) {
          console.error(`Cache initialization failed with status: ${response.status} ${response.statusText}`);
          return;
        }

        console.log('Cache initialization complete');
        const data = await response.json();
        console.log('Cache initialization details:', data);
      } catch (error) {
        console.error('Cache initialization error:', error);
      }
    };

    initializeCache();
  }, []);

  return null;
}
```

### Example 2: Updating a Dynamic Route Page

**Current Implementation:**

```typescript
// src/app/management-portal-x7z9y2/domains/[domain]/page.tsx
import { getDomainDetails } from '@/lib/domains';

type DomainPageProps = {
  params: { domain: string };
};

export default async function DomainPage({ params }: DomainPageProps) {
  const { domain } = params;
  const domainDetails = await getDomainDetails(domain);

  return (
    <div>
      <h1>Domain: {domain}</h1>
      {/* Domain details UI */}
    </div>
  );
}
```

**Updated Implementation:**

```typescript
// src/app/management-portal-x7z9y2/domains/[domain]/page.tsx
import { getDomainDetails } from '@/lib/domains';

type DomainPageProps = {
  params: Promise<{ domain: string }>;
};

export default async function DomainPage(props: DomainPageProps) {
  const params = await props.params;
  const { domain } = params;

  const domainDetails = await getDomainDetails(domain);

  return (
    <div>
      <h1>Domain: {domain}</h1>
      {/* Domain details UI */}
    </div>
  );
}
```

### Example 3: Updating an API Route Handler

**Current Implementation:**

```typescript
// src/app/api/admin/domains/[domain]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getDomainDetails, updateDomain } from '@/lib/domains';

export async function GET(
  request: NextRequest,
  { params }: { params: { domain: string } }
) {
  const { domain } = params;
  const domainDetails = await getDomainDetails(domain);

  return NextResponse.json(domainDetails);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { domain: string } }
) {
  const { domain } = params;
  const data = await request.json();

  const result = await updateDomain(domain, data);
  return NextResponse.json(result);
}
```

**Updated Implementation:**

```typescript
// src/app/api/admin/domains/[domain]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getDomainDetails, updateDomain } from '@/lib/domains';

// Add caching configuration if needed
export const dynamic = 'force-dynamic'; // Or 'force-static' if you want caching

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ domain: string }> }
) {
  const resolvedParams = await params;
  const { domain } = resolvedParams;

  const domainDetails = await getDomainDetails(domain);

  return NextResponse.json(domainDetails);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ domain: string }> }
) {
  const resolvedParams = await params;
  const { domain } = resolvedParams;

  const data = await request.json();

  const result = await updateDomain(domain, data);
  return NextResponse.json(result);
}
```

## Conclusion

Migrating to Next.js 15.3.0 requires careful attention to the async API changes and caching strategies. By following this guide, you can systematically update the VanishPost application to fully comply with Next.js 15.3.0 best practices.

The key points to remember are:

1. **Async APIs**: Update all uses of `cookies()`, `headers()`, `draftMode()`, `params`, and `searchParams` to use the async pattern.

2. **Fetch Caching**: Be explicit about your caching strategy for fetch requests. Use `cache: 'force-cache'` for data that can be cached and `cache: 'no-store'` for data that should always be fresh.

3. **Route Handlers**: Update GET route handlers to use the appropriate caching strategy with the `dynamic` configuration.

4. **Client Components**: Use the `use()` hook to handle promises in client components.

5. **Testing**: Test each component after updating to ensure it works correctly.

By addressing these changes systematically, you can ensure a smooth migration to Next.js 15.3.0 and take advantage of the latest features and improvements.

## References

- [Next.js 15 Upgrade Guide](https://nextjs.org/docs/app/guides/upgrading/version-15)
- [Next.js Fetch API](https://nextjs.org/docs/app/api-reference/functions/fetch)
- [Next.js Cookies API](https://nextjs.org/docs/app/api-reference/functions/cookies)
- [Next.js Headers API](https://nextjs.org/docs/app/api-reference/functions/headers)
- [Next.js Route Segment Config](https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config)
- [React `use()` Hook](https://react.dev/reference/react/use)
