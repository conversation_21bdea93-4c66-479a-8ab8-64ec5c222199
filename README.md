# VanishPost - Disposable Email Service

VanishPost is a disposable email service that allows users to generate temporary email addresses and view received emails through a clean web interface. The system automatically cleans up expired emails to maintain performance and storage efficiency.

## Features

- **Temporary Email Addresses**: Generate disposable email addresses that expire after 30 minutes
- **Real-time Email Viewing**: View emails as they arrive with automatic 14-second refresh
- **HTML Email Support**: Full support for HTML emails with proper sanitization and accurate rendering
- **Attachment Handling**: View and download email attachments
- **Responsive Design**: Works on mobile, tablet, and desktop devices
- **User-friendly Interface**: Clean, intuitive interface with helpful guide emails
- **Domain Rotation**: Automatic rotation between vanishpost.site and quickvanish.site domains
- **Persistent State**: Email read status and current email address persist across browser refreshes
- **Expiration Timer**: Visual countdown timer showing remaining time before email address expires
- **Color-coded Alerts**: Timer changes from green to red when email is close to expiring
- **Automatic Cleanup**: Expired email addresses are automatically removed
- **Accessibility**: Fully accessible with keyboard navigation, ARIA attributes, and semantic HTML
- **Modular Architecture**: Clean separation of concerns with custom hooks and components
- **Comprehensive Testing**: Unit and component tests for reliability and maintainability

## Tech Stack

- **Frontend**: Next.js 15.3.0 with TypeScript 5.8.3, React, Tailwind CSS 4.1.3
- **Backend**: Next.js API routes
- **Databases**: Supabase PostgreSQL (for temporary email management), MySQL (remote for email storage)
- **Analytics**: PostHog for privacy-focused analytics tracking
- **Email Server**: go-guerrilla (pre-configured on Digital Ocean droplet)
- **Deployment**: Digital Ocean droplet with PM2 process manager
- **Testing**: Jest and React Testing Library for unit and component testing
- **State Management**: Custom React hooks for clean, modular state management
- **Real-time Updates**: Supabase real-time subscriptions for instant email notifications

## Prerequisites

- Node.js 22.14 or later
- Supabase account and project
- Access to the go-guerrilla email server

## Getting Started

### Installation

1. Clone the repository

```bash
git clone https://github.com/yourusername/vanishpost.git
cd vanishpost
```

2. Install dependencies

```bash
npm install
```

3. Set up environment variables

Create a `.env.local` file in the root directory with the following variables:

```
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-url.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Guerrilla Database Configuration
GUERRILLA_DB_HOST=your-guerrilla-db-host
GUERRILLA_DB_USER=your-guerrilla-db-user
GUERRILLA_DB_PASSWORD=your-guerrilla-db-password
GUERRILLA_DB_NAME=your-guerrilla-db-name

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY=your-posthog-key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# Admin Authentication
ADMIN_USERNAME=admin
ADMIN_PASSWORD=secure_password_here
JWT_SECRET=your_secure_jwt_secret_here

# Email Configuration
# Domains are handled in the code (vanishpost.site and quickvanish.site)
EMAIL_EXPIRATION_MINUTES=30

# Cleanup Configuration
CLEANUP_API_KEY=your-secret-api-key
```

4. Run the development server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Email Rendering

VanishPost uses an iframe-based email renderer to provide the most accurate rendering of HTML emails:

- **Complete Style Isolation**: Emails are rendered in an isolated iframe to prevent style conflicts
- **Original Styling Preservation**: All original email styles are preserved exactly as intended
- **Security**: The iframe is sandboxed for enhanced security
- **Responsive Rendering**: The iframe adjusts to the content size for proper display
- **Cross-Client Compatibility**: Rendering closely mimics how email clients display emails
- **Print Support**: Special print styles for proper printing of emails with backgrounds and URLs
- **Web Font Support**: Load custom web fonts inside the iframe for consistent typography
- **Mobile Optimization**: Responsive viewport settings for better mobile rendering
- **Performance Optimization**: Content hashing and memoization to prevent unnecessary re-renders

The implementation is in `src/components/IframeEmailRenderer.tsx` and can be tested at `/test-iframe-renderer`.

## Project Structure

```
vanishpost/
├── app/                  # Next.js App Router pages
├── components/           # React components
│   ├── __tests__/        # Component tests
│   ├── EmailApp.tsx      # Main application component
│   ├── EmailControls.tsx # Email address controls
│   ├── EmailList.tsx     # Email list component
│   ├── EmailViewer.tsx   # Email viewer component
│   ├── IframeEmailRenderer.tsx # Iframe-based email renderer
│   ├── Footer.tsx        # Footer component
│   ├── Navbar.tsx        # Navigation bar component
│   └── Notifications.tsx # Notification components
├── hooks/                # Custom React hooks
│   ├── __tests__/        # Hook tests
│   ├── useAutoRefresh.ts # Auto-refresh functionality
│   ├── useEmailSelection.ts # Email selection logic
│   └── useEmailStorage.ts # Email storage and retrieval
├── lib/                  # Utility functions and constants
│   ├── __tests__/        # Utility tests
│   ├── constants.ts      # Application constants
│   ├── emailProcessing.ts # Email processing utilities
│   └── errorHandling.ts  # Error handling utilities
├── public/               # Static assets
├── .env.local            # Environment variables (not in repo)
├── jest.config.js        # Jest configuration
├── next.config.js        # Next.js configuration
├── package.json          # Project dependencies
├── tailwind.config.js    # Tailwind CSS configuration
└── tsconfig.json         # TypeScript configuration
```

## Database Setup

### Supabase Setup

Run the following scripts to set up the Supabase database:

```bash
# Set up the Supabase database schema
npm run setup-supabase

# Set up Row-Level Security policies
npm run setup-supabase-rls

# Test the Supabase connection
npm run test-supabase
```

The Supabase database has the following schema:

```sql
-- Temporary Emails Table
CREATE TABLE temp_emails (
  id SERIAL PRIMARY KEY,
  email_address VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  last_checked_at TIMESTAMPTZ
);

-- Note: Analytics is now handled by PostHog

-- Database Configurations Table
CREATE TABLE database_configurations (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  is_active BOOLEAN DEFAULT false,
  guerrilla_config JSONB NOT NULL,
  supabase_config JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

For more details on the Supabase migration, see [SUPABASE_MIGRATION.md](./docs/SUPABASE_MIGRATION.md).

### Database Configuration System

Fademail includes a flexible database configuration system that allows you to manage and switch between different database connections without redeploying the application:

- **Multiple Configurations**: Create and manage multiple database connection configurations
- **Connection Testing**: Test connections before activating them
- **Dynamic Switching**: Switch between configurations through the admin interface
- **Secure Storage**: Securely store connection credentials in the database
- **Fallback Mechanism**: Automatically fall back to environment variables if needed

Access the database configuration system through the admin portal at `/management-portal-x7z9y2/database-config`.

For detailed documentation, see [DATABASE_CONFIGURATION.md](./docs/DATABASE_CONFIGURATION.md).

## Testing

The project includes comprehensive tests for components, hooks, and utility functions. To run the tests:

```bash
npm test
```

To run tests with coverage:

```bash
npm test -- --coverage
```

The tests are organized in the following directories:
- `components/__tests__/` - Component tests
- `hooks/__tests__/` - Custom hook tests
- `lib/__tests__/` - Utility function tests

## API Routes

### Public API Routes

- `POST /api/generate` - Generate a new temporary email address
- `GET /api/generate?email=<EMAIL>` - Check if an email address exists and is valid
- `GET /api/emails/[address]` - Get emails for a specific email address
- `POST /api/cleanup` - Clean up expired email addresses
### Admin API Routes

#### Database Configuration API Routes

- `POST /api/management-portal-x7z9y2/database-config/test` - Test database connections
- `GET /api/management-portal-x7z9y2/database-config` - Get all database configurations
- `POST /api/management-portal-x7z9y2/database-config` - Create a new database configuration
- `PUT /api/management-portal-x7z9y2/database-config/[id]` - Update a database configuration
- `DELETE /api/management-portal-x7z9y2/database-config/[id]` - Delete a database configuration
- `POST /api/management-portal-x7z9y2/database-config/[id]/activate` - Set a configuration as active

## Deployment

### Building for Production

```bash
npm run build
```

### Running in Production

```bash
npm start
```

### Deploying with PM2

```bash
pm2 start npm -- start
```

### Syncing with Production Repository

This project uses a manual process to sync changes from the development repository to the production repository. There are two ways to do this:

1. **GitHub Actions Workflow (Recommended)**:
   - First, you need to set up a Personal Access Token (PAT) with repo permissions:
     1. Go to your GitHub account settings
     2. Select "Developer settings" > "Personal access tokens" > "Tokens (classic)"
     3. Generate a new token with "repo" permissions
     4. Copy the token
     5. Go to your repository settings > "Secrets and variables" > "Actions"
     6. Add a new repository secret named `PRODUCTION_REPO_TOKEN` with the token value

   - To trigger the sync:
     1. Go to the GitHub repository
     2. Click on the "Actions" tab
     3. Select the "Sync to Production Repository" workflow
     4. Click "Run workflow" and confirm

   This workflow is defined in `.github/workflows/sync-production.yml` and handles the syncing process.

2. **Manual Scripts**:
   - On Linux/Mac:
     ```bash
     bash scripts/sync_production.sh
     ```

   - On Windows:
     ```bash
     .\scripts\sync_production.ps1
     ```

The production repository is deployed to Vercel, so after syncing, the changes will be automatically deployed.

## Scheduled Cleanup

To automatically clean up expired email addresses, set up a cron job to call the cleanup API endpoint:

```bash
0 * * * * curl -X POST -H "Authorization: Bearer your-secret-api-key" https://yourdomain.com/api/cleanup
```

## Project Status

For the current project status and roadmap, see:
- [PROJECT_STATUS.md](./PROJECT_STATUS.md) - Current status of the project
- [ROADMAP.md](./ROADMAP.md) - Detailed project roadmap

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Next.js](https://nextjs.org) - The React framework for production
- [Tailwind CSS](https://tailwindcss.com) - A utility-first CSS framework
- [Supabase](https://supabase.com) - Open source Firebase alternative
- [go-guerrilla](https://github.com/flashmob/go-guerrilla) - SMTP server written in Go
- [Recharts](https://recharts.org) - Composable charting library for React
