-- Ad Configuration Table
CREATE TABLE IF NOT EXISTS ad_config (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  placement_id VARCHAR(50) NOT NULL,
  domain VARCHAR(255) NOT NULL,
  ad_unit_id VARCHAR(255) NOT NULL,
  ad_client_id VARCHAR(255) NOT NULL,
  is_enabled BOOLEAN DEFAULT true,
  device_types JSONB DEFAULT '["desktop", "tablet", "mobile"]'::jsonb,
  format VARCHAR(50) DEFAULT 'auto',
  display_options JSONB DEFAULT '{}'::jsonb,
  schedule JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(placement_id, domain)
);

-- Add comment to table
COMMENT ON TABLE ad_config IS 'Stores configuration for ad placements across the application';

-- Add comments to columns
COMMENT ON COLUMN ad_config.placement_id IS 'Identifier for the ad placement location (e.g., top, middle, sidebar)';
COMMENT ON COLUMN ad_config.domain IS 'Domain this configuration applies to. Use * for wildcard';
COMMENT ON COLUMN ad_config.ad_unit_id IS 'Google AdSense ad unit ID';
COMMENT ON COLUMN ad_config.ad_client_id IS 'Google AdSense publisher ID (ca-pub-XXXXXXXXXXXXXXXX)';
COMMENT ON COLUMN ad_config.is_enabled IS 'Whether this ad placement is currently enabled';
COMMENT ON COLUMN ad_config.device_types IS 'Array of device types this ad should appear on';
COMMENT ON COLUMN ad_config.format IS 'Ad format (auto, rectangle, horizontal, etc.)';
COMMENT ON COLUMN ad_config.display_options IS 'JSON object with display options like position, margin, etc.';
COMMENT ON COLUMN ad_config.schedule IS 'JSON object with scheduling options like start/end times';

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_ad_config_placement_domain ON ad_config(placement_id, domain);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_ad_config_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_ad_config_updated_at
BEFORE UPDATE ON ad_config
FOR EACH ROW
EXECUTE FUNCTION update_ad_config_updated_at();

-- Insert sample configurations for testing
INSERT INTO ad_config (placement_id, domain, ad_unit_id, ad_client_id, is_enabled, device_types, format, display_options)
VALUES
  ('top', '*', '1234567890', 'ca-pub-1234567890123456', true, '["desktop", "tablet", "mobile"]'::jsonb, 'auto', '{"margin": "10px 0", "backgroundColor": "transparent"}'::jsonb),
  ('middle', '*', '0987654321', 'ca-pub-1234567890123456', true, '["desktop", "tablet"]'::jsonb, 'auto', '{"margin": "15px 0", "backgroundColor": "transparent"}'::jsonb)
ON CONFLICT (placement_id, domain) DO NOTHING;
