/**
 * SMTP Tester API Route
 *
 * API endpoint for testing SMTP server connectivity
 * POST /api/tools/smtp-tester
 */

import { NextRequest, NextResponse } from 'next/server';
import { smtpService } from '@/lib/tools/smtp-tester/smtpService';
import { validateSmtpTestRequest, sanitizeSmtpTestRequest } from '@/lib/tools/smtp-tester/validation';
import { extractTestAddressId } from '@/lib/tools/smtp-tester/utils';
import { SmtpTestRequest, SmtpTestResult, EmailAnalysisResult } from '@/types/smtp';
import { logError } from '@/lib/logging';

// Rate limiting storage (in production, use Redis or database)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

function getRateLimitKey(request: NextRequest): string {
  // Use IP address for rate limiting
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';
  return `smtp-test:${ip}`;
}

function checkRateLimit(key: string): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute window
  const maxRequests = 10; // Max 10 requests per minute

  const current = rateLimitMap.get(key);

  if (!current || now > current.resetTime) {
    // Reset or initialize
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
    return { allowed: true };
  }

  if (current.count >= maxRequests) {
    return { allowed: false, resetTime: current.resetTime };
  }

  current.count++;
  return { allowed: true };
}

async function analyzeEmailAuthentication(testAddress: string, testAddressId?: string): Promise<EmailAnalysisResult | null> {
  try {
    console.log(`Attempting to analyze email authentication for: ${testAddress}`);

    // Use provided test address ID or extract from address as fallback
    let finalTestAddressId: string | undefined = testAddressId;
    if (!finalTestAddressId) {
      console.log('No test address ID provided, attempting to extract from address...');
      const extractedId = extractTestAddressId(testAddress);
      if (!extractedId) {
        console.log(`Failed to extract UUID from test address: ${testAddress}`);
        return null;
      }
      finalTestAddressId = extractedId;
    }

    console.log(`Using test address ID: ${finalTestAddressId}`);

    // Call the existing Email Tester analysis API
    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/tools/email-tester/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        testAddress,
        testAddressId: finalTestAddressId
      })
    });

    if (!response.ok) {
      console.log(`Email analysis failed with status: ${response.status}`);
      return null;
    }

    const data = await response.json();

    if (data.success && data.analysis) {
      console.log('Email analysis completed successfully');
      return data.analysis;
    } else {
      console.log('Email analysis returned no results');
      return null;
    }
  } catch (error) {
    console.log(`Email analysis error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitKey = getRateLimitKey(request);
    const rateLimit = checkRateLimit(rateLimitKey);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          success: false,
          error: 'Rate limit exceeded. Please try again later.',
          resetTime: rateLimit.resetTime
        },
        { status: 429 }
      );
    }

    // Parse request body
    const body = await request.json();
    const testRequest: SmtpTestRequest = body;

    // Validate request
    const validation = validateSmtpTestRequest(testRequest);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Sanitize request
    const sanitizedRequest = sanitizeSmtpTestRequest(testRequest);

    console.log(`SMTP test request: ${sanitizedRequest.testMode} mode for ${sanitizedRequest.config.server}`);

    // Perform SMTP test
    const smtpResult = await smtpService.testSmtpConnection({
      config: sanitizedRequest.config,
      testMode: sanitizedRequest.testMode,
      recipient: sanitizedRequest.recipient
    });

    // Prepare response
    const result: SmtpTestResult = {
      success: smtpResult.success,
      messageId: smtpResult.messageId,
      logs: smtpResult.logs.join('\n'),
      error: smtpResult.error,
      testAddress: smtpResult.testAddress
    };

    // For auto mode, attempt email authentication analysis
    if (smtpResult.success && sanitizedRequest.testMode === 'auto' && smtpResult.testAddress) {
      console.log('Attempting email authentication analysis...');

      // Wait a moment for the email to be processed
      await new Promise(resolve => setTimeout(resolve, 2000));

      const analysisResult = await analyzeEmailAuthentication(smtpResult.testAddress, smtpResult.testAddressId);
      if (analysisResult) {
        result.analysisResults = analysisResult;
        console.log('Email authentication analysis completed');
      } else {
        console.log('Email authentication analysis not available yet');
      }
    }

    console.log(`SMTP test completed: ${smtpResult.success ? 'SUCCESS' : 'FAILED'}`);

    return NextResponse.json(result);

  } catch (error) {
    logError('SMTP Tester API', 'Unexpected error during SMTP test', error);

    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred during SMTP testing',
        logs: 'Internal server error - please try again later'
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to test SMTP connections.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to test SMTP connections.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to test SMTP connections.' },
    { status: 405 }
  );
}
