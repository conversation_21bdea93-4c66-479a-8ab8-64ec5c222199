'use client';

import React, { useRef, useEffect, useMemo } from 'react';

interface IframeEmailRendererProps {
  /**
   * The HTML content to render in the iframe
   */
  html: string;

  /**
   * Optional height for the iframe
   */
  height?: string | number;

  /**
   * Optional className for the iframe container
   */
  className?: string;

  /**
   * Optional array of web fonts to load in the iframe
   * Each font should be a Google Fonts URL or similar
   */
  webFonts?: string[];
}

/**
 * IframeEmailRenderer Component
 *
 * Renders email HTML content in an isolated iframe for the most accurate rendering
 * This prevents styles from the parent document from affecting the email content
 */
// Helper function to generate a simple content hash for memoization
function generateContentHash(content: string): string {
  // Simple hash function for client-side use
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString(36);
}

export default function IframeEmailRenderer({ html, height = '100%', className = '', webFonts = [] }: IframeEmailRendererProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Generate a content hash for memoization
  const contentHash = useMemo(() => generateContentHash(html + webFonts.join('')), [html, webFonts]);

  useEffect(() => {
    // Get the iframe element
    const iframe = iframeRef.current;
    if (!iframe) return;

    // Function to write HTML to the iframe
    const writeToIframe = () => {
      try {
        // Get the iframe document
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
        if (!iframeDoc) return;

        // Open the document for writing
        iframeDoc.open();

        // Write the HTML content with a basic reset
        iframeDoc.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, minimum-scale=1">
              <meta name="format-detection" content="telephone=no">
              ${webFonts.map(font => `<link href="${font}" rel="stylesheet">`).join('')}
              <style>
                /* Minimal reset to ensure proper rendering */
                body {
                  margin: 0;
                  padding: 0;
                  font-family: Arial, sans-serif;
                  line-height: 1.4;
                  overflow: hidden;
                }

                /* Ensure images don't overflow */
                img {
                  max-width: 100%;
                  height: auto;
                }

                /* Ensure tables don't overflow */
                table {
                  max-width: 100%;
                }

                /* Default link styling */
                a:not([style]) {
                  color: #0000EE;
                  text-decoration: underline;
                }

                /* Print-specific styles */
                @media print {
                  body {
                    margin: 0;
                    padding: 0;
                    width: 100%;
                  }

                  /* Ensure background colors and images print */
                  * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                  }

                  /* Ensure links show their URLs when printed */
                  a[href]:after {
                    content: " (" attr(href) ")";
                    font-size: 0.8em;
                    font-weight: normal;
                    color: #333;
                  }

                  /* Don't show URL for images or internal/javascript links */
                  a[href^="#"]:after,
                  a[href^="javascript:"]:after,
                  a[href*="mailto:"]:after,
                  a[href$=".jpg"]:after,
                  a[href$=".jpeg"]:after,
                  a[href$=".png"]:after,
                  a[href$=".gif"]:after {
                    content: "";
                  }
                }
              </style>
            </head>
            <body>
              ${html}
            </body>
          </html>
        `);

        // Close the document
        iframeDoc.close();

        // Adjust iframe height to content if needed
        if (height === 'auto') {
          const body = iframeDoc.body;
          const html = iframeDoc.documentElement;

          const height = Math.max(
            body.scrollHeight,
            body.offsetHeight,
            html.clientHeight,
            html.scrollHeight,
            html.offsetHeight
          );

          iframe.style.height = `${height}px`;
        }
      } catch (error) {
        console.error('Error rendering email in iframe:', error);
      }
    };

    // Write to the iframe
    writeToIframe();

    // Add a load event listener to ensure content is written after iframe loads
    iframe.addEventListener('load', writeToIframe);

    // Cleanup
    return () => {
      iframe.removeEventListener('load', writeToIframe);
    };
  }, [contentHash, height]);

  return (
    <div className={`iframe-email-container ${className}`} style={{ width: '100%', overflow: 'hidden' }}>
      <iframe
        ref={iframeRef}
        title="Email Content"
        style={{
          width: '100%',
          height: typeof height === 'number' ? `${height}px` : height,
          border: 'none',
          overflow: 'hidden'
        }}
        sandbox="allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-scripts"
      />
    </div>
  );
}
