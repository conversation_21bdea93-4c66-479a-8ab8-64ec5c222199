'use client';

import RateLimitConfiguration from '@/components/admin/RateLimitConfiguration';

/**
 * Rate Limit Configuration Page for Secure Admin Portal.
 */
export default function RateLimitConfigPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Rate Limit Configuration</h1>
        <p className="mt-2 text-sm text-gray-600">
          Configure and manage dynamic rate limiting parameters for all VanishPost endpoints. Changes take effect immediately.
        </p>
      </div>

      <RateLimitConfiguration />
    </div>
  );
}
