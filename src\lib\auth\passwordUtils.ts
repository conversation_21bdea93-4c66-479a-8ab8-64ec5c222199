/**
 * Password utilities for secure authentication
 */
import bcrypt from 'bcrypt';

// Number of salt rounds for bcrypt
const SALT_ROUNDS = 10;

/**
 * Hash a password using bcrypt
 * @param password The plain text password to hash
 * @returns The hashed password
 */
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, SALT_ROUNDS);
}

/**
 * Compare a plain text password with a hashed password
 * @param plainPassword The plain text password to check
 * @param hashedPassword The hashed password to compare against
 * @returns True if the passwords match, false otherwise
 */
export async function comparePassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(plainPassword, hashedPassword);
}

/**
 * Generate a hash for the admin password to be stored in environment variables
 * This is a utility function to be used during setup
 * @param password The admin password to hash
 * @returns The hashed password
 */
export async function generateAdminPasswordHash(password: string): Promise<string> {
  const hashedPassword = await hash<PERSON>assword(password);
  console.log('Hashed admin password:', hashedPassword);
  return hashedPassword;
}
