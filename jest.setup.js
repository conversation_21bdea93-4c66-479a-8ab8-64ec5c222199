// Learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Add TextEncoder and TextDecoder to the global scope for JSDOM
const { TextEncoder, TextDecoder } = require('util');
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock NodeCache
jest.mock('node-cache', () => {
  return jest.fn().mockImplementation(() => {
    const cache = new Map();
    return {
      get: jest.fn(key => cache.get(key)),
      set: jest.fn((key, value) => {
        cache.set(key, value);
        return true;
      }),
      flushAll: jest.fn(() => {
        cache.clear();
      }),
      keys: jest.fn(() => Array.from(cache.keys())),
      getStats: jest.fn(() => ({ hits: 0, misses: 0 }))
    };
  });
});
