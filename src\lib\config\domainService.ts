/**
 * Domain management service for Fademail
 *
 * This service provides functions to manage domains and their settings
 */
import { createServerSupabaseClient } from '@/lib/supabase';
import NodeCache from 'node-cache';
import { DomainConfig, DomainSettings } from './types';
import { logInfo, logError } from '@/lib/logging';
import { Json } from '@/lib/database.types';

// Cache domain configuration for 5 minutes
export const domainCache = new NodeCache({ stdTTL: 300, checkperiod: 60 });

// Default domain settings
const defaultDomainSettings: DomainSettings = {
  isDefault: false,
  weight: 100,
  features: {
    adsEnabled: true,
    analyticsEnabled: true,
    autoRefreshEnabled: true
  }
};

/**
 * Convert JSON data to DomainSettings with proper type safety
 */
function jsonToDomainSettings(json: Json): DomainSettings {
  // If json is null or not an object, return default settings
  if (!json || typeof json !== 'object' || Array.isArray(json)) {
    return { ...defaultDomainSettings };
  }

  // Cast to any to access properties
  const settings = json as any;

  // Create a properly structured DomainSettings object
  return {
    isDefault: typeof settings.isDefault === 'boolean' ? settings.isDefault : defaultDomainSettings.isDefault,
    weight: typeof settings.weight === 'number' ? settings.weight : defaultDomainSettings.weight,
    features: {
      adsEnabled: settings.features?.adsEnabled ?? defaultDomainSettings.features.adsEnabled,
      analyticsEnabled: settings.features?.analyticsEnabled ?? defaultDomainSettings.features.analyticsEnabled,
      autoRefreshEnabled: settings.features?.autoRefreshEnabled ?? defaultDomainSettings.features.autoRefreshEnabled
    }
  };
}

/**
 * Get all domains
 *
 * @returns Array of domain configurations
 */
export async function getAllDomains(): Promise<DomainConfig[]> {
  try {
    // Check cache first
    const cacheKey = 'domains:all';
    const cachedValue = domainCache.get<DomainConfig[]>(cacheKey);
    if (cachedValue !== undefined) {
      return cachedValue;
    }

    // Get from database
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('domain_config')
      .select('*')
      .order('domain');

    if (error) {
      logError('domain', 'Error getting all domains', { error });
      return [];
    }

    // Map database results to DomainConfig objects
    const domains: DomainConfig[] = data.map(item => ({
      domain: item.domain,
      isActive: item.is_active,
      settings: jsonToDomainSettings(item.settings),
      createdAt: item.created_at,
      updatedAt: item.updated_at
    }));

    // Cache the domains
    domainCache.set(cacheKey, domains);
    return domains;
  } catch (error) {
    logError('domain', 'Error getting all domains', { error });
    return [];
  }
}

/**
 * Get active domains
 *
 * @returns Array of active domain configurations
 */
export async function getActiveDomains(): Promise<DomainConfig[]> {
  try {
    // Check cache first
    const cacheKey = 'domains:active';
    const cachedValue = domainCache.get<DomainConfig[]>(cacheKey);
    if (cachedValue !== undefined) {
      return cachedValue;
    }

    // Get from database
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('domain_config')
      .select('*')
      .eq('is_active', true)
      .order('domain');

    if (error) {
      logError('domain', 'Error getting active domains', { error });
      return [];
    }

    // Map database results to DomainConfig objects
    const domains: DomainConfig[] = data.map(item => ({
      domain: item.domain,
      isActive: item.is_active,
      settings: jsonToDomainSettings(item.settings),
      createdAt: item.created_at,
      updatedAt: item.updated_at
    }));

    // Cache the domains
    domainCache.set(cacheKey, domains);
    return domains;
  } catch (error) {
    logError('domain', 'Error getting active domains', { error });
    return [];
  }
}

/**
 * Get a domain by name
 *
 * @param domain The domain name
 * @returns The domain configuration or null if not found
 */
export async function getDomain(domain: string): Promise<DomainConfig | null> {
  try {
    // Check cache first
    const cacheKey = `domain:${domain}`;
    const cachedValue = domainCache.get<DomainConfig>(cacheKey);
    if (cachedValue !== undefined) {
      return cachedValue;
    }

    // Get from database
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('domain_config')
      .select('*')
      .eq('domain', domain)
      .single();

    if (error || !data) {
      return null;
    }

    // Map database result to DomainConfig object
    const domainConfig: DomainConfig = {
      domain: data.domain,
      isActive: data.is_active,
      settings: jsonToDomainSettings(data.settings),
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };

    // Cache the domain
    domainCache.set(cacheKey, domainConfig);
    return domainConfig;
  } catch (error) {
    logError('domain', `Error getting domain: ${domain}`, { error });
    return null;
  }
}

/**
 * Get the default domain
 *
 * @returns The default domain configuration or null if not found
 */
export async function getDefaultDomain(): Promise<DomainConfig | null> {
  try {
    // Check cache first
    const cacheKey = 'domain:default';
    const cachedValue = domainCache.get<DomainConfig>(cacheKey);
    if (cachedValue !== undefined) {
      return cachedValue;
    }

    // Get all active domains
    const domains = await getActiveDomains();

    // Find the default domain
    const defaultDomain = domains.find(domain => domain.settings.isDefault);

    if (!defaultDomain) {
      // If no default domain is found, return the first active domain
      if (domains.length > 0) {
        domainCache.set(cacheKey, domains[0]);
        return domains[0];
      }
      return null;
    }

    // Cache the default domain
    domainCache.set(cacheKey, defaultDomain);
    return defaultDomain;
  } catch (error) {
    logError('domain', 'Error getting default domain', { error });
    return null;
  }
}

/**
 * Add a new domain
 *
 * @param domain The domain name
 * @param isActive Whether the domain is active
 * @param settings The domain settings
 * @returns The new domain configuration or null if failed
 */
export async function addDomain(
  domain: string,
  isActive: boolean = true,
  settings: Partial<DomainSettings> = {}
): Promise<DomainConfig | null> {
  try {
    const supabase = createServerSupabaseClient();

    // Merge with default settings
    const mergedSettings: DomainSettings = {
      ...defaultDomainSettings,
      ...settings
    };

    // If this is the first domain, make it the default
    if ((await getAllDomains()).length === 0) {
      mergedSettings.isDefault = true;
    }

    const now = new Date().toISOString();

    // Convert DomainSettings to Json for database storage
    const settingsJson = mergedSettings as unknown as Json;

    const { data, error } = await supabase
      .from('domain_config')
      .insert({
        domain,
        is_active: isActive,
        settings: settingsJson,
        created_at: now,
        updated_at: now
      })
      .select()
      .single();

    if (error || !data) {
      logError('domain', `Error adding domain: ${domain}`, { error });
      return null;
    }

    // Map database result to DomainConfig object
    const domainConfig: DomainConfig = {
      domain: data.domain,
      isActive: data.is_active,
      settings: jsonToDomainSettings(data.settings),
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };

    // Clear domain caches
    clearDomainCache();

    logInfo('domain', `Added domain: ${domain}`, { domainConfig });
    return domainConfig;
  } catch (error) {
    logError('domain', `Error adding domain: ${domain}`, { error });
    return null;
  }
}

/**
 * Update a domain
 *
 * @param domain The domain name
 * @param isActive Whether the domain is active
 * @param settings The domain settings
 * @returns The updated domain configuration or null if failed
 */
export async function updateDomain(
  domain: string,
  isActive?: boolean,
  settings?: Partial<DomainSettings>
): Promise<DomainConfig | null> {
  try {
    // Get the current domain
    const currentDomain = await getDomain(domain);

    if (!currentDomain) {
      logError('domain', `Domain not found: ${domain}`);
      return null;
    }

    const supabase = createServerSupabaseClient();

    // Prepare update object
    const updates: any = {
      updated_at: new Date().toISOString()
    };

    if (isActive !== undefined) {
      updates.is_active = isActive;
    }

    if (settings) {
      // Merge settings and convert to Json for database storage
      const mergedSettings = {
        ...currentDomain.settings,
        ...settings
      };
      updates.settings = mergedSettings as unknown as Json;
    }

    const { data, error } = await supabase
      .from('domain_config')
      .update(updates)
      .eq('domain', domain)
      .select()
      .single();

    if (error || !data) {
      logError('domain', `Error updating domain: ${domain}`, { error });
      return null;
    }

    // Map database result to DomainConfig object
    const domainConfig: DomainConfig = {
      domain: data.domain,
      isActive: data.is_active,
      settings: jsonToDomainSettings(data.settings),
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };

    // Clear domain caches
    clearDomainCache();

    logInfo('domain', `Updated domain: ${domain}`, { domainConfig });
    return domainConfig;
  } catch (error) {
    logError('domain', `Error updating domain: ${domain}`, { error });
    return null;
  }
}

/**
 * Delete a domain
 *
 * @param domain The domain name
 * @returns True if successful, false otherwise
 */
export async function deleteDomain(domain: string): Promise<boolean> {
  try {
    // Get the current domain
    const currentDomain = await getDomain(domain);

    if (!currentDomain) {
      logError('domain', `Domain not found: ${domain}`);
      return false;
    }

    // Check if this is the default domain
    if (currentDomain.settings.isDefault) {
      // Find another domain to make default
      const domains = await getAllDomains();
      const otherDomain = domains.find(d => d.domain !== domain);

      if (otherDomain) {
        // Make the other domain the default
        await updateDomain(otherDomain.domain, true, { isDefault: true });
      } else {
        logError('domain', `Cannot delete the only domain: ${domain}`);
        return false;
      }
    }

    const supabase = createServerSupabaseClient();

    const { error } = await supabase
      .from('domain_config')
      .delete()
      .eq('domain', domain);

    if (error) {
      logError('domain', `Error deleting domain: ${domain}`, { error });
      return false;
    }

    // Clear domain caches
    clearDomainCache();

    logInfo('domain', `Deleted domain: ${domain}`);
    return true;
  } catch (error) {
    logError('domain', `Error deleting domain: ${domain}`, { error });
    return false;
  }
}

/**
 * Set the default domain
 *
 * @param domain The domain name
 * @returns True if successful, false otherwise
 */
export async function setDefaultDomain(domain: string): Promise<boolean> {
  try {
    // Get the current domain
    const currentDomain = await getDomain(domain);

    if (!currentDomain) {
      logError('domain', `Domain not found: ${domain}`);
      return false;
    }

    const supabase = createServerSupabaseClient();

    // First, get all domains to update them individually
    const allDomains = await getAllDomains();

    // Update each domain to set isDefault to false
    for (const d of allDomains) {
      if (d.domain !== domain && d.settings.isDefault) {
        // Create settings with isDefault set to false
        const updatedSettings = {
          ...d.settings,
          isDefault: false
        };

        // Update the domain
        await supabase
          .from('domain_config')
          .update({
            settings: updatedSettings as unknown as Json,
            updated_at: new Date().toISOString()
          })
          .eq('domain', d.domain);
      }
    }

    // Then, set the default flag for the specified domain
    // Create merged settings and convert to Json for database storage
    const mergedSettings = {
      ...currentDomain.settings,
      isDefault: true
    };

    const { error } = await supabase
      .from('domain_config')
      .update({
        settings: mergedSettings as unknown as Json,
        updated_at: new Date().toISOString()
      })
      .eq('domain', domain);

    if (error) {
      logError('domain', `Error setting default domain: ${domain}`, { error });
      return false;
    }

    // Clear domain caches
    clearDomainCache();

    logInfo('domain', `Set default domain: ${domain}`);
    return true;
  } catch (error) {
    logError('domain', `Error setting default domain: ${domain}`, { error });
    return false;
  }
}

/**
 * Get a domain for email generation based on rotation settings
 *
 * @returns A domain name to use for email generation
 */
export async function getDomainForEmailGeneration(): Promise<string> {
  try {
    // Get all active domains
    const domains = await getActiveDomains();

    if (domains.length === 0) {
      // No domains found, return a default
      return process.env.EMAIL_TESTER_DOMAIN || 'fademail.site';
    }

    if (domains.length === 1) {
      // Only one domain, return it
      return domains[0].domain;
    }

    // Multiple domains, use weighted random selection
    const totalWeight = domains.reduce((sum, domain) => sum + domain.settings.weight, 0);
    let random = Math.random() * totalWeight;

    for (const domain of domains) {
      random -= domain.settings.weight;
      if (random <= 0) {
        return domain.domain;
      }
    }

    // Fallback to the first domain
    return domains[0].domain;
  } catch (error) {
    logError('domain', 'Error getting domain for email generation', { error });
    return process.env.EMAIL_TESTER_DOMAIN || 'fademail.site';
  }
}

/**
 * Clear the domain cache
 */
export function clearDomainCache(): void {
  domainCache.flushAll();
  logInfo('domain', 'Cleared domain cache');
}

// Export the service object for compatibility with productionSafeguards
export const domainService = {
  getAllDomains,
  getActiveDomains,
  getDomain,
  getDefaultDomain,
  addDomain,
  updateDomain,
  deleteDomain,
  setDefaultDomain,
  getDomainForEmailGeneration,
  clearDomainCache
};

export default domainService;
