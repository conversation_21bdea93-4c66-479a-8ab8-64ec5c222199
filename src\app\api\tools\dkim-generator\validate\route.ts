import { NextRequest, NextResponse } from 'next/server';
import { validateDkimRecord, validateDomainFormat, sanitizeDomain } from '@/lib/tools/shared/dnsValidation';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { DkimValidationRequest, DkimValidationResponse } from '@/types/dkim';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body: DkimValidationRequest = await request.json();
    const { domain, selector, expectedPublicKey } = body;

    // Validate inputs
    if (!domain || !selector) {
      return NextResponse.json(
        {
          success: false,
          message: 'Missing required fields: domain and selector are required',
        },
        { status: 400 }
      );
    }

    // Sanitize and validate domain
    const cleanDomain = sanitizeDomain(domain);
    if (!validateDomainFormat(cleanDomain)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid domain format',
        },
        { status: 400 }
      );
    }

    // Validate selector format
    const selectorRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,62}[a-zA-Z0-9]$|^[a-zA-Z0-9]$/;
    if (!selectorRegex.test(selector)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid selector format. Use alphanumeric characters and hyphens only.',
        },
        { status: 400 }
      );
    }

    console.log(`Validating DKIM record for ${selector}._domainkey.${cleanDomain}`);

    // Perform DNS validation
    const validationResult = await validateDkimRecord(cleanDomain, selector, expectedPublicKey);

    // Store validation history in database
    const supabase = await createServerSupabaseClient();

    // Try to find the corresponding generated record
    const { data: generatedRecord } = await supabase
      .from('generated_records')
      .select('id')
      .eq('domain', cleanDomain)
      .eq('selector', selector)
      .eq('record_type', 'dkim')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    // Store validation history
    const { error: historyError } = await supabase
      .from('dns_validation_history')
      .insert({
        record_id: generatedRecord?.id || null,
        validation_type: 'dkim',
        domain: cleanDomain,
        record_name: validationResult.recordName,
        expected_value: expectedPublicKey || null,
        actual_values: validationResult.records,
        is_valid: validationResult.isValid,
        errors: validationResult.errors,
      });

    if (historyError) {
      console.warn('Failed to store validation history:', historyError);
      // Don't fail the request if history storage fails
    }

    // Update the generated record validation status if found
    if (generatedRecord?.id) {
      const { error: updateError } = await supabase
        .from('generated_records')
        .update({
          validated_at: new Date().toISOString(),
          validation_status: validationResult.isValid ? 'valid' : 'invalid',
          validation_errors: validationResult.errors,
        })
        .eq('id', generatedRecord.id);

      if (updateError) {
        console.warn('Failed to update record validation status:', updateError);
      }
    }

    // Prepare response
    const response: DkimValidationResponse = {
      success: true,
      data: {
        exists: validationResult.exists,
        records: validationResult.records,
        isValid: validationResult.isValid,
        errors: validationResult.errors,
        lastChecked: validationResult.lastChecked,
        publicKeyMatch: expectedPublicKey ? validationResult.isValid : undefined,
      },
    };

    console.log(`DKIM validation completed for ${cleanDomain}: ${validationResult.isValid ? 'VALID' : 'INVALID'}`);
    return NextResponse.json(response);

  } catch (error) {
    console.error('DKIM Validation API Error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Failed to validate DKIM record',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    {
      success: false,
      message: 'Method not allowed. Use POST to validate DKIM records.',
    },
    { status: 405 }
  );
}
