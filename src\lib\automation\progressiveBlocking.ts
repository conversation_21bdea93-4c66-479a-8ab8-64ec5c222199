/**
 * Progressive Blocking System
 * Implements escalating responses to repeated abuse patterns
 * Automatically escalates from warnings to temporary blocks to permanent blocks
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface ProgressiveAction {
  level: 1 | 2 | 3 | 4 | 5;
  name: string;
  description: string;
  duration?: number; // milliseconds
  action: 'log' | 'warn' | 'temp_block' | 'long_block' | 'permanent_block';
}

interface ThreatHistory {
  identifier: string;
  identifierType: 'ip' | 'session' | 'ip_range';
  violations: number;
  lastViolation: Date;
  currentLevel: number;
  totalScore: number;
}

// Progressive escalation levels
const ESCALATION_LEVELS: ProgressiveAction[] = [
  {
    level: 1,
    name: 'First Warning',
    description: 'Log suspicious activity for monitoring',
    action: 'log'
  },
  {
    level: 2,
    name: 'Second Warning',
    description: 'Log with elevated priority and flag for review',
    action: 'warn'
  },
  {
    level: 3,
    name: 'Temporary Block',
    description: 'Block for 1 hour to cool down',
    duration: 60 * 60 * 1000, // 1 hour
    action: 'temp_block'
  },
  {
    level: 4,
    name: 'Extended Block',
    description: 'Block for 24 hours due to repeated violations',
    duration: 24 * 60 * 60 * 1000, // 24 hours
    action: 'long_block'
  },
  {
    level: 5,
    name: 'Permanent Block',
    description: 'Permanent block due to persistent abuse',
    action: 'permanent_block'
  }
];

interface ViolationEvent {
  identifier: string;
  identifierType: 'ip' | 'session' | 'ip_range';
  violationType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
  metadata?: any;
}

/**
 * Process a violation and determine appropriate response
 */
export async function processViolation(violation: ViolationEvent): Promise<{
  action: ProgressiveAction;
  applied: boolean;
  reason: string;
}> {
  try {
    // Get threat history
    const history = await getThreatHistory(violation.identifier, violation.identifierType);
    
    // Calculate violation score
    const violationScore = calculateViolationScore(violation);
    
    // Update threat history
    const updatedHistory = await updateThreatHistory(history, violationScore);
    
    // Determine escalation level
    const escalationLevel = determineEscalationLevel(updatedHistory, violation);
    const action = ESCALATION_LEVELS[escalationLevel - 1];
    
    // Apply the action
    const applied = await applyProgressiveAction(
      violation.identifier,
      violation.identifierType,
      action,
      violation,
      updatedHistory
    );
    
    return {
      action,
      applied,
      reason: `Level ${escalationLevel}: ${action.description} (${updatedHistory.violations} violations, score: ${updatedHistory.totalScore})`
    };
    
  } catch (error) {
    console.error('Error processing violation:', error);
    return {
      action: ESCALATION_LEVELS[0],
      applied: false,
      reason: 'Error processing violation'
    };
  }
}

/**
 * Get threat history for an identifier
 */
async function getThreatHistory(
  identifier: string,
  identifierType: 'ip' | 'session' | 'ip_range'
): Promise<ThreatHistory> {
  try {
    const { data, error } = await supabase
      .from('threat_history')
      .select('*')
      .eq('identifier', identifier)
      .eq('identifier_type', identifierType)
      .single();

    if (error || !data) {
      // Create new history record
      return {
        identifier,
        identifierType,
        violations: 0,
        lastViolation: new Date(),
        currentLevel: 0,
        totalScore: 0
      };
    }

    return {
      identifier: data.identifier,
      identifierType: data.identifier_type,
      violations: data.violations,
      lastViolation: new Date(data.last_violation),
      currentLevel: data.current_level,
      totalScore: data.total_score
    };
  } catch (error) {
    console.error('Error getting threat history:', error);
    return {
      identifier,
      identifierType,
      violations: 0,
      lastViolation: new Date(),
      currentLevel: 0,
      totalScore: 0
    };
  }
}

/**
 * Calculate violation score based on type and severity
 */
function calculateViolationScore(violation: ViolationEvent): number {
  let baseScore = 1;

  // Severity multiplier
  switch (violation.severity) {
    case 'critical': baseScore *= 4; break;
    case 'high': baseScore *= 3; break;
    case 'medium': baseScore *= 2; break;
    case 'low': baseScore *= 1; break;
  }

  // Confidence multiplier
  baseScore *= violation.confidence;

  // Violation type multiplier
  switch (violation.violationType) {
    case 'ip_rotation': baseScore *= 3; break;
    case 'facebook_abuse': baseScore *= 2.5; break;
    case 'burst_protection': baseScore *= 2; break;
    case 'rate_limit': baseScore *= 1.5; break;
    default: baseScore *= 1; break;
  }

  return Math.round(baseScore * 10) / 10; // Round to 1 decimal
}

/**
 * Update threat history with new violation
 */
async function updateThreatHistory(
  history: ThreatHistory,
  violationScore: number
): Promise<ThreatHistory> {
  try {
    const now = new Date();
    const timeSinceLastViolation = now.getTime() - history.lastViolation.getTime();
    const hoursSinceLastViolation = timeSinceLastViolation / (1000 * 60 * 60);

    // Decay score if it's been a while since last violation
    let decayedScore = history.totalScore;
    if (hoursSinceLastViolation > 24) {
      decayedScore *= 0.8; // 20% decay after 24 hours
    }
    if (hoursSinceLastViolation > 168) { // 1 week
      decayedScore *= 0.5; // Additional 50% decay after 1 week
    }

    const updatedHistory: ThreatHistory = {
      ...history,
      violations: history.violations + 1,
      lastViolation: now,
      totalScore: decayedScore + violationScore
    };

    // Save to database
    const { error } = await supabase
      .from('threat_history')
      .upsert({
        identifier: updatedHistory.identifier,
        identifier_type: updatedHistory.identifierType,
        violations: updatedHistory.violations,
        last_violation: updatedHistory.lastViolation.toISOString(),
        current_level: updatedHistory.currentLevel,
        total_score: updatedHistory.totalScore,
        updated_at: now.toISOString()
      });

    if (error) {
      console.error('Error updating threat history:', error);
    }

    return updatedHistory;
  } catch (error) {
    console.error('Error in updateThreatHistory:', error);
    return history;
  }
}

/**
 * Determine escalation level based on history and current violation
 */
function determineEscalationLevel(
  history: ThreatHistory,
  violation: ViolationEvent
): number {
  // Base level on total score
  let level = 1;

  if (history.totalScore >= 20) level = 5; // Permanent block
  else if (history.totalScore >= 12) level = 4; // Extended block
  else if (history.totalScore >= 6) level = 3; // Temporary block
  else if (history.totalScore >= 3) level = 2; // Warning
  else level = 1; // Log only

  // Immediate escalation for critical violations
  if (violation.severity === 'critical' && violation.confidence >= 0.9) {
    level = Math.max(level, 3); // At least temporary block
  }

  // Cap at maximum level
  return Math.min(level, 5);
}

/**
 * Apply the progressive action
 */
async function applyProgressiveAction(
  identifier: string,
  identifierType: 'ip' | 'session' | 'ip_range',
  action: ProgressiveAction,
  violation: ViolationEvent,
  history: ThreatHistory
): Promise<boolean> {
  try {
    const now = new Date();

    // Log the action
    await supabase
      .from('security_events')
      .insert({
        event_type: 'progressive_action',
        session_id: identifierType === 'session' ? identifier : null,
        ip_address: identifierType === 'ip' ? identifier : null,
        severity: action.level >= 3 ? 'high' : 'medium',
        description: `Progressive blocking level ${action.level}: ${action.description}`,
        metadata: {
          identifier,
          identifier_type: identifierType,
          action_level: action.level,
          action_name: action.name,
          violation_type: violation.violationType,
          violation_score: calculateViolationScore(violation),
          total_score: history.totalScore,
          violations_count: history.violations
        },
        action_taken: action.action
      });

    // Apply blocking actions
    if (action.action === 'temp_block' || action.action === 'long_block') {
      const expiresAt = action.duration 
        ? new Date(now.getTime() + action.duration)
        : null;

      if (identifierType === 'session') {
        await supabase
          .from('blocked_sessions')
          .insert({
            session_id: identifier,
            reason: `Progressive blocking: ${action.description}`,
            blocked_by: 'Progressive Blocking System',
            expires_at: expiresAt?.toISOString(),
            is_active: true
          });
      } else if (identifierType === 'ip' || identifierType === 'ip_range') {
        const ipRange = identifierType === 'ip' ? `${identifier}/32` : identifier;
        await supabase
          .from('blocked_ip_ranges')
          .insert({
            ip_range: ipRange,
            reason: `Progressive blocking: ${action.description}`,
            blocked_by: 'Progressive Blocking System',
            expires_at: expiresAt?.toISOString(),
            is_active: true
          });
      }
    } else if (action.action === 'permanent_block') {
      if (identifierType === 'session') {
        await supabase
          .from('blocked_sessions')
          .insert({
            session_id: identifier,
            reason: `Permanent block: ${action.description}`,
            blocked_by: 'Progressive Blocking System',
            expires_at: null, // Permanent
            is_active: true
          });
      } else if (identifierType === 'ip' || identifierType === 'ip_range') {
        const ipRange = identifierType === 'ip' ? `${identifier}/32` : identifier;
        await supabase
          .from('blocked_ip_ranges')
          .insert({
            ip_range: ipRange,
            reason: `Permanent block: ${action.description}`,
            blocked_by: 'Progressive Blocking System',
            expires_at: null, // Permanent
            is_active: true
          });
      }
    }

    // Update current level in history
    await supabase
      .from('threat_history')
      .update({ current_level: action.level })
      .eq('identifier', identifier)
      .eq('identifier_type', identifierType);

    console.log(`🚨 Progressive Action Applied: Level ${action.level} for ${identifierType} ${identifier}`);
    return true;

  } catch (error) {
    console.error('Error applying progressive action:', error);
    return false;
  }
}

/**
 * Check if an identifier should be escalated based on recent activity
 */
export async function checkForEscalation(
  identifier: string,
  identifierType: 'ip' | 'session' | 'ip_range'
): Promise<boolean> {
  try {
    const history = await getThreatHistory(identifier, identifierType);
    
    // Check if escalation is needed based on recent activity
    const recentThreshold = new Date(Date.now() - 60 * 60 * 1000); // Last hour
    
    const { count: recentViolations } = await supabase
      .from('security_events')
      .select('*', { count: 'exact', head: true })
      .eq(identifierType === 'session' ? 'session_id' : 'ip_address', identifier)
      .gte('created_at', recentThreshold.toISOString())
      .in('severity', ['high', 'critical']);

    // Escalate if there are multiple recent violations
    if ((recentViolations || 0) >= 3) {
      await processViolation({
        identifier,
        identifierType,
        violationType: 'repeated_violations',
        severity: 'high',
        confidence: 0.9,
        metadata: { recent_violations: recentViolations }
      });
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking for escalation:', error);
    return false;
  }
}

/**
 * Get escalation statistics
 */
export async function getEscalationStats(timeWindowHours: number = 24): Promise<{
  totalActions: number;
  actionsByLevel: Record<number, number>;
  topTargets: Array<{ identifier: string; level: number; violations: number }>;
}> {
  try {
    const timeWindow = new Date(Date.now() - timeWindowHours * 60 * 60 * 1000);

    const { data: actions } = await supabase
      .from('security_events')
      .select('metadata')
      .eq('event_type', 'progressive_action')
      .gte('created_at', timeWindow.toISOString());

    const actionsByLevel: Record<number, number> = {};
    const targetCounts: Record<string, { level: number; violations: number }> = {};

    actions?.forEach(action => {
      const level = action.metadata?.action_level || 1;
      const identifier = action.metadata?.identifier || 'unknown';
      
      actionsByLevel[level] = (actionsByLevel[level] || 0) + 1;
      
      if (!targetCounts[identifier]) {
        targetCounts[identifier] = { level: 0, violations: 0 };
      }
      targetCounts[identifier].level = Math.max(targetCounts[identifier].level, level);
      targetCounts[identifier].violations++;
    });

    const topTargets = Object.entries(targetCounts)
      .map(([identifier, data]) => ({ identifier, ...data }))
      .sort((a, b) => b.level - a.level || b.violations - a.violations)
      .slice(0, 10);

    return {
      totalActions: actions?.length || 0,
      actionsByLevel,
      topTargets
    };
  } catch (error) {
    console.error('Error getting escalation stats:', error);
    return {
      totalActions: 0,
      actionsByLevel: {},
      topTargets: []
    };
  }
}

export default {
  processViolation,
  checkForEscalation,
  getEscalationStats,
  ESCALATION_LEVELS
};
