Below is a detailed, step-by-step guide in Markdown format for implementing the Email Deliverability Testing Tool in your Next.js, React, and TypeScript application, VanishPost. These instructions are designed to be clear and actionable for your AI code assistant, with an emphasis on critical aspects like retrieving Mailauth requirements from your database or email headers. I’ve included explanations for each step and referenced the Mailauth documentation where applicable.

---

# Step-by-Step Guide to Implement the Email Deliverability Testing Tool

This guide will help you build an Email Deliverability Testing Tool that generates test email addresses, analyzes incoming emails for authentication (SPF, DKIM, DMARC), and provides a detailed report with a score and recommendations. The tool integrates with your existing Guerrilla email server, MySQL database, and the Deepseek AI API.

**Mailauth Documentation**: [https://github.com/postalsys/mailauth](https://github.com/postalsys/mailauth)

---

## Step 1: Set Up the Frontend
**Objective**: Build a user interface where users can initiate the test, see a unique test email address, and view the analysis report.

### Instructions
1. **Add Navigation**:
   - Open your main navigation component (e.g., `components/Navbar.tsx`).
   - Add a "Tools" dropdown menu if it doesn’t already exist.
   - Include an "Email Deliverability Test" option linking to `/tools/deliverability-test`.

2. **Create the Deliverability Test Page**:
   - Create a new file: `pages/tools/deliverability-test.tsx`.
   - Build a React component with the following features:
     - **Test Email Display**: Show a unique test email address (e.g., `<EMAIL>`).
     - **Status Indicator**: Display the current state (e.g., "Pending," "Analyzing," "Complete").
     - **Report Section**: Once analysis is complete, show:
       - Deliverability score (e.g., "8/10").
       - Pass/fail status for SPF, DKIM, and DMARC.
       - List of issues (e.g., "Missing DKIM signature").
       - Suggested DNS fixes (e.g., "Add TXT record: v=spf1 include:_spf.example.com ~all").
       - Implementation steps (e.g., "Log in to your DNS provider...").

3. **State Management**:
   - Use React’s `useState` or a state management library like Redux to track:
     - The generated test email address.
     - The analysis status.
     - The report data from the backend.

### Critical Aspects
- Ensure the test email address is unique by using a library like `uuid` (install with `npm install uuid`).
- Store the test address in your database (e.g., Supabase or MySQL) to link it to the analysis process.

---

## Step 2: Generate and Manage Test Email Addresses
**Objective**: Generate unique test email addresses and store them for tracking.

### Instructions
1. **Generate a Unique Address**:
   - Install `uuid` if not already installed: `npm install uuid`.
   - Create a function to generate the address:
     ```typescript
     import { v4 as uuidv4 } from 'uuid';

     function generateTestEmail() {
       return `delivtest-${uuidv4()}@fademail.site`;
     }
     ```

2. **Store the Address**:
   - Add the test email to your database (e.g., a `test_emails` table in MySQL or Supabase).
   - Example table structure:
     ```sql
     CREATE TABLE test_emails (
       id INT AUTO_INCREMENT PRIMARY KEY,
       email VARCHAR(255) NOT NULL,
       user_id VARCHAR(50), -- Optional, if tied to a user
       status ENUM('pending', 'analyzing', 'complete') DEFAULT 'pending',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
     );
     ```
   - Insert the email with a "pending" status when generated.

### Critical Aspects
- Ensure the email address is unique and tied to the user’s session or account (if authenticated).
- Use this address to later fetch the corresponding email data from the `guerrilla_mail` table.

---

## Step 3: Retrieve Email Data from MySQL
**Objective**: Fetch the raw email and metadata from your `guerrilla_mail` table for analysis.

### Instructions
1. **Understand the Table Structure**:
   - Your `guerrilla_mail` table contains:
     - `mail_id`: Unique identifier for the email.
     - `mail`: Raw email content (headers + body).
     - `ip_addr`: Client IP in binary format.
     - `return_path`: MAIL FROM address.

2. **Query the Database**:
   - Use a MySQL query to fetch the email data based on the test email address:
     ```sql
     SELECT mail, ip_addr, return_path
     FROM guerrilla_mail
     WHERE mail_to = '<EMAIL>'
     LIMIT 1;
     ```

3. **Convert Binary IP**:
   - The `ip_addr` field is binary (e.g., `0x905B6474000000000000000000000000`).
   - Convert it to a readable IP (e.g., `**************`) with this function:
     ```typescript
     function convertBinaryIp(binaryIp: Buffer): string {
       const hex = binaryIp.toString('hex').slice(-8); // Last 4 bytes for IPv4
       const ip = parseInt(hex, 16);
       return [
         (ip >> 24) & 255,
         (ip >> 16) & 255,
         (ip >> 8) & 255,
         ip & 255
       ].join('.');
     }
     ```

### Critical Aspects
- **Raw Email (`mail`)**: This is the full email including headers (`Received`, `DKIM-Signature`, etc.), required by Mailauth for parsing.
- **Client IP (`ip_addr`)**: Convert it accurately for SPF checks.
- **MAIL FROM (`return_path`)**: Use this as the sender address for SPF and DMARC alignment.
- **HELO Hostname**: Not stored directly; Mailauth can extract it from `Received` headers.

---

## Step 4: Integrate Mailauth for Authentication Checks
**Objective**: Use Mailauth to analyze SPF, DKIM, and DMARC based on the retrieved email data.

### Instructions
1. **Install Mailauth**:
   - Run: `npm install mailauth`.

2. **Analyze the Email**:
   - Use the `authenticate` function from Mailauth with the raw email and metadata:
     ```typescript
     import { authenticate } from 'mailauth';

     async function analyzeEmail(rawEmail: string, clientIp: string, returnPath: string) {
       const result = await authenticate(rawEmail, {
         ip: clientIp,           // From converted ip_addr
         sender: returnPath,     // From return_path
         trustReceived: true     // Extracts HELO from Received headers
       });
       return result;
     }
     ```

3. **Interpret Results**:
   - The `result` object includes:
     - `spf`: `{ pass: boolean, domain: string, ... }`
     - `dkim`: `{ pass: boolean, domain: string, ... }`
     - `dmarc`: `{ pass: boolean, policy: string, aligned: boolean, ... }`
   - Example usage:
     ```typescript
     const analysis = await analyzeEmail(rawEmail, clientIp, returnPath);
     console.log('SPF:', analysis.spf.pass);
     console.log('DKIM:', analysis.dkim.pass);
     console.log('DMARC:', analysis.dmarc.pass);
     ```

### Critical Aspects
- **Raw Email**: Must include all headers; Mailauth parses `Received`, `DKIM-Signature`, etc., from here.
- **Client IP**: Required for SPF checks; ensure correct conversion from `ip_addr`.
- **Sender**: Use `return_path` for SPF and DMARC alignment.
- **HELO**: Set `trustReceived: true` to let Mailauth infer it from headers if needed.

**Mailauth Documentation**: [https://github.com/postalsys/mailauth](https://github.com/postalsys/mailauth)

---

## Step 5: Integrate Deepseek AI API
**Objective**: Send Mailauth results to Deepseek AI for a deliverability score and recommendations.

### Instructions
1. **Prepare Data**:
   - Extract key details from Mailauth results:
     ```typescript
     const payload = {
       spf: { pass: analysis.spf.pass, domain: analysis.spf.domain },
       dkim: { pass: analysis.dkim.pass, domain: analysis.dkim.domain },
       dmarc: { policy: analysis.dmarc.policy, aligned: analysis.dmarc.aligned }
     };
     ```

2. **Make API Request**:
   - Send a POST request to the Deepseek AI API (replace `API_KEY` and `URL` with actual values):
     ```typescript
     const response = await fetch('https://api.deepseek.com/v1/analyze', {
       method: 'POST',
       headers: {
         'Content-Type': 'application/json',
         'Authorization': `Bearer API_KEY`
       },
       body: JSON.stringify(payload)
     });
     const aiResult = await response.json();
     ```

3. **Handle Response**:
   - Expect fields like:
     - `score`: Number (1-10).
     - `issues`: Array of strings (e.g., ["Missing SPF record"]).
     - `fixes`: Array of DNS records and steps.

### Critical Aspects
- Ensure the payload matches Deepseek’s expected format (check their API docs).
- Handle API errors gracefully (e.g., network issues, invalid responses).

---

## Step 6: Display the Report
**Objective**: Show the analysis results and AI recommendations in the frontend.

### Instructions
1. **Update the Frontend**:
   - In `pages/tools/deliverability-test.tsx`, fetch results from the API and render:
     ```typescript
     const [report, setReport] = useState(null);

     useEffect(() => {
       async function fetchReport() {
         const res = await fetch('/api/analyze', { method: 'POST', body: JSON.stringify({ mailId }) });
         const data = await res.json();
         setReport(data);
       }
       fetchReport();
     }, [mailId]);
     ```

2. **Render the Report**:
   - Display the data in a structured format:
     ```jsx
     {report && (
       <div>
         <h2>Deliverability Score: {report.score}/10</h2>
         <p>SPF: {report.spf.pass ? 'Pass' : 'Fail'}</p>
         <p>DKIM: {report.dkim.pass ? 'Pass' : 'Fail'}</p>
         <p>DMARC: {report.dmarc.pass ? 'Pass' : 'Fail'}</p>
         <h3>Issues</h3>
         <ul>{report.issues.map(issue => <li>{issue}</li>)}</ul>
         <h3>Fixes</h3>
         <pre>{report.fixes.join('\n')}</pre>
       </div>
     )}
     ```

### Critical Aspects
- Make the report actionable with copyable DNS records.
- Handle loading and error states (e.g., "Waiting for email...").

---

## Step 7: Backend API Route
**Objective**: Create a Next.js API route to handle email analysis.

### Instructions
1. **Create the API Route**:
   - File: `pages/api/analyze.ts`.
   - Implement the logic to fetch and analyze email data.

```typescript
import { NextApiRequest, NextApiResponse } from 'next';
import { authenticate } from 'mailauth';
import mysql from 'mysql2/promise';

function convertBinaryIp(binaryIp: Buffer): string {
  const hex = binaryIp.toString('hex').slice(-8);
  const ip = parseInt(hex, 16);
  return [(ip >> 24) & 255, (ip >> 16) & 255, (ip >> 8) & 255, ip & 255].join('.');
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { mailId } = req.body;

  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    user: 'root',
    password: 'ok',
    database: 'gmail_mail'
  });

  try {
    const [rows] = await connection.execute(
      'SELECT mail, ip_addr, return_path FROM guerrilla_mail WHERE mail_id = ?',
      [mailId]
    );

    if (!rows.length) {
      return res.status(404).json({ error: 'Email not found' });
    }

    const { mail, ip_addr, return_path } = rows[0];
    const clientIp = convertBinaryIp(ip_addr);

    const result = await authenticate(mail, {
      ip: clientIp,
      sender: return_path,
      trustReceived: true
    });

    // TODO: Add Deepseek AI API integration here

    res.status(200).json({
      score: 8, // Placeholder; replace with AI result
      spf: result.spf,
      dkim: result.dkim,
      dmarc: result.dmarc,
      issues: ['Placeholder issue'], // Replace with AI result
      fixes: ['Placeholder fix']     // Replace with AI result
    });
  } catch (error) {
    console.error('Analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze email' });
  } finally {
    await connection.end();
  }
}
```

---

## Additional Notes
- **IP Reputation**: Optionally integrate with APIs like Talos Intelligence for IP checks.
- **Spam Scoring**: Consider libraries like `spamassassin` or Deepseek AI if supported.
- **Error Handling**: Add robust checks for missing data or failed API calls.

---

This guide provides a complete process for your AI code assistant to implement the tool. Follow each step carefully, and refer to the Mailauth documentation for additional details: [https://github.com/postalsys/mailauth](https://github.com/postalsys/mailauth). Let me know if you need further clarification!