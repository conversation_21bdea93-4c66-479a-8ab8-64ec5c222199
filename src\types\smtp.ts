/**
 * SMTP Tester Tool Type Definitions
 *
 * TypeScript interfaces and types for the SMTP Tester Tool
 */

export interface SmtpConfig {
  server: string;
  port: number;
  encryption: 'none' | 'tls' | 'ssl';
  username: string;
  password: string;
  sender: string;
}

export interface SmtpTestRequest {
  config: SmtpConfig;
  testMode: 'auto' | 'custom';
  recipient?: string; // For custom mode
}

export interface SmtpTestResult {
  success: boolean;
  messageId?: string;
  logs: string;
  error?: string;
  testAddress?: string; // For auto mode
  testAddressId?: string; // Database ID for Email Tester integration
  analysisResults?: EmailAnalysisResult; // From existing Email Tester
}

export interface SmtpProvider {
  name: string;
  displayName: string;
  server: string;
  port: number;
  encryption: 'tls' | 'ssl';
  instructions: string;
  setupUrl?: string;
}

export interface SmtpFormData {
  server: string;
  port: string;
  encryption: 'none' | 'tls' | 'ssl';
  username: string;
  password: string;
  sender: string;
  testMode: 'auto' | 'custom';
  recipient: string;
}

export interface SmtpValidationErrors {
  server?: string;
  port?: string;
  username?: string;
  password?: string;
  sender?: string;
  recipient?: string;
}

// Re-export from existing Email Tester types for integration
export interface EmailAnalysisResult {
  spf?: {
    pass?: boolean;
    result?: string;
    reason?: string;
  };
  dkim?: {
    pass?: boolean;
    result?: string;
    reason?: string;
  };
  dmarc?: {
    pass?: boolean;
    result?: string;
    reason?: string;
  };
  mx?: {
    result?: string;
    info?: string;
  };
  reverseDns?: {
    result?: string;
    info?: string;
    hostname?: string;
  };
  analysis?: {
    spf?: {
      status?: string;
      details?: string;
    };
    dkim?: {
      status?: string;
      details?: string;
    };
    dmarc?: {
      status?: string;
      details?: string;
    };
    mx?: {
      status?: string;
      details?: string;
    };
    reverseDns?: {
      status?: string;
      details?: string;
    };
  };
  enhancedAuthResults?: {
    mx?: {
      result?: string;
      info?: string;
    };
    reverseDns?: {
      result?: string;
      info?: string;
      hostname?: string;
    };
  };
  score?: number;
  overallScore?: number;
  recommendations?: (string | RecommendationObject)[];
}

export interface RecommendationObject {
  category?: string;
  issue?: string;
  recommendation?: string;
  dnsTemplate?: string;
  implementationSteps?: string[];
  priority?: string;
}

export interface SmtpTestState {
  isLoading: boolean;
  result: SmtpTestResult | null;
  error: string | null;
  formData: SmtpFormData;
  validationErrors: SmtpValidationErrors;
  validationSuccess: boolean;
}
