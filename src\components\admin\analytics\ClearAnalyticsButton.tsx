'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/Button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Trash2, Alert<PERSON>riangle, RefreshCw } from 'lucide-react';

interface ClearAnalyticsData {
  analyticsEvents: number;
  sessionAnalytics: number;
  total: number;
}

interface ClearAnalyticsButtonProps {
  onDataCleared?: () => void;
}

/**
 * Clear Analytics Button Component
 * 
 * Provides functionality to clear all analytics data for testing purposes.
 * Includes confirmation dialog and shows what will be cleared.
 */
export default function ClearAnalyticsButton({ onDataCleared }: ClearAnalyticsButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<ClearAnalyticsData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  /**
   * Fetch current analytics data counts
   */
  const fetchAnalyticsCounts = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/analytics/clear');
      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to fetch analytics counts');
      }

      setAnalyticsData(data.counts);
      setShowConfirmation(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Clear all analytics data
   */
  const clearAnalyticsData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/management-portal-x7z9y2/analytics/clear', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to clear analytics data');
      }

      setSuccess(`Successfully cleared ${data.cleared.analyticsEvents} analytics events and ${data.cleared.sessionAnalytics} session records`);
      setShowConfirmation(false);
      setAnalyticsData(null);

      // Notify parent component that data was cleared
      if (onDataCleared) {
        onDataCleared();
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Cancel confirmation dialog
   */
  const cancelClear = () => {
    setShowConfirmation(false);
    setAnalyticsData(null);
    setError(null);
  };

  if (showConfirmation && analyticsData) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardHeader>
          <CardTitle className="text-red-800 flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Confirm Clear Analytics Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-white p-4 rounded-md border">
              <h4 className="font-medium text-gray-900 mb-2">Data to be cleared:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Analytics Events: <span className="font-medium">{analyticsData.analyticsEvents}</span></li>
                <li>• Session Analytics: <span className="font-medium">{analyticsData.sessionAnalytics}</span></li>
                <li className="font-medium text-red-600">• Total Records: {analyticsData.total}</li>
              </ul>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <p className="text-sm text-yellow-800">
                <strong>Warning:</strong> This action cannot be undone. All analytics data will be permanently deleted.
              </p>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            )}

            <div className="flex gap-3">
              <Button
                onClick={clearAnalyticsData}
                disabled={isLoading}
                variant="danger"
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                {isLoading ? 'Clearing...' : 'Yes, Clear All Data'}
              </Button>
              <Button
                onClick={cancelClear}
                disabled={isLoading}
                variant="outline"
              >
                Cancel
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Clear Analytics Data</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Clear all analytics data for testing purposes. This will remove all analytics events and session data.
          </p>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <p className="text-sm text-green-800">{success}</p>
            </div>
          )}

          <Button
            onClick={fetchAnalyticsCounts}
            disabled={isLoading}
            variant="secondary"
            className="flex items-center gap-2"
          >
            {isLoading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            {isLoading ? 'Loading...' : 'Clear Analytics Data'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
