'use client';

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface DialogPortalProps {
  children: React.ReactNode;
}

/**
 * DialogPortal renders its children in a portal at the document body level,
 * outside of any scrollable containers to ensure proper fixed positioning.
 */
export function DialogPortal({ children }: DialogPortalProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Only render the portal on the client side
  if (!mounted) return null;

  return createPortal(
    <div className="dialog-portal">{children}</div>,
    document.body
  );
}
