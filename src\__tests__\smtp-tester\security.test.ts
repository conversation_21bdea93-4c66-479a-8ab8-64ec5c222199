/**
 * SMTP Security Tests
 * 
 * Security-focused tests for SMTP tester functionality
 */

import { NextRequest } from 'next/server';
import { POST as smtpTesterPost } from '@/app/api/tools/smtp-tester/route';
import { sanitizeSmtpConfig, validateSmtpConfig } from '@/lib/tools/smtp-tester/validation';

// Mock dependencies
jest.mock('@/lib/tools/smtp-tester/smtpService', () => ({
  smtpService: {
    testSmtpConnection: jest.fn()
  }
}));

jest.mock('@/lib/logging', () => ({
  logError: jest.fn()
}));

describe('SMTP Security Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Input Sanitization', () => {
    it('should sanitize XSS attempts in server field', () => {
      const maliciousConfig = {
        server: '<script>alert("xss")</script>smtp.gmail.com',
        port: 587,
        encryption: 'tls' as const,
        username: '<EMAIL>',
        password: 'password123',
        sender: '<EMAIL>'
      };

      const sanitized = sanitizeSmtpConfig(maliciousConfig);
      expect(sanitized.server).toBe('scriptalert("xss")/scriptsmtp.gmail.com');
      expect(sanitized.server).not.toContain('<script>');
    });

    it('should sanitize SQL injection attempts', () => {
      const maliciousConfig = {
        server: "smtp.gmail.com'; DROP TABLE users; --",
        port: 587,
        encryption: 'tls' as const,
        username: "<EMAIL>'; DELETE FROM accounts; --",
        password: 'password123',
        sender: '<EMAIL>'
      };

      const sanitized = sanitizeSmtpConfig(maliciousConfig);
      expect(sanitized.server).not.toContain('DROP TABLE');
      expect(sanitized.username).not.toContain('DELETE FROM');
    });

    it('should sanitize command injection attempts', () => {
      const maliciousConfig = {
        server: 'smtp.gmail.com; rm -rf /',
        port: 587,
        encryption: 'tls' as const,
        username: '<EMAIL>',
        password: 'password123; cat /etc/passwd',
        sender: '<EMAIL>'
      };

      const sanitized = sanitizeSmtpConfig(maliciousConfig);
      expect(sanitized.server).not.toContain('; rm -rf');
      expect(sanitized.password).not.toContain('; cat /etc/passwd');
    });

    it('should handle Unicode and special characters safely', () => {
      const unicodeConfig = {
        server: 'smtp.gmail.com\u0000\u0001\u0002',
        port: 587,
        encryption: 'tls' as const,
        username: '<EMAIL>\u200B\u200C\u200D',
        password: 'password123',
        sender: '<EMAIL>'
      };

      const sanitized = sanitizeSmtpConfig(unicodeConfig);
      expect(sanitized.server).not.toContain('\u0000');
      expect(sanitized.username).not.toContain('\u200B');
    });
  });

  describe('Input Validation', () => {
    it('should reject extremely long server names', () => {
      const longServerConfig = {
        server: 'a'.repeat(1000) + '.com',
        port: 587,
        encryption: 'tls' as const,
        username: '<EMAIL>',
        password: 'password123',
        sender: '<EMAIL>'
      };

      const validation = validateSmtpConfig(longServerConfig);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Invalid SMTP server address');
    });

    it('should reject invalid port ranges', () => {
      const invalidPorts = [-1, 0, 65536, 99999, NaN, Infinity];

      invalidPorts.forEach(port => {
        const config = {
          server: 'smtp.gmail.com',
          port,
          encryption: 'tls' as const,
          username: '<EMAIL>',
          password: 'password123',
          sender: '<EMAIL>'
        };

        const validation = validateSmtpConfig(config);
        expect(validation.isValid).toBe(false);
        expect(validation.errors).toContain('Port must be between 1 and 65535');
      });
    });

    it('should reject malformed email addresses', () => {
      const malformedEmails = [
        'not-an-email',
        '@domain.com',
        'user@',
        '<EMAIL>',
        'user@domain',
        '<EMAIL>',
        '<EMAIL>',
        '<script>alert("xss")</script>@domain.com'
      ];

      malformedEmails.forEach(email => {
        const config = {
          server: 'smtp.gmail.com',
          port: 587,
          encryption: 'tls' as const,
          username: email,
          password: 'password123',
          sender: email
        };

        const validation = validateSmtpConfig(config);
        expect(validation.isValid).toBe(false);
      });
    });

    it('should reject empty or whitespace-only required fields', () => {
      const emptyFieldConfigs = [
        { server: '', field: 'server' },
        { server: '   ', field: 'server' },
        { username: '', field: 'username' },
        { username: '   ', field: 'username' },
        { password: '', field: 'password' },
        { sender: '', field: 'sender' },
        { sender: '   ', field: 'sender' }
      ];

      emptyFieldConfigs.forEach(({ field, ...overrides }) => {
        const config = {
          server: 'smtp.gmail.com',
          port: 587,
          encryption: 'tls' as const,
          username: '<EMAIL>',
          password: 'password123',
          sender: '<EMAIL>',
          ...overrides
        };

        const validation = validateSmtpConfig(config);
        expect(validation.isValid).toBe(false);
      });
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits on API endpoints', async () => {
      const validRequest = {
        config: {
          server: 'smtp.gmail.com',
          port: 587,
          encryption: 'tls',
          username: '<EMAIL>',
          password: 'password123',
          sender: '<EMAIL>'
        },
        testMode: 'auto'
      };

      const requests = Array(15).fill(null).map(() => 
        new NextRequest('http://localhost:3000/api/tools/smtp-tester', {
          method: 'POST',
          body: JSON.stringify(validRequest),
          headers: {
            'Content-Type': 'application/json',
            'x-forwarded-for': '*************' // Same IP for all requests
          }
        })
      );

      // Execute requests in parallel
      const responses = await Promise.all(
        requests.map(request => smtpTesterPost(request))
      );

      // At least some requests should be rate limited
      const rateLimitedResponses = responses.filter(response => response.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it('should allow requests from different IPs', async () => {
      const validRequest = {
        config: {
          server: 'smtp.gmail.com',
          port: 587,
          encryption: 'tls',
          username: '<EMAIL>',
          password: 'password123',
          sender: '<EMAIL>'
        },
        testMode: 'auto'
      };

      const requests = Array(5).fill(null).map((_, index) => 
        new NextRequest('http://localhost:3000/api/tools/smtp-tester', {
          method: 'POST',
          body: JSON.stringify(validRequest),
          headers: {
            'Content-Type': 'application/json',
            'x-forwarded-for': `192.168.1.${100 + index}` // Different IPs
          }
        })
      );

      const responses = await Promise.all(
        requests.map(request => smtpTesterPost(request))
      );

      // All requests from different IPs should be allowed (assuming no other rate limits)
      const successfulResponses = responses.filter(response => response.status !== 429);
      expect(successfulResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Password Security', () => {
    it('should not log passwords in error messages', async () => {
      const { logError } = require('@/lib/logging');
      const { smtpService } = require('@/lib/tools/smtp-tester/smtpService');
      
      smtpService.testSmtpConnection.mockRejectedValue(new Error('Authentication failed'));

      const requestWithPassword = {
        config: {
          server: 'smtp.gmail.com',
          port: 587,
          encryption: 'tls',
          username: '<EMAIL>',
          password: 'supersecretpassword123',
          sender: '<EMAIL>'
        },
        testMode: 'auto'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester', {
        method: 'POST',
        body: JSON.stringify(requestWithPassword),
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1'
        }
      });

      await smtpTesterPost(request);

      // Check that password is not logged
      const logCalls = logError.mock.calls;
      logCalls.forEach(call => {
        const logMessage = JSON.stringify(call);
        expect(logMessage).not.toContain('supersecretpassword123');
      });
    });

    it('should mask passwords in debug output', () => {
      const { maskPassword } = require('@/lib/tools/smtp-tester/utils');
      
      const password = 'supersecretpassword123';
      const masked = maskPassword(password);
      
      expect(masked).not.toBe(password);
      expect(masked).toContain('*');
      expect(masked.length).toBe(password.length);
    });
  });

  describe('SMTP Connection Security', () => {
    it('should enforce connection timeouts', () => {
      const nodemailer = require('nodemailer');
      const { SmtpService } = require('@/lib/tools/smtp-tester/smtpService');
      
      const smtpService = new SmtpService();
      const config = {
        server: 'smtp.gmail.com',
        port: 587,
        encryption: 'tls' as const,
        username: '<EMAIL>',
        password: 'password123',
        sender: '<EMAIL>'
      };

      // Mock createTransport to capture configuration
      const mockTransporter = { verify: jest.fn(), sendMail: jest.fn(), close: jest.fn() };
      nodemailer.createTransport.mockReturnValue(mockTransporter);

      smtpService.testSmtpConnection({ config, testMode: 'auto' });

      const transportConfig = nodemailer.createTransport.mock.calls[0][0];
      expect(transportConfig.connectionTimeout).toBe(10000);
      expect(transportConfig.greetingTimeout).toBe(5000);
      expect(transportConfig.socketTimeout).toBe(10000);
    });

    it('should use secure configurations for SSL/TLS', () => {
      const nodemailer = require('nodemailer');
      const { SmtpService } = require('@/lib/tools/smtp-tester/smtpService');
      
      const smtpService = new SmtpService();
      const sslConfig = {
        server: 'smtp.gmail.com',
        port: 465,
        encryption: 'ssl' as const,
        username: '<EMAIL>',
        password: 'password123',
        sender: '<EMAIL>'
      };

      const mockTransporter = { verify: jest.fn(), sendMail: jest.fn(), close: jest.fn() };
      nodemailer.createTransport.mockReturnValue(mockTransporter);

      smtpService.testSmtpConnection({ config: sslConfig, testMode: 'auto' });

      const transportConfig = nodemailer.createTransport.mock.calls[0][0];
      expect(transportConfig.secure).toBe(true);
      expect(transportConfig.port).toBe(465);
    });

    it('should reject insecure configurations when appropriate', () => {
      const insecureConfig = {
        server: 'smtp.example.com',
        port: 25,
        encryption: 'none' as const,
        username: '<EMAIL>',
        password: 'password123',
        sender: '<EMAIL>'
      };

      const validation = validateSmtpConfig(insecureConfig);
      // Should still be valid but with warnings in a real implementation
      expect(validation.isValid).toBe(true);
    });
  });

  describe('Data Exposure Prevention', () => {
    it('should not expose sensitive data in API responses', async () => {
      const { smtpService } = require('@/lib/tools/smtp-tester/smtpService');
      
      smtpService.testSmtpConnection.mockResolvedValue({
        success: true,
        messageId: 'test-message-id',
        logs: ['Connection successful', 'Email sent'],
        testAddress: '<EMAIL>',
        duration: 1500
      });

      const requestWithSensitiveData = {
        config: {
          server: 'smtp.gmail.com',
          port: 587,
          encryption: 'tls',
          username: '<EMAIL>',
          password: 'topsecret123',
          sender: '<EMAIL>'
        },
        testMode: 'auto'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester', {
        method: 'POST',
        body: JSON.stringify(requestWithSensitiveData),
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1'
        }
      });

      const response = await smtpTesterPost(request);
      const responseText = await response.text();

      // Response should not contain sensitive data
      expect(responseText).not.toContain('topsecret123');
      expect(responseText).not.toContain('<EMAIL>');
    });
  });
});
