import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logInfo, logError } from '@/lib/logging';
import { defaultAppConfig } from '@/lib/config/configService';

/**
 * API route to initialize the application configuration
 * This should be called once during setup or deployment
 */
export async function GET(request: NextRequest) {
  try {
    // In production, you would add authentication here
    // to ensure only admins can initialize the database

    const supabase = createServerSupabaseClient();

    // Check if app_config table exists
    try {
      const { data, error } = await supabase
        .from('app_config')
        .select('key')
        .limit(1);

      if (error && error.code === 'PGRST116') {
        // Table doesn't exist, create it
        logInfo('admin', 'Creating app_config table');
        
        // Create the app_config table
        const { error: createError } = await supabase.rpc('execute_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS app_config (
              key VARCHAR(50) PRIMARY KEY,
              value JSONB NOT NULL,
              updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
            );
          `
        });

        if (createError) {
          throw new Error(`Failed to create app_config table: ${createError.message}`);
        }
      }
    } catch (error) {
      // If the execute_sql function doesn't exist, provide instructions
      logError('admin', 'Error checking app_config table', { error });
      return NextResponse.json({
        success: false,
        message: 'Could not check or create app_config table. Please create it manually in the Supabase dashboard.',
        sql: `
          CREATE TABLE IF NOT EXISTS app_config (
            key VARCHAR(50) PRIMARY KEY,
            value JSONB NOT NULL,
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
          );
        `
      }, { status: 500 });
    }

    // Insert default configuration
    const configEntries = Object.entries(defaultAppConfig).map(([key, value]) => ({
      key,
      value,
      updated_at: new Date().toISOString()
    }));

    // Insert config entries with upsert to avoid duplicates
    for (const entry of configEntries) {
      const { error } = await supabase
        .from('app_config')
        .upsert(entry, { onConflict: 'key' });

      if (error) {
        logError('admin', `Error inserting config ${entry.key}`, { error });
        return NextResponse.json({
          success: false,
          message: `Failed to insert config ${entry.key}: ${error.message}`
        }, { status: 500 });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Application configuration initialized successfully',
      config: defaultAppConfig
    });
  } catch (error) {
    logError('admin', 'Error initializing application configuration', { error });
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
