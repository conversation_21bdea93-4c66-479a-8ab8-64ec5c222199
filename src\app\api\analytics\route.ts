/**
 * Public Analytics API Endpoint
 *
 * This endpoint accepts analytics events from the client-side without requiring authentication.
 * It includes comprehensive rate limiting, input validation, and security measures.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logError, logInfo } from '@/lib/logging';
import { CacheInvalidation } from '@/lib/analytics/cache';
import {
  checkRateLimit,
  updateRateLimit,
  getRateLimitHeaders,
  getClientIP
} from '@/lib/analytics/rateLimiting';
import {
  validateAnalyticsEvent,
  validateTimestamp,
  validateRequestSize
} from '@/lib/analytics/validation';
import {
  createSecureAnalyticsResponse,
  createSecureErrorResponse,
  createRateLimitResponse,
  createCORSPreflightResponse,
  logSecurityEvent
} from '@/lib/analytics/securityHeaders';

/**
 * Sanitize and prepare event data for database insertion
 */
function sanitizeEventData(data: any, clientIP: string, userAgent: string): any {
  return {
    event_type: data.eventType,
    session_id: data.sessionId,
    user_id: data.userId || null,
    session_start_time: data.sessionStartTime ? new Date(data.sessionStartTime).toISOString() : null,
    session_duration: data.sessionDuration || null,
    page_path: data.pagePath || '/',
    referrer: data.referrer || null,
    country: data.country || 'unknown',
    browser: data.browser || 'unknown',
    device_type: data.deviceType || 'unknown',
    timestamp: data.timestamp ? new Date(data.timestamp).toISOString() : new Date().toISOString(),
    additional_data: data.metadata || {},
  };
}

/**
 * POST /api/analytics
 * Accept analytics events from client-side with comprehensive security
 */
export async function POST(request: NextRequest) {
  let requestSuccess = false;

  try {
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Rate limiting is handled in middleware, but we can add additional checks here
    const rateLimitResult = checkRateLimit(request, 'analytics');
    const rateLimitHeaders = getRateLimitHeaders(rateLimitResult);

    // If rate limit exceeded (backup check)
    if (!rateLimitResult.allowed) {
      logSecurityEvent('rate_limit_exceeded', {
        ip: clientIP,
        userAgent,
        endpoint: '/api/analytics',
      });

      return createRateLimitResponse(rateLimitResult.retryAfter, rateLimitHeaders);
    }

    // Validate request size
    const contentLength = parseInt(request.headers.get('content-length') || '0');
    const sizeValidation = validateRequestSize(contentLength);

    if (!sizeValidation.valid) {
      logSecurityEvent('invalid_request', {
        ip: clientIP,
        userAgent,
        endpoint: '/api/analytics',
        error: sizeValidation.error,
      });

      return createSecureErrorResponse(sizeValidation.error!, {
        status: 413,
        additionalHeaders: rateLimitHeaders
      });
    }

    // Parse request body
    let requestData;
    try {
      requestData = await request.json();
    } catch (error) {
      logSecurityEvent('invalid_request', {
        ip: clientIP,
        userAgent,
        endpoint: '/api/analytics',
        error: 'Invalid JSON in request body',
      });

      return createSecureErrorResponse('Invalid JSON in request body', {
        status: 400,
        additionalHeaders: rateLimitHeaders
      });
    }

    // Handle batch events
    const events = Array.isArray(requestData) ? requestData : [requestData];

    if (events.length > 50) {
      logSecurityEvent('invalid_request', {
        ip: clientIP,
        userAgent,
        endpoint: '/api/analytics',
        error: `Too many events in batch: ${events.length}`,
      });

      return createSecureErrorResponse('Too many events in batch. Maximum 50 events allowed.', {
        status: 400,
        additionalHeaders: rateLimitHeaders
      });
    }

    const results = [];
    const supabase = createServerSupabaseClient();

    for (const eventData of events) {
      // Validate event data using new validation system
      const validation = validateAnalyticsEvent(eventData);
      if (!validation.success) {
        results.push({
          success: false,
          error: validation.error,
          eventType: eventData.eventType || 'unknown',
        });
        continue;
      }

      const validatedData = validation.data!;

      // Additional timestamp validation
      if (validatedData.timestamp) {
        const timestampValidation = validateTimestamp(validatedData.timestamp);
        if (!timestampValidation.valid) {
          results.push({
            success: false,
            error: timestampValidation.error,
            eventType: validatedData.eventType,
          });
          continue;
        }
      }

      try {
        // Sanitize and prepare data using validated data
        const sanitizedData = sanitizeEventData(validatedData, clientIP, userAgent);

        // Insert into database
        const { error } = await supabase
          .from('analytics_events')
          .insert(sanitizedData);

        if (error) {
          logError('AnalyticsAPI', `Failed to insert analytics event`, { error, eventData: validatedData });
          results.push({
            success: false,
            error: 'Failed to store event',
            eventType: validatedData.eventType,
          });
        } else {
          // Invalidate relevant cache entries
          const invalidatedCount = CacheInvalidation.invalidateByEventType(validatedData.eventType);

          results.push({
            success: true,
            eventType: validatedData.eventType,
          });

          logInfo('AnalyticsAPI', 'Event stored and cache invalidated', {
            eventType: validatedData.eventType,
            invalidatedEntries: invalidatedCount
          });
        }

      } catch (error) {
        logError('AnalyticsAPI', `Error processing analytics event`, { error, eventData: validatedData });
        results.push({
          success: false,
          error: 'Internal server error',
          eventType: validatedData.eventType,
        });
      }
    }

    // Log successful events
    const successfulEvents = results.filter(r => r.success).length;
    if (successfulEvents > 0) {
      logInfo('AnalyticsAPI', `Processed ${successfulEvents}/${events.length} analytics events`, { clientIP });
      requestSuccess = true;
    }

    // Update rate limit based on success (if configured to skip successful requests)
    updateRateLimit(request, 'analytics', requestSuccess);

    // Return results with rate limit headers
    const allSuccessful = results.every(r => r.success);

    return createSecureAnalyticsResponse(
      {
        success: allSuccessful,
        processed: successfulEvents,
        total: events.length,
        results: results,
      },
      {
        status: allSuccessful ? 200 : 207, // 207 Multi-Status for partial success
        additionalHeaders: rateLimitHeaders
      }
    );

  } catch (error) {
    logError('AnalyticsAPI', 'Unexpected error in analytics endpoint', { error });

    // Update rate limit for failed request
    updateRateLimit(request, 'analytics', false);

    const rateLimitHeaders = getRateLimitHeaders(checkRateLimit(request, 'analytics'));

    return createSecureErrorResponse('Internal server error', {
      status: 500,
      additionalHeaders: rateLimitHeaders
    });
  } finally {
    // Ensure rate limit is updated even if there's an unexpected error
    if (!requestSuccess) {
      updateRateLimit(request, 'analytics', false);
    }
  }
}

/**
 * OPTIONS /api/analytics
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return createCORSPreflightResponse(false);
}

/**
 * GET /api/analytics
 * Return basic endpoint information (for health checks)
 */
export async function GET() {
  return createSecureAnalyticsResponse({
    service: 'VanishPost Analytics API',
    version: '1.0.0',
    status: 'operational',
    endpoints: {
      POST: 'Submit analytics events',
      GET: 'Health check and service information',
      OPTIONS: 'CORS preflight'
    },
    rateLimit: {
      window: '1 minute',
      maxRequests: 100
    },
    supportedEvents: [
      'email_generated',
      'email_copied',
      'email_opened',
      'email_deleted',
      'manual_refresh',
      'session_start',
      'session_end',
      'emails_received_count',
      'email_received',
      'page_view',
      'heartbeat'
    ],
    security: {
      rateLimiting: 'enabled',
      inputValidation: 'enabled',
      securityHeaders: 'enabled'
    }
  });
}
