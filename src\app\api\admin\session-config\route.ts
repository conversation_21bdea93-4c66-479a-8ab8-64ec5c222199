/**
 * Session Configuration Management API
 * 
 * Backend endpoint for managing session and rate limiting configurations
 * Requires admin authentication
 */

import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth/adminAuth';
import {
  getSessionConfig,
  getRateLimitConfig,
  updateSessionConfig,
  updateRateLimitConfig,
  applySessionPreset,
  applyRateLimitPreset,
  resetToDefaults,
  getConfigurationSummary,
  SESSION_PRESETS,
  RATE_LIMIT_PRESETS,
  SessionConfig,
  RateLimitConfig
} from '@/lib/config/sessionConfig';

/**
 * GET - Get current configuration
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const action = request.nextUrl.searchParams.get('action');

    switch (action) {
      case 'summary':
        return NextResponse.json({
          success: true,
          data: getConfigurationSummary()
        });

      case 'session':
        return NextResponse.json({
          success: true,
          data: getSessionConfig()
        });

      case 'rateLimit':
        return NextResponse.json({
          success: true,
          data: getRateLimitConfig()
        });

      case 'presets':
        return NextResponse.json({
          success: true,
          data: {
            sessionPresets: Object.keys(SESSION_PRESETS),
            rateLimitPresets: Object.keys(RATE_LIMIT_PRESETS)
          }
        });

      default:
        // Return combined session config with rate limits for admin interface
        try {
          const sessionConfig = await getSessionConfig();
          const rateLimitConfig = await getRateLimitConfig();

          // Try to get summary, but don't fail if it errors
          let summary = null;
          try {
            summary = getConfigurationSummary();
          } catch (summaryError) {
            console.warn('Failed to get configuration summary:', summaryError);
            summary = { error: 'Summary unavailable' };
          }

          return NextResponse.json({
            success: true,
            data: {
              ...sessionConfig,
              rateLimits: rateLimitConfig,
              summary
            }
          });
        } catch (configError) {
          console.error('Error getting configuration:', configError);
          return NextResponse.json({
            success: false,
            error: 'Failed to get configuration'
          }, { status: 500 });
        }
    }

  } catch (error) {
    console.error('Error getting session configuration:', error);
    console.error('Error details:', error instanceof Error ? error.message : String(error));
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');

    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * POST - Update configuration
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const body = await request.json();
    const { action, config, preset } = body;

    switch (action) {
      case 'updateSession':
        if (!config) {
          return NextResponse.json({
            success: false,
            error: 'Session config is required'
          }, { status: 400 });
        }
        
        await updateSessionConfig(config as Partial<SessionConfig>);

        // Return combined config for admin interface
        const sessionConfig = await getSessionConfig();
        const rateLimitConfig = await getRateLimitConfig();

        return NextResponse.json({
          success: true,
          message: 'Session configuration updated',
          data: {
            ...sessionConfig,
            rateLimits: rateLimitConfig
          }
        });

      case 'updateRateLimit':
        if (!config) {
          return NextResponse.json({
            success: false,
            error: 'Rate limit config is required'
          }, { status: 400 });
        }

        await updateRateLimitConfig(config as Partial<RateLimitConfig>);

        return NextResponse.json({
          success: true,
          message: 'Rate limit configuration updated',
          data: await getRateLimitConfig()
        });

      case 'applySessionPreset':
        if (!preset || !(preset in SESSION_PRESETS)) {
          return NextResponse.json({
            success: false,
            error: 'Valid session preset is required'
          }, { status: 400 });
        }
        
        applySessionPreset(preset as keyof typeof SESSION_PRESETS);
        
        return NextResponse.json({
          success: true,
          message: `Applied session preset: ${preset}`,
          data: getSessionConfig()
        });

      case 'applyRateLimitPreset':
        if (!preset || !(preset in RATE_LIMIT_PRESETS)) {
          return NextResponse.json({
            success: false,
            error: 'Valid rate limit preset is required'
          }, { status: 400 });
        }
        
        applyRateLimitPreset(preset as keyof typeof RATE_LIMIT_PRESETS);
        
        return NextResponse.json({
          success: true,
          message: `Applied rate limit preset: ${preset}`,
          data: getRateLimitConfig()
        });

      case 'resetToDefaults':
        resetToDefaults();
        
        return NextResponse.json({
          success: true,
          message: 'Configuration reset to defaults',
          data: {
            session: getSessionConfig(),
            rateLimit: getRateLimitConfig()
          }
        });

      case 'emergencyMode':
        const { enabled } = body;
        
        if (enabled) {
          applyRateLimitPreset('EMERGENCY');
          applySessionPreset('HIGH_SECURITY');
        } else {
          applyRateLimitPreset('PRODUCTION');
          applySessionPreset('PRODUCTION');
        }
        
        return NextResponse.json({
          success: true,
          message: `Emergency mode ${enabled ? 'enabled' : 'disabled'}`,
          data: getConfigurationSummary()
        });

      case 'quickAdjust':
        const { type, adjustment } = body;
        
        if (type === 'session' && adjustment) {
          const currentConfig = getSessionConfig();
          const newConfig: Partial<SessionConfig> = {};
          
          if (adjustment.duration) {
            newConfig.sessionDuration = adjustment.duration * 60 * 60 * 1000; // Convert hours to ms
          }
          if (adjustment.maxExtensions !== undefined) {
            newConfig.maxSessionExtensions = adjustment.maxExtensions;
          }
          
          await updateSessionConfig(newConfig);
        } else if (type === 'rateLimit' && adjustment) {
          const currentConfig = await getRateLimitConfig();
          const newConfig: Partial<RateLimitConfig> = { ...currentConfig };
          
          if (adjustment.emailLimit) {
            newConfig.emailGeneration = {
              ...currentConfig.emailGeneration,
              sessionLimits: {
                ...currentConfig.emailGeneration.sessionLimits,
                maxRequests: adjustment.emailLimit
              }
            };
          }
          
          await updateRateLimitConfig(newConfig);
        }

        return NextResponse.json({
          success: true,
          message: 'Quick adjustment applied',
          data: getConfigurationSummary()
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Error updating session configuration:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

/**
 * PUT - Bulk configuration update
 */
export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const body = await request.json();
    const { sessionConfig, rateLimitConfig } = body;

    if (sessionConfig) {
      await updateSessionConfig(sessionConfig as Partial<SessionConfig>);
    }

    if (rateLimitConfig) {
      await updateRateLimitConfig(rateLimitConfig as Partial<RateLimitConfig>);
    }

    return NextResponse.json({
      success: true,
      message: 'Bulk configuration update completed',
      data: {
        session: getSessionConfig(),
        rateLimit: getRateLimitConfig(),
        summary: getConfigurationSummary()
      }
    });

  } catch (error) {
    console.error('Error in bulk configuration update:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

/**
 * DELETE - Reset specific configuration
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const type = request.nextUrl.searchParams.get('type');

    switch (type) {
      case 'session':
        applySessionPreset('PRODUCTION');
        return NextResponse.json({
          success: true,
          message: 'Session configuration reset to production defaults',
          data: getSessionConfig()
        });

      case 'rateLimit':
        applyRateLimitPreset('PRODUCTION');
        return NextResponse.json({
          success: true,
          message: 'Rate limit configuration reset to production defaults',
          data: getRateLimitConfig()
        });

      case 'all':
        resetToDefaults();
        return NextResponse.json({
          success: true,
          message: 'All configuration reset to defaults',
          data: {
            session: getSessionConfig(),
            rateLimit: getRateLimitConfig()
          }
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid reset type'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Error resetting configuration:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
