'use client';

import { LiveSessionMonitor } from '@/components/admin/security/LiveSessionMonitor';

/**
 * Live Sessions Page for Secure Admin Portal
 */
export default function LiveSessionsPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Live Session Monitor</h1>
        <p className="mt-2 text-sm text-gray-600">
          Real-time monitoring of active sessions, IP rotation detection, and threat assessment.
        </p>
      </div>

      <LiveSessionMonitor />
    </div>
  );
}
