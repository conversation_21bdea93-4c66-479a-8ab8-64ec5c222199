/**
 * <PERSON><PERSON>t to test connections to both databases
 */
const mysql = require('mysql2/promise');

// Local database configuration
const localDbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'tempmail_web_user',
  password: process.env.DB_PASSWORD || 'yiWTpFChtFW3neWCS',
  database: process.env.DB_NAME || 'tempmail_web',
};

// Guerrilla database configuration
const guerrillaDbConfig = {
  host: process.env.GUERRILLA_DB_HOST || 'localhost',
  user: process.env.GUERRILLA_DB_USER || 'user',
  password: process.env.GUERRILLA_DB_PASSWORD || '',
  database: process.env.GUERRILLA_DB_NAME || 'guerrilla_db',
};

async function testLocalDatabase() {
  console.log('Testing connection to local database...');
  let connection;

  try {
    connection = await mysql.createConnection(localDbConfig);
    console.log('✅ Successfully connected to local database');

    // Test TempEmail table
    const [tables] = await connection.execute("SHOW TABLES LIKE 'TempEmail'");

    if (tables.length > 0) {
      console.log('✅ TempEmail table exists');

      // Check table structure
      const [columns] = await connection.execute("SHOW COLUMNS FROM TempEmail");
      console.log('Table structure:');
      columns.forEach(col => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''}`);
      });

      // Check indexes
      const [indexes] = await connection.execute("SHOW INDEX FROM TempEmail");
      console.log('Indexes:');
      const uniqueIndexes = new Set();
      indexes.forEach(idx => {
        if (!uniqueIndexes.has(idx.Key_name)) {
          console.log(`  - ${idx.Key_name} (${idx.Column_name})`);
          uniqueIndexes.add(idx.Key_name);
        }
      });

      // Test insert and select
      console.log('Testing insert and select operations...');
      const testEmail = `test_${Date.now()}@fademail.site`;
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 1); // Expires tomorrow

      await connection.execute(
        'INSERT INTO TempEmail (emailAddress, expirationDate) VALUES (?, ?)',
        [testEmail, expirationDate]
      );

      const [rows] = await connection.execute(
        'SELECT * FROM TempEmail WHERE emailAddress = ?',
        [testEmail]
      );

      if (rows.length > 0) {
        console.log('✅ Successfully inserted and retrieved test email');
        console.log('Test email details:', rows[0]);

        // Clean up test data
        await connection.execute(
          'DELETE FROM TempEmail WHERE emailAddress = ?',
          [testEmail]
        );
        console.log('✅ Successfully cleaned up test data');
      } else {
        console.log('❌ Failed to retrieve test email');
      }
    } else {
      console.log('❌ TempEmail table does not exist. Please run the setup-database script first.');
    }

    return true;
  } catch (error) {
    console.error('❌ Error testing local database:', error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function testGuerrillaDatabase() {
  console.log('\nTesting connection to guerrilla database...');
  let connection;

  try {
    connection = await mysql.createConnection(guerrillaDbConfig);
    console.log('✅ Successfully connected to guerrilla database');

    // Test guerrilla_mail table
    const [tables] = await connection.execute("SHOW TABLES LIKE 'guerrilla_mail'");

    if (tables.length > 0) {
      console.log('✅ guerrilla_mail table exists');

      // Get email count
      const [countResult] = await connection.execute("SELECT COUNT(*) as count FROM guerrilla_mail");
      console.log(`Total emails in guerrilla_mail: ${countResult[0].count}`);

      // Get sample email (if any)
      const [emails] = await connection.execute("SELECT mail_id, date, `from`, `to`, subject FROM guerrilla_mail LIMIT 1");

      if (emails.length > 0) {
        console.log('Sample email:');
        console.log(`  - ID: ${emails[0].mail_id}`);
        console.log(`  - Date: ${emails[0].date}`);
        console.log(`  - From: ${emails[0].from}`);
        console.log(`  - To: ${emails[0].to}`);
        console.log(`  - Subject: ${emails[0].subject}`);
      } else {
        console.log('No emails found in the guerrilla_mail table');
      }
    } else {
      console.log('❌ guerrilla_mail table does not exist');
    }

    return true;
  } catch (error) {
    console.error('❌ Error testing guerrilla database:', error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function runTests() {
  console.log('=== Database Connection Tests ===\n');

  const localSuccess = await testLocalDatabase();
  const guerrillaSuccess = await testGuerrillaDatabase();

  console.log('\n=== Test Summary ===');
  console.log(`Local Database: ${localSuccess ? '✅ Connected' : '❌ Failed'}`);
  console.log(`Guerrilla Database: ${guerrillaSuccess ? '✅ Connected' : '❌ Failed'}`);

  if (!localSuccess || !guerrillaSuccess) {
    process.exit(1);
  }
}

// Run the tests
runTests().catch(console.error);
