/**
 * Session Persistence Testing Suite
 * 
 * This module provides comprehensive testing for the session persistence fix
 * to ensure that sessions persist across page refreshes and rate limiting works correctly.
 */

import { generateEmailWithSession } from '@/lib/api/secureApiClient';
import { 
  initializeSessionManagement, 
  getCurrentSessionId, 
  clearSession,
  getSessionMetadata 
} from '@/lib/session/persistentSessionManager';

export interface SessionTestResult {
  testName: string;
  passed: boolean;
  details: string;
  sessionId?: string;
  error?: string;
}

export interface SessionTestSuite {
  results: SessionTestResult[];
  overallPassed: boolean;
  summary: string;
}

/**
 * Test session persistence across simulated page refreshes
 */
export async function testSessionPersistence(): Promise<SessionTestResult> {
  try {
    // Clear any existing session
    clearSession();
    
    // Initialize first session
    const session1 = initializeSessionManagement();
    const sessionId1 = session1.sessionId;
    
    // Simulate page refresh by initializing again
    const session2 = initializeSessionManagement();
    const sessionId2 = session2.sessionId;
    
    if (sessionId1 === sessionId2) {
      return {
        testName: 'Session Persistence',
        passed: true,
        details: `Session ID persisted across refresh: ${sessionId1}`,
        sessionId: sessionId1
      };
    } else {
      return {
        testName: 'Session Persistence',
        passed: false,
        details: `Session ID changed on refresh: ${sessionId1} → ${sessionId2}`,
        sessionId: sessionId2
      };
    }
  } catch (error) {
    return {
      testName: 'Session Persistence',
      passed: false,
      details: 'Test failed with error',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test that session IDs are included in API requests
 */
export async function testSessionHeaderInclusion(): Promise<SessionTestResult> {
  try {
    // Clear and initialize session
    clearSession();
    const session = initializeSessionManagement();
    
    // Mock fetch to capture headers
    const originalFetch = global.fetch;
    let capturedHeaders: Headers | undefined;
    
    global.fetch = jest.fn().mockImplementation((url, options) => {
      capturedHeaders = new Headers(options?.headers);
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            emailAddress: '<EMAIL>',
            expirationDate: new Date().toISOString(),
            success: true
          }
        })
      });
    });
    
    // Make API request
    await generateEmailWithSession();
    
    // Restore original fetch
    global.fetch = originalFetch;
    
    // Check if session header was included
    const sessionHeader = capturedHeaders?.get('x-session-id');
    
    if (sessionHeader === session.sessionId) {
      return {
        testName: 'Session Header Inclusion',
        passed: true,
        details: `Session ID correctly included in headers: ${sessionHeader}`,
        sessionId: session.sessionId
      };
    } else {
      return {
        testName: 'Session Header Inclusion',
        passed: false,
        details: `Session ID not included or incorrect: expected ${session.sessionId}, got ${sessionHeader}`,
        sessionId: session.sessionId
      };
    }
  } catch (error) {
    return {
      testName: 'Session Header Inclusion',
      passed: false,
      details: 'Test failed with error',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test session metadata persistence
 */
export async function testSessionMetadata(): Promise<SessionTestResult> {
  try {
    // Clear and initialize session
    clearSession();
    const session = initializeSessionManagement();
    
    // Get metadata
    const metadata = getSessionMetadata();
    
    if (!metadata) {
      return {
        testName: 'Session Metadata',
        passed: false,
        details: 'Session metadata not found',
        sessionId: session.sessionId
      };
    }
    
    // Check required fields
    const requiredFields = ['sessionId', 'createdAt', 'expiresAt', 'lastActivity'];
    const missingFields = requiredFields.filter(field => !(field in metadata));
    
    if (missingFields.length > 0) {
      return {
        testName: 'Session Metadata',
        passed: false,
        details: `Missing metadata fields: ${missingFields.join(', ')}`,
        sessionId: session.sessionId
      };
    }
    
    // Check expiration is in the future
    if (metadata.expiresAt <= Date.now()) {
      return {
        testName: 'Session Metadata',
        passed: false,
        details: 'Session expiration is in the past',
        sessionId: session.sessionId
      };
    }
    
    return {
      testName: 'Session Metadata',
      passed: true,
      details: `Session metadata complete and valid. Expires: ${new Date(metadata.expiresAt).toISOString()}`,
      sessionId: session.sessionId
    };
  } catch (error) {
    return {
      testName: 'Session Metadata',
      passed: false,
      details: 'Test failed with error',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test rate limiting with persistent sessions
 */
export async function testRateLimitingWithSessions(): Promise<SessionTestResult> {
  try {
    // This test would need to be run in a controlled environment
    // For now, we'll just verify the session is consistent
    
    clearSession();
    const session = initializeSessionManagement();
    
    // Simulate multiple requests with the same session
    const sessionId1 = getCurrentSessionId();
    
    // Simulate some time passing
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const sessionId2 = getCurrentSessionId();
    
    if (sessionId1 === sessionId2 && sessionId1 === session.sessionId) {
      return {
        testName: 'Rate Limiting Session Consistency',
        passed: true,
        details: `Session ID consistent across multiple checks: ${sessionId1}`,
        sessionId: sessionId1
      };
    } else {
      return {
        testName: 'Rate Limiting Session Consistency',
        passed: false,
        details: `Session ID inconsistent: ${sessionId1} → ${sessionId2}`,
        sessionId: sessionId2 || undefined
      };
    }
  } catch (error) {
    return {
      testName: 'Rate Limiting Session Consistency',
      passed: false,
      details: 'Test failed with error',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test localStorage functionality
 */
export async function testLocalStorageIntegration(): Promise<SessionTestResult> {
  try {
    // Check if localStorage is available
    if (typeof window === 'undefined' || !window.localStorage) {
      return {
        testName: 'LocalStorage Integration',
        passed: false,
        details: 'localStorage not available (server-side or unsupported browser)'
      };
    }
    
    // Clear and initialize session
    clearSession();
    const session = initializeSessionManagement();
    
    // Check if session is stored in localStorage
    const storedSessionId = localStorage.getItem('vanishpost_session_id');
    const storedMetadata = localStorage.getItem('vanishpost_session_metadata');
    
    if (!storedSessionId || !storedMetadata) {
      return {
        testName: 'LocalStorage Integration',
        passed: false,
        details: 'Session data not stored in localStorage',
        sessionId: session.sessionId
      };
    }
    
    if (storedSessionId !== session.sessionId) {
      return {
        testName: 'LocalStorage Integration',
        passed: false,
        details: `Stored session ID mismatch: expected ${session.sessionId}, got ${storedSessionId}`,
        sessionId: session.sessionId
      };
    }
    
    // Try to parse metadata
    try {
      const metadata = JSON.parse(storedMetadata);
      if (metadata.sessionId !== session.sessionId) {
        return {
          testName: 'LocalStorage Integration',
          passed: false,
          details: 'Metadata session ID mismatch',
          sessionId: session.sessionId
        };
      }
    } catch (parseError) {
      return {
        testName: 'LocalStorage Integration',
        passed: false,
        details: 'Failed to parse stored metadata',
        sessionId: session.sessionId
      };
    }
    
    return {
      testName: 'LocalStorage Integration',
      passed: true,
      details: 'Session data correctly stored and retrieved from localStorage',
      sessionId: session.sessionId
    };
  } catch (error) {
    return {
      testName: 'LocalStorage Integration',
      passed: false,
      details: 'Test failed with error',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Run complete session persistence test suite
 */
export async function runSessionPersistenceTestSuite(): Promise<SessionTestSuite> {
  console.log('🧪 Running Session Persistence Test Suite...');
  
  const tests = [
    testSessionPersistence,
    testSessionHeaderInclusion,
    testSessionMetadata,
    testRateLimitingWithSessions,
    testLocalStorageIntegration
  ];
  
  const results: SessionTestResult[] = [];
  
  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);
      console.log(`${result.passed ? '✅' : '❌'} ${result.testName}: ${result.details}`);
    } catch (error) {
      results.push({
        testName: test.name,
        passed: false,
        details: 'Test execution failed',
        error: error instanceof Error ? error.message : String(error)
      });
      console.log(`❌ ${test.name}: Test execution failed`);
    }
  }
  
  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  const overallPassed = passedTests === totalTests;
  
  const summary = `${passedTests}/${totalTests} tests passed. ${overallPassed ? 'All tests passed! ✅' : 'Some tests failed. ❌'}`;
  
  console.log(`\n📊 Test Suite Summary: ${summary}`);
  
  return {
    results,
    overallPassed,
    summary
  };
}

/**
 * Manual test instructions for developers
 */
export function getManualTestInstructions(): string {
  return `
🧪 Manual Session Persistence Testing Instructions

1. **Session Persistence Test:**
   - Open VanishPost in your browser
   - Generate an email address (note the session ID in console)
   - Refresh the page (F5 or Ctrl+R)
   - Check console - session ID should be the same
   - Generate another email - should be blocked by rate limiting

2. **Rate Limiting Test:**
   - Clear browser data or use incognito mode
   - Generate 2 email addresses quickly (should work)
   - Try to generate a 3rd email - should be blocked
   - Refresh the page multiple times
   - Try to generate another email - should still be blocked

3. **Cross-Tab Test:**
   - Open VanishPost in two browser tabs
   - Generate emails in both tabs
   - Both should use the same session ID
   - Rate limiting should apply across both tabs

4. **Storage Test:**
   - Open browser DevTools → Application → Local Storage
   - Look for 'vanishpost_session_id' and 'vanishpost_session_metadata'
   - Values should persist across page refreshes

Expected Results:
✅ Session ID persists across page refreshes
✅ Rate limiting works correctly with persistent sessions
✅ Session data is stored in localStorage
✅ Same session is used across multiple tabs
✅ API requests include session headers

If any test fails, the session bypass vulnerability still exists!
`;
}

export default {
  runSessionPersistenceTestSuite,
  testSessionPersistence,
  testSessionHeaderInclusion,
  testSessionMetadata,
  testRateLimitingWithSessions,
  testLocalStorageIntegration,
  getManualTestInstructions
};
