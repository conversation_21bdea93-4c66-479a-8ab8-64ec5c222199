'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent, type ChartConfig } from '@/components/ui/Chart';
import { Skeleton } from '@/components/ui/Skeleton';

interface EventTimeSeriesChartProps {
  timeRange: string;
  customDateRange?: { start: string; end: string } | null;
}

interface TimelineData {
  timestamp: string;
  email_address_generated: number;
  email_address_copied: number;
  email_opened: number;
  email_deleted: number;
  manual_refresh_triggered: number;
  session_start: number;
}

// Chart configuration for shadcn Chart component
const chartConfig = {
  email_address_generated: {
    label: "Emails Generated",
    color: "hsl(142, 76%, 36%)", // emerald-600
  },
  email_address_copied: {
    label: "Emails Copied",
    color: "hsl(38, 92%, 50%)", // amber-500
  },
  email_opened: {
    label: "Emails Opened",
    color: "hsl(262, 83%, 58%)", // violet-500
  },
  manual_refresh_triggered: {
    label: "Manual Refreshes",
    color: "hsl(199, 89%, 48%)", // cyan-500
  },
  session_start: {
    label: "Sessions Started",
    color: "hsl(217, 91%, 60%)", // blue-500
  },
} satisfies ChartConfig

/**
 * Event Time Series Chart Component
 * 
 * Displays events over time using a line chart with multiple series
 * for different event types.
 */
export default function EventTimeSeriesChart({ 
  timeRange, 
  customDateRange 
}: EventTimeSeriesChartProps) {
  const [timelineData, setTimelineData] = useState<TimelineData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch timeline data from the API
   */
  const fetchTimelineData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      
      if (customDateRange) {
        params.append('startDate', customDateRange.start);
        params.append('endDate', customDateRange.end);
      } else {
        params.append('timeRange', timeRange);
      }
      
      params.append('aggregation', 'timeline');

      const response = await fetch(`/api/management-portal-x7z9y2/analytics?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch timeline data');
      }

      // Transform the timeline data for the chart
      const timeline = result.data.timeline || {};
      const transformedData: TimelineData[] = Object.entries(timeline)
        .map(([timestamp, events]: [string, any]) => ({
          timestamp,
          email_address_generated: events.email_address_generated || 0,
          email_address_copied: events.email_address_copied || 0,
          email_opened: events.email_opened || 0,
          email_deleted: events.email_deleted || 0,
          manual_refresh_triggered: events.manual_refresh_triggered || 0,
          session_start: events.session_start || 0,
        }))
        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

      setTimelineData(transformedData);

    } catch (error) {
      console.error('Error fetching timeline data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch timeline data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when props change
  useEffect(() => {
    fetchTimelineData();
  }, [timeRange, customDateRange]);

  /**
   * Format timestamp for display
   */
  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    
    // Format based on time range
    if (timeRange === '1h') {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (timeRange === '24h') {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit'
      });
    }
  };

  /**
   * Custom label formatter for the chart tooltip
   */
  const formatTooltipLabel = (label: string) => {
    const date = new Date(label);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="h-80 space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-24" />
        </div>
        <Skeleton className="h-64 w-full" />
        <div className="flex justify-center gap-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center gap-2">
              <Skeleton className="h-3 w-3 rounded-full" />
              <Skeleton className="h-3 w-16" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-80 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 font-medium">Error loading chart data</p>
          <p className="text-gray-500 text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  if (timelineData.length === 0) {
    return (
      <div className="h-80 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500 font-medium">No data available</p>
          <p className="text-gray-400 text-sm mt-1">
            No events found for the selected time period
          </p>
        </div>
      </div>
    );
  }

  return (
    <ChartContainer config={chartConfig} className="h-80">
      <LineChart data={timelineData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="timestamp"
          tickFormatter={formatTimestamp}
          fontSize={12}
        />
        <YAxis fontSize={12} />
        <ChartTooltip
          content={<ChartTooltipContent labelFormatter={formatTooltipLabel} />}
        />
        <ChartLegend content={<ChartLegendContent />} />

        <Line
          type="monotone"
          dataKey="email_address_generated"
          stroke="var(--color-email_address_generated)"
          strokeWidth={2}
          dot={{ fill: 'var(--color-email_address_generated)', strokeWidth: 2, r: 3 }}
          activeDot={{ r: 5 }}
        />

        <Line
          type="monotone"
          dataKey="email_address_copied"
          stroke="var(--color-email_address_copied)"
          strokeWidth={2}
          dot={{ fill: 'var(--color-email_address_copied)', strokeWidth: 2, r: 3 }}
          activeDot={{ r: 5 }}
        />

        <Line
          type="monotone"
          dataKey="email_opened"
          stroke="var(--color-email_opened)"
          strokeWidth={2}
          dot={{ fill: 'var(--color-email_opened)', strokeWidth: 2, r: 3 }}
          activeDot={{ r: 5 }}
        />

        <Line
          type="monotone"
          dataKey="manual_refresh_triggered"
          stroke="var(--color-manual_refresh_triggered)"
          strokeWidth={2}
          dot={{ fill: 'var(--color-manual_refresh_triggered)', strokeWidth: 2, r: 3 }}
          activeDot={{ r: 5 }}
        />

        <Line
          type="monotone"
          dataKey="session_start"
          stroke="var(--color-session_start)"
          strokeWidth={2}
          dot={{ fill: 'var(--color-session_start)', strokeWidth: 2, r: 3 }}
          activeDot={{ r: 5 }}
        />
      </LineChart>
    </ChartContainer>
  );
}
