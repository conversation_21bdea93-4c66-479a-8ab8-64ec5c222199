'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Line, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface EventTimeSeriesChartProps {
  timeRange: string;
  customDateRange?: { start: string; end: string } | null;
}

interface TimelineData {
  timestamp: string;
  email_address_generated: number;
  email_address_copied: number;
  email_opened: number;
  email_deleted: number;
  manual_refresh_triggered: number;
  session_start: number;
}

/**
 * Event Time Series Chart Component
 * 
 * Displays events over time using a line chart with multiple series
 * for different event types.
 */
export default function EventTimeSeriesChart({ 
  timeRange, 
  customDateRange 
}: EventTimeSeriesChartProps) {
  const [timelineData, setTimelineData] = useState<TimelineData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch timeline data from the API
   */
  const fetchTimelineData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      
      if (customDateRange) {
        params.append('startDate', customDateRange.start);
        params.append('endDate', customDateRange.end);
      } else {
        params.append('timeRange', timeRange);
      }
      
      params.append('aggregation', 'timeline');

      const response = await fetch(`/api/management-portal-x7z9y2/analytics?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch timeline data');
      }

      // Transform the timeline data for the chart
      const timeline = result.data.timeline || {};
      const transformedData: TimelineData[] = Object.entries(timeline)
        .map(([timestamp, events]: [string, any]) => ({
          timestamp,
          email_address_generated: events.email_address_generated || 0,
          email_address_copied: events.email_address_copied || 0,
          email_opened: events.email_opened || 0,
          email_deleted: events.email_deleted || 0,
          manual_refresh_triggered: events.manual_refresh_triggered || 0,
          session_start: events.session_start || 0,
        }))
        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

      setTimelineData(transformedData);

    } catch (error) {
      console.error('Error fetching timeline data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch timeline data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when props change
  useEffect(() => {
    fetchTimelineData();
  }, [timeRange, customDateRange]);

  /**
   * Format timestamp for display
   */
  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    
    // Format based on time range
    if (timeRange === '1h') {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (timeRange === '24h') {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit'
      });
    }
  };

  /**
   * Custom tooltip for the chart
   */
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const date = new Date(label);
      
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">
            {date.toLocaleString('en-US', {
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <div className="h-80 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-80 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 font-medium">Error loading chart data</p>
          <p className="text-gray-500 text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  if (timelineData.length === 0) {
    return (
      <div className="h-80 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500 font-medium">No data available</p>
          <p className="text-gray-400 text-sm mt-1">
            No events found for the selected time period
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={timelineData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="timestamp" 
            tickFormatter={formatTimestamp}
            stroke="#666"
            fontSize={12}
          />
          <YAxis stroke="#666" fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          <Line 
            type="monotone" 
            dataKey="email_address_generated" 
            stroke="#10b981" 
            strokeWidth={2}
            name="Emails Generated"
            dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
          />
          
          <Line 
            type="monotone" 
            dataKey="email_address_copied" 
            stroke="#f59e0b" 
            strokeWidth={2}
            name="Emails Copied"
            dot={{ fill: '#f59e0b', strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
          />
          
          <Line 
            type="monotone" 
            dataKey="email_opened" 
            stroke="#8b5cf6" 
            strokeWidth={2}
            name="Emails Opened"
            dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
          />
          
          <Line 
            type="monotone" 
            dataKey="manual_refresh_triggered" 
            stroke="#06b6d4" 
            strokeWidth={2}
            name="Manual Refreshes"
            dot={{ fill: '#06b6d4', strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
          />
          
          <Line 
            type="monotone" 
            dataKey="session_start" 
            stroke="#3b82f6" 
            strokeWidth={2}
            name="Sessions Started"
            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
