'use client';

import { useState, useEffect } from 'react';
import AdPerformanceMonitor from '@/components/admin/AdPerformanceMonitor';
import { useLocalStorage } from '@/lib/hooks/useLocalStorage';
import { Button } from '@/components/ui/Button';

/**
 * Ad Optimization Page
 *
 * This page provides tools for optimizing ad loading and performance
 */
interface OptimizationSettings {
  lazyLoading: {
    enabled: boolean;
    threshold: number;
  };
  caching: {
    adCacheTtl: number;
    domainCacheTtl: number;
  };
  performance: {
    maxAdsPerPage: number;
    refreshInterval: number;
  };
}

const defaultSettings: OptimizationSettings = {
  lazyLoading: {
    enabled: true,
    threshold: 0.1
  },
  caching: {
    adCacheTtl: 300,
    domainCacheTtl: 900
  },
  performance: {
    maxAdsPerPage: 3,
    refreshInterval: 60
  }
};

export default function AdOptimizationPage() {
  const [activeTab, setActiveTab] = useState<'performance' | 'settings'>('performance');
  const [settings, setSettings] = useLocalStorage<OptimizationSettings>('ad-optimization-settings', defaultSettings);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  // Handle settings change
  const handleSettingsChange = (category: keyof OptimizationSettings, setting: string, value: any) => {
    setSettings({
      ...settings,
      [category]: {
        ...settings[category],
        [setting]: value
      }
    });
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      setSaveMessage(null);

      // In a real implementation, we would save the settings to the server
      // For now, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSaveMessage('Settings saved successfully');
    } catch (error) {
      setSaveMessage('Error saving settings');
      console.error('Error saving settings:', error);
    } finally {
      setIsSaving(false);

      // Clear the message after 3 seconds
      setTimeout(() => {
        setSaveMessage(null);
      }, 3000);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex items-center mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
        </svg>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Ad Optimization</h1>
          <p className="mt-1 text-sm text-gray-600">
            Monitor and optimize ad loading performance across your Fademail domains.
          </p>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            <button
              onClick={() => setActiveTab('performance')}
              className={`${
                activeTab === 'performance'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm flex items-center`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
              </svg>
              Performance Metrics
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`${
                activeTab === 'settings'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm flex items-center`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
              Optimization Settings
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'performance' && (
            <div>
              <AdPerformanceMonitor />
            </div>
          )}

          {activeTab === 'settings' && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.5 2a3.5 3.5 0 101.665 6.58L8.585 10l-1.42 1.42a3.5 3.5 0 101.414 1.414l1.42-1.42 1.42 1.42a3.5 3.5 0 101.414-1.414L11.415 10l1.42-1.42A3.5 3.5 0 1011.5 7.165L10 8.585l-1.42-1.42A3.5 3.5 0 005.5 2zM4 5.5a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm1.5 8a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm8-8a1.5 1.5 0 110-3 1.5 1.5 0 010 3zm0 8a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" clipRule="evenodd" />
                </svg>
                Ad Loading Optimization
              </h3>

              <div className="space-y-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.05 3.636a1 1 0 010 1.414 7 7 0 000 9.9 1 1 0 11-1.414 1.414 9 9 0 010-12.728 1 1 0 011.414 0zm9.9 0a1 1 0 011.414 0 9 9 0 010 12.728 1 1 0 11-1.414-1.414 7 7 0 000-9.9 1 1 0 010-1.414zM7.879 6.464a1 1 0 010 1.414 3 3 0 000 4.243 1 1 0 11-1.415 1.414 5 5 0 010-7.07 1 1 0 011.415 0zm4.242 0a1 1 0 011.415 0 5 5 0 010 7.072 1 1 0 01-1.415-1.415 3 3 0 000-4.242 1 1 0 010-1.415zM10 9a1 1 0 011 1v.01a1 1 0 11-2 0V10a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    Lazy Loading
                  </h4>
                  <div className="flex items-center">
                    <input
                      id="enable-lazy-loading"
                      type="checkbox"
                      checked={settings.lazyLoading.enabled}
                      onChange={(e) => handleSettingsChange('lazyLoading', 'enabled', e.target.checked)}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                    <label htmlFor="enable-lazy-loading" className="ml-2 block text-sm text-gray-900">
                      Enable lazy loading for ads
                    </label>
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Ads will only load when they are about to enter the viewport, reducing initial page load time.
                  </p>

                  <div className="mt-4">
                    <label htmlFor="lazy-loading-threshold" className="block text-sm font-medium text-gray-700">
                      Visibility Threshold
                    </label>
                    <select
                      id="lazy-loading-threshold"
                      value={settings.lazyLoading.threshold}
                      onChange={(e) => handleSettingsChange('lazyLoading', 'threshold', parseFloat(e.target.value))}
                      disabled={!settings.lazyLoading.enabled}
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    >
                      <option value={0}>0% (Load as soon as any part is visible)</option>
                      <option value={0.1}>10% (Default)</option>
                      <option value={0.25}>25%</option>
                      <option value={0.5}>50%</option>
                      <option value={0.75}>75%</option>
                      <option value={1}>100% (Load only when fully visible)</option>
                    </select>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                    Caching
                  </h4>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="ad-cache-ttl" className="block text-sm font-medium text-gray-700">
                        Ad Cache TTL (seconds)
                      </label>
                      <input
                        type="number"
                        id="ad-cache-ttl"
                        value={settings.caching.adCacheTtl}
                        onChange={(e) => handleSettingsChange('caching', 'adCacheTtl', parseInt(e.target.value))}
                        min={60}
                        max={3600}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Time to live for individual ad cache entries (default: 300 seconds)
                      </p>
                    </div>

                    <div>
                      <label htmlFor="domain-cache-ttl" className="block text-sm font-medium text-gray-700">
                        Domain Cache TTL (seconds)
                      </label>
                      <input
                        type="number"
                        id="domain-cache-ttl"
                        value={settings.caching.domainCacheTtl}
                        onChange={(e) => handleSettingsChange('caching', 'domainCacheTtl', parseInt(e.target.value))}
                        min={60}
                        max={3600}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Time to live for domain ad cache entries (default: 900 seconds)
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                    </svg>
                    Performance Limits
                  </h4>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="max-ads-per-page" className="block text-sm font-medium text-gray-700">
                        Maximum Ads Per Page
                      </label>
                      <input
                        type="number"
                        id="max-ads-per-page"
                        value={settings.performance.maxAdsPerPage}
                        onChange={(e) => handleSettingsChange('performance', 'maxAdsPerPage', parseInt(e.target.value))}
                        min={1}
                        max={10}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Limit the number of ads that can be displayed on a single page
                      </p>
                    </div>

                    <div>
                      <label htmlFor="refresh-interval" className="block text-sm font-medium text-gray-700">
                        Ad Refresh Interval (seconds)
                      </label>
                      <input
                        type="number"
                        id="refresh-interval"
                        value={settings.performance.refreshInterval}
                        onChange={(e) => handleSettingsChange('performance', 'refreshInterval', parseInt(e.target.value))}
                        min={30}
                        max={300}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        How often to check for new ads (default: 60 seconds)
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-between items-center">
                {saveMessage && (
                  <p className={`text-sm ${saveMessage.includes('Error') ? 'text-red-600' : 'text-green-600'}`}>
                    {saveMessage}
                  </p>
                )}
                <Button
                  onClick={handleSaveSettings}
                  isLoading={isSaving}
                  leftIcon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
                    </svg>
                  }
                >
                  {isSaving ? 'Saving...' : 'Save Settings'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
