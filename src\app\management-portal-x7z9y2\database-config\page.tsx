'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { Spinner } from '@/components/ui/Spinner';
import { Badge } from '@/components/ui/Badge';
import { DatabaseConfig } from '@/lib/types/databaseConfig';
import ConfigurationModal from '@/components/admin/DatabaseConfigModal';

export default function DatabaseConfigPage() {
  const [configurations, setConfigurations] = useState<DatabaseConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentConfig, setCurrentConfig] = useState<DatabaseConfig | null>(null);
  const [actionLoading, setActionLoading] = useState<number | null>(null);

  // Fetch configurations on mount
  useEffect(() => {
    fetchConfigurations();
  }, []);

  // Fetch all configurations
  const fetchConfigurations = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/database-config');

      if (!response.ok) {
        throw new Error(`Failed to fetch configurations: ${response.status} ${response.statusText}`);
      }

      const configs = await response.json();
      setConfigurations(configs);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching configurations');
      console.error('Error fetching configurations:', err);
    } finally {
      setLoading(false);
    }
  };

  // Set a configuration as active
  const handleSetActive = async (id: number) => {
    try {
      setActionLoading(id);

      const response = await fetch('/api/management-portal-x7z9y2/database-config/set-active', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ id })
      });

      const result = await response.json();

      if (result.success) {
        // Refresh the configurations list
        await fetchConfigurations();
      } else {
        setError(result.error || 'Failed to set configuration as active');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while setting active configuration');
      console.error('Error setting active configuration:', err);
    } finally {
      setActionLoading(null);
    }
  };

  // Delete a configuration
  const handleDelete = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this configuration?')) {
      return;
    }

    try {
      setActionLoading(id);

      const response = await fetch(`/api/management-portal-x7z9y2/database-config?id=${id}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        // Refresh the configurations list
        await fetchConfigurations();
      } else {
        setError(result.error || 'Failed to delete configuration');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while deleting configuration');
      console.error('Error deleting configuration:', err);
    } finally {
      setActionLoading(null);
    }
  };

  // Open modal to edit a configuration
  const handleEdit = (config: DatabaseConfig) => {
    setCurrentConfig(config);
    setIsModalOpen(true);
  };

  // Open modal to create a new configuration
  const handleCreate = () => {
    setCurrentConfig(null);
    setIsModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = (refreshNeeded: boolean) => {
    setIsModalOpen(false);

    if (refreshNeeded) {
      fetchConfigurations();
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
            <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
          </svg>
          <h1 className="text-2xl font-bold text-gray-900">Database Configuration</h1>
        </div>
        <p className="mt-2 text-sm text-gray-600 ml-11">
          Manage connection settings for Guerrilla Mail and Supabase databases
        </p>
      </div>

      <Card className="shadow-md border-0">
        <CardHeader>
          <CardTitle className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
            </svg>
            Database Connections
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-md flex-grow">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-700">
                    Create and manage database connection configurations for both Guerrilla Mail and Supabase databases.
                    You can create multiple configurations and set one as active. The active configuration will be used for all database operations.
                  </p>
                </div>
              </div>
            </div>

            <Button
              onClick={handleCreate}
              className="bg-indigo-600 hover:bg-indigo-700 text-white shadow-sm transition-all duration-200 transform hover:scale-105 whitespace-nowrap"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add Configuration
            </Button>
          </div>

          {error && (
            <Alert variant="error" className="mb-6 border border-red-200">
              <AlertDescription className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {error}
              </AlertDescription>
            </Alert>
          )}

          {loading ? (
            <div className="flex flex-col justify-center items-center py-16 bg-gray-50 rounded-lg border border-gray-200">
              <Spinner size="lg" className="text-indigo-600 mb-4" />
              <p className="text-gray-500 animate-pulse">Loading configurations...</p>
            </div>
          ) : configurations.length === 0 ? (
            <div className="text-center py-16 bg-gray-50 border border-dashed border-gray-300 rounded-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
              </svg>
              <p className="text-gray-500 mb-2">No database configurations found</p>
              <p className="text-gray-400 text-sm mb-6">Use the "Add Configuration" button above to get started</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6">
              {configurations.map((config) => (
                <div
                  key={config.id}
                  className={`rounded-lg border ${config.isActive ? 'border-indigo-200 bg-indigo-50/30 shadow-md' : 'border-gray-200 bg-white'} overflow-hidden transition-all duration-200 hover:shadow-md`}
                >
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between p-4 border-b border-gray-100">
                    <div className="flex items-center mb-4 md:mb-0">
                      <div className={`w-2 h-10 rounded-full mr-4 ${config.isActive ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                      <div>
                        <div className="flex flex-wrap items-center">
                          <h3 className="text-lg font-semibold text-gray-900 mr-3">{config.name}</h3>
                          {config.isActive && (
                            <Badge className="mt-1 md:mt-0 bg-green-100 text-green-800 border border-green-200">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                              Active
                            </Badge>
                          )}
                          {!config.isActive && (
                            <Badge variant="secondary" className="mt-1 md:mt-0 text-gray-600 border-gray-300">Inactive</Badge>
                          )}
                        </div>
                        {config.description && (
                          <p className="text-sm text-gray-500 mt-1 pr-4">{config.description}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(config)}
                        disabled={!!actionLoading}
                        className="border-gray-300 text-gray-700 hover:bg-gray-50"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                        Edit
                      </Button>

                      {!config.isActive && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleSetActive(config.id!)}
                          disabled={!!actionLoading}
                          className="border-indigo-300 bg-indigo-50 text-indigo-700 hover:bg-indigo-100"
                        >
                          {actionLoading === config.id ? (
                            <Spinner className="h-4 w-4 text-indigo-600" />
                          ) : (
                            <>
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                              Set Active
                            </>
                          )}
                        </Button>
                      )}

                      {!config.isActive && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(config.id!)}
                          disabled={!!actionLoading}
                          className="border-red-300 text-red-700 hover:bg-red-50"
                        >
                          {actionLoading === config.id ? (
                            <Spinner className="h-4 w-4 text-red-600" />
                          ) : (
                            <>
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                              Delete
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M3 12v3c0 1.657 3.134 3 7 3s7-1.343 7-3v-3c0 1.657-3.134 3-7 3s-7-1.343-7-3z" />
                          <path d="M3 7v3c0 1.657 3.134 3 7 3s7-1.343 7-3V7c0 1.657-3.134 3-7 3S3 8.657 3 7z" />
                          <path d="M17 5c0 1.657-3.134 3-7 3S3 6.657 3 5s3.134-3 7-3 7 1.343 7 3z" />
                        </svg>
                        Guerrilla Database
                      </h4>
                      <div className="space-y-2">
                        <div className="flex flex-wrap">
                          <span className="text-xs font-medium text-gray-500 w-20 min-w-[5rem]">Host:</span>
                          <span className="text-xs text-gray-900 font-mono break-all">{config.guerrillaConfig.host}</span>
                        </div>
                        <div className="flex flex-wrap">
                          <span className="text-xs font-medium text-gray-500 w-20 min-w-[5rem]">Port:</span>
                          <span className="text-xs text-gray-900 font-mono">{config.guerrillaConfig.port}</span>
                        </div>
                        <div className="flex flex-wrap">
                          <span className="text-xs font-medium text-gray-500 w-20 min-w-[5rem]">Database:</span>
                          <span className="text-xs text-gray-900 font-mono break-all">{config.guerrillaConfig.database}</span>
                        </div>
                        <div className="flex flex-wrap">
                          <span className="text-xs font-medium text-gray-500 w-20 min-w-[5rem]">User:</span>
                          <span className="text-xs text-gray-900 font-mono break-all">{config.guerrillaConfig.user}</span>
                        </div>
                        <div className="flex flex-wrap">
                          <span className="text-xs font-medium text-gray-500 w-20 min-w-[5rem]">SSL:</span>
                          <span className="text-xs text-gray-900">{config.guerrillaConfig.ssl ? 'Enabled' : 'Disabled'}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                        </svg>
                        Supabase Configuration
                      </h4>
                      <div className="space-y-2">
                        <div className="flex flex-wrap">
                          <span className="text-xs font-medium text-gray-500 w-20 min-w-[5rem]">URL:</span>
                          <span className="text-xs text-gray-900 font-mono break-all">{config.supabaseConfig.url}</span>
                        </div>
                        <div className="flex flex-wrap">
                          <span className="text-xs font-medium text-gray-500 w-20 min-w-[5rem]">API Key:</span>
                          <span className="text-xs text-gray-900 font-mono">
                            {config.supabaseConfig.apiKey ? '••••••••••••••••••••' : 'Not set'}
                          </span>
                        </div>
                        <div className="flex flex-wrap">
                          <span className="text-xs font-medium text-gray-500 w-20 min-w-[5rem]">Service Key:</span>
                          <span className="text-xs text-gray-900">
                            {config.supabaseConfig.serviceRoleKey ? 'Configured' : 'Not set'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="px-4 py-3 bg-gray-50 text-xs text-gray-500 border-t border-gray-100">
                    Last updated: {config.updatedAt ? new Date(config.updatedAt).toLocaleString() : 'N/A'}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {isModalOpen && (
        <ConfigurationModal
          config={currentConfig}
          onClose={handleModalClose}
        />
      )}
    </div>
  );
}
