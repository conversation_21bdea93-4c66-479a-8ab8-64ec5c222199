/**
 * Global cache registry for the application
 * 
 * This module provides a central registry for all caches in the application,
 * allowing them to be cleared from a single place.
 */
import NodeCache from 'node-cache';

// Create a global cache registry to track all caches in the application
const globalCacheRegistry: Record<string, NodeCache> = {};

/**
 * Register a cache with the global registry
 * @param name The name of the cache
 * @param cache The cache instance
 */
export function registerCache(name: string, cache: NodeCache): void {
  globalCacheRegistry[name] = cache;
  console.log(`Registered cache: ${name}`);
}

/**
 * Get a cache from the global registry
 * @param name The name of the cache
 * @returns The cache instance, or undefined if not found
 */
export function getCache(name: string): NodeCache | undefined {
  return globalCacheRegistry[name];
}

/**
 * Clear a specific cache
 * @param name The name of the cache to clear
 * @returns True if the cache was found and cleared, false otherwise
 */
export function clearCache(name: string): boolean {
  const cache = globalCacheRegistry[name];
  if (cache) {
    cache.flushAll();
    return true;
  }
  return false;
}

/**
 * Clear all caches in the registry
 * @returns An array of cache names that were cleared
 */
export function clearAllCaches(): string[] {
  const clearedCaches: string[] = [];
  
  Object.entries(globalCacheRegistry).forEach(([name, cache]) => {
    cache.flushAll();
    clearedCaches.push(name);
  });
  
  return clearedCaches;
}

/**
 * Get all registered cache names
 * @returns An array of cache names
 */
export function getAllCacheNames(): string[] {
  return Object.keys(globalCacheRegistry);
}

/**
 * Get cache statistics
 * @returns An object with cache statistics
 */
export function getCacheStats(): Record<string, { keys: number, hits: number, misses: number }> {
  const stats: Record<string, { keys: number, hits: number, misses: number }> = {};
  
  Object.entries(globalCacheRegistry).forEach(([name, cache]) => {
    stats[name] = {
      keys: cache.keys().length,
      hits: cache.getStats().hits,
      misses: cache.getStats().misses
    };
  });
  
  return stats;
}

export default globalCacheRegistry;
