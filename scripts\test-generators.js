#!/usr/bin/env node

/**
 * Comprehensive Testing Script for DKIM and DMARC Generators
 * 
 * This script performs end-to-end testing of the email authentication tools
 * including API endpoints, database operations, and performance validation.
 */

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const TEST_DOMAIN = 'test-vanishpost.com';
const TEST_SELECTOR = 'test';

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function assert(condition, message) {
  if (condition) {
    testResults.passed++;
    log(`PASS: ${message}`, 'success');
  } else {
    testResults.failed++;
    testResults.errors.push(message);
    log(`FAIL: ${message}`, 'error');
  }
}

// HTTP request helper
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const protocol = options.protocol === 'https:' ? https : http;
    
    const req = protocol.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const jsonBody = body ? JSON.parse(body) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test functions
async function testDkimGeneration() {
  log('Testing DKIM Generation...');

  const testCases = [
    {
      name: 'Generate 1024-bit DKIM key',
      data: {
        domain: TEST_DOMAIN,
        selector: TEST_SELECTOR,
        keyStrength: 1024,
        sessionId: 'test-1024'
      }
    },
    {
      name: 'Generate 2048-bit DKIM key',
      data: {
        domain: TEST_DOMAIN,
        selector: 'test2048',
        keyStrength: 2048,
        sessionId: 'test-2048'
      }
    }
  ];

  for (const testCase of testCases) {
    try {
      const startTime = Date.now();
      
      const response = await makeRequest({
        hostname: new URL(BASE_URL).hostname,
        port: new URL(BASE_URL).port || (BASE_URL.includes('https') ? 443 : 80),
        path: '/api/tools/dkim-generator/generate',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(JSON.stringify(testCase.data))
        }
      }, testCase.data);

      const duration = Date.now() - startTime;

      // Test response status
      assert(response.statusCode === 200, `${testCase.name}: HTTP 200 response`);

      // Test response structure
      assert(response.body.success === true, `${testCase.name}: Success flag is true`);
      assert(response.body.data, `${testCase.name}: Response contains data`);
      assert(response.body.data.privateKey, `${testCase.name}: Private key generated`);
      assert(response.body.data.publicKey, `${testCase.name}: Public key generated`);
      assert(response.body.data.dnsRecord, `${testCase.name}: DNS record generated`);
      assert(response.body.data.recordName, `${testCase.name}: Record name generated`);

      // Test DNS record format
      const dnsRecord = response.body.data.dnsRecord;
      assert(dnsRecord.includes('v=DKIM1'), `${testCase.name}: DNS record has DKIM version`);
      assert(dnsRecord.includes('k=rsa'), `${testCase.name}: DNS record specifies RSA key`);
      assert(dnsRecord.includes('p='), `${testCase.name}: DNS record contains public key`);

      // Test record name format
      const expectedRecordName = `${testCase.data.selector}._domainkey.${testCase.data.domain}`;
      assert(response.body.data.recordName === expectedRecordName, 
        `${testCase.name}: Record name format correct`);

      // Test key strength
      assert(response.body.data.keyStrength === testCase.data.keyStrength,
        `${testCase.name}: Key strength matches request`);

      // Test performance
      assert(duration < 10000, `${testCase.name}: Generation completed within 10 seconds (${duration}ms)`);

      // Test key length (2048-bit keys should be longer)
      if (testCase.data.keyStrength === 2048) {
        assert(response.body.data.publicKey.length > 300,
          `${testCase.name}: 2048-bit public key is appropriately long`);
      }

      log(`${testCase.name}: Completed in ${duration}ms`);

    } catch (error) {
      assert(false, `${testCase.name}: ${error.message}`);
    }
  }
}

async function testDmarcGeneration() {
  log('Testing DMARC Generation...');

  const testCases = [
    {
      name: 'Generate DMARC with none policy',
      data: {
        domain: TEST_DOMAIN,
        policy: 'none',
        rua: 'mailto:<EMAIL>',
        pct: 100,
        sessionId: 'test-dmarc-none'
      }
    },
    {
      name: 'Generate DMARC with quarantine policy',
      data: {
        domain: TEST_DOMAIN,
        policy: 'quarantine',
        rua: 'mailto:<EMAIL>',
        pct: 50,
        sp: 'reject',
        adkim: 's',
        aspf: 'r',
        sessionId: 'test-dmarc-quarantine'
      }
    },
    {
      name: 'Generate DMARC with reject policy',
      data: {
        domain: TEST_DOMAIN,
        policy: 'reject',
        rua: 'mailto:<EMAIL>',
        ruf: 'mailto:<EMAIL>',
        pct: 100,
        sessionId: 'test-dmarc-reject'
      }
    }
  ];

  for (const testCase of testCases) {
    try {
      const startTime = Date.now();
      
      const response = await makeRequest({
        hostname: new URL(BASE_URL).hostname,
        port: new URL(BASE_URL).port || (BASE_URL.includes('https') ? 443 : 80),
        path: '/api/tools/dmarc-generator/generate',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(JSON.stringify(testCase.data))
        }
      }, testCase.data);

      const duration = Date.now() - startTime;

      // Test response status
      assert(response.statusCode === 200, `${testCase.name}: HTTP 200 response`);

      // Test response structure
      assert(response.body.success === true, `${testCase.name}: Success flag is true`);
      assert(response.body.data, `${testCase.name}: Response contains data`);
      assert(response.body.data.dnsRecord, `${testCase.name}: DNS record generated`);
      assert(response.body.data.recordName, `${testCase.name}: Record name generated`);

      // Test DNS record format
      const dnsRecord = response.body.data.dnsRecord;
      assert(dnsRecord.includes('v=DMARC1'), `${testCase.name}: DNS record has DMARC version`);
      assert(dnsRecord.includes(`p=${testCase.data.policy}`), 
        `${testCase.name}: DNS record has correct policy`);
      assert(dnsRecord.includes(`rua=${testCase.data.rua}`), 
        `${testCase.name}: DNS record has RUA address`);

      // Test record name format
      const expectedRecordName = `_dmarc.${testCase.data.domain}`;
      assert(response.body.data.recordName === expectedRecordName, 
        `${testCase.name}: Record name format correct`);

      // Test policy-specific fields
      if (testCase.data.sp) {
        assert(dnsRecord.includes(`sp=${testCase.data.sp}`), 
          `${testCase.name}: DNS record has subdomain policy`);
      }

      if (testCase.data.ruf) {
        assert(dnsRecord.includes(`ruf=${testCase.data.ruf}`), 
          `${testCase.name}: DNS record has RUF address`);
      }

      // Test performance
      assert(duration < 5000, `${testCase.name}: Generation completed within 5 seconds (${duration}ms)`);

      log(`${testCase.name}: Completed in ${duration}ms`);

    } catch (error) {
      assert(false, `${testCase.name}: ${error.message}`);
    }
  }
}

async function testErrorHandling() {
  log('Testing Error Handling...');

  const errorTestCases = [
    {
      name: 'DKIM: Missing required fields',
      endpoint: '/api/tools/dkim-generator/generate',
      data: { domain: TEST_DOMAIN }, // Missing selector and keyStrength
      expectedStatus: 400
    },
    {
      name: 'DKIM: Invalid domain format',
      endpoint: '/api/tools/dkim-generator/generate',
      data: {
        domain: 'invalid..domain',
        selector: 'test',
        keyStrength: 1024,
        sessionId: 'test-invalid-domain'
      },
      expectedStatus: 400
    },
    {
      name: 'DKIM: Invalid key strength',
      endpoint: '/api/tools/dkim-generator/generate',
      data: {
        domain: TEST_DOMAIN,
        selector: 'test',
        keyStrength: 512, // Invalid
        sessionId: 'test-invalid-key'
      },
      expectedStatus: 400
    },
    {
      name: 'DMARC: Missing required fields',
      endpoint: '/api/tools/dmarc-generator/generate',
      data: { domain: TEST_DOMAIN }, // Missing policy and rua
      expectedStatus: 400
    },
    {
      name: 'DMARC: Invalid policy',
      endpoint: '/api/tools/dmarc-generator/generate',
      data: {
        domain: TEST_DOMAIN,
        policy: 'invalid',
        rua: 'mailto:<EMAIL>',
        pct: 100,
        sessionId: 'test-invalid-policy'
      },
      expectedStatus: 400
    }
  ];

  for (const testCase of errorTestCases) {
    try {
      const response = await makeRequest({
        hostname: new URL(BASE_URL).hostname,
        port: new URL(BASE_URL).port || (BASE_URL.includes('https') ? 443 : 80),
        path: testCase.endpoint,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(JSON.stringify(testCase.data))
        }
      }, testCase.data);

      assert(response.statusCode === testCase.expectedStatus, 
        `${testCase.name}: Returns HTTP ${testCase.expectedStatus}`);
      assert(response.body.success === false, 
        `${testCase.name}: Success flag is false`);
      assert(response.body.message, 
        `${testCase.name}: Error message provided`);

    } catch (error) {
      assert(false, `${testCase.name}: ${error.message}`);
    }
  }
}

async function testPerformance() {
  log('Testing Performance...');

  // Test concurrent DKIM generations
  const concurrentTests = Array(5).fill(null).map((_, i) => ({
    domain: `test${i}.vanishpost.com`,
    selector: 'test',
    keyStrength: 1024,
    sessionId: `concurrent-test-${i}`
  }));

  const startTime = Date.now();
  
  try {
    const promises = concurrentTests.map(data => 
      makeRequest({
        hostname: new URL(BASE_URL).hostname,
        port: new URL(BASE_URL).port || (BASE_URL.includes('https') ? 443 : 80),
        path: '/api/tools/dkim-generator/generate',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(JSON.stringify(data))
        }
      }, data)
    );

    const responses = await Promise.all(promises);
    const totalDuration = Date.now() - startTime;

    // All requests should succeed
    const allSuccessful = responses.every(r => r.statusCode === 200 && r.body.success);
    assert(allSuccessful, 'Concurrent DKIM generations: All requests successful');

    // Should complete within reasonable time
    assert(totalDuration < 30000, 
      `Concurrent DKIM generations: Completed within 30 seconds (${totalDuration}ms)`);

    log(`Concurrent test completed in ${totalDuration}ms`);

  } catch (error) {
    assert(false, `Concurrent DKIM generations: ${error.message}`);
  }
}

// Main test runner
async function runTests() {
  log('🚀 Starting VanishPost Email Authentication Tools Test Suite');
  log(`Testing against: ${BASE_URL}`);
  
  try {
    await testDkimGeneration();
    await testDmarcGeneration();
    await testErrorHandling();
    await testPerformance();

    // Print summary
    log('\n📊 Test Summary:');
    log(`✅ Passed: ${testResults.passed}`);
    log(`❌ Failed: ${testResults.failed}`);
    
    if (testResults.failed > 0) {
      log('\n🔍 Failed Tests:');
      testResults.errors.forEach(error => log(`  • ${error}`, 'error'));
      process.exit(1);
    } else {
      log('\n🎉 All tests passed! The email authentication tools are working correctly.');
      process.exit(0);
    }

  } catch (error) {
    log(`Fatal error during testing: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests, testResults };
