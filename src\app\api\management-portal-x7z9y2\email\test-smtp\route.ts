import { NextRequest, NextResponse } from 'next/server';
import { testSmtpConnection } from '@/lib/services/emailService';
import { logError } from '@/lib/logging';

/**
 * API route for testing SMTP connection
 */
export async function GET(request: NextRequest) {
  try {
    const result = await testSmtpConnection();
    
    return NextResponse.json(result);
  } catch (error) {
    logError('email', 'Error testing SMTP connection', { error });
    return NextResponse.json({
      success: false,
      message: 'An error occurred while testing SMTP connection'
    }, { status: 500 });
  }
}
