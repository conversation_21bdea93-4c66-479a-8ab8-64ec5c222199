import { useState, useCallback } from 'react';

/**
 * Hook for tracking image loading in iframes
 * 
 * This hook provides functionality to:
 * - Track total number of images in iframe content
 * - Track number of loaded images
 * - Calculate loading progress
 */
export function useImageLoading() {
  // State to track total images
  const [totalImages, setTotalImages] = useState<number>(0);
  
  // State to track loaded images
  const [loadedImages, setLoadedImages] = useState<number>(0);
  
  // Handle total images message
  const handleTotalImages = useCallback((count: number) => {
    setTotalImages(count);
  }, []);
  
  // Handle images loaded message
  const handleImagesLoaded = useCallback((count: number, total: number) => {
    setLoadedImages(count);
    
    // If total is provided and different from current total, update it
    if (total !== undefined && total !== totalImages) {
      setTotalImages(total);
    }
  }, [totalImages]);
  
  // Reset loading state
  const resetImageLoading = useCallback(() => {
    setTotalImages(0);
    setLoadedImages(0);
  }, []);
  
  // Calculate loading progress percentage
  const imageLoadingProgress = totalImages > 0 
    ? Math.round((loadedImages / totalImages) * 100) 
    : 100;
  
  // Check if all images are loaded
  const allImagesLoaded = totalImages === 0 || loadedImages >= totalImages;
  
  return {
    totalImages,
    loadedImages,
    imageLoadingProgress,
    allImagesLoaded,
    handleTotalImages,
    handleImagesLoaded,
    resetImageLoading
  };
}
