/**
 * API route for managing individual ad placements
 */
import { NextRequest, NextResponse } from 'next/server';
import { getAdPlacement, updateAdPlacement, deleteAdPlacement } from '@/lib/config/adService';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/management-portal-x7z9y2/ads/[placementId]/[domain]
 *
 * Get a specific ad placement
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ placementId: string; domain: string }> }
) {
  try {
    // Get the placementId and domain from the params
    const resolvedParams = await params;
    const { placementId, domain } = resolvedParams;

    // Decode the placementId in case it contains URL-encoded characters
    const decodedPlacementId = decodeURIComponent(placementId);

    // Handle special characters in the placementId
    // This ensures that the placementId is properly handled in the database query
    const sanitizedPlacementId = decodedPlacementId.replace(/[^a-zA-Z0-9-_]/g, '_');

    // Log the received parameters for debugging
    console.log(`Getting ad placement: ${decodedPlacementId} for domain: ${domain}`);

    // Additional logging for troubleshooting
    console.log(`Original placementId from URL: ${placementId}`);
    console.log(`Decoded placementId: ${decodedPlacementId}`);
    console.log(`Sanitized placementId: ${sanitizedPlacementId}`);

    // Get ad placement using the sanitized placement ID
    console.log(`Calling getAdPlacement with placementId: "${sanitizedPlacementId}", domain: "${domain}"`);
    const adPlacement = await getAdPlacement(sanitizedPlacementId, domain);

    if (!adPlacement) {
      return NextResponse.json(
        { success: false, error: 'Ad placement not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: adPlacement
    });
  } catch (error) {
    logError('api', 'Error getting ad placement', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to get ad placement' },
      { status: 500 }
    );
  }
}



/**
 * PUT /api/management-portal-x7z9y2/ads/[placementId]/[domain]
 *
 * Update an ad placement
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ placementId: string; domain: string }> }
) {
  try {
    // Get the placementId and domain from the params
    const resolvedParams = await params;
    const { placementId, domain } = resolvedParams;

    // Decode the placementId in case it contains URL-encoded characters
    const decodedPlacementId = decodeURIComponent(placementId);

    // Handle special characters in the placementId
    // This ensures that the placementId is properly handled in the database query
    const sanitizedPlacementId = decodedPlacementId.replace(/[^a-zA-Z0-9-_]/g, '_');

    // Log the received parameters for debugging
    console.log(`Updating ad placement: ${decodedPlacementId} for domain: ${domain}`);

    // Additional logging for troubleshooting
    console.log(`Original placementId from URL: ${placementId}`);
    console.log(`Decoded placementId: ${decodedPlacementId}`);
    console.log(`Sanitized placementId: ${sanitizedPlacementId}`);

    // Get update data from request body
    const updates = await request.json();

    try {
      // Update ad placement using the sanitized placement ID
      console.log(`Calling updateAdPlacement with placementId: "${sanitizedPlacementId}", domain: "${domain}"`);

      const updatedAd = await updateAdPlacement(sanitizedPlacementId, domain, updates);

      logInfo('api', 'Updated ad placement', {
        placementId: decodedPlacementId,
        domain,
        updates
      });

      return NextResponse.json({
        success: true,
        data: updatedAd,
        message: 'Ad placement updated successfully'
      });
    } catch (dbError) {
      // Check if this is a not found error
      const errorMessage = dbError instanceof Error ? dbError.message : 'Unknown error';
      console.error(`Error updating ad placement: ${errorMessage}`);

      if (errorMessage.includes('not found')) {
        return NextResponse.json(
          { success: false, error: `Ad placement not found: ${decodedPlacementId} for domain: ${domain}` },
          { status: 404 }
        );
      }

      // Log the error and return a 500 response
      logError('api', 'Database error updating ad placement', { error: dbError, placementId: decodedPlacementId, domain });

      return NextResponse.json(
        { success: false, error: `Database error updating ad placement: ${errorMessage}` },
        { status: 500 }
      );
    }
  } catch (error) {
    logError('api', 'Error updating ad placement', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to update ad placement' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/management-portal-x7z9y2/ads/[placementId]/[domain]
 *
 * Delete an ad placement
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ placementId: string; domain: string }> }
) {
  try {
    // Get the placementId and domain from the params
    const resolvedParams = await params;
    const { placementId, domain } = resolvedParams;

    // Decode the placementId in case it contains URL-encoded characters
    const decodedPlacementId = decodeURIComponent(placementId);

    // Handle special characters in the placementId
    // This ensures that the placementId is properly handled in the database query
    const sanitizedPlacementId = decodedPlacementId.replace(/[^a-zA-Z0-9-_]/g, '_');

    // Log the received parameters for debugging
    console.log(`Deleting ad placement: ${decodedPlacementId} for domain: ${domain}`);

    // Additional logging for troubleshooting
    console.log(`Original placementId from URL: ${placementId}`);
    console.log(`Decoded placementId: ${decodedPlacementId}`);
    console.log(`Sanitized placementId: ${sanitizedPlacementId}`);

    try {
      // Delete ad placement using the sanitized placement ID
      console.log(`Calling deleteAdPlacement with placementId: "${sanitizedPlacementId}", domain: "${domain}"`);
      const success = await deleteAdPlacement(sanitizedPlacementId, domain);

      if (!success) {
        return NextResponse.json(
          { success: false, error: 'Failed to delete ad placement' },
          { status: 500 }
        );
      }

      logInfo('api', 'Deleted ad placement', {
        placementId: decodedPlacementId,
        domain
      });

      return NextResponse.json({
        success: true,
        message: 'Ad placement deleted successfully'
      });
    } catch (dbError) {
      // Log the error and return a 500 response
      logError('api', 'Database error deleting ad placement', { error: dbError, placementId: decodedPlacementId, domain });

      return NextResponse.json(
        { success: false, error: 'Database error deleting ad placement' },
        { status: 500 }
      );
    }
  } catch (error) {
    logError('api', 'Error deleting ad placement', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to delete ad placement' },
      { status: 500 }
    );
  }
}
