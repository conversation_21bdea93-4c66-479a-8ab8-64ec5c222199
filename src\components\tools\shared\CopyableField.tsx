'use client';

// Shared Copyable Field Component - Placeholder implementation
import React, { useState } from 'react';

interface CopyableFieldProps {
  label: string;
  value: string;
  multiline?: boolean;
  sensitive?: boolean;
  className?: string;
}

export default function CopyableField({
  label,
  value,
  multiline = false,
  sensitive = false,
  className = ''
}: CopyableFieldProps) {
  const [copied, setCopied] = useState(false);
  const [isVisible, setIsVisible] = useState(!sensitive);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(value);
      setCopied(true);
      setTimeout(() => setCopied(false), 2500);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const displayValue = isVisible ? value : '••••••••••••••••••••••••••••••••';

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-[#1b130e] mb-2">
          {label}
        </label>
      )}

      <div className="relative">
        <div className="bg-[#f3ece8] rounded-md">
          <div className="flex">
            {multiline ? (
              <textarea
                value={displayValue}
                readOnly
                className="w-full bg-transparent text-sm text-[#1b130e] resize-none border-none outline-none p-3 flex-1 min-w-0"
                rows={Math.min(Math.max(value.split('\n').length, 3), 10)}
              />
            ) : (
              <code className="text-sm text-[#1b130e] break-all p-3 flex-1 min-w-0">
                {displayValue}
              </code>
            )}
            <div className="flex-shrink-0 p-3 pl-2 flex items-start space-x-2">
              {sensitive && (
                <button
                  type="button"
                  onClick={() => setIsVisible(!isVisible)}
                  className="text-sm text-[#4a3728] hover:text-[#1b130e] transition-colors"
                >
                  {isVisible ? 'Hide' : 'Show'}
                </button>
              )}
              <button
                type="button"
                onClick={handleCopy}
                className={`transition-all duration-200 ${
                  copied
                    ? 'text-[#1b130e] scale-110'
                    : 'text-[#4a3728] hover:text-[#1b130e] hover:scale-105'
                }`}
                title={copied ? 'Copied!' : 'Copy'}
              >
                {copied ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {sensitive && (
        <div className="border border-[#f59e0b] rounded p-2">
          <p className="text-xs text-[#1b130e]">
            <strong>⚠️ Sensitive Data:</strong> Keep this information secure and never share it publicly.
          </p>
        </div>
      )}
    </div>
  );
}
