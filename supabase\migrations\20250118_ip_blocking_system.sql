-- IP Blocking System for VanishPost
-- This migration creates tables for IP blocking, whitelisting, and rate limit violation tracking

-- Table for blocked IP addresses
CREATE TABLE IF NOT EXISTS blocked_ips (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ip_address INET NOT NULL,
    reason TEXT NOT NULL,
    blocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    blocked_by TEXT NOT NULL, -- Admin username who blocked the IP
    expires_at TIMESTAMP WITH TIME ZONE, -- Optional expiration
    unblocked_at TIMESTAMP WITH TIME ZONE,
    unblocked_by TEXT, -- Admin username who unblocked the IP
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}', -- Additional data like user agent, country, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for whitelisted IP addresses (bypass rate limits)
CREATE TABLE IF NOT EXISTS ip_whitelist (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ip_address INET NOT NULL,
    reason TEXT NOT NULL,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    added_by TEXT NOT NULL, -- Admin username who added the IP
    removed_at TIMESTAMP WITH TIME ZONE,
    removed_by TEXT, -- Admin username who removed the IP
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for tracking rate limit violations
CREATE TABLE IF NOT EXISTS rate_limit_violations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ip_address INET NOT NULL,
    endpoint TEXT NOT NULL, -- Which endpoint was violated (e.g., 'emailGeneration')
    violation_count INTEGER DEFAULT 1,
    first_violation TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_violation TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_agent TEXT,
    is_resolved BOOLEAN DEFAULT FALSE, -- Whether the violation has been addressed
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by TEXT, -- Admin who resolved the violation
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_blocked_ips_ip_address ON blocked_ips(ip_address);
CREATE INDEX IF NOT EXISTS idx_blocked_ips_active ON blocked_ips(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_blocked_ips_expires ON blocked_ips(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_ip_whitelist_ip_address ON ip_whitelist(ip_address);
CREATE INDEX IF NOT EXISTS idx_ip_whitelist_active ON ip_whitelist(is_active) WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_rate_violations_ip_endpoint ON rate_limit_violations(ip_address, endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_violations_unresolved ON rate_limit_violations(is_resolved) WHERE is_resolved = FALSE;
CREATE INDEX IF NOT EXISTS idx_rate_violations_last_violation ON rate_limit_violations(last_violation);

-- Unique constraints
CREATE UNIQUE INDEX IF NOT EXISTS idx_blocked_ips_unique_active 
ON blocked_ips(ip_address) WHERE is_active = TRUE;

CREATE UNIQUE INDEX IF NOT EXISTS idx_ip_whitelist_unique_active 
ON ip_whitelist(ip_address) WHERE is_active = TRUE;

CREATE UNIQUE INDEX IF NOT EXISTS idx_rate_violations_unique_unresolved 
ON rate_limit_violations(ip_address, endpoint) WHERE is_resolved = FALSE;

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_blocked_ips_updated_at 
    BEFORE UPDATE ON blocked_ips 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ip_whitelist_updated_at 
    BEFORE UPDATE ON ip_whitelist 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rate_limit_violations_updated_at 
    BEFORE UPDATE ON rate_limit_violations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up expired blocks automatically
CREATE OR REPLACE FUNCTION cleanup_expired_blocks()
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    UPDATE blocked_ips 
    SET is_active = FALSE,
        unblocked_at = NOW(),
        unblocked_by = 'system_expiration'
    WHERE is_active = TRUE 
    AND expires_at IS NOT NULL 
    AND expires_at < NOW();
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to clean up expired blocks (if pg_cron is available)
-- This will run every hour to clean up expired blocks
-- Note: pg_cron extension needs to be enabled for this to work
-- SELECT cron.schedule('cleanup-expired-blocks', '0 * * * *', 'SELECT cleanup_expired_blocks();');

-- Insert some example data for testing (remove in production)
-- INSERT INTO ip_whitelist (ip_address, reason, added_by) VALUES 
-- ('127.0.0.1', 'Localhost for development', 'system'),
-- ('::1', 'IPv6 localhost for development', 'system');

-- Comments for documentation
COMMENT ON TABLE blocked_ips IS 'Stores IP addresses that are blocked from accessing the service';
COMMENT ON TABLE ip_whitelist IS 'Stores IP addresses that bypass rate limiting';
COMMENT ON TABLE rate_limit_violations IS 'Tracks rate limit violations for monitoring and automatic blocking';

COMMENT ON COLUMN blocked_ips.metadata IS 'JSON field for storing additional information like user agent, country, violation history';
COMMENT ON COLUMN blocked_ips.expires_at IS 'Optional expiration date for temporary blocks';
COMMENT ON COLUMN rate_limit_violations.endpoint IS 'The rate-limited endpoint that was violated (emailGeneration, analytics, etc.)';
COMMENT ON COLUMN rate_limit_violations.is_resolved IS 'Whether the violation has been addressed by an admin';

-- Grant permissions (adjust based on your RLS policies)
-- These are basic permissions - you should implement Row Level Security (RLS) policies
-- ALTER TABLE blocked_ips ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE ip_whitelist ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE rate_limit_violations ENABLE ROW LEVEL SECURITY;
