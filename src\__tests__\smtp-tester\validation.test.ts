/**
 * SMTP Validation Unit Tests
 * 
 * Tests for input validation and sanitization functions
 */

import {
  validateEmailAddress,
  validateSmtpServer,
  validatePort,
  sanitizeString,
  validateSmtpConfig,
  validateSmtpTestRequest,
  sanitizeSmtpConfig,
  sanitizeSmtpTestRequest
} from '@/lib/tools/smtp-tester/validation';
import { SmtpConfig, SmtpTestRequest } from '@/types/smtp';

describe('SMTP Validation Functions', () => {
  describe('validateEmailAddress', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmailAddress('<EMAIL>')).toBe(true);
      expect(validateEmailAddress('<EMAIL>')).toBe(true);
      expect(validateEmailAddress('<EMAIL>')).toBe(true);
      expect(validateEmailAddress('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(validateEmailAddress('invalid-email')).toBe(false);
      expect(validateEmailAddress('test@')).toBe(false);
      expect(validateEmailAddress('@domain.com')).toBe(false);
      expect(validateEmailAddress('<EMAIL>')).toBe(false);
      expect(validateEmailAddress('')).toBe(false);
      expect(validateEmailAddress('test@domain')).toBe(false);
    });

    it('should handle whitespace correctly', () => {
      expect(validateEmailAddress(' <EMAIL> ')).toBe(true);
      expect(validateEmailAddress('test @example.com')).toBe(false);
    });
  });

  describe('validateSmtpServer', () => {
    it('should validate correct SMTP server addresses', () => {
      expect(validateSmtpServer('smtp.gmail.com')).toBe(true);
      expect(validateSmtpServer('mail.example.org')).toBe(true);
      expect(validateSmtpServer('smtp-relay.company.co.uk')).toBe(true);
      expect(validateSmtpServer('mail123.test-domain.net')).toBe(true);
    });

    it('should reject invalid SMTP server addresses', () => {
      expect(validateSmtpServer('invalid-server')).toBe(false);
      expect(validateSmtpServer('smtp.')).toBe(false);
      expect(validateSmtpServer('.gmail.com')).toBe(false);
      expect(validateSmtpServer('')).toBe(false);
      expect(validateSmtpServer('smtp..gmail.com')).toBe(false);
    });

    it('should handle whitespace correctly', () => {
      expect(validateSmtpServer(' smtp.gmail.com ')).toBe(true);
      expect(validateSmtpServer('smtp .gmail.com')).toBe(false);
    });
  });

  describe('validatePort', () => {
    it('should validate correct port numbers', () => {
      expect(validatePort(25)).toBe(true);
      expect(validatePort(587)).toBe(true);
      expect(validatePort(465)).toBe(true);
      expect(validatePort(2525)).toBe(true);
      expect(validatePort(1)).toBe(true);
      expect(validatePort(65535)).toBe(true);
    });

    it('should reject invalid port numbers', () => {
      expect(validatePort(0)).toBe(false);
      expect(validatePort(-1)).toBe(false);
      expect(validatePort(65536)).toBe(false);
      expect(validatePort(1.5)).toBe(false);
      expect(validatePort(NaN)).toBe(false);
      expect(validatePort(Infinity)).toBe(false);
    });
  });

  describe('sanitizeString', () => {
    it('should remove dangerous characters', () => {
      expect(sanitizeString('test<script>alert("xss")</script>')).toBe('testscriptalert("xss")/script');
      expect(sanitizeString('normal text')).toBe('normal text');
      expect(sanitizeString('  spaced text  ')).toBe('spaced text');
    });

    it('should handle empty and whitespace strings', () => {
      expect(sanitizeString('')).toBe('');
      expect(sanitizeString('   ')).toBe('');
      expect(sanitizeString('\t\n\r')).toBe('');
    });
  });

  describe('validateSmtpConfig', () => {
    const validConfig: SmtpConfig = {
      server: 'smtp.gmail.com',
      port: 587,
      encryption: 'tls',
      username: '<EMAIL>',
      password: 'password123',
      sender: '<EMAIL>'
    };

    it('should validate correct SMTP configuration', () => {
      const result = validateSmtpConfig(validConfig);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid server', () => {
      const invalidConfig = { ...validConfig, server: 'invalid-server' };
      const result = validateSmtpConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid SMTP server address');
    });

    it('should reject invalid port', () => {
      const invalidConfig = { ...validConfig, port: 70000 };
      const result = validateSmtpConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Port must be between 1 and 65535');
    });

    it('should reject invalid encryption', () => {
      const invalidConfig = { ...validConfig, encryption: 'invalid' as any };
      const result = validateSmtpConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid encryption type');
    });

    it('should reject empty username', () => {
      const invalidConfig = { ...validConfig, username: '' };
      const result = validateSmtpConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Username is required');
    });

    it('should reject empty password', () => {
      const invalidConfig = { ...validConfig, password: '' };
      const result = validateSmtpConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password is required');
    });

    it('should reject invalid sender email', () => {
      const invalidConfig = { ...validConfig, sender: 'invalid-email' };
      const result = validateSmtpConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Valid sender email address is required');
    });

    it('should collect multiple validation errors', () => {
      const invalidConfig: SmtpConfig = {
        server: 'invalid',
        port: -1,
        encryption: 'invalid' as any,
        username: '',
        password: '',
        sender: 'invalid-email'
      };
      const result = validateSmtpConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
    });
  });

  describe('validateSmtpTestRequest', () => {
    const validRequest: SmtpTestRequest = {
      config: {
        server: 'smtp.gmail.com',
        port: 587,
        encryption: 'tls',
        username: '<EMAIL>',
        password: 'password123',
        sender: '<EMAIL>'
      },
      testMode: 'auto'
    };

    it('should validate correct auto mode request', () => {
      const result = validateSmtpTestRequest(validRequest);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate correct custom mode request', () => {
      const customRequest = {
        ...validRequest,
        testMode: 'custom' as const,
        recipient: '<EMAIL>'
      };
      const result = validateSmtpTestRequest(customRequest);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid test mode', () => {
      const invalidRequest = { ...validRequest, testMode: 'invalid' as any };
      const result = validateSmtpTestRequest(invalidRequest);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid test mode');
    });

    it('should require recipient for custom mode', () => {
      const invalidRequest = { ...validRequest, testMode: 'custom' as const };
      const result = validateSmtpTestRequest(invalidRequest);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Valid recipient email address is required for custom mode');
    });

    it('should reject invalid recipient email in custom mode', () => {
      const invalidRequest = {
        ...validRequest,
        testMode: 'custom' as const,
        recipient: 'invalid-email'
      };
      const result = validateSmtpTestRequest(invalidRequest);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Valid recipient email address is required for custom mode');
    });
  });

  describe('sanitizeSmtpConfig', () => {
    it('should sanitize SMTP configuration', () => {
      const dirtyConfig: SmtpConfig = {
        server: ' smtp.gmail.com ',
        port: 587,
        encryption: 'tls',
        username: ' <EMAIL> ',
        password: 'password123',
        sender: ' <EMAIL> '
      };

      const sanitized = sanitizeSmtpConfig(dirtyConfig);
      expect(sanitized.server).toBe('smtp.gmail.com');
      expect(sanitized.username).toBe('<EMAIL>');
      expect(sanitized.sender).toBe('<EMAIL>');
      expect(sanitized.password).toBe('password123'); // Password should not be trimmed
    });

    it('should remove dangerous characters', () => {
      const dangerousConfig: SmtpConfig = {
        server: 'smtp<script>.gmail.com',
        port: 587,
        encryption: 'tls',
        username: 'test<>@gmail.com',
        password: 'password123',
        sender: 'test<>@gmail.com'
      };

      const sanitized = sanitizeSmtpConfig(dangerousConfig);
      expect(sanitized.server).toBe('smtpscript.gmail.com');
      expect(sanitized.username).toBe('<EMAIL>');
      expect(sanitized.sender).toBe('<EMAIL>');
    });
  });

  describe('sanitizeSmtpTestRequest', () => {
    it('should sanitize SMTP test request', () => {
      const dirtyRequest: SmtpTestRequest = {
        config: {
          server: ' smtp.gmail.com ',
          port: 587,
          encryption: 'tls',
          username: ' <EMAIL> ',
          password: 'password123',
          sender: ' <EMAIL> '
        },
        testMode: 'custom',
        recipient: ' <EMAIL> '
      };

      const sanitized = sanitizeSmtpTestRequest(dirtyRequest);
      expect(sanitized.config.server).toBe('smtp.gmail.com');
      expect(sanitized.config.username).toBe('<EMAIL>');
      expect(sanitized.recipient).toBe('<EMAIL>');
    });
  });
});
