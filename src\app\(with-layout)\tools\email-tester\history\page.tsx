/**
 * Email Tester History Page
 *
 * This page displays the history of email tests.
 */

'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import EmailTesterLoadingOverlay from '@/components/tools/email-tester/EmailTesterLoadingOverlay';
import Breadcrumbs from '@/components/seo/Breadcrumbs';
import { WebApplicationSchema } from '@/components/seo/StructuredData';

interface TestHistoryItem {
  id: string;
  created_at: string;
  sender_domain: string;
  overall_score: number;
  spf_result: string;
  dkim_result: string;
  dmarc_result: string;
  email_tester_addresses: {
    test_address: string;
  };
}

export default function HistoryPage() {
  const [history, setHistory] = useState<TestHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        const response = await fetch('/api/tools/email-tester/history');
        const data = await response.json();

        if (data.success) {
          setHistory(data.history);
        } else {
          setError(data.message || 'Failed to load test history');
        }
      } catch (error) {
        setError('An error occurred while loading the test history');
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchHistory();
  }, []);

  // Helper function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Helper function to determine score color (earth-tone colors)
  const getScoreColor = (score: number) => {
    if (score >= 8) return '#66b077'; // Green
    if (score >= 5) return '#f59e0b'; // Amber
    return '#f59e0b'; // Amber for low scores
  };

  // Helper function to determine status color (earth-tone colors)
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass':
        return '#66b077'; // Green
      case 'fail':
        return '#f59e0b'; // Amber
      case 'neutral':
        return '#f59e0b'; // Amber
      default:
        return '#4a3728'; // Dark brown
    }
  };

  // Breadcrumb items
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Tools', url: '/tools' },
    { name: 'Email Tester', url: '/tools/email-tester' },
    { name: 'Test History', url: '/tools/email-tester/history', current: true },
  ];

  if (isLoading) {
    return <EmailTesterLoadingOverlay isVisible={true} message="Loading test history..." />;
  }

  return (
    <>
      {/* Structured Data */}
      <WebApplicationSchema
        name="Email Test History"
        description="View and track your email deliverability test history and performance improvements over time"
        url="https://vanishpost.com/tools/email-tester/history"
        applicationCategory="BusinessApplication"
      />

      <div className="min-h-screen" style={{ backgroundColor: '#fbfaf8' }}>
        <div className="max-w-5xl mx-auto px-4 py-8">
          {/* Breadcrumb Navigation */}
          <Breadcrumbs items={breadcrumbItems} />

          <div className="mb-8">
            <Link
              href="/tools/email-tester"
              className="group inline-flex items-center space-x-2 transition-colors duration-200"
              style={{ color: '#956b50' }}
              onMouseEnter={(e) => e.currentTarget.style.color = '#1b130e'}
              onMouseLeave={(e) => e.currentTarget.style.color = '#956b50'}
            >
              <svg className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span className="font-medium">Back to Email Tester</span>
            </Link>
          </div>

          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-2" style={{ color: '#1b130e' }}>Email Test History - Track Deliverability Performance</h1>
            <p className="text-base max-w-2xl mx-auto" style={{ color: '#4a3728' }}>
              View your previous email deliverability test results, track SPF, DKIM, DMARC improvements, and monitor email authentication performance over time.
            </p>
          </div>

        {error && (
          <div className="px-6 py-4 rounded-2xl mb-8" style={{ backgroundColor: '#f59e0b', border: '1px solid #f59e0b', color: '#ffffff' }}>
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#ffffff' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {error}
            </div>
          </div>
        )}

        {history.length > 0 ? (
          <div className="rounded-2xl overflow-hidden" style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8' }}>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead style={{ backgroundColor: '#f3ece8' }}>
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: '#4a3728' }}>
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: '#4a3728' }}>
                    Domain
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: '#4a3728' }}>
                    Score
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: '#4a3728' }}>
                    SPF
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: '#4a3728' }}>
                    DKIM
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: '#4a3728' }}>
                    DMARC
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: '#4a3728' }}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody style={{ backgroundColor: '#fbfaf8' }}>
                {history.map((item) => (
                  <tr key={item.id} className="hover:opacity-80 transition-opacity duration-200" style={{ borderBottom: '1px solid #f3ece8' }}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm" style={{ color: '#4a3728' }}>
                      {formatDate(item.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono" style={{ color: '#1b130e' }}>
                      {item.sender_domain}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium" style={{ color: getScoreColor(item.overall_score) }}>
                        {item.overall_score}/10
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium" style={{ color: getStatusColor(item.spf_result) }}>
                        {item.spf_result}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium" style={{ color: getStatusColor(item.dkim_result) }}>
                        {item.dkim_result}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium" style={{ color: getStatusColor(item.dmarc_result) }}>
                        {item.dmarc_result}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <Link href={`/tools/email-tester/results/${item.id}`} className="hover:underline transition-colors duration-200" style={{ color: '#956b50' }}>
                        View Details
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="rounded-2xl p-8 text-center" style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8' }}>
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4" style={{ backgroundColor: '#f3ece8' }}>
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#956b50' }}>
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <p className="text-lg mb-4" style={{ color: '#1b130e' }}>No test history available</p>
          <p className="mb-6" style={{ color: '#4a3728' }}>You haven't run any deliverability tests yet. Start by running your first test to see results here.</p>
          <Link
            href="/tools/email-tester"
            className="inline-flex items-center px-6 py-3 rounded-xl transition-all duration-300 font-semibold hover:scale-105"
            style={{ backgroundColor: '#1b130e', color: '#ffffff' }}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Run Your First Test
          </Link>
        </div>
      )}
      </div>
    </div>
    </>
  );
}
