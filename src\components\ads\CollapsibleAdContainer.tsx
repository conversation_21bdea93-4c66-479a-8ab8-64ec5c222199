'use client';

import { useState, useEffect } from 'react';
import CollapsibleAd from './CollapsibleAd';
import { trackAdImpression, trackAdClick } from '@/lib/config/adService';
import { ClientOnly } from '@/lib/utils/suppressHydrationWarning';

interface CollapsibleAdContainerProps {
  adId: string;
  adUnitId: string;
  adClientId?: string; // Add adClientId as an optional prop
  position: 'top' | 'bottom' | 'left' | 'right';
  displayOptions?: {
    initiallyExpanded?: boolean;
    rememberState?: boolean;
    expandAfterMinutes?: number;
    dismissible?: boolean;
  };
}

export default function CollapsibleAdContainer({
  adId,
  adUnitId,
  adClientId,
  position,
  displayOptions = {}
}: CollapsibleAdContainerProps) {
  const [impressionTracked, setImpressionTracked] = useState(false);

  // Default display options
  const {
    initiallyExpanded = true,
    rememberState = true,
    expandAfterMinutes = 10,
    dismissible = true
  } = displayOptions;

  // Track impression when ad is expanded
  const handleExpand = async () => {
    if (!impressionTracked) {
      await trackAdImpression(adId);
      setImpressionTracked(true);
    }
  };

  // Track click when ad is clicked
  const handleAdClick = async () => {
    await trackAdClick(adId);
  };

  // Track initial impression if initially expanded
  useEffect(() => {
    if (initiallyExpanded && !impressionTracked) {
      trackAdImpression(adId).then(() => {
        setImpressionTracked(true);
      });
    }
  }, [adId, initiallyExpanded, impressionTracked]);

  // Render ad content based on type
  const renderAdContent = () => {
    // Determine client ID and slot ID for Google ads
    let clientId = adClientId || 'ca-pub-8397529755029714';
    let slotId = adUnitId;

    // Check if this is a combined adUnitId (old format: ca-pub-XXXX/YYYY)
    if (adUnitId.startsWith('ca-pub-')) {
      const parts = adUnitId.split('/');
      clientId = parts[0];
      slotId = parts[1] || '';
    }

    // For Google AdSense ads
    if (adUnitId.startsWith('ca-pub-') || adClientId) {
      // Use useEffect to push the ad after the component mounts
      useEffect(() => {
        try {
          (window.adsbygoogle = window.adsbygoogle || []).push({});
        } catch (error) {
          console.error('Error loading AdSense ad:', error);
        }
      }, []);

      return (
        <div className="google-ad">
          <ins
            className="adsbygoogle"
            style={{ display: 'block' }}
            data-ad-client={clientId}
            data-ad-slot={slotId}
            data-ad-format="auto"
            data-full-width-responsive="true"
          />
        </div>
      );
    }

    // For non-Google ads, render custom ad content
    return (
      <div className="custom-ad">
        <div className="text-center p-2">
          <p className="text-sm text-gray-700 mb-2">Advertisement</p>
          <div
            className="bg-gray-100 p-4 rounded-md cursor-pointer"
            onClick={handleAdClick}
          >
            <p className="font-medium text-gray-800">Custom Ad Content</p>
            <p className="text-sm text-gray-600 mt-1">Ad ID: {adUnitId}</p>
          </div>
        </div>
      </div>
    );
  };

  // Use ClientOnly to prevent hydration errors with AdSense
  return (
    <ClientOnly>
      <CollapsibleAd
        adId={adId}
        adContent={renderAdContent()}
        position={position}
        initiallyExpanded={initiallyExpanded}
        rememberState={rememberState}
        autoExpandAfterMinutes={expandAfterMinutes}
        dismissible={dismissible}
        onExpand={handleExpand}
        className="collapsible-ad-container"
      />
    </ClientOnly>
  );
}
