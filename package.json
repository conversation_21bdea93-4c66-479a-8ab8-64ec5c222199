{"name": "js_vanishpost", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "prebuild": "node scripts/remove-analytics-toggle.js", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:performance": "k6 run performance/load-test.js", "manage-whitelist": "tsx scripts/manage-ip-whitelist.ts", "test:performance:admin": "k6 run performance/admin-load-test.js", "test:security:zap": "node security/zap-scan.js", "test:security:auth": "node security/auth-security.js", "setup-db": "node scripts/setup-database.js", "test-db": "node scripts/test-database-connections.js", "send-test-email": "node scripts/send-test-email.js", "check-email": "node scripts/check-email-address.js", "check-guerrilla": "node scripts/check-guerrilla-emails.js", "insert-email": "node scripts/insert-email-address.js", "test-api": "node scripts/test-api-endpoint.js", "init-analytics": "node scripts/init-analytics-db.js", "check-analytics": "node scripts/check-analytics-table.js", "add-sample-analytics": "node scripts/add-sample-analytics.js", "setup-supabase": "node scripts/setup-supabase-db.js", "test-supabase": "node scripts/test-supabase-connection.js", "migrate-to-supabase": "node scripts/migrate-to-supabase.js", "setup-supabase-rls": "node scripts/setup-supabase-rls.js", "setup-backend-management": "node scripts/setup-backend-management.js", "create-backend-tables": "node scripts/create-backend-tables.js", "seed:ads": "node scripts/seed-ads.js", "test:ad-update": "node scripts/test-ad-update.js", "add-schedule-column": "node scripts/add-schedule-column.js", "test:schedule-update": "node scripts/test-schedule-update.js", "setup-all": "npm run setup-supabase && npm run setup-supabase-rls && npm run setup-backend-management", "setup-deliverability-tool": "node scripts/setup-deliverability-tool.js", "init-security": "node scripts/initialize-security.js", "security-audit": "npm run init-security && npm run test:security:auth"}, "dependencies": {"@heroicons/react": "^2.2.0", "@iframe-resizer/react": "^5.4.6", "@open-iframe-resizer/react": "^1.4.3", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.39", "@react-email/render": "^1.1.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/forms": "^0.5.10", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "iframe-resizer-react": "^5.1.5", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mailauth": "^4.8.6", "mailparser": "^3.7.2", "mysql2": "^3.14.0", "next": "15.3.0", "node-cache": "^5.1.2", "node-forge": "^1.3.1", "nodemailer": "^6.10.1", "posthog-js": "^1.242.1", "posthog-node": "^4.17.1", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-iframe": "^1.8.5", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcrypt": "^5.0.2", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/mailparser": "^3.4.5", "@types/node": "^20", "@types/node-forge": "^1.3.11", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "k6": "^0.0.0", "node-fetch": "^2.7.0", "tailwindcss": "^4", "typescript": "^5"}}