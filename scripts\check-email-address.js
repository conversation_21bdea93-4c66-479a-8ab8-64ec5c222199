/**
 * <PERSON><PERSON><PERSON> to check if an email address exists in the local database
 * 
 * Usage: node scripts/check-email-address.js <EMAIL>
 */
const mysql = require('mysql2/promise');

// Local database configuration
const localDbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'tempmail_web_user',
  password: process.env.DB_PASSWORD || 'yiWTpFChtFW3neWCS',
  database: process.env.DB_NAME || 'tempmail_web',
};

async function checkEmailAddress() {
  // Get the email address from command line arguments
  const emailAddress = process.argv[2];
  
  if (!emailAddress) {
    console.error('Please provide an email address');
    console.error('Usage: node scripts/check-email-address.js <EMAIL>');
    process.exit(1);
  }
  
  console.log(`Checking email address: ${emailAddress}`);
  
  let connection;
  try {
    // Create connection
    connection = await mysql.createConnection(localDbConfig);
    
    // Check if the email address exists in the database
    const [rows] = await connection.execute(
      'SELECT * FROM TempEmail WHERE emailAddress = ?',
      [emailAddress]
    );
    
    if (rows.length === 0) {
      console.log('❌ Email address not found in the local database');
      return;
    }
    
    const tempEmail = rows[0];
    console.log('✅ Email address found in the local database');
    console.log('Details:');
    console.log(`  - ID: ${tempEmail.id}`);
    console.log(`  - Email Address: ${tempEmail.emailAddress}`);
    console.log(`  - Creation Time: ${tempEmail.creationTime}`);
    console.log(`  - Expiration Date: ${tempEmail.expirationDate}`);
    
    // Check if the email address has expired
    const now = new Date();
    if (tempEmail.expirationDate < now) {
      console.log('❌ Email address has expired');
    } else {
      console.log('✅ Email address is still valid');
    }
    
  } catch (error) {
    console.error('Error checking email address:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the function
checkEmailAddress().catch(console.error);
