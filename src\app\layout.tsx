import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { PostHogProvider } from "@/lib/analytics/posthog";
import CleanupSchedulerInitializer from "@/components/CleanupSchedulerInitializer";
import GuerrillaCleanupSchedulerInitializer from "@/components/GuerrillaCleanupSchedulerInitializer";
import MaintenanceSchedulerInitializer from "@/components/MaintenanceSchedulerInitializer";
import CacheInitializer from "@/components/CacheInitializer";
import CookieConsent from "@/components/CookieConsent";
import AdSenseScriptLoader from "@/components/AdSenseScriptLoader";

// Load Inter font with specific weights
const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "VanishPost - Secure Temporary Email Service",
  description: "Generate secure temporary email addresses that expire in 15 minutes. Protect your privacy with VanishPost's disposable email service.",
  keywords: "temporary email, disposable email, temp email, email privacy, temp mail, vanishpost",
  authors: [{ name: "VanishPost Team" }],
  creator: "VanishPost",
  publisher: "VanishPost",
  metadataBase: new URL("https://vanishpost.com"),
  alternates: {
    canonical: "/",
  },
  // Google AdSense verification meta tag
  verification: {
    google: "ca-pub-****************",
  },
  // Additional meta tags for AdSense verification
  other: {
    "google-adsense-account": "ca-pub-****************",
  },
  openGraph: {
    title: "VanishPost - Secure Temporary Email Service",
    description: "Generate secure temporary email addresses that expire in 15 minutes. Protect your privacy with VanishPost's disposable email service.",
    url: "https://vanishpost.com",
    siteName: "VanishPost",
    locale: "en_US",
    type: "website",
    images: [
      {
        url: "https://vanishpost.com/vanishpost-temporary-email-icon-512.png",
        width: 512,
        height: 512,
        alt: "VanishPost Logo",
      },
      {
        url: "https://vanishpost.com/vanishpost-temporary-email-icon-192.png",
        width: 192,
        height: 192,
        alt: "VanishPost Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "VanishPost - Secure Temporary Email Service",
    description: "Generate secure temporary email addresses that expire in 15 minutes. Protect your privacy with VanishPost.",
    creator: "@vanishpost",
    images: ["https://vanishpost.com/vanishpost-temporary-email-icon-512.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/vanishpost-temporary-email-icon-192.png', sizes: '192x192', type: 'image/png' },
      { url: '/vanishpost-temporary-email-icon-512.png', sizes: '512x512', type: 'image/png' },
    ],
    apple: [
      { url: '/vanishpost-temporary-email-apple-touch-icon.png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/vanishpost-temporary-email-logo.svg',
        color: '#ce601c',
      },
    ],
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: "#fafafa",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        {/* Google AdSense script is now loaded via the AdSenseScript component */}
        {/* Font preconnects removed - Inter font is loaded via Next.js font optimization */}

        {/* Favicon configuration - comprehensive set for all browsers and platforms */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/vanishpost-temporary-email-icon-192.png" type="image/png" sizes="192x192" />
        <link rel="icon" href="/vanishpost-temporary-email-icon-512.png" type="image/png" sizes="512x512" />
        <link rel="apple-touch-icon" href="/vanishpost-temporary-email-apple-touch-icon.png" />
        <link rel="mask-icon" href="/vanishpost-temporary-email-logo.svg" color="#ce601c" />
        <link rel="manifest" href="/manifest.json" />

        {/* Preconnect to AdSense domains to improve performance */}
        <link rel="preconnect" href="https://pagead2.googlesyndication.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://googleads.g.doubleclick.net" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://tpc.googlesyndication.com" crossOrigin="anonymous" />
      </head>
      <body className="antialiased bg-background text-neutral-800">
        <PostHogProvider>
          <CleanupSchedulerInitializer />
          <GuerrillaCleanupSchedulerInitializer />
          <MaintenanceSchedulerInitializer />
          <CacheInitializer />
          <div className="min-h-screen flex flex-col">
            {children}
          </div>
          {/* Cookie Consent Banner */}
          <CookieConsent />

          {/* Load AdSense script client-side only using the loader component */}
          <AdSenseScriptLoader clientId="ca-pub-****************" />
        </PostHogProvider>
      </body>
    </html>
  );
}
