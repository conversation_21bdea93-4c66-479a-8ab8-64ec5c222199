/**
 * API route for managing the cleanup scheduler
 * 
 * This API provides functionality to start, stop, and check the status of the cleanup scheduler.
 */
import { NextRequest, NextResponse } from 'next/server';
import { logInfo, logError } from '@/lib/logging';
import { 
  startCleanupScheduler, 
  stopCleanupScheduler, 
  isCleanupSchedulerRunning 
} from '@/lib/cleanup/cleanupScheduler';
import { getConfig, updateConfig } from '@/lib/config/configService';

/**
 * POST /api/admin/cleanup/scheduler
 * 
 * Start or stop the cleanup scheduler
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, intervalMinutes, autoStart } = body;
    
    if (action === 'start') {
      // Validate interval
      if (!intervalMinutes || intervalMinutes <= 0) {
        return NextResponse.json({
          success: false,
          error: 'Invalid cleanup interval'
        }, { status: 400 });
      }
      
      // Update configuration
      await updateConfig('cleanupIntervalMinutes', intervalMinutes);
      if (autoStart !== undefined) {
        await updateConfig('cleanupAutoStart', autoStart);
      }
      
      // Start scheduler
      const started = await startCleanupScheduler();
      
      if (!started) {
        return NextResponse.json({
          success: false,
          error: 'Failed to start cleanup scheduler'
        }, { status: 500 });
      }
      
      logInfo('cleanup', 'Cleanup scheduler started', { intervalMinutes });
      
      return NextResponse.json({
        success: true,
        message: 'Cleanup scheduler started',
        intervalMinutes
      });
    } else if (action === 'stop') {
      // Stop scheduler
      await stopCleanupScheduler();

      logInfo('cleanup', 'Cleanup scheduler stopped');

      return NextResponse.json({
        success: true,
        message: 'Cleanup scheduler stopped'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }
  } catch (error) {
    logError('cleanup', 'Error managing cleanup scheduler', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

/**
 * GET /api/admin/cleanup/scheduler
 * 
 * Get the status of the cleanup scheduler
 */
export async function GET(request: NextRequest) {
  try {
    const running = isCleanupSchedulerRunning();
    const intervalMinutes = await getConfig('cleanupIntervalMinutes');
    const autoStart = await getConfig('cleanupAutoStart');
    const lastRunTime = await getConfig('cleanupLastRunTime');
    const schedulerStartTime = await getConfig('cleanupSchedulerStartTime');

    // Calculate next run time if running
    let nextRunAt = null;
    if (running && intervalMinutes && schedulerStartTime) {
      const startTime = new Date(schedulerStartTime).getTime();
      const intervalMs = intervalMinutes * 60 * 1000;
      const now = Date.now();

      // Calculate how many intervals have passed since start
      const elapsedTime = now - startTime;
      const intervalsPassed = Math.floor(elapsedTime / intervalMs);

      // Calculate the next run time based on the scheduler start time and intervals
      const nextRunTime = startTime + ((intervalsPassed + 1) * intervalMs);
      nextRunAt = new Date(nextRunTime).toISOString();
    }

    return NextResponse.json({
      success: true,
      status: {
        running,
        intervalMinutes,
        nextRunAt,
        autoStart: autoStart !== false // Default to true if not set
      }
    });
  } catch (error) {
    logError('cleanup', 'Error getting cleanup scheduler status', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
