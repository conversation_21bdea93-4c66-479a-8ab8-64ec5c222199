const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkTable() {
  try {
    const conn = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'tempmail_web_user',
      password: process.env.DB_PASSWORD || 'yiWTpFChtFW3neWCS',
      database: process.env.DB_NAME || 'tempmail_web'
    });
    
    console.log('Connected to database');
    
    const [rows] = await conn.execute('SHOW TABLES LIKE "analytics_events"');
    
    if (rows.length > 0) {
      console.log('The analytics_events table exists');
    } else {
      console.log('The analytics_events table does NOT exist');
      
      // Create the table
      console.log('Creating analytics_events table...');
      
      await conn.execute(`
        CREATE TABLE IF NOT EXISTS analytics_events (
          id INT AUTO_INCREMENT PRIMARY KEY,
          event_type VARCHAR(50) NOT NULL,
          page_path VARCHAR(255),
          referrer VARCHAR(255),
          country VARCHAR(50),
          browser VARCHAR(50),
          device_type VARCHAR(20),
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          additional_data JSON
        )
      `);
      
      console.log('Table created successfully');
      
      // Create indexes
      console.log('Creating indexes...');
      
      try {
        await conn.execute('CREATE INDEX idx_event_type ON analytics_events(event_type)');
        console.log('Created index on event_type');
      } catch (error) {
        console.log('Index on event_type may already exist:', error.message);
      }
      
      try {
        await conn.execute('CREATE INDEX idx_timestamp ON analytics_events(timestamp)');
        console.log('Created index on timestamp');
      } catch (error) {
        console.log('Index on timestamp may already exist:', error.message);
      }
      
      try {
        await conn.execute('CREATE INDEX idx_page_path ON analytics_events(page_path)');
        console.log('Created index on page_path');
      } catch (error) {
        console.log('Index on page_path may already exist:', error.message);
      }
    }
    
    await conn.end();
    console.log('Database connection closed');
  } catch (err) {
    console.error('Error:', err);
  }
}

checkTable();
