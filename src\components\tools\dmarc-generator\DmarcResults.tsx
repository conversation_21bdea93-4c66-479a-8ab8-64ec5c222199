'use client';

// DMARC Results Display Component - Enhanced with modern UI and collapsible instructions
import React, { useState } from 'react';
import { DmarcGenerateResponse } from '@/types/dmarc';
import DnsRecordDisplay from '../shared/DnsRecordDisplay';

interface DmarcResultsProps {
  result?: DmarcGenerateResponse['data'];
  loading?: boolean;
  onValidate?: (domain: string, policy: string) => void;
  validating?: boolean;
}

// Collapsible Section Component
interface CollapsibleSectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  defaultOpen?: boolean;
  variant?: 'primary' | 'secondary';
}

function CollapsibleSection({ title, icon, children, defaultOpen = false, variant = 'primary' }: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const bgColor = variant === 'primary' ? 'bg-[#956b50]/5' : 'bg-[#f3ece8]';
  const borderColor = variant === 'primary' ? 'border-[#956b50]/20' : 'border-[#4a3728]/20';
  const iconColor = variant === 'primary' ? 'text-[#956b50]' : 'text-[#4a3728]';

  return (
    <div className={`${bgColor} ${borderColor} border rounded-xl overflow-hidden transition-all duration-200`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-6 py-4 flex items-center justify-between hover:bg-black/5 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <div className={`${iconColor} flex-shrink-0`}>
            {icon}
          </div>
          <h3 className="text-lg font-semibold text-[#1b130e] text-left">
            {title}
          </h3>
        </div>
        <div className={`transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}>
          <svg className="w-5 h-5 text-[#4a3728]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>

      <div className={`transition-all duration-300 ease-in-out ${isOpen ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'} overflow-hidden`}>
        <div className="px-6 pb-6">
          {children}
        </div>
      </div>
    </div>
  );
}

export default function DmarcResults({ result, loading, onValidate, validating }: DmarcResultsProps) {
  if (loading) {
    return (
      <div className="bg-white border border-[#4a3728] rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-[#f3ece8] rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            <div>
              <div className="h-4 bg-[#f3ece8] rounded w-1/4 mb-2"></div>
              <div className="h-16 bg-[#f3ece8] rounded"></div>
            </div>
            <div>
              <div className="h-4 bg-[#f3ece8] rounded w-1/4 mb-2"></div>
              <div className="h-20 bg-[#f3ece8] rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!result) {
    return null;
  }

  const getPolicyColor = (policy: string) => {
    switch (policy) {
      case 'none': return 'text-[#66b077]';
      case 'quarantine': return 'text-[#f59e0b]';
      case 'reject': return 'text-[#dc2626]';
      default: return 'text-[#1b130e]';
    }
  };

  const getPolicyIcon = (policy: string) => {
    switch (policy) {
      case 'none':
        return (
          <svg className="h-5 w-5 text-[#66b077]" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
      case 'quarantine':
        return (
          <svg className="h-5 w-5 text-[#f59e0b]" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'reject':
        return (
          <svg className="h-5 w-5 text-[#dc2626]" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Modern Success Header */}
      <div className="bg-gradient-to-r from-[#956b50]/10 to-[#4a3728]/10 border border-[#956b50]/30 rounded-2xl p-4">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gradient-to-br from-[#956b50] to-[#4a3728] rounded-xl flex items-center justify-center shadow-lg flex-shrink-0" style={{ aspectRatio: '1' }}>
              <svg className="h-6 w-6 text-white flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" preserveAspectRatio="xMidYMid meet">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-bold text-[#1b130e] mb-2">
              DMARC Policy Generated Successfully!
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="bg-white/60 backdrop-blur-sm rounded-lg p-2 border border-[#4a3728]/10">
                <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Domain</div>
                <div className="font-mono font-semibold text-[#1b130e] text-sm">{result.domain}</div>
              </div>
              <div className="bg-white/60 backdrop-blur-sm rounded-lg p-2 border border-[#4a3728]/10">
                <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Policy</div>
                <div className={`font-semibold capitalize flex items-center text-sm ${getPolicyColor(result.policy.policy)}`}>
                  {getPolicyIcon(result.policy.policy)}
                  <span className="ml-2">{result.policy.policy}</span>
                </div>
              </div>
              <div className="bg-white/60 backdrop-blur-sm rounded-lg p-2 border border-[#4a3728]/10">
                <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Coverage</div>
                <div className="font-semibold text-[#1b130e] text-sm">{result.policy.pct}%</div>
              </div>
            </div>
            <div className="mt-4 text-sm text-[#4a3728]">
              Generated on {new Date(result.generatedAt).toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* DNS Record Display - Enhanced */}
      <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-[#956b50]/10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ aspectRatio: '1' }}>
            <svg className="w-4 h-4 text-[#956b50] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-bold text-[#1b130e]">DMARC DNS Record</h3>
        </div>
        <DnsRecordDisplay
          recordName={result.recordName}
          recordValue={result.dnsRecord}
          recordType="TXT"
          title=""
        />
      </div>



      {/* Quick Setup Guide - Collapsible */}
      <CollapsibleSection
        title="Quick Setup Guide"
        icon={
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        }
        variant="primary"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-[#956b50]/20">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-[#956b50] text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0" style={{ aspectRatio: '1' }}>1</div>
                <h4 className="font-semibold text-[#1b130e]">Add DNS Record</h4>
              </div>
              <p className="text-sm text-[#4a3728]">
                Copy the DNS record above and add it to your domain's DNS settings as a TXT record.
              </p>
            </div>
            <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-[#956b50]/20">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-[#956b50] text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0" style={{ aspectRatio: '1' }}>2</div>
                <h4 className="font-semibold text-[#1b130e]">Wait for Propagation</h4>
              </div>
              <p className="text-sm text-[#4a3728]">
                Allow 24-48 hours for DNS changes to propagate globally before testing.
              </p>
            </div>
            <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-[#956b50]/20">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-[#956b50] text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0" style={{ aspectRatio: '1' }}>3</div>
                <h4 className="font-semibold text-[#1b130e]">Monitor Reports</h4>
              </div>
              <p className="text-sm text-[#4a3728]">
                Check your email at {result.policy.rua} for DMARC reports and authentication data.
              </p>
            </div>
          </div>
          {result.policy.policy === 'none' && (
            <div className="bg-gradient-to-r from-[#f59e0b]/10 to-[#ce601c]/10 border border-[#f59e0b]/30 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <svg className="w-5 h-5 text-[#f59e0b]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h5 className="font-semibold text-[#1b130e]">Next Step: Upgrade Policy</h5>
              </div>
              <p className="text-sm text-[#4a3728]">
                After 1-2 weeks of monitoring, consider upgrading to "quarantine" policy for enhanced security.
              </p>
            </div>
          )}
        </div>
      </CollapsibleSection>

      {/* Detailed Setup Instructions - Collapsible */}
      <CollapsibleSection
        title="Detailed Setup Instructions"
        icon={
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        }
        variant="secondary"
      >
        <div className="space-y-6">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-sm hover:shadow-lg transition-all duration-300">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-[#956b50]/10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ aspectRatio: '1' }}>
                <svg className="w-4 h-4 text-[#956b50] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h4 className="text-lg font-bold text-[#1b130e]">DNS Configuration Steps</h4>
            </div>
            <div className="space-y-3 text-sm text-[#4a3728]">
              <div className="flex items-start">
                <span className="font-bold mr-2 text-[#956b50]">1.</span>
                <span>Log into your DNS provider's control panel (e.g., Cloudflare, GoDaddy, Namecheap)</span>
              </div>
              <div className="flex items-start">
                <span className="font-bold mr-2 text-[#956b50]">2.</span>
                <span>Navigate to the DNS management section for your domain</span>
              </div>
              <div className="flex items-start">
                <span className="font-bold mr-2 text-[#956b50]">3.</span>
                <span>Create a new TXT record with the name: <code className="bg-gray-100 px-1 rounded">{result.recordName}</code></span>
              </div>
              <div className="flex items-start">
                <span className="font-bold mr-2 text-[#956b50]">4.</span>
                <span>Paste the DMARC record value from above into the TXT record content</span>
              </div>
              <div className="flex items-start">
                <span className="font-bold mr-2 text-[#956b50]">5.</span>
                <span>Set TTL to 3600 seconds (1 hour) or use your provider's default</span>
              </div>
              <div className="flex items-start">
                <span className="font-bold mr-2 text-[#956b50]">6.</span>
                <span>Save the record and wait for DNS propagation (24-48 hours)</span>
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-sm hover:shadow-lg transition-all duration-300">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-[#956b50]/10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ aspectRatio: '1' }}>
                <svg className="w-4 h-4 text-[#956b50] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h4 className="text-lg font-bold text-[#1b130e]">DMARC Reporting Setup</h4>
            </div>
            <div className="space-y-3 text-sm text-[#4a3728]">
              <div className="flex items-start">
                <span className="text-[#956b50] mr-2">•</span>
                <span>DMARC reports will be sent to: <strong>{result.policy.rua}</strong></span>
              </div>
              <div className="flex items-start">
                <span className="text-[#956b50] mr-2">•</span>
                <span>Reports are typically sent daily by major email providers (Gmail, Outlook, Yahoo)</span>
              </div>
              <div className="flex items-start">
                <span className="text-[#956b50] mr-2">•</span>
                <span>Reports contain authentication results and help identify legitimate vs. fraudulent email sources</span>
              </div>
              <div className="flex items-start">
                <span className="text-[#956b50] mr-2">•</span>
                <span>Use DMARC report analyzers to interpret the XML reports and gain insights</span>
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-sm hover:shadow-lg transition-all duration-300">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-[#956b50]/10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ aspectRatio: '1' }}>
                <svg className="w-4 h-4 text-[#956b50] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h4 className="text-lg font-bold text-[#1b130e]">Policy Implementation Best Practices</h4>
            </div>
            <div className="space-y-3 text-sm text-[#4a3728]">
              <div className="flex items-start">
                <span className="text-[#956b50] mr-2">•</span>
                <span><strong>Start with "none" policy:</strong> Monitor authentication without affecting email delivery</span>
              </div>
              <div className="flex items-start">
                <span className="text-[#956b50] mr-2">•</span>
                <span><strong>Analyze reports:</strong> Review 1-2 weeks of DMARC reports to understand your email ecosystem</span>
              </div>
              <div className="flex items-start">
                <span className="text-[#956b50] mr-2">•</span>
                <span><strong>Ensure SPF and DKIM:</strong> Verify SPF and DKIM records are properly configured before enforcing</span>
              </div>
              <div className="flex items-start">
                <span className="text-[#956b50] mr-2">•</span>
                <span><strong>Gradual enforcement:</strong> Move from "none" → "quarantine" → "reject" as confidence increases</span>
              </div>
              <div className="flex items-start">
                <span className="text-[#956b50] mr-2">•</span>
                <span><strong>Monitor continuously:</strong> Keep reviewing reports even after full enforcement</span>
              </div>
            </div>
          </div>
        </div>
      </CollapsibleSection>

      {/* DNS Validation - Enhanced */}
      <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-8 h-8 bg-[#956b50]/10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ aspectRatio: '1' }}>
            <svg className="w-4 h-4 text-[#956b50] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-bold text-[#1b130e]">DNS Validation</h3>
        </div>
        <p className="text-[#4a3728] mb-4 text-sm">
          After adding the DNS record, use this tool to verify it's properly configured and propagated globally.
        </p>
        <button
          onClick={() => onValidate?.(result.domain, result.policy.policy)}
          disabled={validating}
          className="bg-gradient-to-r from-[#956b50] to-[#4a3728] text-white px-8 py-3 rounded-xl hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-semibold"
        >
          {validating ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
              Validating DNS Record...
            </>
          ) : (
            <>
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Validate DNS Record
            </>
          )}
        </button>
      </div>



      {/* Cross-Tool Integration - Enhanced */}
      <div className="bg-gradient-to-r from-[#956b50]/10 to-[#4a3728]/10 border border-[#956b50]/30 rounded-2xl p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-8 h-8 bg-[#956b50]/10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ aspectRatio: '1' }}>
            <svg className="w-4 h-4 text-[#956b50] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
          </div>
          <h3 className="text-lg font-bold text-[#1b130e]">Complete Your Email Authentication</h3>
        </div>
        <p className="text-[#4a3728] mb-4 text-sm">
          Enhance your email security by setting up DKIM authentication and testing your complete email authentication setup.
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <a
            href="/tools/dkim-generator"
            className="group bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-6 hover:border-[#4a3728]/40 transition-all duration-200 hover:shadow-lg"
          >
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-[#4a3728]/10 rounded-lg flex items-center justify-center group-hover:bg-[#4a3728]/20 transition-colors flex-shrink-0" style={{ aspectRatio: '1' }}>
                <svg className="w-5 h-5 text-[#4a3728] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
              </div>
              <h4 className="font-semibold text-[#1b130e]">Generate DKIM Keys</h4>
            </div>
            <p className="text-sm text-[#4a3728]">Create DKIM authentication keys for your domain</p>
          </a>
          <a
            href="/tools/email-tester"
            className="group bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-6 hover:border-[#4a3728]/40 transition-all duration-200 hover:shadow-lg"
          >
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-[#66b077]/10 rounded-lg flex items-center justify-center group-hover:bg-[#66b077]/20 transition-colors flex-shrink-0" style={{ aspectRatio: '1' }}>
                <svg className="w-5 h-5 text-[#66b077] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h4 className="font-semibold text-[#1b130e]">Test Implementation</h4>
            </div>
            <p className="text-sm text-[#4a3728]">Verify your complete email authentication setup</p>
          </a>
        </div>
      </div>


    </div>
  );
}
