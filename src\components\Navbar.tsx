'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import Logo from './Logo';

/**
 * Navbar Component
 *
 * Displays the application navbar with navigation links
 * The logo is clickable and links to the home page
 */
export default function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isToolsDropdownOpen, setIsToolsDropdownOpen] = useState(false);
  const toolsDropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (toolsDropdownRef.current && !toolsDropdownRef.current.contains(event.target as Node)) {
        setIsToolsDropdownOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div
      className="sticky top-0 z-10 transition-all duration-300"
      style={{
        backgroundColor: 'rgba(255, 255, 255, 0.98)', // White glassmorphic background with maximum opacity for visibility
        backdropFilter: 'blur(20px) saturate(180%) brightness(1.1)', // Enhanced blur with saturation and brightness
        WebkitBackdropFilter: 'blur(20px) saturate(180%) brightness(1.1)', // Safari support
        border: '1px solid rgba(255, 255, 255, 0.2)', // Subtle white border
        borderTop: 'none', // Remove top border for cleaner look
        borderBottom: '1px solid rgba(206, 96, 28, 0.25)', // Brand orange border to match email controls
      }}
    >
      <div className="flex items-center justify-between w-full max-w-5xl mx-auto px-4 py-3">
        {/* Logo - Clickable and links to home page */}
        <Logo />

        {/* Navigation Links - Desktop */}
        <div className="hidden md:flex items-center space-x-8">
          <Link
            href="/features"
            className="transition-colors duration-200 text-sm font-medium"
            style={{ color: 'var(--earth-brown-medium)' }}
            onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
          >
            Features
          </Link>

          {/* Tools Dropdown */}
          <div className="relative" ref={toolsDropdownRef}>
            <button
              onClick={() => setIsToolsDropdownOpen(!isToolsDropdownOpen)}
              className="transition-colors duration-200 text-sm font-medium flex items-center"
              style={{ color: 'var(--earth-brown-medium)' }}
              onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
              onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
            >
              Tools
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-4 w-4 ml-1 transition-transform duration-200 ${isToolsDropdownOpen ? 'rotate-180' : ''}`}
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>

            {/* Dropdown Menu */}
            {isToolsDropdownOpen && (
              <div
                className="absolute right-0 mt-2 w-48 rounded-xl py-1 z-20 animate-fadeIn"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)', // White glassmorphic background with higher opacity
                  backdropFilter: 'blur(16px) saturate(180%)', // Enhanced blur with saturation
                  WebkitBackdropFilter: 'blur(16px) saturate(180%)', // Safari support
                  border: '1px solid rgba(206, 96, 28, 0.25)', // Brand orange border to match email controls
                  borderTop: '1px solid rgba(255, 255, 255, 0.3)', // Keep white top border for separation
                }}
              >
                <Link
                  href="/tools"
                  className="block px-4 py-2 text-sm transition-all duration-200 rounded-lg mx-1"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.4)'; // White glassmorphic hover
                    e.currentTarget.style.color = 'var(--brand-orange)';
                    e.currentTarget.style.backdropFilter = 'blur(8px)';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(8px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--earth-brown-medium)';
                    e.currentTarget.style.backdropFilter = 'none';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
                  }}
                  onClick={() => setIsToolsDropdownOpen(false)}
                  aria-label="View all VanishPost tools"
                >
                  All Tools
                </Link>
                <Link
                  href="/"
                  className="block px-4 py-2 text-sm transition-all duration-200 rounded-lg mx-1"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.4)'; // White glassmorphic hover
                    e.currentTarget.style.color = 'var(--brand-orange)';
                    e.currentTarget.style.backdropFilter = 'blur(8px)';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(8px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--earth-brown-medium)';
                    e.currentTarget.style.backdropFilter = 'none';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
                  }}
                  onClick={() => setIsToolsDropdownOpen(false)}
                  aria-label="Generate temporary email address"
                >
                  Temporary Email
                </Link>
                <Link
                  href="/tools/email-tester"
                  className="block px-4 py-2 text-sm transition-all duration-200 rounded-lg mx-1"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.4)'; // White glassmorphic hover
                    e.currentTarget.style.color = 'var(--brand-orange)';
                    e.currentTarget.style.backdropFilter = 'blur(8px)';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(8px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--earth-brown-medium)';
                    e.currentTarget.style.backdropFilter = 'none';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
                  }}
                  onClick={() => setIsToolsDropdownOpen(false)}
                  aria-label="Test email deliverability and authentication"
                >
                  Email Tester
                </Link>
                <Link
                  href="/tools/smtp-tester"
                  className="block px-4 py-2 text-sm transition-all duration-200 rounded-lg mx-1"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.4)'; // White glassmorphic hover
                    e.currentTarget.style.color = 'var(--brand-orange)';
                    e.currentTarget.style.backdropFilter = 'blur(8px)';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(8px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--earth-brown-medium)';
                    e.currentTarget.style.backdropFilter = 'none';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
                  }}
                  onClick={() => setIsToolsDropdownOpen(false)}
                  aria-label="Test SMTP server connectivity and email authentication"
                >
                  SMTP Tester
                </Link>
                <Link
                  href="/tools/dkim-generator"
                  className="block px-4 py-2 text-sm transition-all duration-200 rounded-lg mx-1"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.4)'; // White glassmorphic hover
                    e.currentTarget.style.color = 'var(--brand-orange)';
                    e.currentTarget.style.backdropFilter = 'blur(8px)';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(8px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--earth-brown-medium)';
                    e.currentTarget.style.backdropFilter = 'none';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
                  }}
                  onClick={() => setIsToolsDropdownOpen(false)}
                  aria-label="Generate DKIM keys and DNS records"
                >
                  DKIM Generator
                </Link>
                <Link
                  href="/tools/dmarc-generator"
                  className="block px-4 py-2 text-sm transition-all duration-200 rounded-lg mx-1"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.4)'; // White glassmorphic hover
                    e.currentTarget.style.color = 'var(--brand-orange)';
                    e.currentTarget.style.backdropFilter = 'blur(8px)';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(8px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--earth-brown-medium)';
                    e.currentTarget.style.backdropFilter = 'none';
                    (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
                  }}
                  onClick={() => setIsToolsDropdownOpen(false)}
                  aria-label="Generate DMARC policy records"
                >
                  DMARC Generator
                </Link>
              </div>
            )}
          </div>

          <Link
            href="/faq"
            className="transition-colors duration-200 text-sm font-medium"
            style={{ color: 'var(--earth-brown-medium)' }}
            onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
          >
            FAQ
          </Link>
          <Link
            href="/about"
            className="transition-colors duration-200 text-sm font-medium"
            style={{ color: 'var(--earth-brown-medium)' }}
            onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
          >
            About
          </Link>
          <Link
            href="/contact"
            className="transition-colors duration-200 text-sm font-medium"
            style={{ color: 'var(--earth-brown-medium)' }}
            onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
          >
            Contact
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="md:hidden p-1.5 rounded-full transition-all duration-200 active:scale-95"
          style={{ color: 'var(--earth-brown-medium)' }}
          onMouseEnter={(e) => {
            e.currentTarget.style.color = 'var(--brand-orange)';
            e.currentTarget.style.backgroundColor = 'var(--brand-orange-hover)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.color = 'var(--earth-brown-medium)';
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
          aria-label="Toggle mobile menu"
        >
          {isMobileMenuOpen ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="4" y1="8" x2="20" y2="8"></line>
              <line x1="4" y1="16" x2="20" y2="16"></line>
            </svg>
          )}
        </button>
      </div>

      {/* Mobile Menu - Dropdown */}
      {isMobileMenuOpen && (
        <div
          className="md:hidden animate-fadeIn"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.85)', // White glassmorphic background with higher opacity
            backdropFilter: 'blur(16px) saturate(180%)', // Enhanced blur with saturation
            WebkitBackdropFilter: 'blur(16px) saturate(180%)', // Safari support
            borderTop: '1px solid rgba(206, 96, 28, 0.25)', // Brand orange top border to match email controls
            borderBottom: '1px solid rgba(206, 96, 28, 0.25)', // Brand orange bottom border for consistency
            borderLeft: '1px solid rgba(255, 255, 255, 0.2)', // Subtle white side borders
            borderRight: '1px solid rgba(255, 255, 255, 0.2)', // Subtle white side borders
          }}
        >
          <div className="py-2 px-4 space-y-1 max-w-5xl mx-auto">
            <Link
              href="/features"
              className="block py-2.5 px-3 rounded-lg transition-all duration-200 text-sm font-medium"
              style={{ color: 'var(--earth-brown-medium)' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = 'var(--brand-orange)';
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.3)'; // White glassmorphic hover
                e.currentTarget.style.backdropFilter = 'blur(8px)';
                (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(8px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = 'var(--earth-brown-medium)';
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.backdropFilter = 'none';
                (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
              }}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Features
            </Link>

            {/* Tools Section in Mobile Menu */}
            <div className="block py-2.5 px-3 rounded-lg transition-colors duration-200 text-sm font-medium"
                 style={{ color: 'var(--earth-brown-medium)' }}>
              <span className="font-medium">Tools</span>
              <div className="pl-4 mt-1 space-y-1" style={{ borderLeft: '2px solid var(--earth-beige-secondary)' }}>
                <Link
                  href="/tools"
                  className="block py-1.5 text-sm transition-colors duration-200"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
                  onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
                  onClick={() => setIsMobileMenuOpen(false)}
                  aria-label="View all VanishPost tools"
                >
                  All Tools
                </Link>
                <Link
                  href="/"
                  className="block py-1.5 text-sm transition-colors duration-200"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
                  onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
                  onClick={() => setIsMobileMenuOpen(false)}
                  aria-label="Generate temporary email address"
                >
                  Temporary Email
                </Link>
                <Link
                  href="/tools/email-tester"
                  className="block py-1.5 text-sm transition-colors duration-200"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
                  onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
                  onClick={() => setIsMobileMenuOpen(false)}
                  aria-label="Test email deliverability and authentication"
                >
                  Email Tester
                </Link>
                <Link
                  href="/tools/smtp-tester"
                  className="block py-1.5 text-sm transition-colors duration-200"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
                  onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
                  onClick={() => setIsMobileMenuOpen(false)}
                  aria-label="Test SMTP server connectivity and email authentication"
                >
                  SMTP Tester
                </Link>
                <Link
                  href="/tools/dkim-generator"
                  className="block py-1.5 text-sm transition-colors duration-200"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
                  onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
                  onClick={() => setIsMobileMenuOpen(false)}
                  aria-label="Generate DKIM keys and DNS records"
                >
                  DKIM Generator
                </Link>
                <Link
                  href="/tools/dmarc-generator"
                  className="block py-1.5 text-sm transition-colors duration-200"
                  style={{ color: 'var(--earth-brown-medium)' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = 'var(--brand-orange)'}
                  onMouseLeave={(e) => e.currentTarget.style.color = 'var(--earth-brown-medium)'}
                  onClick={() => setIsMobileMenuOpen(false)}
                  aria-label="Generate DMARC policy records"
                >
                  DMARC Generator
                </Link>
              </div>
            </div>

            <Link
              href="/faq"
              className="block py-2.5 px-3 rounded-lg transition-all duration-200 text-sm font-medium"
              style={{ color: 'var(--earth-brown-medium)' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = 'var(--brand-orange)';
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.3)'; // White glassmorphic hover
                e.currentTarget.style.backdropFilter = 'blur(8px)';
                (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(8px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = 'var(--earth-brown-medium)';
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.backdropFilter = 'none';
                (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
              }}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              FAQ
            </Link>
            <Link
              href="/about"
              className="block py-2.5 px-3 rounded-lg transition-all duration-200 text-sm font-medium"
              style={{ color: 'var(--earth-brown-medium)' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = 'var(--brand-orange)';
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.3)'; // White glassmorphic hover
                e.currentTarget.style.backdropFilter = 'blur(8px)';
                (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(8px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = 'var(--earth-brown-medium)';
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.backdropFilter = 'none';
                (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
              }}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              About
            </Link>
            <Link
              href="/contact"
              className="block py-2.5 px-3 rounded-lg transition-all duration-200 text-sm font-medium"
              style={{ color: 'var(--earth-brown-medium)' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = 'var(--brand-orange)';
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.3)'; // White glassmorphic hover
                e.currentTarget.style.backdropFilter = 'blur(8px)';
                (e.currentTarget.style as any).WebkitBackdropFilter = 'blur(8px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = 'var(--earth-brown-medium)';
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.backdropFilter = 'none';
                (e.currentTarget.style as any).WebkitBackdropFilter = 'none';
              }}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Contact
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
