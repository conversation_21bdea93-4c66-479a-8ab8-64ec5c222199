import { test, expect } from '@playwright/test';

test.describe('Admin Dashboard', () => {
  test('should require authentication to access the admin dashboard', async ({ page }) => {
    // Navigate to the admin dashboard
    await page.goto('/admin');
    
    // Check that we're redirected to the login page
    await page.waitForURL('**/admin/login');
    
    // Check that the login form is displayed
    const loginForm = await page.isVisible('form');
    expect(loginForm).toBe(true);
    
    // Check that the username and password fields are displayed
    const usernameField = await page.isVisible('input[name="username"]');
    const passwordField = await page.isVisible('input[name="password"]');
    expect(usernameField).toBe(true);
    expect(passwordField).toBe(true);
  });
  
  test('should show error message for invalid credentials', async ({ page }) => {
    // Navigate to the admin login page
    await page.goto('/admin/login');
    
    // Fill in invalid credentials
    await page.fill('input[name="username"]', 'invalid-user');
    await page.fill('input[name="password"]', 'invalid-password');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check that an error message is displayed
    await page.waitForSelector('.error-message');
    const errorMessage = await page.textContent('.error-message');
    expect(errorMessage).toContain('Invalid credentials');
  });
  
  test('should login with valid credentials and show the dashboard', async ({ page }) => {
    // This test assumes that there's a valid test user with username 'test-admin' and password 'test-password'
    // In a real scenario, you would set up a test user in your database before running this test
    
    // Navigate to the admin login page
    await page.goto('/admin/login');
    
    // Fill in valid credentials
    await page.fill('input[name="username"]', 'test-admin');
    await page.fill('input[name="password"]', 'test-password');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check that we're redirected to the dashboard
    await page.waitForURL('**/admin/dashboard');
    
    // Check that the dashboard is displayed
    const dashboard = await page.isVisible('.admin-dashboard');
    expect(dashboard).toBe(true);
    
    // Check that the analytics section is displayed
    const analytics = await page.isVisible('.analytics-section');
    expect(analytics).toBe(true);
  });
  
  test('should display analytics data in the dashboard', async ({ page }) => {
    // This test assumes that the user is already logged in
    // In a real scenario, you would log in before running this test
    
    // Navigate to the admin dashboard
    await page.goto('/admin/dashboard');
    
    // Check that the page views chart is displayed
    const pageViewsChart = await page.isVisible('.page-views-chart');
    expect(pageViewsChart).toBe(true);
    
    // Check that the email generation chart is displayed
    const emailGenerationChart = await page.isVisible('.email-generation-chart');
    expect(emailGenerationChart).toBe(true);
    
    // Check that the email view chart is displayed
    const emailViewChart = await page.isVisible('.email-view-chart');
    expect(emailViewChart).toBe(true);
    
    // Check that the email deletion chart is displayed
    const emailDeletionChart = await page.isVisible('.email-deletion-chart');
    expect(emailDeletionChart).toBe(true);
  });
  
  test('should allow changing the date range for analytics data', async ({ page }) => {
    // This test assumes that the user is already logged in
    // In a real scenario, you would log in before running this test
    
    // Navigate to the admin dashboard
    await page.goto('/admin/dashboard');
    
    // Check that the date range selector is displayed
    const dateRangeSelector = await page.isVisible('.date-range-selector');
    expect(dateRangeSelector).toBe(true);
    
    // Select a different date range
    await page.selectOption('.date-range-selector select', '7');
    
    // Wait for the charts to update
    await page.waitForTimeout(1000);
    
    // Check that the charts are still displayed
    const pageViewsChart = await page.isVisible('.page-views-chart');
    expect(pageViewsChart).toBe(true);
  });
});
