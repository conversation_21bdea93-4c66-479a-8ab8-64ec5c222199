'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Checkbox } from '@/components/ui/Checkbox';
import { Spinner } from '@/components/ui/Spinner';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { formatDistanceToNow } from 'date-fns';

interface MaintenanceResult {
  success: boolean;
  guerrillaOptimized: boolean;
  supabaseVacuumed: boolean;
  duration: number;
  timestamp: string;
  error?: string;
  details?: {
    guerrillaDetails?: string;
    supabaseDetails?: string;
  };
}

interface SchedulerStatus {
  running: boolean;
  intervalHours: number;
  nextRunAt: string | null;
}

export default function MaintenanceManager() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastResult, setLastResult] = useState<MaintenanceResult | null>(null);
  const [schedulerStatus, setSchedulerStatus] = useState<SchedulerStatus | null>(null);
  const [intervalHours, setIntervalHours] = useState(24);

  // Fetch scheduler status on mount
  useEffect(() => {
    fetchSchedulerStatus();
  }, []);

  // Fetch scheduler status
  const fetchSchedulerStatus = async () => {
    try {
      const response = await fetch('/api/management-portal-x7z9y2/maintenance/scheduler');

      if (!response.ok) {
        throw new Error(`Failed to fetch scheduler status: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setSchedulerStatus(data.status);
      setIntervalHours(data.status.intervalHours || 24);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching scheduler status');
      console.error('Error fetching scheduler status:', err);
    }
  };

  // Run maintenance manually
  const runMaintenance = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/maintenance');

      if (!response.ok) {
        throw new Error(`Failed to run maintenance: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setLastResult(data);

      // Refresh scheduler status
      fetchSchedulerStatus();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while running maintenance');
      console.error('Error running maintenance:', err);
    } finally {
      setLoading(false);
    }
  };

  // Start scheduler
  const startScheduler = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/maintenance/scheduler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'start',
          intervalHours,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to start scheduler: ${response.status} ${response.statusText}`);
      }

      await fetchSchedulerStatus();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while starting scheduler');
      console.error('Error starting scheduler:', err);
    } finally {
      setLoading(false);
    }
  };

  // Stop scheduler
  const stopScheduler = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/maintenance/scheduler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'stop',
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to stop scheduler: ${response.status} ${response.statusText}`);
      }

      await fetchSchedulerStatus();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while stopping scheduler');
      console.error('Error stopping scheduler:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" />
            </svg>
            Database Maintenance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-gray-500">
              Database maintenance optimizes performance by reclaiming space and updating statistics after cleanup operations.
              Run maintenance manually or schedule it to run automatically.
            </p>

            {error && (
              <Alert variant="error" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex flex-col md:flex-row gap-4 items-start">
              <Button
                onClick={runMaintenance}
                disabled={loading}
                className="flex items-center"
              >
                {loading ? (
                  <>
                    <Spinner className="mr-2 h-4 w-4" />
                    Running...
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" />
                    </svg>
                    Run Maintenance Now
                  </>
                )}
              </Button>

              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Checkbox
                      id="auto-maintenance"
                      checked={schedulerStatus?.running || false}
                      onCheckedChange={(checked) => {
                        // Set loading state first to prevent flickering
                        setLoading(true);

                        // Use setTimeout to allow the UI to update before making the API call
                        setTimeout(() => {
                          if (checked) {
                            startScheduler();
                          } else {
                            stopScheduler();
                          }
                        }, 50);
                      }}
                      disabled={loading}
                      className="transition-all duration-300 ease-in-out"
                    />
                    {loading && (
                      <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50 rounded">
                        <div className="w-3 h-3 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                      </div>
                    )}
                  </div>
                  <label htmlFor="auto-maintenance" className="text-sm font-medium transition-opacity duration-300 ease-in-out" style={{ opacity: loading ? 0.5 : 1 }}>
                    Schedule Automatic Maintenance
                  </label>
                </div>

                <div className={`flex items-center gap-2 transition-all duration-300 ease-in-out ${!schedulerStatus?.running ? 'opacity-50' : 'opacity-100'}`}>
                  <label htmlFor="interval-hours" className="text-sm text-gray-700 whitespace-nowrap transition-colors duration-300">
                    Run every:
                  </label>
                  <div className="relative">
                    <Input
                      id="interval-hours"
                      type="number"
                      min="1"
                      max="168"
                      value={intervalHours}
                      onChange={(e) => setIntervalHours(parseInt(e.target.value) || 24)}
                      className="w-20 transition-all duration-300 ease-in-out"
                      disabled={!schedulerStatus?.running || loading}
                    />
                    {loading && schedulerStatus?.running && (
                      <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-50 rounded">
                        <div className="w-3 h-3 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                      </div>
                    )}
                  </div>
                  <span className="text-sm text-gray-700 transition-colors duration-300">hours</span>

                  {schedulerStatus?.running && schedulerStatus?.intervalHours !== intervalHours && (
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                        // Add click animation class
                        const button = e.currentTarget;
                        button.classList.add('animate-click');

                        // Remove the animation class after it completes
                        setTimeout(() => {
                          button.classList.remove('animate-click');
                        }, 300);

                        // Call the original handler
                        startScheduler();
                      }}
                      disabled={loading}
                      className="ml-2 px-3 py-1 shadow-sm hover:scale-105 transition-all duration-200 hover:shadow-md hover:bg-indigo-50 hover:border-indigo-300 hover:text-indigo-700 animate-pulse active:scale-95 active:shadow-inner active:bg-indigo-100"
                      leftIcon={
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 animate-spin-slow" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                        </svg>
                      }
                    >
                      Update
                    </Button>
                  )}
                </div>

                {schedulerStatus?.running && schedulerStatus.nextRunAt && (
                  <p className="text-xs text-gray-500">
                    Next run: {formatDistanceToNow(new Date(schedulerStatus.nextRunAt), { addSuffix: true })}
                  </p>
                )}
              </div>
            </div>

            {lastResult && (
              <div className="mt-6 border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                    <h3 className="text-sm font-semibold text-gray-900">Last Maintenance Result</h3>
                  </div>
                </div>

                <div className="bg-white p-4">
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="bg-gray-50 p-3 rounded-md">
                      <div className="text-xs text-gray-500 uppercase mb-1">Status</div>
                      <div className={`text-sm font-medium ${lastResult.success ? 'text-green-600' : 'text-red-600'} flex items-center`}>
                        {lastResult.success ? (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            Success
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                            Failed
                          </>
                        )}
                      </div>
                    </div>

                    <div className="bg-gray-50 p-3 rounded-md">
                      <div className="text-xs text-gray-500 uppercase mb-1">Time</div>
                      <div className="text-sm font-medium text-gray-900 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                        </svg>
                        {new Date(lastResult.timestamp).toLocaleString()}
                      </div>
                    </div>

                    <div className="bg-gray-50 p-3 rounded-md">
                      <div className="text-xs text-gray-500 uppercase mb-1">Guerrilla DB</div>
                      <div className={`text-sm font-medium ${lastResult.guerrillaOptimized ? 'text-green-600' : 'text-red-600'} flex items-center`}>
                        {lastResult.guerrillaOptimized ? (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            Optimized
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                            Not Optimized
                          </>
                        )}
                      </div>
                    </div>

                    <div className="bg-gray-50 p-3 rounded-md">
                      <div className="text-xs text-gray-500 uppercase mb-1">Supabase DB</div>
                      <div className={`text-sm font-medium ${lastResult.supabaseVacuumed ? 'text-green-600' : 'text-red-600'} flex items-center`}>
                        {lastResult.supabaseVacuumed ? (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            Vacuumed
                          </>
                        ) : (
                          <>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                            Not Vacuumed
                          </>
                        )}
                      </div>
                    </div>

                    <div className="bg-gray-50 p-3 rounded-md">
                      <div className="text-xs text-gray-500 uppercase mb-1">Duration</div>
                      <div className="text-sm font-medium text-gray-900 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                        </svg>
                        {lastResult.duration}ms
                      </div>
                    </div>
                  </div>

                  {lastResult.error && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                      <div className="flex items-center text-red-800 font-medium mb-1">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        Error
                      </div>
                      <p className="text-sm text-red-700">{lastResult.error}</p>
                    </div>
                  )}

                  {lastResult.details && (
                    <div className="border border-gray-200 rounded-md overflow-hidden">
                      <div className="bg-gray-50 px-3 py-2 border-b border-gray-200">
                        <div className="text-xs font-medium text-gray-700">Details</div>
                      </div>
                      <div className="p-3 space-y-2 text-sm">
                        {lastResult.details.guerrillaDetails && (
                          <div>
                            <span className="font-medium text-gray-700">Guerrilla:</span>{' '}
                            <span className="text-gray-600">{lastResult.details.guerrillaDetails}</span>
                          </div>
                        )}
                        {lastResult.details.supabaseDetails && (
                          <div>
                            <span className="font-medium text-gray-700">Supabase:</span>{' '}
                            <span className="text-gray-600">{lastResult.details.supabaseDetails}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
