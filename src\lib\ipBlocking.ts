/**
 * IP Blocking System for VanishPost
 * 
 * Provides functionality to block/unblock IP addresses with reasons and expiration
 * Moved to lib root for better deployment compatibility
 */

import { createServerSupabaseClient } from './supabase';
import { logger } from './logging/Logger';

export interface BlockedIP {
  id: string;
  ipAddress: string;
  reason: string;
  blockedAt: string;
  blockedBy: string; // Admin user who blocked the IP
  expiresAt?: string; // Optional expiration date
  isActive: boolean;
  metadata?: {
    userAgent?: string;
    country?: string;
    requestCount?: number;
    lastViolation: string;
  };
}

export interface IPWhitelist {
  id: string;
  ipAddress: string;
  reason: string;
  addedAt: string;
  addedBy: string;
  isActive: boolean;
}

export interface RateLimitViolation {
  id: string;
  ipAddress: string;
  endpoint: string;
  violationCount: number;
  firstViolation: string;
  lastViolation: string;
}

/**
 * Check if an IP address is blocked
 */
export async function isIPBlocked(ipAddress: string): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('blocked_ips')
      .select('*')
      .eq('ip_address', ipAddress)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') {
      await logger.error('IP_BLOCKING', 'Error checking blocked IP', { ipAddress, error });
      return false;
    }

    if (data) {
      // Check if the block has expired
      if (data.expires_at && new Date(data.expires_at) < new Date()) {
        // Deactivate expired block
        await deactivateExpiredBlock(data.id);
        return false;
      }
      return true;
    }

    return false;
  } catch (error) {
    await logger.error('IP_BLOCKING', 'Error checking blocked IP', { ipAddress, error });
    return false;
  }
}

/**
 * Check if an IP address is whitelisted
 */
export async function isIPWhitelisted(ipAddress: string): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ip_whitelist')
      .select('*')
      .eq('ip_address', ipAddress)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') {
      await logger.error('IP_BLOCKING', 'Error checking whitelisted IP', { ipAddress, error });
      return false;
    }

    return !!data;
  } catch (error) {
    await logger.error('IP_BLOCKING', 'Error checking whitelisted IP', { ipAddress, error });
    return false;
  }
}

/**
 * Block an IP address
 */
export async function blockIP(
  ipAddress: string,
  reason: string,
  blockedBy: string,
  expiresAt?: Date,
  metadata?: any
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createServerSupabaseClient();
    
    const blockData = {
      ip_address: ipAddress,
      reason,
      blocked_by: blockedBy,
      blocked_at: new Date().toISOString(),
      expires_at: expiresAt?.toISOString(),
      is_active: true,
      metadata
    };

    const { error } = await supabase
      .from('blocked_ips')
      .insert(blockData);

    if (error) {
      await logger.error('IP_BLOCKING', 'Error blocking IP', { ipAddress, error });
      return { success: false, error: error.message };
    }

    await logger.info('IP_BLOCKING', 'IP blocked successfully', { ipAddress, reason, blockedBy });
    return { success: true };
  } catch (error) {
    await logger.error('IP_BLOCKING', 'Error blocking IP', { ipAddress, error });
    return { success: false, error: 'Internal server error' };
  }
}

/**
 * Unblock an IP address
 */
export async function unblockIP(
  ipAddress: string,
  unblockedBy: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createServerSupabaseClient();
    
    const { error } = await supabase
      .from('blocked_ips')
      .update({ 
        is_active: false,
        unblocked_at: new Date().toISOString(),
        unblocked_by: unblockedBy
      })
      .eq('ip_address', ipAddress)
      .eq('is_active', true);

    if (error) {
      await logger.error('IP_BLOCKING', 'Error unblocking IP', { ipAddress, error });
      return { success: false, error: error.message };
    }

    await logger.info('IP_BLOCKING', 'IP unblocked successfully', { ipAddress, unblockedBy });
    return { success: true };
  } catch (error) {
    await logger.error('IP_BLOCKING', 'Error unblocking IP', { ipAddress, error });
    return { success: false, error: 'Internal server error' };
  }
}

/**
 * Add IP to whitelist
 */
export async function whitelistIP(
  ipAddress: string,
  reason: string,
  addedBy: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createServerSupabaseClient();
    
    const whitelistData = {
      ip_address: ipAddress,
      reason,
      added_by: addedBy,
      added_at: new Date().toISOString(),
      is_active: true
    };

    const { error } = await supabase
      .from('ip_whitelist')
      .insert(whitelistData);

    if (error) {
      await logger.error('IP_BLOCKING', 'Error whitelisting IP', { ipAddress, error });
      return { success: false, error: error.message };
    }

    await logger.info('IP_BLOCKING', 'IP whitelisted successfully', { ipAddress, reason, addedBy });
    return { success: true };
  } catch (error) {
    await logger.error('IP_BLOCKING', 'Error whitelisting IP', { ipAddress, error });
    return { success: false, error: 'Internal server error' };
  }
}

/**
 * Remove IP from whitelist
 */
export async function removeFromWhitelist(
  ipAddress: string,
  removedBy: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createServerSupabaseClient();
    
    const { error } = await supabase
      .from('ip_whitelist')
      .update({ 
        is_active: false,
        removed_at: new Date().toISOString(),
        removed_by: removedBy
      })
      .eq('ip_address', ipAddress)
      .eq('is_active', true);

    if (error) {
      await logger.error('IP_BLOCKING', 'Error removing IP from whitelist', { ipAddress, error });
      return { success: false, error: error.message };
    }

    await logger.info('IP_BLOCKING', 'IP removed from whitelist successfully', { ipAddress, removedBy });
    return { success: true };
  } catch (error) {
    await logger.error('IP_BLOCKING', 'Error removing IP from whitelist', { ipAddress, error });
    return { success: false, error: 'Internal server error' };
  }
}

/**
 * Record a rate limit violation using UPSERT to handle existing records
 */
export async function recordRateLimitViolation(
  ipAddress: string,
  endpoint: string,
  userAgent?: string
): Promise<void> {
  try {
    const supabase = createServerSupabaseClient();
    const now = new Date().toISOString();

    // First, try to get existing record
    const { data: existing, error: fetchError } = await supabase
      .from('rate_limit_violations')
      .select('*')
      .eq('ip_address', ipAddress)
      .eq('endpoint', endpoint)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      await logger.error('IP_BLOCKING', 'Error fetching existing violation record', { ipAddress, endpoint, error: fetchError });
      return;
    }

    if (existing) {
      // Update existing record - increment count and update last violation time
      const { error: updateError } = await supabase
        .from('rate_limit_violations')
        .update({
          violation_count: existing.violation_count + 1,
          last_violation: now,
          user_agent: userAgent || existing.user_agent
        })
        .eq('ip_address', ipAddress)
        .eq('endpoint', endpoint);

      if (updateError) {
        await logger.error('IP_BLOCKING', 'Error updating rate limit violation', { ipAddress, endpoint, error: updateError });
      } else {
        await logger.info('IP_BLOCKING', 'Rate limit violation updated', {
          ipAddress,
          endpoint,
          violationCount: existing.violation_count + 1
        });
      }
    } else {
      // Create new record
      const violationData = {
        ip_address: ipAddress,
        endpoint,
        violation_count: 1,
        first_violation: now,
        last_violation: now,
        user_agent: userAgent
      };

      const { error: insertError } = await supabase
        .from('rate_limit_violations')
        .insert(violationData);

      if (insertError) {
        await logger.error('IP_BLOCKING', 'Error creating rate limit violation record', { ipAddress, endpoint, error: insertError });
      } else {
        await logger.info('IP_BLOCKING', 'New rate limit violation recorded', { ipAddress, endpoint });
      }
    }
  } catch (error) {
    await logger.error('IP_BLOCKING', 'Error in recordRateLimitViolation', { ipAddress, endpoint, error });
  }
}

/**
 * Helper function to deactivate expired blocks
 */
async function deactivateExpiredBlock(blockId: string): Promise<void> {
  try {
    const supabase = createServerSupabaseClient();
    
    await supabase
      .from('blocked_ips')
      .update({ 
        is_active: false,
        unblocked_at: new Date().toISOString(),
        unblocked_by: 'system_expiration'
      })
      .eq('id', blockId);

    await logger.info('IP_BLOCKING', 'Expired IP block deactivated', { blockId });
  } catch (error) {
    await logger.error('IP_BLOCKING', 'Error deactivating expired block', { blockId, error });
  }
}
