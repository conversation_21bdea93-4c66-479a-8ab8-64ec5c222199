/**
 * Deepseek AI Integration for Email Testing
 *
 * This module provides integration with the Deepseek AI API for analyzing
 * email deliverability and generating recommendations.
 */

import { logError } from '@/lib/logging';
import { AuthResults } from './headerParser';

/**
 * Interface for Deepseek AI analysis request
 */
export interface DeepseekAnalysisRequest {
  headers: string;
  authResults: AuthResults;
  senderDomain: string;
  senderIp: string;
  spamScore: number;
}

/**
 * Interface for SPF analysis
 */
export interface SpfAnalysis {
  status: 'pass' | 'fail' | 'neutral' | 'none' | 'error';
  details: string;
  recommendation?: string;
}

/**
 * Interface for DKIM analysis
 */
export interface DkimAnalysis {
  status: 'pass' | 'fail' | 'neutral' | 'none' | 'error';
  details: string;
  recommendation?: string;
}

/**
 * Interface for DMARC analysis
 */
export interface DmarcAnalysis {
  status: 'pass' | 'fail' | 'neutral' | 'none' | 'error';
  details: string;
  recommendation?: string;
}

/**
 * Interface for IP reputation analysis
 */
export interface IpReputationAnalysis {
  status: 'good' | 'fair' | 'poor' | 'unknown';
  details: string;
}

/**
 * Interface for spam content analysis
 */
export interface SpamContentAnalysis {
  status: 'low' | 'medium' | 'high';
  details: string;
}

/**
 * Interface for recommendation
 */
export interface DeliverabilityRecommendation {
  category: string;
  issue: string;
  recommendation: string;
  dnsTemplate?: string;
  implementationSteps?: string;
  priority: number;
}

/**
 * Interface for Deepseek AI analysis response
 */
export interface DeepseekAnalysisResponse {
  overallScore: number; // 1-10
  analysis: {
    spf: SpfAnalysis;
    dkim: DkimAnalysis;
    dmarc: DmarcAnalysis;
    ipReputation: IpReputationAnalysis;
    spamContent: SpamContentAnalysis;
  };
  recommendations: DeliverabilityRecommendation[];
}

/**
 * Analyze email deliverability using Deepseek AI
 * @param data Email data for analysis
 * @returns Analysis results from Deepseek AI
 */
export async function analyzeEmailDeliverability(
  data: DeepseekAnalysisRequest
): Promise<DeepseekAnalysisResponse> {
  try {
    const apiKey = process.env.DEEPSEEK_API_KEY;
    if (!apiKey) {
      throw new Error('Deepseek API key is not configured');
    }

    // Create an enhanced prompt for the Deepseek model to analyze the email headers
    const prompt = `
      Analyze the following email for deliverability issues using the detailed authentication results:

      Email Headers (truncated):
      ${data.headers.substring(0, 1000)}... (truncated for brevity)

      Enhanced Authentication Results:
      SPF: ${data.authResults.spf.result} for domain ${data.authResults.spf.domain}
      ${data.authResults.spf.mechanism ? `- Mechanism: ${data.authResults.spf.mechanism}` : ''}
      ${data.authResults.spf.qualifier ? `- Qualifier: ${data.authResults.spf.qualifier}` : ''}
      ${data.authResults.spf.info ? `- Info: ${data.authResults.spf.info}` : ''}

      DKIM: ${data.authResults.dkim.result} for domain ${data.authResults.dkim.domain}
      ${data.authResults.dkim.selector ? `- Selector: ${data.authResults.dkim.selector}` : ''}
      ${data.authResults.dkim.algorithm ? `- Algorithm: ${data.authResults.dkim.algorithm}` : ''}
      ${data.authResults.dkim.info ? `- Info: ${data.authResults.dkim.info}` : ''}

      DMARC: ${data.authResults.dmarc.result} for domain ${data.authResults.dmarc.domain}
      ${data.authResults.dmarc.policy ? `- Policy: ${data.authResults.dmarc.policy}` : ''}
      ${data.authResults.dmarc.disposition ? `- Disposition: ${data.authResults.dmarc.disposition}` : ''}
      ${data.authResults.dmarc.alignment ? `- Alignment: SPF=${data.authResults.dmarc.alignment.spf}, DKIM=${data.authResults.dmarc.alignment.dkim}` : ''}

      Additional Information:
      Sender Domain: ${data.senderDomain}
      Sender IP: ${data.senderIp}
      ${data.authResults.heloHostname ? `HELO Hostname: ${data.authResults.heloHostname}` : ''}
      ${data.authResults.returnPath ? `Return Path: ${data.authResults.returnPath}` : ''}
      ${data.authResults.reverseDns ? `Reverse DNS: ${data.authResults.reverseDns}` : ''}
      Spam Score: ${data.spamScore}

      IMPORTANT SCORING REQUIREMENTS:
      Calculate the overallScore using exactly this 10-point system:
      - SPF Pass = 2 points, Fail/Other = 0 points
      - DKIM Pass = 2 points, Fail/Other = 0 points
      - DMARC Pass = 2 points, Fail/Other = 0 points
      - MX Records Pass/Warning = 2 points, Fail/Other = 0 points
      - PTR Records Pass/Warning = 2 points, Fail/Other = 0 points
      - Total possible: 10 points (5 protocols × 2 points each)

      Based on the authentication results provided:
      - SPF: ${(data.authResults.spf.result as any)?.result || data.authResults.spf.result} (${((data.authResults.spf.result as any)?.result || data.authResults.spf.result) === 'pass' ? '2 points' : '0 points'})
      - DKIM: ${data.authResults.dkim.result} (${data.authResults.dkim.result === 'pass' ? '2 points' : '0 points'})
      - DMARC: ${(data.authResults.dmarc.result as any)?.result || data.authResults.dmarc.result} (${((data.authResults.dmarc.result as any)?.result || data.authResults.dmarc.result) === 'pass' ? '2 points' : '0 points'})
      - MX Records: ${data.authResults.mx?.result || 'none'} (${(data.authResults.mx?.result === 'pass' || data.authResults.mx?.result === 'warning') ? '2 points' : '0 points'})
      - PTR Records: ${data.authResults.reverseDns?.result || 'none'} (${(data.authResults.reverseDns?.result === 'pass' || data.authResults.reverseDns?.result === 'warning') ? '2 points' : '0 points'})

      Provide a detailed analysis of email deliverability issues and recommendations in JSON format with the following structure:
      {
        "overallScore": number from 0-10 (calculated using the exact scoring system above),
        "analysis": {
          "spf": {
            "status": "pass" | "fail" | "neutral" | "none",
            "details": "detailed explanation"
          },
          "dkim": {
            "status": "pass" | "fail" | "neutral" | "none",
            "details": "detailed explanation"
          },
          "dmarc": {
            "status": "pass" | "fail" | "neutral" | "none",
            "details": "detailed explanation"
          },
          "ipReputation": {
            "status": "good" | "fair" | "poor" | "unknown",
            "details": "detailed explanation"
          },
          "spamContent": {
            "status": "low" | "medium" | "high",
            "details": "detailed explanation"
          }
        },
        "recommendations": [
          {
            "category": "category name",
            "issue": "issue description",
            "recommendation": "recommendation text",
            "dnsTemplate": "DNS record template if applicable",
            "implementationSteps": "step-by-step implementation guide",
            "priority": priority number (1-5, 1 being highest)
          }
        ]
      }

      Return ONLY the JSON object without any additional text, markdown formatting, or code blocks.
    `;

    // Call the Deepseek chat completions API
    const response = await fetch('https://api.deepseek.com/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: "deepseek-chat",
        messages: [
          { role: "system", content: "You are an expert in email deliverability and authentication protocols." },
          { role: "user", content: prompt }
        ],
        temperature: 0.2,
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Deepseek API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    // Extract the JSON response from the model's text output
    const content = result.choices[0].message.content;
    let analysisResult: DeepseekAnalysisResponse;

    try {
      // Try to parse the JSON response
      const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/) || content.match(/({[\s\S]*})/);
      const jsonStr = jsonMatch ? jsonMatch[1] : content;
      analysisResult = JSON.parse(jsonStr);
    } catch (parseError) {
      console.error('Error parsing JSON response:', parseError);
      // Fall back to the default analysis
      return generateFallbackAnalysis(data);
    }

    return analysisResult;
  } catch (error) {
    console.error('Error in analyzeEmailDeliverability:', error);
    logError('deepseek-ai', 'Error analyzing email deliverability with Deepseek AI', error);

    // If the Deepseek API is not available or returns an error,
    // provide a fallback analysis based on the authentication results
    return generateFallbackAnalysis(data);
  }
}

/**
 * Generate a fallback analysis when the Deepseek AI API is unavailable
 * @param data Email data for analysis
 * @returns Fallback analysis results
 */
function generateFallbackAnalysis(data: DeepseekAnalysisRequest): DeepseekAnalysisResponse {
  const { authResults, senderDomain, spamScore } = data;

  // Determine SPF status - handle both direct result and nested result structures
  let spfStatus = 'none';
  if (authResults.spf) {
    const spfData = authResults.spf as any;

    // Check if it's a direct result string
    if (typeof spfData.result === 'string') {
      spfStatus = spfData.result;
    }
    // Check if it's nested in a result object (Mailauth structure)
    else if (spfData.result && typeof spfData.result.result === 'string') {
      spfStatus = spfData.result.result;
    }
    // Fallback to checking the status field
    else if (spfData.status && typeof spfData.status.result === 'string') {
      spfStatus = spfData.status.result;
    }
  }

  // Normalize SPF status
  spfStatus = spfStatus === 'pass' ? 'pass' :
              spfStatus === 'fail' ? 'fail' :
              spfStatus === 'neutral' ? 'neutral' : 'none';

  // Determine DKIM status - handle both direct result and nested result structures
  let dkimStatus = 'none';
  if (authResults.dkim) {
    const dkimData = authResults.dkim as any;

    // Check if it's a direct result string
    if (typeof dkimData.result === 'string') {
      dkimStatus = dkimData.result;
    }
    // Check if it's nested in a result object (Mailauth structure)
    else if (dkimData.result && typeof dkimData.result.result === 'string') {
      dkimStatus = dkimData.result.result;
    }
    // Fallback to checking the status field
    else if (dkimData.status && typeof dkimData.status.result === 'string') {
      dkimStatus = dkimData.status.result;
    }
  }

  // Normalize DKIM status
  dkimStatus = dkimStatus === 'pass' ? 'pass' :
               dkimStatus === 'fail' ? 'fail' :
               dkimStatus === 'neutral' ? 'neutral' : 'none';

  // Determine DMARC status - handle both object and nested object structures
  let dmarcStatus = 'none';
  if (authResults.dmarc) {
    const dmarcData = authResults.dmarc as any;

    // Check if it's a direct result string
    if (typeof dmarcData.result === 'string') {
      dmarcStatus = dmarcData.result;
    }
    // Check if it's nested in a result object (like SPF)
    else if (dmarcData.result && typeof dmarcData.result.result === 'string') {
      dmarcStatus = dmarcData.result.result;
    }
    // Fallback to checking the status field
    else if (dmarcData.status && typeof dmarcData.status.result === 'string') {
      dmarcStatus = dmarcData.status.result;
    }
  }

  // Normalize status
  dmarcStatus = dmarcStatus === 'pass' ? 'pass' :
                dmarcStatus === 'fail' ? 'fail' :
                dmarcStatus === 'neutral' ? 'neutral' : 'none';

  // Calculate overall score (1-10) based on exactly 5 protocols
  // Each protocol contributes 2 points when it passes
  // Only "fail" results reduce the score; "warning" results are treated as passing
  let overallScore = 0;

  // Debug logging for scoring calculation
  console.log('=== COMPREHENSIVE SCORING CALCULATION DEBUG ===');
  console.log('Environment:', process.env.NODE_ENV);
  console.log('Deployment Platform:', process.env.VERCEL ? 'Vercel' : process.env.COOLIFY ? 'Coolify' : 'Local/Other');
  console.log('Timestamp:', new Date().toISOString());
  console.log('Sender Domain:', data.senderDomain);
  console.log('Sender IP:', data.senderIp);

  // Log raw authentication results for debugging
  console.log('=== RAW AUTHENTICATION RESULTS ===');
  console.log('SPF Raw Result:', JSON.stringify(authResults.spf, null, 2));
  console.log('DKIM Raw Result:', JSON.stringify(authResults.dkim, null, 2));
  console.log('DMARC Raw Result:', JSON.stringify(authResults.dmarc, null, 2));
  console.log('MX Raw Result:', JSON.stringify(authResults.mx, null, 2));
  console.log('PTR Raw Result:', JSON.stringify(authResults.reverseDns, null, 2));

  // Log processed status values with extraction details
  console.log('=== PROCESSED STATUS VALUES ===');
  console.log('SPF Status Extraction:');
  console.log('  - Final Status:', spfStatus);
  console.log('  - Direct result:', authResults.spf?.result);
  console.log('  - Nested result:', (authResults.spf as any)?.result?.result);
  console.log('  - Status result:', (authResults.spf as any)?.status?.result);
  console.log('DKIM Status Extraction:');
  console.log('  - Final Status:', dkimStatus);
  console.log('  - Direct result:', authResults.dkim?.result);
  console.log('  - Nested result:', (authResults.dkim as any)?.result?.result);
  console.log('  - Status result:', (authResults.dkim as any)?.status?.result);
  console.log('DMARC Status:', dmarcStatus);
  console.log('MX Status:', authResults.mx?.result || 'undefined');
  console.log('PTR Status:', authResults.reverseDns?.result || 'undefined');

  // Initialize scoring breakdown for detailed logging
  const scoringBreakdown = {
    spf: { status: spfStatus, points: 0, reason: '' },
    dkim: { status: dkimStatus, points: 0, reason: '' },
    dmarc: { status: dmarcStatus, points: 0, reason: '' },
    mx: { status: authResults.mx?.result || 'none', points: 0, reason: '' },
    ptr: { status: authResults.reverseDns?.result || 'none', points: 0, reason: '' }
  };

  console.log('=== DETAILED SCORING CALCULATION ===');

  // SPF: 2 points for pass, 0 for fail/other
  if (spfStatus === 'pass') {
    overallScore += 2;
    scoringBreakdown.spf.points = 2;
    scoringBreakdown.spf.reason = 'SPF authentication passed';
    console.log('✅ SPF: +2 points (status: pass, total:', overallScore, ')');
  } else {
    scoringBreakdown.spf.reason = `SPF authentication failed or missing (status: ${spfStatus})`;
    console.log('❌ SPF: +0 points (status:', spfStatus, ', total:', overallScore, ')');
  }

  // DKIM: 2 points for pass, 0 for fail/other
  if (dkimStatus === 'pass') {
    overallScore += 2;
    scoringBreakdown.dkim.points = 2;
    scoringBreakdown.dkim.reason = 'DKIM signature verified successfully';
    console.log('✅ DKIM: +2 points (status: pass, total:', overallScore, ')');
  } else {
    scoringBreakdown.dkim.reason = `DKIM signature failed or missing (status: ${dkimStatus})`;
    console.log('❌ DKIM: +0 points (status:', dkimStatus, ', total:', overallScore, ')');
  }

  // DMARC: 2 points for pass, 0 for fail/other
  if (dmarcStatus === 'pass') {
    overallScore += 2;
    scoringBreakdown.dmarc.points = 2;
    scoringBreakdown.dmarc.reason = 'DMARC policy compliance verified';
    console.log('✅ DMARC: +2 points (status: pass, total:', overallScore, ')');
  } else {
    scoringBreakdown.dmarc.reason = `DMARC policy failed or missing (status: ${dmarcStatus})`;
    console.log('❌ DMARC: +0 points (status:', dmarcStatus, ', total:', overallScore, ')');
  }

  // MX Records: 2 points for pass or warning, 0 for fail/other
  const mxStatus = authResults.mx?.result || 'none';
  console.log('MX Detailed Analysis:');
  console.log('  - Status:', mxStatus);
  console.log('  - Domain:', authResults.mx?.domain || 'unknown');
  console.log('  - Records Found:', authResults.mx?.totalRecords || 0);
  console.log('  - Issues:', authResults.mx?.issues || []);

  if (mxStatus === 'pass' || mxStatus === 'warning') {
    overallScore += 2;
    scoringBreakdown.mx.points = 2;
    scoringBreakdown.mx.reason = `MX records validated successfully (${mxStatus})`;
    console.log('✅ MX: +2 points (status:', mxStatus, ', total:', overallScore, ')');
  } else {
    scoringBreakdown.mx.reason = `MX record validation failed (status: ${mxStatus})`;
    console.log('❌ MX: +0 points (status:', mxStatus, ', total:', overallScore, ')');
  }

  // PTR Records: 2 points for pass or warning, 0 for fail/other
  const ptrStatus = authResults.reverseDns?.result || 'none';
  console.log('PTR Detailed Analysis:');
  console.log('  - Status:', ptrStatus);
  console.log('  - IP Address:', authResults.reverseDns?.ipAddress || 'unknown');
  console.log('  - Hostname:', authResults.reverseDns?.hostname || 'unknown');
  console.log('  - Domain Match:', authResults.reverseDns?.domainMatch || false);
  console.log('  - Issues:', authResults.reverseDns?.issues || []);

  if (ptrStatus === 'pass' || ptrStatus === 'warning') {
    overallScore += 2;
    scoringBreakdown.ptr.points = 2;
    scoringBreakdown.ptr.reason = `PTR record validated successfully (${ptrStatus})`;
    console.log('✅ PTR: +2 points (status:', ptrStatus, ', total:', overallScore, ')');
  } else {
    scoringBreakdown.ptr.reason = `PTR record validation failed (status: ${ptrStatus})`;
    console.log('❌ PTR: +0 points (status:', ptrStatus, ', total:', overallScore, ')');
  }

  // Log comprehensive scoring summary
  console.log('=== SCORING SUMMARY ===');
  console.log('Scoring Breakdown:', JSON.stringify(scoringBreakdown, null, 2));
  console.log('Total Points Awarded:', overallScore);
  console.log('Expected Maximum:', 10);
  console.log('Protocols Passing:', Object.values(scoringBreakdown).filter(p => p.points > 0).length, '/ 5');

  // Validate scoring logic
  const expectedScore = Object.values(scoringBreakdown).reduce((sum, protocol) => sum + protocol.points, 0);
  if (expectedScore !== overallScore) {
    console.error('❌ SCORING VALIDATION ERROR: Expected', expectedScore, 'but calculated', overallScore);
  } else {
    console.log('✅ Scoring validation passed');
  }

  // Ensure score is within range (0-10)
  const originalScore = overallScore;
  overallScore = Math.max(0, Math.min(10, overallScore));

  if (originalScore !== overallScore) {
    console.warn('⚠️ Score clamped from', originalScore, 'to', overallScore);
  }

  console.log('=== FINAL SCORING RESULT ===');
  console.log('Final Score:', overallScore, '/ 10');
  console.log('Environment:', process.env.NODE_ENV);
  console.log('Timestamp:', new Date().toISOString());
  console.log('=== END COMPREHENSIVE SCORING DEBUG ===');

  // Generate recommendations
  const recommendations: DeliverabilityRecommendation[] = [];

  // Enhanced SPF recommendation
  if (spfStatus !== 'pass') {
    const spfIssue = authResults.spf.info || 'SPF record is missing or invalid';
    const ipForRecord = authResults.ipAddress || data.senderIp || '0.0.0.0/24';

    recommendations.push({
      category: 'spf',
      issue: spfIssue,
      recommendation: `Configure an SPF record for ${senderDomain} to authorize your sending servers. ${authResults.spf.mechanism ? `Current mechanism: ${authResults.spf.mechanism}` : ''}`,
      dnsTemplate: `${senderDomain}. IN TXT "v=spf1 ip4:${ipForRecord} -all"`,
      implementationSteps: '1. Add this TXT record to your DNS\n2. Wait for DNS propagation (up to 24 hours)\n3. Test again\n4. Consider using include: mechanisms for third-party services',
      priority: 1
    });
  }

  // Enhanced DKIM recommendation
  if (dkimStatus !== 'pass') {
    const dkimIssue = authResults.dkim.info || 'DKIM signature is missing or invalid';
    const selectorInfo = authResults.dkim.selector ? ` (selector: ${authResults.dkim.selector})` : '';

    recommendations.push({
      category: 'dkim',
      issue: dkimIssue,
      recommendation: `Configure DKIM for ${senderDomain} to cryptographically sign your emails.${selectorInfo}`,
      implementationSteps: '1. Generate DKIM keys for your domain\n2. Add the public key as a TXT record (e.g., selector._domainkey.yourdomain.com)\n3. Configure your mail server to sign outgoing emails\n4. Test the DKIM signature',
      priority: 2
    });
  }

  // Enhanced DMARC recommendation
  if (dmarcStatus !== 'pass') {
    const dmarcIssue = authResults.dmarc.info || 'DMARC policy is missing or invalid';
    const currentPolicy = authResults.dmarc.policy ? ` Current policy: ${authResults.dmarc.policy}` : '';
    const alignmentInfo = authResults.dmarc.alignment ?
      ` Alignment issues: SPF=${authResults.dmarc.alignment.spf || 'unknown'}, DKIM=${authResults.dmarc.alignment.dkim || 'unknown'}` : '';

    recommendations.push({
      category: 'dmarc',
      issue: dmarcIssue + currentPolicy + alignmentInfo,
      recommendation: `Configure DMARC for ${senderDomain} to protect against email spoofing and improve deliverability.`,
      dnsTemplate: `_dmarc.${senderDomain}. IN TXT "v=DMARC1; p=none; rua=mailto:dmarc@${senderDomain}; ruf=mailto:dmarc@${senderDomain}"`,
      implementationSteps: '1. Add this TXT record to your DNS\n2. Start with p=none to monitor results\n3. Review DMARC reports\n4. Gradually increase enforcement to p=quarantine and then p=reject\n5. Ensure SPF and DKIM alignment',
      priority: 3
    });
  }

  return {
    overallScore,
    analysis: {
      spf: {
        status: spfStatus as any,
        details: `SPF authentication ${spfStatus} for domain ${authResults.spf.domain || senderDomain}. ${authResults.spf.info || ''} ${authResults.spf.mechanism ? `Mechanism: ${authResults.spf.mechanism}` : ''}`
      },
      dkim: {
        status: dkimStatus as any,
        details: `DKIM signature ${dkimStatus} for domain ${authResults.dkim.domain || senderDomain}. ${authResults.dkim.info || ''} ${authResults.dkim.selector ? `Selector: ${authResults.dkim.selector}` : ''} ${authResults.dkim.algorithm ? `Algorithm: ${authResults.dkim.algorithm}` : ''}`
      },
      dmarc: {
        status: dmarcStatus as any,
        details: `DMARC policy ${dmarcStatus} for domain ${authResults.dmarc.domain || senderDomain}. ${authResults.dmarc.info || ''} ${authResults.dmarc.policy ? `Policy: ${authResults.dmarc.policy}` : ''} ${authResults.dmarc.alignment ? `Alignment: SPF=${authResults.dmarc.alignment.spf}, DKIM=${authResults.dmarc.alignment.dkim}` : ''}`
      },
      ipReputation: {
        status: 'unknown',
        details: `IP reputation information is not available in fallback mode. ${authResults.reverseDns ? `Reverse DNS: ${authResults.reverseDns}` : ''} ${authResults.heloHostname ? `HELO: ${authResults.heloHostname}` : ''}`
      },
      spamContent: {
        status: spamScore > 5 ? 'high' : spamScore > 3 ? 'medium' : 'low',
        details: `Spam score: ${spamScore}. ${spamScore > 5 ? 'High spam score may significantly impact deliverability.' : spamScore > 3 ? 'Moderate spam score may affect deliverability.' : 'Low spam score is good for deliverability.'}`
      }
    },
    recommendations
  };
}
