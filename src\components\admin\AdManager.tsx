'use client';

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { PlusIcon, ArrowPathIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/solid';
import { AdConfig, AdDisplayOptions } from '@/lib/config/types';
import AdPlacementCard from './AdPlacementCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/Select';
import Link from 'next/link';

export default function AdManager() {
  const [adPlacements, setAdPlacements] = useState<AdConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDomain, setSelectedDomain] = useState<string>('all');
  const [domains, setDomains] = useState<string[]>([]);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Get the current pathname to determine which API route to use
  const pathname = usePathname();
  const isManagementPortal = pathname?.includes('management-portal-x7z9y2');
  const apiBasePath = isManagementPortal ? '/api/management-portal-x7z9y2' : '/api/admin';

  // Fetch all domains
  const fetchDomains = async () => {
    try {
      const response = await fetch(`${apiBasePath}/domains`);

      if (!response.ok) {
        throw new Error(`Failed to fetch domains: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        // Extract domain names from domain configs
        const domainNames = data.data.map((domain: any) => domain.domain);
        setDomains(domainNames);
      } else {
        console.error('Failed to fetch domains:', data.error);
      }
    } catch (err) {
      console.error('Error fetching domains:', err);
    }
  };

  // Fetch ad placements
  const fetchAdPlacements = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${apiBasePath}/ads`);

      if (!response.ok) {
        throw new Error(`Failed to fetch ad placements: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setAdPlacements(data.data);
      } else {
        setError(data.error || 'Failed to fetch ad placements');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching ad placements');
      console.error('Error fetching ad placements:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchDomains();
    fetchAdPlacements();
  }, []);

  // Handle update ad placement
  const handleUpdateAdPlacement = async (
    placementId: string,
    domain: string,
    updates: {
      adUnitId?: string;
      adClientId?: string; // Add adClientId as an optional update field
      isEnabled?: boolean;
      deviceTypes?: string[];
      displayOptions?: AdDisplayOptions;
    }
  ) => {
    try {
      setLoading(true);
      setError(null);

      // Sanitize the placementId to ensure it's safe for URL encoding
      // Replace any problematic characters with URL-safe alternatives
      const sanitizedPlacementId = placementId.replace(/[^a-zA-Z0-9-_]/g, '_');

      // Properly encode the placementId for the URL
      const encodedPlacementId = encodeURIComponent(sanitizedPlacementId);
      const encodedDomain = encodeURIComponent(domain);

      console.log(`Updating ad placement: ${placementId} (sanitized: ${sanitizedPlacementId}, encoded: ${encodedPlacementId}) for domain: ${domain}`);
      console.log(`Update data:`, updates);

      // Add retry logic for better reliability
      let retries = 3;
      let response = null;

      while (retries > 0) {
        try {
          response = await fetch(`${apiBasePath}/ads/${encodedPlacementId}/${encodedDomain}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(updates),
            // Add cache control to prevent caching issues
            cache: 'no-store'
          });

          // If successful, break out of the retry loop
          break;
        } catch (fetchError) {
          console.error(`Fetch error (retries left: ${retries - 1}):`, fetchError);
          retries--;

          // If we've exhausted all retries, rethrow the error
          if (retries === 0) {
            throw fetchError;
          }

          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      if (!response) {
        throw new Error('Failed to connect to the server after multiple attempts');
      }

      // Check if the response is ok before proceeding
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Server responded with status ${response.status}: ${errorText}`);
        throw new Error(`Server error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      if (data.success) {
        fetchAdPlacements();
        setSuccessMessage('Ad placement updated successfully');

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } else {
        setError(data.error || 'Failed to update ad placement');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while updating ad placement');
      console.error('Error updating ad placement:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle delete ad placement
  const handleDeleteAdPlacement = async (placementId: string, domain: string) => {
    try {
      if (!confirm(`Are you sure you want to delete the ad placement "${placementId}" for domain "${domain}"?`)) {
        return;
      }

      setLoading(true);
      setError(null);

      // Sanitize the placementId to ensure it's safe for URL encoding
      // Replace any problematic characters with URL-safe alternatives
      const sanitizedPlacementId = placementId.replace(/[^a-zA-Z0-9-_]/g, '_');

      // Properly encode the placementId for the URL
      const encodedPlacementId = encodeURIComponent(sanitizedPlacementId);
      const encodedDomain = encodeURIComponent(domain);

      console.log(`Deleting ad placement: ${placementId} (sanitized: ${sanitizedPlacementId}, encoded: ${encodedPlacementId}) for domain: ${domain}`);

      // Add retry logic for better reliability
      let retries = 3;
      let response = null;

      while (retries > 0) {
        try {
          response = await fetch(`${apiBasePath}/ads/${encodedPlacementId}/${encodedDomain}`, {
            method: 'DELETE',
            // Add cache control to prevent caching issues
            cache: 'no-store'
          });

          // If successful, break out of the retry loop
          break;
        } catch (fetchError) {
          console.error(`Fetch error (retries left: ${retries - 1}):`, fetchError);
          retries--;

          // If we've exhausted all retries, rethrow the error
          if (retries === 0) {
            throw fetchError;
          }

          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      if (!response) {
        throw new Error('Failed to connect to the server after multiple attempts');
      }

      // Check if the response is ok before proceeding
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Server responded with status ${response.status}: ${errorText}`);
        throw new Error(`Server error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      if (data.success) {
        fetchAdPlacements();
        setSuccessMessage('Ad placement deleted successfully');

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } else {
        setError(data.error || 'Failed to delete ad placement');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while deleting ad placement');
      console.error('Error deleting ad placement:', err);
    } finally {
      setLoading(false);
    }
  };

  // Filter ad placements by domain
  const filteredAdPlacements = selectedDomain === 'all'
    ? adPlacements
    : adPlacements.filter(ad => ad.domain === selectedDomain);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
          </svg>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Ad Management</h1>
            <p className="mt-1 text-sm text-gray-600">Configure and manage ad placements across your Fademail domains.</p>
          </div>
        </div>
        <div className="flex items-center">
          <button
            type="button"
            onClick={() => {
              fetchDomains();
              fetchAdPlacements();
            }}
            disabled={loading}
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#605f5f] hover:bg-[#505050] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#605f5f] disabled:opacity-50 mr-4"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-1.5 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <Link
            href="/management-portal-x7z9y2/ads/new"
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#ce601c] hover:bg-[#b85518] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#ce601c]"
          >
            <PlusIcon className="h-4 w-4 mr-1.5" />
            Add Ad Placement
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <XCircleIcon className="h-5 w-5 text-red-400 mr-2" />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2" />
            <span className="text-green-700">{successMessage}</span>
          </div>
        </div>
      )}

      <div className="bg-white shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">Ad Placements</h3>
          <div className="flex items-center space-x-2">
            <label htmlFor="domain-filter" className="text-sm font-medium text-gray-700">
              Filter by Domain:
            </label>
            <Select value={selectedDomain} onValueChange={(value) => setSelectedDomain(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select domain" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Domains</SelectItem>
                {domains.map((domain) => (
                  <SelectItem key={domain} value={domain}>
                    {domain}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="px-4 py-5 sm:p-6">
          {loading && !adPlacements.length ? (
            <div className="flex justify-center items-center h-32">
              <ArrowPathIcon className="h-8 w-8 text-indigo-500 animate-spin" />
            </div>
          ) : filteredAdPlacements.length > 0 ? (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {filteredAdPlacements.map((ad) => (
                <AdPlacementCard
                  key={`${ad.placementId}-${ad.domain}`}
                  adPlacement={ad}
                  onUpdate={handleUpdateAdPlacement}
                  onDelete={handleDeleteAdPlacement}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">
                {selectedDomain === 'all'
                  ? 'No ad placements found. Click "Add Ad Placement" to create one.'
                  : `No ad placements found for domain "${selectedDomain}".`}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
