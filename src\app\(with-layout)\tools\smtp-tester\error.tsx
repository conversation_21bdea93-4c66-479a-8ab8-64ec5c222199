/**
 * SMTP Tester Error Component
 * 
 * Error boundary for SMTP Tester page
 */

'use client';

import React from 'react';

interface SmtpTesterErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function SmtpTesterError({ error, reset }: SmtpTesterErrorProps) {
  return (
    <div className="min-h-screen" style={{ backgroundColor: '#fbfaf8' }}>
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-[#f3ece8] to-[#fbfaf8] border-b border-[#4a3728]/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{ color: '#1b130e' }}>
              Something went wrong
            </h1>
            <p className="text-xl md:text-2xl mb-6" style={{ color: '#4a3728' }}>
              An error occurred while loading the SMTP Tester Tool
            </p>
          </div>
        </div>
      </div>

      {/* Error Details */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-red-500/30 p-6">
          <div className="flex items-start gap-4">
            <svg className="w-8 h-8 text-red-500 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="flex-1">
              <h2 className="text-xl font-bold text-red-700 mb-2">Error Details</h2>
              <p className="text-red-600 mb-4">
                {error.message || 'An unexpected error occurred while loading the SMTP Tester Tool.'}
              </p>
              
              {error.digest && (
                <p className="text-sm text-red-500 mb-4">
                  Error ID: {error.digest}
                </p>
              )}

              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={reset}
                  className="px-6 py-2 bg-[#ce601c] text-white rounded-md hover:bg-[#ce601c]/90 transition-colors duration-200"
                >
                  Try Again
                </button>
                <a
                  href="/tools"
                  className="px-6 py-2 border border-[#4a3728] text-[#4a3728] rounded-md hover:bg-[#4a3728] hover:text-white transition-colors duration-200 text-center"
                >
                  Back to Tools
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Troubleshooting Tips */}
        <div className="mt-8 bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#f59e0b] to-[#ce601c] flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-bold" style={{ color: '#1b130e' }}>Troubleshooting Tips</h3>
          </div>

          <div className="space-y-3 text-sm" style={{ color: '#4a3728' }}>
            <div className="flex items-start gap-2">
              <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>Refresh the page to try loading the tool again</span>
            </div>
            <div className="flex items-start gap-2">
              <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>Check your internet connection</span>
            </div>
            <div className="flex items-start gap-2">
              <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>Clear your browser cache and cookies</span>
            </div>
            <div className="flex items-start gap-2">
              <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>Try using a different browser</span>
            </div>
            <div className="flex items-start gap-2">
              <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>If the problem persists, please contact support</span>
            </div>
          </div>
        </div>

        {/* Alternative Tools */}
        <div className="mt-8 bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-6">
          <h3 className="text-lg font-bold mb-4" style={{ color: '#1b130e' }}>Other Email Tools</h3>
          <p className="text-sm mb-4" style={{ color: '#4a3728' }}>
            While we work on fixing this issue, you can try our other email tools:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a
              href="/tools/email-tester"
              className="p-4 border border-[#4a3728]/20 rounded-lg hover:bg-[#f3ece8] transition-colors duration-200"
            >
              <h4 className="font-semibold mb-2" style={{ color: '#1b130e' }}>Email Tester Tool</h4>
              <p className="text-sm" style={{ color: '#4a3728' }}>
                Test email deliverability and authentication
              </p>
            </a>
            
            <a
              href="/tools/dkim-generator"
              className="p-4 border border-[#4a3728]/20 rounded-lg hover:bg-[#f3ece8] transition-colors duration-200"
            >
              <h4 className="font-semibold mb-2" style={{ color: '#1b130e' }}>DKIM Generator</h4>
              <p className="text-sm" style={{ color: '#4a3728' }}>
                Generate DKIM records for email authentication
              </p>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
