'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import LazyLoadAd from './LazyLoadAd';
import CollapsibleAdContainer from './CollapsibleAdContainer';
import { useDebounce } from '@/lib/hooks/useDebounce';

interface OptimizedAdRendererProps {
  domain: string;
  deviceType: 'desktop' | 'tablet' | 'mobile';
  maxAdsPerPage?: number;
  refreshInterval?: number; // in seconds
  onAdLoad?: (adId: string) => void;
  onAdError?: (adId: string, error: Error) => void;
}

interface ClientAd {
  placementId: string;
  adUnitId: string;
  displayOptions?: any;
  schedule?: any;
}

export default function OptimizedAdRenderer({
  domain,
  deviceType,
  maxAdsPerPage = 3,
  refreshInterval = 60,
  onAdLoad,
  onAdError
}: OptimizedAdRendererProps) {
  const [ads, setAds] = useState<ClientAd[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isPageVisible = useRef(true);
  const isPageIdle = useRef(false);
  const idleTimerRef = useRef<NodeJS.Timeout | null>(null);
  const idleThreshold = 60; // seconds of inactivity to consider page idle
  
  // Track user activity
  const resetIdleTimer = useCallback(() => {
    if (idleTimerRef.current) {
      clearTimeout(idleTimerRef.current);
    }
    
    isPageIdle.current = false;
    
    idleTimerRef.current = setTimeout(() => {
      isPageIdle.current = true;
    }, idleThreshold * 1000);
  }, [idleThreshold]);
  
  // Set up visibility and idle detection
  useEffect(() => {
    const handleVisibilityChange = () => {
      isPageVisible.current = document.visibilityState === 'visible';
    };
    
    const handleUserActivity = () => {
      resetIdleTimer();
    };
    
    // Initial setup
    resetIdleTimer();
    
    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    document.addEventListener('mousemove', handleUserActivity);
    document.addEventListener('keydown', handleUserActivity);
    document.addEventListener('scroll', handleUserActivity);
    document.addEventListener('click', handleUserActivity);
    document.addEventListener('touchstart', handleUserActivity);
    
    return () => {
      // Clean up
      if (idleTimerRef.current) {
        clearTimeout(idleTimerRef.current);
      }
      
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      document.removeEventListener('mousemove', handleUserActivity);
      document.removeEventListener('keydown', handleUserActivity);
      document.removeEventListener('scroll', handleUserActivity);
      document.removeEventListener('click', handleUserActivity);
      document.removeEventListener('touchstart', handleUserActivity);
    };
  }, [resetIdleTimer]);
  
  // Debounced fetch function to prevent excessive API calls
  const debouncedFetchAds = useDebounce(async () => {
    if (!isPageVisible.current) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/ads?domain=${domain}&deviceType=${deviceType}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch ads: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Limit the number of ads
        const limitedAds = data.data.slice(0, maxAdsPerPage);
        setAds(limitedAds);
        setLastRefresh(new Date());
      } else {
        setError(data.error || 'Failed to fetch ads');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching ads');
      console.error('Error fetching ads:', err);
    } finally {
      setLoading(false);
    }
  }, 300);
  
  // Fetch ads on mount and when dependencies change
  useEffect(() => {
    debouncedFetchAds();
    
    // Set up refresh timer
    const setupRefreshTimer = () => {
      if (refreshTimerRef.current) {
        clearTimeout(refreshTimerRef.current);
      }
      
      refreshTimerRef.current = setTimeout(() => {
        // Only refresh if page is visible and not idle
        if (isPageVisible.current && !isPageIdle.current) {
          debouncedFetchAds();
        }
        
        // Set up next refresh
        setupRefreshTimer();
      }, refreshInterval * 1000);
    };
    
    setupRefreshTimer();
    
    return () => {
      if (refreshTimerRef.current) {
        clearTimeout(refreshTimerRef.current);
      }
    };
  }, [domain, deviceType, maxAdsPerPage, refreshInterval, debouncedFetchAds]);
  
  // Handle ad load event
  const handleAdLoad = useCallback((adId: string) => {
    if (onAdLoad) onAdLoad(adId);
  }, [onAdLoad]);
  
  // Handle ad error event
  const handleAdError = useCallback((adId: string, error: Error) => {
    if (onAdError) onAdError(adId, error);
  }, [onAdError]);
  
  // Render nothing while loading or if there's an error
  if (loading && ads.length === 0) {
    return null;
  }
  
  if (error && ads.length === 0) {
    return null;
  }
  
  return (
    <>
      {ads.map(ad => {
        // Check if this is a collapsible ad
        if (ad.displayOptions?.displayMode === 'collapsible') {
          return (
            <CollapsibleAdContainer
              key={`${ad.placementId}-${domain}`}
              adId={ad.placementId}
              adUnitId={ad.adUnitId}
              position={ad.displayOptions?.position || 'bottom'}
              displayOptions={{
                initiallyExpanded: ad.displayOptions?.initiallyExpanded,
                rememberState: ad.displayOptions?.rememberState,
                expandAfterMinutes: ad.displayOptions?.expandAfterMinutes,
                dismissible: ad.displayOptions?.dismissible
              }}
            />
          );
        }
        
        // For standard ads, use lazy loading
        return (
          <LazyLoadAd
            key={`${ad.placementId}-${domain}`}
            adId={ad.placementId}
            adUnitId={ad.adUnitId}
            threshold={0.1}
            placeholderHeight={250}
            loadingDelay={ad.displayOptions?.delaySeconds ? ad.displayOptions.delaySeconds * 1000 : 0}
            onLoad={() => handleAdLoad(ad.placementId)}
            onError={(error) => handleAdError(ad.placementId, error)}
            className="mb-4"
          />
        );
      })}
    </>
  );
}
