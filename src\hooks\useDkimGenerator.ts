// DKIM Generator Hook - Full implementation
'use client';

import { useState } from 'react';
import { DkimGenerateRequest, DkimGenerateResponse, DkimValidationRequest, DkimValidationResponse } from '@/types/dkim';

interface UseDkimGeneratorReturn {
  generateKeys: (params: DkimGenerateRequest) => Promise<void>;
  validateDns: (domain: string, selector: string, expectedPublicKey?: string) => Promise<void>;
  result: DkimGenerateResponse['data'] | null;
  validation: DkimValidationResponse['data'] | null;
  loading: boolean;
  error: string | null;
  clearResult: () => void;
  clearError: () => void;
}

export function useDkimGenerator(): UseDkimGeneratorReturn {
  const [result, setResult] = useState<DkimGenerateResponse['data'] | null>(null);
  const [validation, setValidation] = useState<DkimValidationResponse['data'] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateKeys = async (params: DkimGenerateRequest) => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Generating DKIM keys:', params);

      const response = await fetch('/api/tools/dkim-generator/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      const data: DkimGenerateResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || data.message || 'Failed to generate DKIM keys');
      }

      setResult(data.data || null);
      console.log('DKIM keys generated successfully');

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate DKIM keys';
      console.error('DKIM generation error:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const validateDns = async (domain: string, selector: string, expectedPublicKey?: string) => {
    setLoading(true);
    setError(null);
    setValidation(null);

    try {
      console.log('Validating DKIM DNS record:', { domain, selector });

      const requestBody: DkimValidationRequest = {
        domain,
        selector,
        expectedPublicKey,
      };

      const response = await fetch('/api/tools/dkim-generator/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data: DkimValidationResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || data.message || 'Failed to validate DKIM record');
      }

      setValidation(data.data || null);
      console.log('DKIM validation completed:', data.data?.isValid ? 'VALID' : 'INVALID');

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to validate DKIM record';
      console.error('DKIM validation error:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const clearResult = () => {
    setResult(null);
    setValidation(null);
  };

  const clearError = () => {
    setError(null);
  };

  return {
    generateKeys,
    validateDns,
    result,
    validation,
    loading,
    error,
    clearResult,
    clearError,
  };
}
