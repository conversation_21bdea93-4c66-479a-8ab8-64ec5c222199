import { test, expect } from '@playwright/test';

test.describe('Admin Domain Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the admin login page
    await page.goto('/admin/login');

    // Fill in valid credentials
    await page.fill('input[name="username"]', 'test-admin');
    await page.fill('input[name="password"]', 'test-password');

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for navigation to complete
    await page.waitForURL('/admin/health');
  });

  test('should navigate to domains page', async ({ page }) => {
    // Navigate to the domains page
    await page.click('a[href="/admin/domains"]');

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Domain Management")');

    // Check that the domains list is displayed
    const domainsList = await page.isVisible('table');
    expect(domainsList).toBe(true);
  });

  test('should add a new domain', async ({ page }) => {
    // Navigate to the domains page
    await page.click('a[href="/admin/domains"]');

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Domain Management")');

    // Click the "Add Domain" button
    await page.click('button:has-text("Add Domain")');

    // Wait for the modal to appear
    await page.waitForSelector('div[role="dialog"]');

    // Fill in the domain form
    await page.fill('input[name="domain"]', 'test-domain.com');
    await page.check('input[name="is_active"]');
    await page.fill('input[name="weight"]', '50');

    // Submit the form
    await page.click('button:has-text("Add Domain")');

    // Wait for the success message
    await page.waitForSelector('div:has-text("Domain added successfully")');

    // Check that the new domain appears in the list
    const domainRow = await page.isVisible('tr:has-text("test-domain.com")');
    expect(domainRow).toBe(true);
  });

  test('should edit an existing domain', async ({ page }) => {
    // Navigate to the domains page
    await page.click('a[href="/admin/domains"]');

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Domain Management")');

    // Click the edit button for the first domain
    await page.click('tr:first-child button:has-text("Edit")');

    // Wait for the modal to appear
    await page.waitForSelector('div[role="dialog"]');

    // Change the domain weight
    const weightInput = await page.locator('input[name="weight"]');
    await weightInput.clear();
    await weightInput.fill('75');

    // Submit the form
    await page.click('button:has-text("Update Domain")');

    // Wait for the success message
    await page.waitForSelector('div:has-text("Domain updated successfully")');

    // Check that the domain was updated
    const weightCell = await page.locator('tr:first-child td:nth-child(3)').textContent();
    expect(weightCell).toBe('75');
  });

  test('should toggle domain activation', async ({ page }) => {
    // Navigate to the domains page
    await page.click('a[href="/admin/domains"]');

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Domain Management")');

    // Get the current activation state of the first domain
    const initialState = await page.isChecked('tr:first-child input[type="checkbox"]');

    // Toggle the activation state
    await page.click('tr:first-child input[type="checkbox"]');

    // Wait for the success message
    await page.waitForSelector('div:has-text("Domain updated successfully")');

    // Check that the activation state was toggled
    const newState = await page.isChecked('tr:first-child input[type="checkbox"]');
    expect(newState).toBe(!initialState);
  });

  test('should view domain details', async ({ page }) => {
    // Navigate to the domains page
    await page.click('a[href="/admin/domains"]');

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Domain Management")');

    // Click on the first domain name to view details
    await page.click('tr:first-child td:first-child a');

    // Wait for the domain details page to load
    await page.waitForSelector('h1:has-text("Domain Details")');

    // Check that the domain details are displayed
    const detailsSection = await page.isVisible('section:has-text("Domain Settings")');
    expect(detailsSection).toBe(true);

    // Check that the feature toggles section is displayed
    const featureTogglesSection = await page.isVisible('section:has-text("Feature Toggles")');
    expect(featureTogglesSection).toBe(true);
  });

  test('should update domain feature toggles', async ({ page }) => {
    // Navigate to the domains page
    await page.click('a[href="/admin/domains"]');

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Domain Management")');

    // Click on the first domain name to view details
    await page.click('tr:first-child td:first-child a');

    // Wait for the domain details page to load
    await page.waitForSelector('h1:has-text("Domain Details")');

    // Get the current state of the ads toggle
    const adsToggle = await page.locator('input[name="adsEnabled"]');
    const initialState = await adsToggle.isChecked();

    // Toggle the ads feature
    await adsToggle.click();

    // Submit the form
    await page.click('button:has-text("Save Changes")');

    // Wait for the success message
    await page.waitForSelector('div:has-text("Domain settings updated successfully")');

    // Check that the toggle state was updated
    const newState = await adsToggle.isChecked();
    expect(newState).toBe(!initialState);
  });
});
