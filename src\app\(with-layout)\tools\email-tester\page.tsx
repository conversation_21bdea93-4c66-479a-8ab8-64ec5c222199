/**
 * Email Tester Page
 *
 * This page allows users to test their email server's deliverability
 * by generating a test email address and analyzing the results.
 */

'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import TestAddressGenerator from '@/components/tools/email-tester/TestAddressGenerator';
import TestStatusIndicator from '@/components/tools/email-tester/TestStatusIndicator';
import { useEmailTesterAutoRefresh } from '@/hooks/useEmailTesterAutoRefresh';
import Breadcrumbs from '@/components/seo/Breadcrumbs';
import { SoftwareApplicationSchema, HowToSchema } from '@/components/seo/StructuredData';

export default function EmailDeliverabilityPage() {
  const router = useRouter();
  const [testAddress, setTestAddress] = useState<string | null>(null);
  const [testId, setTestId] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [currentStep, setCurrentStep] = useState(0); // 0 = Step 1, 1 = Step 2
  const checkResultsRef = useRef<HTMLDivElement>(null);
  const carouselRef = useRef<HTMLDivElement>(null);

  // Auto-refresh hook
  const { state: autoRefreshState, startPolling, stopPolling, manualRefresh } = useEmailTesterAutoRefresh();
  const [error, setError] = useState<string | null>(null);

  // Slide navigation functions
  const slideToStep = (step: number) => {
    setCurrentStep(step);
    if (carouselRef.current) {
      const slideWidth = carouselRef.current.offsetWidth;
      carouselRef.current.scrollTo({
        left: step * slideWidth,
        behavior: 'smooth'
      });
    }
  };

  const slideToNextStep = () => {
    if (currentStep < 1) {
      slideToStep(currentStep + 1);
    }
  };

  const slideToPrevStep = () => {
    if (currentStep > 0) {
      slideToStep(currentStep - 1);
    }
  };

  // Effect to handle responsive carousel behavior
  useEffect(() => {
    const handleResize = () => {
      if (carouselRef.current) {
        const slideWidth = carouselRef.current.offsetWidth;
        carouselRef.current.scrollTo({
          left: currentStep * slideWidth,
          behavior: 'auto'
        });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [currentStep]);

  // Generate a new test email address
  const handleGenerateAddress = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('/api/tools/email-tester', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        setTestAddress(data.testAddress);
        setTestId(data.id);
        setError(null); // Clear any previous errors
      } else {
        setError(data.message || 'Failed to generate test address');
      }
    } catch (error) {
      setError('An error occurred while generating the test address');
      console.error(error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle clipboard copy and start auto-refresh
  const handleCopyAddress = () => {
    if (!testAddress || !testId) return;

    console.log('[Main Page] Email address copied, starting auto-refresh');

    // Slide to Step 2 (Check Results)
    slideToNextStep();

    // Start auto-refresh polling
    startPolling({
      testAddress,
      testAddressId: testId,
      onSuccess: (resultId: string) => {
        console.log('[Main Page] Auto-refresh success, redirecting to results');
        router.push(`/tools/email-tester/results/${resultId}`);
      },
      onError: (error: string) => {
        console.log('[Main Page] Auto-refresh error:', error);
        setError(error);
      }
    });
  };

  // Check if an email has been received and analyze it (manual)
  const handleCheckEmail = async () => {
    if (!testAddress || !testId) return;

    setIsChecking(true);
    setError(null);

    try {
      const response = await fetch('/api/tools/email-tester/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ testAddress, testAddressId: testId })
      });

      const data = await response.json();

      if (data.success) {
        // Navigate to the results page
        router.push(`/tools/email-tester/results/${data.resultId}`);
      } else {
        setError(data.message || 'No email found for this test address yet');
      }
    } catch (error) {
      setError('An error occurred while checking for emails');
      console.error(error);
    } finally {
      setIsChecking(false);
    }
  };

  // Breadcrumb items
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Tools', url: '/tools' },
    { name: 'Email Tester', url: '/tools/email-tester', current: true },
  ];

  // Structured data for the tool
  const toolFeatures = [
    'SPF Record Validation',
    'DKIM Signature Verification',
    'DMARC Policy Checking',
    'MX Record Analysis',
    'PTR Record Validation',
    'Email Authentication Scoring',
    'AI-Powered Recommendations',
    'Deliverability Analysis'
  ];

  const howToSteps = [
    {
      name: 'Generate Test Email',
      text: 'Click the generate button to create a unique test email address for your deliverability testing.',
      url: 'https://vanishpost.com/tools/email-tester#step1'
    },
    {
      name: 'Send Test Email',
      text: 'Send an email from your server to the generated test address to begin the analysis.',
      url: 'https://vanishpost.com/tools/email-tester#step2'
    },
    {
      name: 'View Results',
      text: 'Review detailed analysis results including SPF, DKIM, DMARC validation and get improvement recommendations.',
      url: 'https://vanishpost.com/tools/email-tester#step3'
    }
  ];

  return (
    <>
      {/* Structured Data */}
      <SoftwareApplicationSchema
        name="Email Tester Tool"
        description="Free email deliverability testing tool to check SPF, DKIM, DMARC authentication and improve email delivery rates"
        url="https://vanishpost.com/tools/email-tester"
        applicationCategory="BusinessApplication"
        features={toolFeatures}
      />
      <HowToSchema
        name="How to Test Email Deliverability"
        description="Step-by-step guide to test your email server's deliverability and authentication setup"
        steps={howToSteps}
      />

      <div className="min-h-screen" style={{ backgroundColor: '#fbfaf8' }}>
        <div className="max-w-5xl mx-auto px-4 py-8">
          {/* Breadcrumb Navigation */}
          <Breadcrumbs items={breadcrumbItems} />

          {/* Hero Section */}
          <div className="text-center mb-8 mt-8 sm:mt-10 lg:mt-12">
            <h1 className="text-3xl font-bold mb-3" style={{ color: '#1b130e' }}>
              Email Tester - Test Email Deliverability & Authentication
            </h1>
            <p className="text-base max-w-2xl mx-auto leading-normal" style={{ color: '#4a3728' }}>
              Free email deliverability testing tool. Check SPF, DKIM, DMARC authentication, test email server configuration, and get AI-powered recommendations to improve your email delivery rates.
            </p>
          </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-6">
          <div className="flex items-center space-x-3">
            <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 text-white text-sm`} style={{
              backgroundColor: currentStep === 0 ? '#1b130e' : '#66b077',
              borderColor: currentStep === 0 ? '#1b130e' : '#66b077'
            }}>
              {currentStep === 0 ? '1' : '✓'}
            </div>
            <div className={`h-1 w-12 rounded-full transition-all duration-300`} style={{
              backgroundColor: currentStep >= 1 ? '#66b077' : '#f3ece8'
            }}></div>
            <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 text-sm ${
              currentStep >= 1 ? 'text-white' : 'text-gray-400'
            }`} style={{
              backgroundColor: currentStep === 1 ? '#1b130e' : currentStep >= 1 ? '#66b077' : '#f3ece8',
              borderColor: currentStep === 1 ? '#1b130e' : currentStep >= 1 ? '#66b077' : '#f3ece8'
            }}>
              {currentStep >= 1 ? '2' : '2'}
            </div>
            <div className={`h-1 w-12 rounded-full transition-all duration-300`} style={{
              backgroundColor: '#f3ece8'
            }}></div>
            <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 text-gray-400 text-sm`} style={{
              backgroundColor: '#f3ece8',
              borderColor: '#f3ece8'
            }}>
              3
            </div>
          </div>
        </div>

        {/* Horizontal Carousel Container */}
        <div className="relative mb-6">
          {/* Navigation Controls */}
          <div className="flex justify-between items-center mb-3">
            <button
              onClick={slideToPrevStep}
              disabled={currentStep === 0}
              className="flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed"
              style={{ backgroundColor: '#f3ece8', border: '1px solid #f3ece8' }}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#1b130e' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Step Indicators */}
            <div className="flex space-x-2">
              <button
                onClick={() => slideToStep(0)}
                className={`w-2 h-2 rounded-full transition-all duration-300`}
                style={{ backgroundColor: currentStep === 0 ? '#1b130e' : '#f3ece8' }}
              />
              <button
                onClick={() => slideToStep(1)}
                disabled={!testAddress}
                className={`w-2 h-2 rounded-full transition-all duration-300 disabled:cursor-not-allowed`}
                style={{ backgroundColor: currentStep === 1 ? '#1b130e' : '#f3ece8' }}
              />
            </div>

            <button
              onClick={slideToNextStep}
              disabled={currentStep === 1 || !testAddress}
              className="flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed"
              style={{ backgroundColor: '#f3ece8', border: '1px solid #f3ece8' }}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#1b130e' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Carousel Container */}
          <div
            ref={carouselRef}
            className="overflow-hidden bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl shadow-sm"
          >
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentStep * 100}%)` }}
            >
              {/* Step 1 Card */}
              <div className="w-full flex-shrink-0 p-6">
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#1b130e] to-[#4a3728] rounded-xl flex items-center justify-center shadow-lg mr-3 flex-shrink-0">
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet" aria-label="Generate email address icon">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-xl font-bold" style={{ color: '#1b130e' }}>Generate Test Email Address</h2>
                    <p className="text-sm" style={{ color: '#4a3728' }}>Create a unique email address for deliverability testing</p>
                  </div>
                </div>
                <TestAddressGenerator
                  testAddress={testAddress}
                  isGenerating={isGenerating}
                  onGenerate={handleGenerateAddress}
                  onCopyAddress={handleCopyAddress}
                />
              </div>

              {/* Step 2 Card */}
              <div className="w-full flex-shrink-0 p-6">
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[#956b50] to-[#4a3728] rounded-xl flex items-center justify-center shadow-lg mr-3 flex-shrink-0">
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet" aria-label="Check results icon">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-xl font-bold" style={{ color: '#1b130e' }}>Check Email Authentication Results</h2>
                    <p className="text-sm" style={{ color: '#4a3728' }}>Analyze SPF, DKIM, DMARC and email deliverability</p>
                  </div>
                </div>
                <TestStatusIndicator
                  isChecking={isChecking}
                  onCheck={handleCheckEmail}
                  autoRefreshState={autoRefreshState}
                  onStopAutoRefresh={stopPolling}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="px-6 py-4 rounded-2xl mb-8 animate-in slide-in-from-bottom-4" style={{ backgroundColor: '#f59e0b', border: '1px solid #f59e0b', color: '#ffffff' }}>
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#ffffff' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {error}
            </div>
          </div>
        )}

        {/* SEO Content Section */}
        <div className="mt-16 mb-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold mb-6 text-center" style={{ color: '#1b130e' }}>
              About Email Deliverability Testing
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-6 shadow-sm transition-all duration-300 hover:bg-white/90 hover:shadow-md">
                <h3 className="text-lg font-semibold mb-3" style={{ color: '#1b130e' }}>
                  Email Authentication Protocols
                </h3>
                <p className="text-sm leading-relaxed mb-4" style={{ color: '#4a3728' }}>
                  Our email tester validates critical authentication protocols including SPF (Sender Policy Framework),
                  DKIM (DomainKeys Identified Mail), and DMARC (Domain-based Message Authentication, Reporting, and Conformance)
                  to ensure your emails reach the inbox.
                </p>
                <ul className="text-sm space-y-2" style={{ color: '#4a3728' }}>
                  <li className="flex items-start">
                    <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0" style={{ backgroundColor: '#66b077' }}></span>
                    <span><strong>SPF Records:</strong> Verify authorized sending servers</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0" style={{ backgroundColor: '#66b077' }}></span>
                    <span><strong>DKIM Signatures:</strong> Validate email integrity and authenticity</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0" style={{ backgroundColor: '#66b077' }}></span>
                    <span><strong>DMARC Policy:</strong> Protect against email spoofing and phishing</span>
                  </li>
                </ul>
              </div>
              <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-6 shadow-sm transition-all duration-300 hover:bg-white/90 hover:shadow-md">
                <h3 className="text-lg font-semibold mb-3" style={{ color: '#1b130e' }}>
                  Improve Email Delivery Rates
                </h3>
                <p className="text-sm leading-relaxed mb-4" style={{ color: '#4a3728' }}>
                  Get AI-powered recommendations to optimize your email server configuration, improve sender reputation,
                  and increase email deliverability rates. Our tool analyzes MX records, PTR records, and provides
                  actionable insights for better email performance.
                </p>
                <ul className="text-sm space-y-2" style={{ color: '#4a3728' }}>
                  <li className="flex items-start">
                    <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0" style={{ backgroundColor: '#66b077' }}></span>
                    <span><strong>MX Record Analysis:</strong> Validate mail server configuration</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0" style={{ backgroundColor: '#66b077' }}></span>
                    <span><strong>PTR Record Check:</strong> Verify reverse DNS setup</span>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0" style={{ backgroundColor: '#66b077' }}></span>
                    <span><strong>AI Recommendations:</strong> Get personalized improvement suggestions</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Area - Test History Link */}
        <div className="text-center pt-8 pb-8">
          <Link
            href="/tools/email-tester/history"
            className="group inline-flex items-center space-x-1 text-sm transition-colors duration-200 hover:opacity-80"
            style={{ color: '#956b50' }}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>View test history</span>
            <svg className="w-3 h-3 group-hover:translate-x-0.5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </div>
    </div>
    </>
  );
}
