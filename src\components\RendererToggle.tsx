'use client';

import React from 'react';

interface RendererToggleProps {
  rendererType: 'react-iframe' | 'simple-iframe';
  setRendererType: (type: 'react-iframe' | 'simple-iframe') => void;

  // Zoom control props
  currentScale?: number;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onResetZoom?: () => void;
  showZoomControls?: boolean;
}

/**
 * RendererToggle Component
 *
 * A floating, semi-transparent toggle for switching between React Iframe and Simple Iframe renderers.
 * Positioned at the bottom middle of the email viewer.
 */
export default function RendererToggle({
  rendererType,
  setRendererType,
  currentScale = 100,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  showZoomControls = false
}: RendererToggleProps) {
  // Log props for debugging
  console.log('RendererToggle props:', {
    rendererType,
    currentScale,
    showZoomControls,
    hasZoomIn: !!onZoomIn,
    hasZoomOut: !!onZoomOut,
    hasResetZoom: !!onResetZoom
  });
  return (
    <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-10">
      <div className="flex shadow-[0_0_10px_rgba(0,0,0,0.1)] rounded-full overflow-hidden bg-white">
        {/* Toggle container with custom styling for the toggle buttons */}
        <div className="flex rounded-full overflow-hidden">
          <button
            onClick={() => setRendererType('react-iframe')}
            className={`px-3 py-1.5 text-xs font-medium transition-all duration-200 flex items-center focus:outline-none ${
              rendererType === 'react-iframe'
                ? 'bg-[#ce601c] text-white rounded-l-full'
                : 'bg-white text-[#605f5f] hover:bg-gray-50 active:bg-gray-100 active:scale-95 rounded-l-full'
            }`}
            aria-label="Switch to Advanced Iframe renderer"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-3.5 w-3.5 mr-1 ${rendererType === 'react-iframe' ? 'text-white' : 'text-[#ce601c]'}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            Advanced
          </button>

          {/* No divider needed as we want a flat junction */}

          <button
            onClick={() => setRendererType('simple-iframe')}
            className={`px-3 py-1.5 text-xs font-medium transition-all duration-200 flex items-center focus:outline-none ${
              rendererType === 'simple-iframe'
                ? 'bg-[#ce601c] text-white rounded-r-full'
                : 'bg-white text-[#605f5f] hover:bg-gray-50 active:bg-gray-100 active:scale-95 rounded-r-full'
            }`}
            aria-label="Switch to Simple Iframe renderer"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-3.5 w-3.5 mr-1 ${rendererType === 'simple-iframe' ? 'text-white' : 'text-[#ce601c]'}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
            </svg>
            Simple
          </button>
        </div>

        {/* Zoom controls - only shown when showZoomControls is true and rendererType is 'react-iframe' */}
        {showZoomControls && rendererType === 'react-iframe' && (
          <div className="flex ml-2">
            <button
              onClick={onZoomOut}
              className="px-2 py-1.5 text-xs font-medium transition-all duration-200 flex items-center focus:outline-none bg-white text-[#605f5f] hover:bg-gray-50 active:bg-gray-100 active:scale-95 rounded-l-full border border-gray-200"
              title="Zoom out"
              aria-label="Zoom out"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3.5 w-3.5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
              </svg>
            </button>

            {/* Only show reset button when not at 100% */}
            {currentScale !== 100 ? (
              <button
                onClick={onResetZoom}
                className="px-2 py-1.5 text-xs font-medium transition-all duration-200 flex items-center focus:outline-none bg-[#ce601c] text-white hover:bg-[#b85518] active:bg-[#ce601c] active:scale-95 border border-[#ce601c]"
                title="Reset zoom to 100%"
                aria-label="Reset zoom"
              >
                <span className="flex items-center">
                  {currentScale}%
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </span>
              </button>
            ) : (
              <button
                className="px-2 py-1.5 text-xs font-medium transition-all duration-200 flex items-center focus:outline-none bg-white text-[#605f5f] border-t border-b border-gray-200"
                disabled
              >
                <span>{currentScale}%</span>
              </button>
            )}

            <button
              onClick={onZoomIn}
              className="px-2 py-1.5 text-xs font-medium transition-all duration-200 flex items-center focus:outline-none bg-white text-[#605f5f] hover:bg-gray-50 active:bg-gray-100 active:scale-95 rounded-r-full border border-gray-200"
              title="Zoom in"
              aria-label="Zoom in"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3.5 w-3.5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7" />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
