/**
 * Persistent Session Manager for VanishPost
 *
 * This module provides a unified session management system that:
 * 1. Persists sessions across page refreshes
 * 2. Uses localStorage for client-side persistence
 * 3. Provides consistent session IDs for security systems
 * 4. Handles session expiration and cleanup
 */

import React from 'react';
import { generateUUID } from '@/lib/utils/uuid';

export interface SessionMetadata {
  sessionId: string;
  createdAt: number;
  expiresAt: number;
  deviceType?: string;
  browser?: string;
  country?: string;
  referrer?: string;
  lastActivity: number;
}

// Client-safe session configuration (no server-side dependencies)
interface ClientSessionConfig {
  sessionDuration: number; // in milliseconds
  sessionExtensionDuration: number; // in milliseconds
  activityUpdateInterval: number; // in milliseconds
  storageKeys: {
    sessionId: string;
    sessionMetadata: string;
  };
  enableSessionPersistence: boolean;
  enableCrossBrowserSessions: boolean;
  enableSessionExtension: boolean;
  maxSessionExtensions: number;
  cleanupInterval: number;
  enableActivityTracking: boolean;
}

// Default client-side session configuration
const DEFAULT_CLIENT_SESSION_CONFIG: ClientSessionConfig = {
  sessionDuration: 24 * 60 * 60 * 1000, // 24 hours
  sessionExtensionDuration: 4 * 60 * 60 * 1000, // 4 hours
  activityUpdateInterval: 5 * 60 * 1000, // 5 minutes
  storageKeys: {
    sessionId: 'vanishpost_session_id',
    sessionMetadata: 'vanishpost_session_metadata'
  },
  enableSessionPersistence: true,
  enableCrossBrowserSessions: false,
  enableSessionExtension: true,
  maxSessionExtensions: 6,
  cleanupInterval: 60 * 60 * 1000, // 1 hour
  enableActivityTracking: true
};

/**
 * Get client-safe session configuration
 */
function getClientSessionConfig(): ClientSessionConfig {
  return DEFAULT_CLIENT_SESSION_CONFIG;
}

/**
 * Get or create a persistent session ID
 * This function ensures the same session ID is used across page refreshes
 */
export function getOrCreatePersistentSession(): SessionMetadata {
  if (typeof window === 'undefined') {
    // Server-side: generate a temporary session
    return createNewSession();
  }

  const config = getClientSessionConfig();

  try {
    // Try to get existing session from localStorage
    const existingSessionData = localStorage.getItem(config.storageKeys.sessionMetadata);
    
    if (existingSessionData) {
      const sessionMetadata: SessionMetadata = JSON.parse(existingSessionData);
      
      // Check if session is still valid
      if (sessionMetadata.expiresAt > Date.now()) {
        // Update last activity
        sessionMetadata.lastActivity = Date.now();
        localStorage.setItem(config.storageKeys.sessionMetadata, JSON.stringify(sessionMetadata));
        
        return sessionMetadata;
      } else {
        // Session expired, clean up
        clearSession();
      }
    }

    // No valid session exists, create a new one
    return createNewSession();

  } catch (error) {
    console.error('Error managing persistent session:', error);
    // Fallback: create new session
    return createNewSession();
  }
}

/**
 * Create a new session with metadata
 */
function createNewSession(): SessionMetadata {
  const now = Date.now();
  const sessionId = generateUUID();
  const config = getClientSessionConfig();

  const sessionMetadata: SessionMetadata = {
    sessionId,
    createdAt: now,
    expiresAt: now + config.sessionDuration,
    lastActivity: now,
    deviceType: getDeviceType(),
    browser: getBrowser(),
    country: 'unknown', // Could be enhanced with geolocation
    referrer: typeof window !== 'undefined' ? document.referrer || undefined : undefined
  };

  // Store in localStorage (client-side only)
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(config.storageKeys.sessionMetadata, JSON.stringify(sessionMetadata));
      localStorage.setItem(config.storageKeys.sessionId, sessionId);
    } catch (error) {
      console.error('Failed to store session in localStorage:', error);
    }
  }

  return sessionMetadata;
}

/**
 * Get current session ID (simple getter)
 */
export function getCurrentSessionId(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const config = getClientSessionConfig();

  try {
    const sessionData = localStorage.getItem(config.storageKeys.sessionMetadata);
    if (sessionData) {
      const metadata: SessionMetadata = JSON.parse(sessionData);

      // Check if session is still valid
      if (metadata.expiresAt > Date.now()) {
        return metadata.sessionId;
      } else {
        // Session expired
        clearSession();
        return null;
      }
    }

    return localStorage.getItem(config.storageKeys.sessionId);
  } catch (error) {
    console.error('Error getting current session ID:', error);
    return null;
  }
}

/**
 * Update session activity timestamp
 */
export function updateSessionActivity(): void {
  if (typeof window === 'undefined') return;

  const config = getClientSessionConfig();

  try {
    const sessionData = localStorage.getItem(config.storageKeys.sessionMetadata);
    if (sessionData) {
      const metadata: SessionMetadata = JSON.parse(sessionData);
      metadata.lastActivity = Date.now();
      localStorage.setItem(config.storageKeys.sessionMetadata, JSON.stringify(metadata));
    }
  } catch (error) {
    console.error('Error updating session activity:', error);
  }
}

/**
 * Clear current session
 */
export function clearSession(): void {
  if (typeof window === 'undefined') return;

  const config = getClientSessionConfig();

  try {
    localStorage.removeItem(config.storageKeys.sessionId);
    localStorage.removeItem(config.storageKeys.sessionMetadata);
  } catch (error) {
    console.error('Error clearing session:', error);
  }
}

/**
 * Check if current session is valid
 */
export function isSessionValid(): boolean {
  if (typeof window === 'undefined') return false;

  const config = getClientSessionConfig();

  try {
    const sessionData = localStorage.getItem(config.storageKeys.sessionMetadata);
    if (sessionData) {
      const metadata: SessionMetadata = JSON.parse(sessionData);
      return metadata.expiresAt > Date.now();
    }
    return false;
  } catch (error) {
    console.error('Error checking session validity:', error);
    return false;
  }
}

/**
 * Extend session expiration
 */
export function extendSession(additionalTime?: number): void {
  if (typeof window === 'undefined') return;

  const config = getClientSessionConfig();
  const extensionTime = additionalTime || config.sessionExtensionDuration;

  try {
    const sessionData = localStorage.getItem(config.storageKeys.sessionMetadata);
    if (sessionData) {
      const metadata: SessionMetadata = JSON.parse(sessionData);
      metadata.expiresAt = Date.now() + extensionTime;
      metadata.lastActivity = Date.now();
      localStorage.setItem(config.storageKeys.sessionMetadata, JSON.stringify(metadata));
    }
  } catch (error) {
    console.error('Error extending session:', error);
  }
}

/**
 * Get session metadata
 */
export function getSessionMetadata(): SessionMetadata | null {
  if (typeof window === 'undefined') return null;

  const config = getClientSessionConfig();

  try {
    const sessionData = localStorage.getItem(config.storageKeys.sessionMetadata);
    if (sessionData) {
      const metadata: SessionMetadata = JSON.parse(sessionData);

      // Check if session is still valid
      if (metadata.expiresAt > Date.now()) {
        return metadata;
      } else {
        clearSession();
        return null;
      }
    }
    return null;
  } catch (error) {
    console.error('Error getting session metadata:', error);
    return null;
  }
}

/**
 * Detect device type
 */
function getDeviceType(): string {
  if (typeof window === 'undefined') return 'unknown';
  
  const userAgent = navigator.userAgent;
  if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
    return 'mobile';
  } else if (/Tablet|iPad/.test(userAgent)) {
    return 'tablet';
  } else {
    return 'desktop';
  }
}

/**
 * Detect browser
 */
function getBrowser(): string {
  if (typeof window === 'undefined') return 'unknown';
  
  const userAgent = navigator.userAgent;
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  return 'Unknown';
}

/**
 * Initialize session management
 * Call this once when the app starts
 */
export function initializeSessionManagement(): SessionMetadata {
  // Clean up any expired sessions
  if (typeof window !== 'undefined') {
    const config = getClientSessionConfig();
    try {
      const sessionData = localStorage.getItem(config.storageKeys.sessionMetadata);
      if (sessionData) {
        const metadata: SessionMetadata = JSON.parse(sessionData);
        if (metadata.expiresAt <= Date.now()) {
          clearSession();
        }
      }
    } catch (error) {
      console.error('Error during session cleanup:', error);
      clearSession();
    }
  }

  // Get or create session
  return getOrCreatePersistentSession();
}

/**
 * Session management hook for React components
 */
export function useSessionManagement() {
  const [sessionMetadata, setSessionMetadata] = React.useState<SessionMetadata | null>(null);

  React.useEffect(() => {
    const config = getClientSessionConfig();
    const metadata = initializeSessionManagement();
    setSessionMetadata(metadata);

    // Set up periodic session activity updates
    const activityInterval = setInterval(() => {
      updateSessionActivity();
    }, config.activityUpdateInterval);

    return () => {
      clearInterval(activityInterval);
    };
  }, []);

  return {
    sessionId: sessionMetadata?.sessionId || null,
    sessionMetadata,
    updateActivity: updateSessionActivity,
    clearSession,
    extendSession,
    isValid: isSessionValid()
  };
}

// Export for backward compatibility
export const persistentSessionManager = {
  getOrCreatePersistentSession,
  getCurrentSessionId,
  updateSessionActivity,
  clearSession,
  isSessionValid,
  extendSession,
  getSessionMetadata,
  initializeSessionManagement
};
