'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

// Metadata moved to layout.tsx since this is now a client component

export default function MaintenancePage() {
  const router = useRouter();
  const [checking, setChecking] = useState(false);
  const [message, setMessage] = useState<{ text: string; type: 'info' | 'success' | 'error' } | null>(null);

  // Function to check if maintenance mode is still active
  const checkStatus = async () => {
    try {
      // Clear any previous messages
      setMessage(null);
      // Set checking state to show loading indicator
      setChecking(true);

      // Make a request to the status API
      const response = await fetch('/api/status', {
        method: 'GET',
        cache: 'no-store', // Don't cache the response
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      // Parse the response
      const data = await response.json();

      // Check if maintenance mode is still active
      if (response.status === 200 && !data.maintenanceMode) {
        // If maintenance mode is disabled, show success message and redirect
        setMessage({
          text: 'Service is back online! Redirecting to homepage...',
          type: 'success'
        });

        // Redirect to homepage after a short delay
        setTimeout(() => {
          router.push('/');
        }, 1500);
      } else {
        // If maintenance mode is still active, show info message
        setMessage({
          text: 'Maintenance is still in progress. Please check back later.',
          type: 'info'
        });
      }
    } catch (error) {
      // If there's an error, show error message
      setMessage({
        text: 'Error checking status. Please try again.',
        type: 'error'
      });
      console.error('Error checking maintenance status:', error);
    } finally {
      // Reset checking state
      setChecking(false);
    }
  };
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4 py-12">
      <div className="max-w-md w-full space-y-8 text-center">
        <div className="flex justify-center">
          <Image
            src="/vanishpost-temporary-email-logo.svg"
            alt="VanishPost Logo"
            width={80}
            height={80}
            className="h-20 w-20"
          />
        </div>

        <h1 className="mt-6 text-3xl font-extrabold text-gray-900 sm:text-4xl">
          We&apos;ll be back soon!
        </h1>

        <div className="mt-2 text-center">
          <p className="text-lg text-gray-600">
            VanishPost is currently undergoing scheduled maintenance.
          </p>
          <p className="mt-2 text-gray-600">
            We apologize for any inconvenience and appreciate your patience.
            Our team is working hard to improve the service for you.
          </p>
        </div>

        <div className="mt-8 flex justify-center">
          <div className="relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-orange-400 to-orange-600 rounded-lg blur opacity-75 animate-pulse"></div>
            <div className="relative px-7 py-4 bg-white rounded-lg leading-none flex items-center">
              <span className="flex items-center space-x-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span className="text-gray-700">
                  We&apos;re performing system upgrades to enhance your experience
                </span>
              </span>
            </div>
          </div>
        </div>

        <div className="mt-8 text-sm text-gray-500">
          <p>Expected completion: Shortly</p>
          <p className="mt-1">Thank you for your patience!</p>
        </div>

        {/* Status check button */}
        <div className="mt-8">
          <button
            onClick={checkStatus}
            disabled={checking}
            className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white
              ${checking ? 'bg-gray-400 cursor-not-allowed' : 'bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500'}`}
          >
            {checking ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Checking...
              </>
            ) : (
              <>
                <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Check Status
              </>
            )}
          </button>
        </div>

        {/* Status message */}
        {message && (
          <div className={`mt-4 p-3 rounded-md ${
            message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' :
            message.type === 'error' ? 'bg-red-50 text-red-800 border border-red-200' :
            'bg-blue-50 text-blue-800 border border-blue-200'
          }`}>
            <p className="text-sm font-medium">{message.text}</p>
          </div>
        )}

        {/* Admin information removed for security reasons */}
      </div>

      <footer className="mt-auto pt-8 pb-4 text-center text-sm text-gray-500">
        <p>&copy; {new Date().getFullYear()} VanishPost. All rights reserved.</p>
      </footer>
    </div>
  );
}
