'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Users, Eye, Clock, Globe } from 'lucide-react';

interface LiveVisitorData {
  liveVisitors: number;
  activeThresholdMinutes: number;
  metrics: {
    sessionsLastHour: number;
    sessionsLast5Minutes: number;
    avgActiveDuration: number;
  };
  breakdowns: {
    device: Record<string, number>;
    browser: Record<string, number>;
    country: Record<string, number>;
  };
  timestamp: string;
}

interface LiveVisitorCountProps {
  refreshInterval?: number; // in milliseconds, default 10 seconds
  activeThreshold?: number; // in minutes, default 3 minutes
  showBreakdown?: boolean; // whether to show the breakdown card, default true
}

/**
 * Live Visitor Breakdown Component
 *
 * Displays breakdown of live visitors by device, browser, and country.
 */
export function LiveVisitorBreakdown({
  refreshInterval = 10000, // 10 seconds
  activeThreshold = 3 // 3 minutes
}: Omit<LiveVisitorCountProps, 'showBreakdown'>) {
  const [liveData, setLiveData] = useState<LiveVisitorData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch live visitor data from the API
   */
  const fetchLiveVisitors = async () => {
    try {
      setError(null);

      const params = new URLSearchParams();
      params.append('activeThreshold', activeThreshold.toString());
      params.append('includeDetails', 'false');

      const response = await fetch(`/api/management-portal-x7z9y2/analytics/live-visitors?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch live visitors data');
      }

      setLiveData(result.data);

    } catch (error) {
      console.error('Error fetching live visitors:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch live visitors');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchLiveVisitors();
  }, [activeThreshold]);

  // Auto-refresh interval
  useEffect(() => {
    const interval = setInterval(fetchLiveVisitors, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval, activeThreshold]);

  if (isLoading || error || !liveData) {
    return null;
  }

  // Only show if there's breakdown data
  if (!(Object.keys(liveData.breakdowns.device).length > 0 || Object.keys(liveData.breakdowns.browser).length > 0)) {
    return null;
  }

  return (
    <Card className="border-0 bg-gradient-to-br from-gray-50 to-gray-100/50 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-0.5">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-semibold text-gray-700 flex items-center gap-3">
          <div className="p-3 rounded-xl bg-gray-200/50">
            <Globe className="h-4 w-4 text-gray-700" />
          </div>
          Live Visitor Breakdown
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Device Breakdown */}
          {Object.keys(liveData.breakdowns.device).length > 0 && (
            <div className="p-3 rounded-lg bg-blue-50/50">
              <div className="text-xs font-semibold text-blue-700 mb-2">Devices</div>
              <div className="flex flex-wrap gap-2">
                {Object.entries(liveData.breakdowns.device).map(([device, count]) => (
                  <Badge key={device} variant="outline" className="text-xs text-blue-700 border-blue-400 bg-blue-50">
                    {device}: {count}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Browser Breakdown */}
          {Object.keys(liveData.breakdowns.browser).length > 0 && (
            <div className="p-3 rounded-lg bg-green-50/50">
              <div className="text-xs font-semibold text-green-700 mb-2">Browsers</div>
              <div className="flex flex-wrap gap-2">
                {Object.entries(liveData.breakdowns.browser).map(([browser, count]) => (
                  <Badge key={browser} variant="outline" className="text-xs text-green-700 border-green-400 bg-green-50">
                    {browser}: {count}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Country Breakdown */}
          {Object.keys(liveData.breakdowns.country).length > 0 && (
            <div className="p-3 rounded-lg bg-purple-50/50">
              <div className="text-xs font-semibold text-purple-700 mb-2">Countries</div>
              <div className="flex flex-wrap gap-2">
                {Object.entries(liveData.breakdowns.country).map(([country, count]) => (
                  <Badge key={country} variant="outline" className="text-xs text-purple-700 border-purple-400 bg-purple-50">
                    <Globe className="h-3 w-3 mr-1" />
                    {country}: {count}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Live Visitor Count Component
 *
 * Displays real-time visitor count with auto-refresh functionality.
 * Shows current active users and related metrics.
 */
export default function LiveVisitorCount({
  refreshInterval = 10000, // 10 seconds
  activeThreshold = 3, // 3 minutes
  showBreakdown = true // show breakdown by default
}: LiveVisitorCountProps) {
  const [liveData, setLiveData] = useState<LiveVisitorData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  /**
   * Fetch live visitor data from the API
   */
  const fetchLiveVisitors = async () => {
    try {
      setError(null);

      const params = new URLSearchParams();
      params.append('activeThreshold', activeThreshold.toString());
      params.append('includeDetails', 'false'); // We don't need detailed data for the count

      const response = await fetch(`/api/management-portal-x7z9y2/analytics/live-visitors?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch live visitors data');
      }

      setLiveData(result.data);
      setLastUpdate(new Date());

    } catch (error) {
      console.error('Error fetching live visitors:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch live visitors');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchLiveVisitors();
  }, [activeThreshold]);

  // Auto-refresh interval
  useEffect(() => {
    const interval = setInterval(fetchLiveVisitors, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval, activeThreshold]);

  /**
   * Format duration in seconds to human readable format
   */
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)}m`;
    } else {
      return `${Math.round(seconds / 3600)}h`;
    }
  };

  /**
   * Get status color based on visitor count
   */
  const getStatusColor = (count: number): string => {
    if (count === 0) return 'text-gray-500';
    if (count <= 5) return 'text-blue-600';
    if (count <= 20) return 'text-green-600';
    return 'text-orange-600';
  };

  /**
   * Get status indicator
   */
  const getStatusIndicator = (count: number): string => {
    if (count === 0) return '⚪';
    if (count <= 5) return '🔵';
    if (count <= 20) return '🟢';
    return '🟠';
  };

  if (isLoading && !liveData) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-gray-600 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Live Visitors
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-24"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-red-700 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Live Visitors
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-red-600 text-sm">
            Error: {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!liveData) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Main Live Visitor Count Card */}
      <Card className="border-0 bg-gradient-to-br from-gray-50 to-gray-100/50 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-0.5">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-semibold text-gray-700 flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gray-200/50">
              <Users className="h-4 w-4 text-gray-700" />
            </div>
            Live Visitors
            <Badge variant="outline" className="ml-auto text-gray-700 border-gray-500 bg-gray-50">
              {getStatusIndicator(liveData.liveVisitors)} Live
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className={`text-xl font-bold mb-2 ${getStatusColor(liveData.liveVisitors)}`}>
            {liveData.liveVisitors}
          </div>
          <div className="text-xs text-gray-600 mb-4">
            Active in last {liveData.activeThresholdMinutes} minutes
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="flex items-center gap-2 p-2 rounded-lg bg-gray-50/50">
              <Clock className="h-4 w-4 text-gray-600" />
              <span className="text-gray-700 font-medium">Avg: {formatDuration(liveData.metrics.avgActiveDuration)}</span>
            </div>
            <div className="flex items-center gap-2 p-2 rounded-lg bg-gray-50/50">
              <Eye className="h-4 w-4 text-gray-600" />
              <span className="text-gray-700 font-medium">Last 5m: {liveData.metrics.sessionsLast5Minutes}</span>
            </div>
          </div>

          {/* Last Update */}
          <div className="text-xs text-gray-600 mt-4 pt-3 border-t border-gray-200/50 font-medium">
            Updated: {lastUpdate.toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>

      {/* Device/Browser Breakdown */}
      {showBreakdown && (Object.keys(liveData.breakdowns.device).length > 0 || Object.keys(liveData.breakdowns.browser).length > 0) && (
        <Card className="border-0 bg-gradient-to-br from-gray-50 to-gray-100/50 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-0.5">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-semibold text-gray-700 flex items-center gap-3">
              <div className="p-3 rounded-xl bg-gray-200/50">
                <Globe className="h-4 w-4 text-gray-700" />
              </div>
              Live Visitor Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Device Breakdown */}
            {Object.keys(liveData.breakdowns.device).length > 0 && (
              <div className="p-3 rounded-lg bg-blue-50/50">
                <div className="text-xs font-semibold text-blue-700 mb-2">Devices</div>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(liveData.breakdowns.device).map(([device, count]) => (
                    <Badge key={device} variant="outline" className="text-xs text-blue-700 border-blue-400 bg-blue-50">
                      {device}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Browser Breakdown */}
            {Object.keys(liveData.breakdowns.browser).length > 0 && (
              <div className="p-3 rounded-lg bg-green-50/50">
                <div className="text-xs font-semibold text-green-700 mb-2">Browsers</div>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(liveData.breakdowns.browser).map(([browser, count]) => (
                    <Badge key={browser} variant="outline" className="text-xs text-green-700 border-green-400 bg-green-50">
                      {browser}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Country Breakdown */}
            {Object.keys(liveData.breakdowns.country).length > 0 && (
              <div className="p-3 rounded-lg bg-purple-50/50">
                <div className="text-xs font-semibold text-purple-700 mb-2">Countries</div>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(liveData.breakdowns.country).map(([country, count]) => (
                    <Badge key={country} variant="outline" className="text-xs text-purple-700 border-purple-400 bg-purple-50">
                      <Globe className="h-3 w-3 mr-1" />
                      {country}: {count}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
