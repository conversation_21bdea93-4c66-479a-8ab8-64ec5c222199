/**
 * API route for seeding ad placements
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logInfo, logError } from '@/lib/logging';

/**
 * POST /api/management-portal-x7z9y2/ads/seed
 *
 * Seed ad placements for testing
 */
export async function POST(request: NextRequest) {
  try {
    // Sample ad placements
    const adPlacements = [
      {
        placement_id: 'sidebar-top',
        domain: 'fademail.site',
        ad_unit_id: '1234567890',
        ad_client_id: 'ca-pub-8397529755029714',
        is_enabled: true,
        device_types: ['desktop', 'tablet', 'mobile'],
        display_options: {
          position: 'relative',
          margin: '10px 0',
          padding: '10px',
          backgroundColor: '#f9f9f9',
          borderRadius: '4px',
          maxWidth: '100%',
          overflow: 'hidden',
          showBorder: true,
          borderColor: '#e0e0e0',
          labelText: 'Advertisement',
          showLabel: true,
        },
        schedule: {
          useSchedule: true,
          monday: {
            enabled: true,
            startTime: '09:00',
            endTime: '17:00',
          },
          tuesday: {
            enabled: true,
            startTime: '09:00',
            endTime: '17:00',
          },
          wednesday: {
            enabled: true,
            startTime: '09:00',
            endTime: '17:00',
          },
          thursday: {
            enabled: true,
            startTime: '09:00',
            endTime: '17:00',
          },
          friday: {
            enabled: true,
            startTime: '09:00',
            endTime: '17:00',
          },
          saturday: {
            enabled: false,
            startTime: '00:00',
            endTime: '23:59',
          },
          sunday: {
            enabled: false,
            startTime: '00:00',
            endTime: '23:59',
          },
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        placement_id: 'sidebar-bottom',
        domain: 'fademail.site',
        ad_unit_id: 'ca-pub-1234567890/2345678901',
        is_enabled: true,
        device_types: ['desktop', 'tablet'],
        display_options: {
          position: 'relative',
          margin: '10px 0',
          padding: '10px',
          backgroundColor: '#f9f9f9',
          borderRadius: '4px',
          maxWidth: '100%',
          overflow: 'hidden',
          showBorder: true,
          borderColor: '#e0e0e0',
          labelText: 'Advertisement',
          showLabel: true,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        placement_id: 'header',
        domain: 'fademail.site',
        ad_unit_id: 'ca-pub-1234567890/3456789012',
        is_enabled: false,
        device_types: ['desktop'],
        display_options: {
          position: 'relative',
          margin: '0 0 20px 0',
          padding: '15px',
          backgroundColor: '#f0f0f0',
          borderRadius: '4px',
          maxWidth: '100%',
          overflow: 'hidden',
          showBorder: true,
          borderColor: '#d0d0d0',
          labelText: 'Sponsored',
          showLabel: true,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        placement_id: 'footer',
        domain: 'fademail.site',
        ad_unit_id: 'ca-pub-1234567890/4567890123',
        is_enabled: true,
        device_types: ['desktop', 'tablet', 'mobile'],
        display_options: {
          position: 'relative',
          margin: '20px 0 0 0',
          padding: '10px',
          backgroundColor: '#f9f9f9',
          borderRadius: '4px',
          maxWidth: '100%',
          overflow: 'hidden',
          showBorder: true,
          borderColor: '#e0e0e0',
          labelText: 'Advertisement',
          showLabel: true,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        placement_id: 'sidebar-top',
        domain: 'tempmail.dev',
        ad_unit_id: '5678901234',
        ad_client_id: 'ca-pub-8397529755029714',
        is_enabled: true,
        device_types: ['desktop', 'tablet', 'mobile'],
        display_options: {
          position: 'relative',
          margin: '10px 0',
          padding: '10px',
          backgroundColor: '#f9f9f9',
          borderRadius: '4px',
          maxWidth: '100%',
          overflow: 'hidden',
          showBorder: true,
          borderColor: '#e0e0e0',
          labelText: 'Advertisement',
          showLabel: true,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // Get Supabase client
    const supabase = createServerSupabaseClient();

    // Delete existing ad placements
    const { error: deleteError } = await supabase
      .from('ad_config')
      .delete()
      .neq('placement_id', 'dummy');

    if (deleteError) {
      logError('api', 'Error deleting existing ad placements', { error: deleteError });
      return NextResponse.json(
        { success: false, error: 'Failed to delete existing ad placements' },
        { status: 500 }
      );
    }

    // Insert new ad placements
    const { data, error } = await supabase
      .from('ad_config')
      .insert(adPlacements)
      .select();

    if (error) {
      logError('api', 'Error seeding ad placements', { error });
      return NextResponse.json(
        { success: false, error: 'Failed to seed ad placements' },
        { status: 500 }
      );
    }

    logInfo('api', 'Successfully seeded ad placements', { count: data.length });

    return NextResponse.json({
      success: true,
      message: `Successfully seeded ${data.length} ad placements`,
      data
    });
  } catch (error) {
    logError('api', 'Error seeding ad placements', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to seed ad placements' },
      { status: 500 }
    );
  }
}
