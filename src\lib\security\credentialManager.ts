/**
 * Secure credential management system for VanishPost
 * Replaces plain-text environment variables with encrypted storage
 */
import bcrypt from 'bcrypt';
import { createServerSupabaseClient } from '@/lib/supabase/server';

export interface SecureCredential {
  id?: string;
  name: string;
  encryptedValue: string;
  salt: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface AdminCredentials {
  username: string;
  passwordHash: string;
  isActive: boolean;
}

/**
 * Encrypt a credential value using bcrypt
 */
export async function encryptCredential(value: string): Promise<{ encryptedValue: string; salt: string }> {
  const saltRounds = 12; // Higher security for credentials
  const salt = await bcrypt.genSalt(saltRounds);
  const encryptedValue = await bcrypt.hash(value, salt);

  return { encryptedValue, salt };
}

/**
 * Verify a credential value against its encrypted version
 */
export async function verifyCredential(value: string, encryptedValue: string): Promise<boolean> {
  try {
    return await bcrypt.compare(value, encryptedValue);
  } catch (error) {
    console.error('Error verifying credential:', error);
    return false;
  }
}

/**
 * Store a secure credential in the database
 * Note: Using app_config table for now until secure_credentials table is created
 */
export async function storeSecureCredential(name: string, value: string): Promise<boolean> {
  try {
    const supabase = await createServerSupabaseClient();
    const { encryptedValue, salt } = await encryptCredential(value);

    // Store in app_config table with secure_ prefix
    const { error } = await supabase
      .from('app_config')
      .upsert({
        key: `secure_${name}`,
        value: JSON.stringify({ encryptedValue, salt }),
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error storing secure credential:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in storeSecureCredential:', error);
    return false;
  }
}

/**
 * Retrieve and verify a secure credential
 */
export async function getSecureCredential(name: string, valueToVerify: string): Promise<boolean> {
  try {
    const supabase = await createServerSupabaseClient();

    const { data, error } = await supabase
      .from('app_config')
      .select('value')
      .eq('key', `secure_${name}`)
      .single();

    if (error || !data) {
      console.error('Error retrieving secure credential:', error);
      return false;
    }

    if (!data.value || typeof data.value !== 'string') {
      console.error('Invalid credential data format');
      return false;
    }

    const credentialData = JSON.parse(data.value);
    return await verifyCredential(valueToVerify, credentialData.encryptedValue);
  } catch (error) {
    console.error('Error in getSecureCredential:', error);
    return false;
  }
}

/**
 * Get admin credentials with fallback to environment variables for backward compatibility
 */
export async function getAdminCredentials(): Promise<AdminCredentials | null> {
  try {
    // Use environment variables for now (until admin_users table is created)
    const envUsername = process.env.ADMIN_USERNAME;
    const envPasswordHash = process.env.ADMIN_PASSWORD_HASH;
    const envPassword = process.env.ADMIN_PASSWORD;

    if (envUsername && (envPasswordHash || envPassword)) {
      let passwordHash = envPasswordHash;

      // If only plain password is available, hash it (not recommended for production)
      if (!passwordHash && envPassword) {
        console.warn('Using plain-text password from environment. Please migrate to hashed password.');
        passwordHash = await bcrypt.hash(envPassword, 10);
      }

      return {
        username: envUsername,
        passwordHash: passwordHash!,
        isActive: true
      };
    }

    console.error('No admin credentials found in environment variables');
    return null;
  } catch (error) {
    console.error('Error getting admin credentials:', error);
    return null;
  }
}

/**
 * Verify admin login credentials
 */
export async function verifyAdminLogin(username: string, password: string): Promise<boolean> {
  try {
    const credentials = await getAdminCredentials();

    if (!credentials || !credentials.isActive) {
      return false;
    }

    if (credentials.username !== username) {
      return false;
    }

    return await bcrypt.compare(password, credentials.passwordHash);
  } catch (error) {
    console.error('Error verifying admin login:', error);
    return false;
  }
}

/**
 * Migrate environment credentials to secure storage
 * Note: Simplified for current implementation - validates credentials exist
 */
export async function migrateCredentialsToSecureStorage(): Promise<boolean> {
  try {
    const envUsername = process.env.ADMIN_USERNAME;
    const envPassword = process.env.ADMIN_PASSWORD;
    const envPasswordHash = process.env.ADMIN_PASSWORD_HASH;

    if (!envUsername || (!envPassword && !envPasswordHash)) {
      console.log('No environment credentials to migrate');
      return false;
    }

    // For now, just validate that credentials exist and are properly formatted
    if (envPasswordHash) {
      console.log('Admin credentials using secure password hash - migration not needed');
      return true;
    }

    if (envPassword && envPassword.length >= 8) {
      console.log('Admin credentials found but using plain text password - consider using ADMIN_PASSWORD_HASH');
      return true;
    }

    console.error('Admin credentials are incomplete or insecure');
    return false;
  } catch (error) {
    console.error('Error in migrateCredentialsToSecureStorage:', error);
    return false;
  }
}

/**
 * Initialize secure credential system
 */
export async function initializeSecureCredentials(): Promise<void> {
  try {
    // Attempt to migrate existing credentials
    await migrateCredentialsToSecureStorage();

    console.log('Secure credential system initialized');
  } catch (error) {
    console.error('Error initializing secure credentials:', error);
  }
}
