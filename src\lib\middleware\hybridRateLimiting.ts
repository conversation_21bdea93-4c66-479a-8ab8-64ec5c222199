/**
 * Hybrid Rate Limiting System for VanishPost
 * 
 * This system combines IP-based and session-based rate limiting to prevent:
 * 1. IP rotation attacks (tracked by session)
 * 2. Session refresh bypass (tracked by IP)
 * 3. Sophisticated bot attacks using both techniques
 * 
 * Security Strategy:
 * - Both IP and session limits must be satisfied
 * - Progressive blocking for repeat offenders
 * - Real-time threat detection
 */

import { NextRequest } from 'next/server';
import { checkRateLimitAsync } from '@/lib/analytics/rateLimiting';
import { sessionRateLimitMiddleware } from '@/lib/middleware/sessionRateLimiting';
import { logSecurityEvent } from '@/lib/analytics/securityHeaders';
import { getRateLimitConfig } from '@/lib/config/sessionConfig';

export interface HybridRateLimitResult {
  allowed: boolean;
  reason?: string;
  ipLimit: {
    allowed: boolean;
    remaining: number;
    resetTime: number;
    limit: number;
  };
  sessionLimit: {
    allowed: boolean;
    remaining: number;
    resetTime: Date;
    blocked?: boolean;
  };
  securityLevel: 'low' | 'medium' | 'high' | 'critical';
  recommendedAction?: 'allow' | 'warn' | 'block' | 'investigate';
}

export interface HybridRateLimitConfig {
  endpoint: string;
  ipLimits: {
    windowMs: number;
    maxRequests: number;
  };
  sessionLimits: {
    windowMs: number;
    maxRequests: number;
    blockDuration?: number;
  };

  strictMode?: boolean; // If true, both limits must be satisfied
}

// Enhanced rate limit configurations for hybrid approach
const HYBRID_RATE_LIMITS: Record<string, HybridRateLimitConfig> = {
  emailGeneration: {
    endpoint: 'emailGeneration',
    ipLimits: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 5, // Reduced from 15 to 5 per IP per hour
    },
    sessionLimits: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 8, // 8 emails per session per hour
      blockDuration: 60 * 60 * 1000 // Block for 1 hour if exceeded
    },

    strictMode: true // Both IP and session limits must be satisfied
  },

};

/**
 * Main hybrid rate limiting function
 */
export async function checkHybridRateLimit(
  request: NextRequest,
  endpoint: string,
  config?: HybridRateLimitConfig
): Promise<HybridRateLimitResult> {
  try {
    // Use dynamic configuration if available, fallback to hardcoded values
    let rateLimitConfig = config;

    if (!rateLimitConfig) {
      try {
        // Get dynamic rate limit configuration
        const dynamicConfig = await getRateLimitConfig();

        // Convert dynamic config to hybrid config format
        if (endpoint === 'emailGeneration' && dynamicConfig.emailGeneration) {
          rateLimitConfig = {
            endpoint: 'emailGeneration',
            ipLimits: dynamicConfig.emailGeneration.ipLimits,
            sessionLimits: dynamicConfig.emailGeneration.sessionLimits,
            strictMode: dynamicConfig.emailGeneration.strictMode
          };
        }
      } catch (error) {
        console.warn('Failed to get dynamic rate limit config, using fallback:', error);
      }
    }

    // Final fallback to hardcoded values
    if (!rateLimitConfig) {
      rateLimitConfig = HYBRID_RATE_LIMITS[endpoint];
    }

    if (!rateLimitConfig) {
      return createAllowedResult('No rate limit config found');
    }

    // Extract session ID and IP
    const sessionId = extractSessionId(request);
    const clientIP = getClientIP(request);

    // Note: Progressive blocking integration can be added later if needed

    // Check IP-based rate limiting
    const ipResult = await checkRateLimitAsync(request, endpoint);

    // Check session-based rate limiting
    const sessionConfig = {
      windowMs: rateLimitConfig.sessionLimits.windowMs,
      maxRequests: rateLimitConfig.sessionLimits.maxRequests,
      blockDuration: rateLimitConfig.sessionLimits.blockDuration
    };

    // Check if session limits are disabled (maxRequests = 0)
    const sessionLimitsDisabled = sessionConfig.maxRequests === 0;

    const sessionResult = sessionLimitsDisabled
      ? { allowed: true, remaining: 999, resetTime: new Date(Date.now() + sessionConfig.windowMs) }
      : await sessionRateLimitMiddleware(request, endpoint, sessionConfig);

    // Determine overall result based on strict mode
    const ipAllowed = ipResult.allowed;
    const sessionAllowed = sessionResult.allowed;

    let overallAllowed: boolean;
    let reason: string | undefined;
    let securityLevel: 'low' | 'medium' | 'high' | 'critical';
    let recommendedAction: 'allow' | 'warn' | 'block' | 'investigate';

    if (sessionLimitsDisabled) {
      // Session limits disabled: only enforce IP limits
      overallAllowed = ipAllowed;

      if (!overallAllowed) {
        reason = 'IP rate limit exceeded';
        securityLevel = 'high';
        recommendedAction = 'block';
      } else {
        // IP allowed, check for warning conditions
        if (ipResult.remaining <= 1) {
          securityLevel = 'medium';
          recommendedAction = 'warn';
        } else {
          securityLevel = 'low';
          recommendedAction = 'allow';
        }
      }
    } else if (rateLimitConfig.strictMode) {
      // Strict mode: both limits must be satisfied
      overallAllowed = ipAllowed && sessionAllowed;

      if (!overallAllowed) {
        if (!ipAllowed && !sessionAllowed) {
          reason = 'Both IP and session rate limits exceeded';
          securityLevel = 'critical';
          recommendedAction = 'block';
        } else if (!ipAllowed) {
          reason = 'IP rate limit exceeded';
          securityLevel = 'high';
          recommendedAction = 'block';
        } else {
          reason = 'Session rate limit exceeded';
          securityLevel = 'high';
          recommendedAction = 'block';
        }
      } else {
        // Both allowed, but check for warning conditions
        if (ipResult.remaining <= 1 || sessionResult.remaining <= 1) {
          securityLevel = 'medium';
          recommendedAction = 'warn';
        } else {
          securityLevel = 'low';
          recommendedAction = 'allow';
        }
      }
    } else {
      // Permissive mode: either limit can be satisfied
      overallAllowed = ipAllowed || sessionAllowed;

      if (!overallAllowed) {
        reason = 'Both IP and session rate limits exceeded';
        securityLevel = 'critical';
        recommendedAction = 'block';
      } else if (!ipAllowed || !sessionAllowed) {
        securityLevel = 'medium';
        recommendedAction = 'warn';
      } else {
        securityLevel = 'low';
        recommendedAction = 'allow';
      }
    }

    // Log security event if blocked
    if (!overallAllowed) {
      logSecurityEvent('rate_limit_exceeded', {
        ip: clientIP,
        endpoint,
        metadata: {
          reason,
          securityLevel,
          ipAllowed,
          sessionAllowed,
          sessionLimitsDisabled,

          strictMode: rateLimitConfig.strictMode,
          ipLimit: ipResult,
          sessionLimit: sessionResult
        }
      });
    }

    return {
      allowed: overallAllowed,
      reason,
      ipLimit: {
        allowed: ipResult.allowed,
        remaining: ipResult.remaining,
        resetTime: ipResult.resetTime,
        limit: ipResult.limit
      },
      sessionLimit: {
        allowed: sessionResult.allowed,
        remaining: sessionResult.remaining,
        resetTime: sessionResult.resetTime,
        blocked: sessionResult.blocked
      },
      securityLevel,
      recommendedAction
    };

  } catch (error) {
    console.error('Error in hybrid rate limiting:', error);
    
    // Fail securely - deny access on error
    return {
      allowed: false,
      reason: 'Rate limiting system error',
      ipLimit: { allowed: false, remaining: 0, resetTime: Date.now(), limit: 0 },
      sessionLimit: { allowed: false, remaining: 0, resetTime: new Date() },
      securityLevel: 'critical',
      recommendedAction: 'block'
    };
  }
}

/**
 * Extract session ID from request
 */
function extractSessionId(request: NextRequest): string | null {
  return request.headers.get('x-session-id') || 
         request.headers.get('session-id') || 
         request.cookies.get('session-id')?.value || 
         null;
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  return request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
         request.headers.get('x-real-ip') ||
         request.headers.get('cf-connecting-ip') ||
         'unknown';
}

/**
 * Create an allowed result for cases where no rate limiting applies
 */
function createAllowedResult(reason: string): HybridRateLimitResult {
  return {
    allowed: true,
    reason,
    ipLimit: { allowed: true, remaining: 999, resetTime: Date.now() + 3600000, limit: 999 },
    sessionLimit: { allowed: true, remaining: 999, resetTime: new Date(Date.now() + 3600000) },
    securityLevel: 'low',
    recommendedAction: 'allow'
  };
}

/**
 * Get rate limit headers for response
 */
export function getHybridRateLimitHeaders(result: HybridRateLimitResult): Record<string, string> {
  return {
    'X-RateLimit-Limit-IP': result.ipLimit.limit.toString(),
    'X-RateLimit-Remaining-IP': result.ipLimit.remaining.toString(),
    'X-RateLimit-Reset-IP': Math.ceil(result.ipLimit.resetTime / 1000).toString(),
    'X-RateLimit-Limit-Session': result.sessionLimit.remaining.toString(),
    'X-RateLimit-Remaining-Session': result.sessionLimit.remaining.toString(),
    'X-RateLimit-Reset-Session': Math.ceil(result.sessionLimit.resetTime.getTime() / 1000).toString(),
    'X-Security-Level': result.securityLevel,
    'X-Recommended-Action': result.recommendedAction || 'allow'
  };
}

export default checkHybridRateLimit;
