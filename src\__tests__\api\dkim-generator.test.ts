// DKIM Generator API Tests
import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST } from '@/app/api/tools/dkim-generator/generate/route';

// Mock Supabase client
jest.mock('@/lib/supabase/server', () => ({
  createServerSupabaseClient: jest.fn(() => ({
    from: jest.fn(() => ({
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(() => ({
            data: { id: 'test-record-id' },
            error: null
          }))
        }))
      }))
    }))
  }))
}));

// Mock audit logging
jest.mock('@/lib/tools/shared/auditLogging', () => ({
  logRecordGeneration: jest.fn(),
  TOOL_NAMES: {
    DKIM_GENERATOR: 'dkim-generator'
  }
}));

// Mock rate limiting
jest.mock('@/lib/tools/shared/rateLimiting', () => ({
  checkRateLimit: jest.fn(() => true)
}));

describe('DKIM Generator API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/tools/dkim-generator/generate', () => {
    it('should generate DKIM keys successfully', async () => {
      const requestBody = {
        domain: 'example.com',
        selector: 'default',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('privateKey');
      expect(data.data).toHaveProperty('publicKey');
      expect(data.data).toHaveProperty('dnsRecord');
      expect(data.data).toHaveProperty('recordName');
      expect(data.data.domain).toBe('example.com');
      expect(data.data.selector).toBe('default');
      expect(data.data.keyStrength).toBe(1024);
    });

    it('should generate 2048-bit DKIM keys successfully', async () => {
      const requestBody = {
        domain: 'secure.example.com',
        selector: 'secure',
        keyStrength: 2048,
        sessionId: 'test-session-2048'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.keyStrength).toBe(2048);
      expect(data.data.publicKey.length).toBeGreaterThan(300); // 2048-bit keys are longer
    });

    it('should reject missing required fields', async () => {
      const requestBody = {
        domain: 'example.com',
        // Missing selector and keyStrength
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toContain('Missing required fields');
    });

    it('should reject invalid domain format', async () => {
      const requestBody = {
        domain: 'invalid..domain',
        selector: 'default',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toContain('Invalid input format');
    });

    it('should reject invalid selector format', async () => {
      const requestBody = {
        domain: 'example.com',
        selector: 'invalid selector with spaces',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toContain('Invalid input format');
    });

    it('should reject invalid key strength', async () => {
      const requestBody = {
        domain: 'example.com',
        selector: 'default',
        keyStrength: 512, // Invalid key strength
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toContain('Invalid input format');
    });

    it('should handle rate limiting', async () => {
      // Mock rate limiting to return false
      const { checkRateLimit } = require('@/lib/tools/shared/rateLimiting');
      checkRateLimit.mockReturnValue(false);

      const requestBody = {
        domain: 'example.com',
        selector: 'default',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(429);
      expect(data.success).toBe(false);
      expect(data.message).toContain('Rate limit exceeded');
    });

    it('should handle database errors gracefully', async () => {
      // Mock Supabase to return an error
      const { createServerSupabaseClient } = require('@/lib/supabase/server');
      createServerSupabaseClient.mockReturnValue({
        from: jest.fn(() => ({
          insert: jest.fn(() => ({
            select: jest.fn(() => ({
              single: jest.fn(() => ({
                data: null,
                error: { message: 'Database connection failed' }
              }))
            }))
          }))
        }))
      });

      const requestBody = {
        domain: 'example.com',
        selector: 'default',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.message).toContain('Internal server error');
    });

    it('should include proper DNS record format', async () => {
      const requestBody = {
        domain: 'example.com',
        selector: 'test',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.recordName).toBe('test._domainkey.example.com');
      expect(data.data.dnsRecord).toContain('v=DKIM1');
      expect(data.data.dnsRecord).toContain('k=rsa');
      expect(data.data.dnsRecord).toContain('p=');
    });

    it('should include setup instructions', async () => {
      const requestBody = {
        domain: 'example.com',
        selector: 'default',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.instructions).toBeInstanceOf(Array);
      expect(data.data.instructions.length).toBeGreaterThan(0);
    });

    it('should include expiration information', async () => {
      const requestBody = {
        domain: 'example.com',
        selector: 'default',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data).toHaveProperty('generatedAt');
      expect(data.data).toHaveProperty('expiresAt');
      
      const generatedAt = new Date(data.data.generatedAt);
      const expiresAt = new Date(data.data.expiresAt);
      
      expect(expiresAt.getTime()).toBeGreaterThan(generatedAt.getTime());
    });

    it('should log audit events', async () => {
      const { logRecordGeneration } = require('@/lib/tools/shared/auditLogging');

      const requestBody = {
        domain: 'example.com',
        selector: 'default',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      await POST(request);

      expect(logRecordGeneration).toHaveBeenCalledWith(
        'dkim-generator',
        'dkim',
        'example.com',
        'test-session',
        undefined,
        expect.objectContaining({
          selector: 'default',
          keyStrength: 1024,
          recordId: 'test-record-id'
        })
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON requests', async () => {
      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
    });

    it('should handle missing request body', async () => {
      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
    });
  });

  describe('Security', () => {
    it('should sanitize domain input', async () => {
      const requestBody = {
        domain: 'example.com<script>alert("xss")</script>',
        selector: 'default',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
    });

    it('should sanitize selector input', async () => {
      const requestBody = {
        domain: 'example.com',
        selector: 'default<script>alert("xss")</script>',
        keyStrength: 1024,
        sessionId: 'test-session'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/dkim-generator/generate', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
    });
  });
});
