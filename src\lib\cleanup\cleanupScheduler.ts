/**
 * Cleanup Scheduler
 *
 * This module provides functionality to schedule and manage cleanup tasks
 * for expired emails and other resources.
 */
import { getConfig, updateConfig } from '@/lib/config/configService';
import { logInfo, logError } from '@/lib/logging';

// Store for cleanup intervals
let cleanupIntervalId: NodeJS.Timeout | null = null;

/**
 * Start the cleanup scheduler
 *
 * @returns Promise<boolean> - Whether the scheduler was started successfully
 */
export async function startCleanupScheduler(): Promise<boolean> {
  try {
    // If already running, stop it first
    if (cleanupIntervalId) {
      stopCleanupScheduler();
    }

    // Get cleanup interval from configuration
    const cleanupIntervalMinutes = await getConfig('cleanupIntervalMinutes');

    if (!cleanupIntervalMinutes || cleanupIntervalMinutes <= 0) {
      logError('cleanup', 'Invalid cleanup interval', { cleanupIntervalMinutes });
      return false;
    }

    // Convert minutes to milliseconds
    const intervalMs = cleanupIntervalMinutes * 60 * 1000;

    // Record scheduler start time for consistent next run calculations
    const startTime = new Date().toISOString();
    await updateConfig('cleanupSchedulerStartTime', startTime);

    // Schedule cleanup
    cleanupIntervalId = setInterval(async () => {
      try {
        await runCleanup();
        // Update last run time
        await updateConfig('cleanupLastRunTime', new Date().toISOString());
      } catch (error) {
        logError('cleanup', 'Error running scheduled cleanup', { error });
      }
    }, intervalMs);

    logInfo('cleanup', 'Cleanup scheduler started', {
      intervalMinutes: cleanupIntervalMinutes,
      startTime,
      nextRunAt: new Date(Date.now() + intervalMs).toISOString()
    });

    return true;
  } catch (error) {
    logError('cleanup', 'Error starting cleanup scheduler', { error });
    return false;
  }
}

/**
 * Stop the cleanup scheduler
 */
export async function stopCleanupScheduler(): Promise<void> {
  if (cleanupIntervalId) {
    clearInterval(cleanupIntervalId);
    cleanupIntervalId = null;

    // Clear scheduler start time when stopping
    await updateConfig('cleanupSchedulerStartTime', undefined);

    logInfo('cleanup', 'Cleanup scheduler stopped');
  }
}

/**
 * Check if the cleanup scheduler is running
 *
 * @returns boolean - Whether the scheduler is running
 */
export function isCleanupSchedulerRunning(): boolean {
  return cleanupIntervalId !== null;
}

/**
 * Run the cleanup process
 *
 * @returns Promise<void>
 */
export async function runCleanup(): Promise<void> {
  try {
    logInfo('cleanup', 'Running cleanup process');

    // Call the cleanup API - use management portal endpoint for consistency
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001';
    const response = await fetch(`${baseUrl}/api/management-portal-x7z9y2/cleanup`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Cleanup API returned status ${response.status}`);
    }

    const result = await response.json();

    logInfo('cleanup', 'Cleanup process completed', {
      tempEmailsDeleted: result.tempEmailsDeleted,
      guerrillaEmailsDeleted: result.guerrillaEmailsDeleted,
      duration: result.duration
    });
  } catch (error) {
    logError('cleanup', 'Error running cleanup process', { error });
    throw error;
  }
}
