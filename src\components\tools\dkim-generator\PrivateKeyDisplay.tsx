'use client';

// Private Key Display Component - Placeholder implementation
import React, { useState } from 'react';

interface PrivateKeyDisplayProps {
  privateKey?: string;
  visible?: boolean;
}

export default function PrivateKeyDisplay({ privateKey, visible = false }: PrivateKeyDisplayProps) {
  const [isVisible, setIsVisible] = useState(visible);

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-[#1b130e]">
          Private Key
        </label>
        <button
          type="button"
          onClick={() => setIsVisible(!isVisible)}
          className="text-sm text-[#66b077] hover:text-[#07880e] transition-colors"
          disabled={!privateKey}
        >
          {isVisible ? 'Hide' : 'Show'}
        </button>
      </div>
      
      <div className="bg-[#f3ece8] p-4 rounded-md border border-[#4a3728]">
        {privateKey ? (
          <div className="space-y-2">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-[#1b130e]">
                Private Key (Keep Secure!)
              </span>
              <button
                type="button"
                className="text-sm bg-[#66b077] text-white px-3 py-1 rounded hover:bg-[#07880e] transition-colors"
                onClick={() => navigator.clipboard?.writeText(privateKey)}
              >
                Copy
              </button>
            </div>
            <div className="bg-white p-3 rounded border">
              <code className="text-xs text-[#1b130e] break-all">
                {isVisible ? privateKey : '••••••••••••••••••••••••••••••••'}
              </code>
            </div>
            <div className="bg-[#f59e0b] bg-opacity-10 border border-[#f59e0b] rounded p-3">
              <p className="text-sm text-[#1b130e]">
                <strong>⚠️ Security Warning:</strong> Keep this private key secure. 
                Never share it publicly or store it in version control.
              </p>
            </div>
          </div>
        ) : (
          <p className="text-sm text-[#4a3728]">
            Private key will be displayed here after generation...
          </p>
        )}
      </div>
      
      <p className="text-xs text-[#4a3728]">
        Component implementation coming in Phase 2
      </p>
    </div>
  );
}
