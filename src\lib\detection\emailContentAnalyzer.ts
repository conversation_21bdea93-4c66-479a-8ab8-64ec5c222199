/**
 * Email Content Analysis System
 * Detects Facebook account creation abuse and other email-based attack patterns
 * Analyzes email content, senders, and patterns to identify automated abuse
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface EmailPattern {
  pattern: RegExp;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
}

interface EmailAnalysis {
  sessionId: string;
  totalEmails: number;
  facebookEmails: number;
  confirmationCodes: number;
  promotionalEmails: number;
  emailToGenRatio: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
  patterns: string[];
  timeSpan: number; // minutes
}

// Facebook abuse patterns
const FACEBOOK_PATTERNS: EmailPattern[] = [
  {
    pattern: /\d{5,6}\s+is\s+your\s+(Facebook\s+)?confirmation\s+code/i,
    type: 'facebook_confirmation',
    severity: 'high',
    description: 'Facebook confirmation code'
  },
  {
    pattern: /Three\s+ways\s+to\s+get\s+the\s+most\s+from\s+Facebook\s+mobile/i,
    type: 'facebook_promotional',
    severity: 'medium',
    description: 'Facebook promotional email'
  },
  {
    pattern: /Did\s+you\s+just\s+log\s+in\s+near\s+.+\s+on\s+a\s+new\s+device/i,
    type: 'facebook_security',
    severity: 'medium',
    description: 'Facebook security alert'
  },
  {
    pattern: /Action\s+needed\s+on\s+your\s+Facebook\s+account/i,
    type: 'facebook_action',
    severity: 'medium',
    description: 'Facebook account action required'
  }
];

// Other suspicious patterns
const SUSPICIOUS_PATTERNS: EmailPattern[] = [
  {
    pattern: /\d{4,8}\s+is\s+your\s+(verification|confirmation)\s+code/i,
    type: 'verification_code',
    severity: 'medium',
    description: 'Generic verification code'
  },
  {
    pattern: /Welcome\s+to\s+.+!\s+Please\s+verify/i,
    type: 'account_verification',
    severity: 'low',
    description: 'Account verification email'
  }
];

/**
 * Analyze email content patterns for a session
 */
export async function analyzeEmailContent(
  sessionId: string,
  timeWindowMinutes: number = 60
): Promise<EmailAnalysis> {
  try {
    const timeWindow = new Date(Date.now() - timeWindowMinutes * 60 * 1000);

    // Get all emails received by this session
    const { data: emailEvents, error } = await supabase
      .from('analytics_events')
      .select('additional_data, timestamp')
      .eq('session_id', sessionId)
      .eq('event_type', 'email_received')
      .gte('timestamp', timeWindow.toISOString())
      .order('timestamp', { ascending: true });

    if (error) {
      console.error('Error fetching email events:', error);
      return createSafeEmailAnalysis(sessionId);
    }

    // Get email generation count for ratio calculation
    const { data: genEvents, error: genError } = await supabase
      .from('analytics_events')
      .select('id')
      .eq('session_id', sessionId)
      .eq('event_type', 'email_address_generated')
      .gte('timestamp', timeWindow.toISOString());

    if (genError) {
      console.error('Error fetching generation events:', genError);
    }

    const emailsGenerated = genEvents?.length || 0;
    const emailsReceived = emailEvents?.length || 0;

    if (emailsReceived === 0) {
      return createSafeEmailAnalysis(sessionId);
    }

    // Analyze email content
    let facebookEmails = 0;
    let confirmationCodes = 0;
    let promotionalEmails = 0;
    const detectedPatterns = new Set<string>();

    emailEvents.forEach(event => {
      const emailSender = event.additional_data?.emailSender;
      const emailSubject = event.additional_data?.emailSubject || '';

      // Check for Facebook emails
      if (emailSender === 'Facebook' || emailSender === 'Meta') {
        facebookEmails++;
      }

      // Check patterns
      [...FACEBOOK_PATTERNS, ...SUSPICIOUS_PATTERNS].forEach(pattern => {
        if (pattern.pattern.test(emailSubject)) {
          detectedPatterns.add(pattern.type);
          
          if (pattern.type === 'facebook_confirmation') {
            confirmationCodes++;
          } else if (pattern.type === 'facebook_promotional') {
            promotionalEmails++;
          }
        }
      });
    });

    // Calculate metrics
    const emailToGenRatio = emailsGenerated > 0 ? emailsReceived / emailsGenerated : 0;
    const firstEmail = new Date(emailEvents[0].timestamp);
    const lastEmail = new Date(emailEvents[emailEvents.length - 1].timestamp);
    const timeSpan = (lastEmail.getTime() - firstEmail.getTime()) / (1000 * 60);

    // Calculate risk level and confidence
    const { riskLevel, confidence } = calculateEmailRiskLevel({
      totalEmails: emailsReceived,
      facebookEmails,
      confirmationCodes,
      emailToGenRatio,
      timeSpan,
      patterns: Array.from(detectedPatterns)
    });

    const analysis: EmailAnalysis = {
      sessionId,
      totalEmails: emailsReceived,
      facebookEmails,
      confirmationCodes,
      promotionalEmails,
      emailToGenRatio,
      riskLevel,
      confidence,
      patterns: Array.from(detectedPatterns),
      timeSpan
    };

    // Log the analysis
    await logEmailAnalysis(analysis);

    // Auto-block if high confidence Facebook bot
    if (riskLevel === 'critical' && confidence >= 0.85) {
      await autoBlockForEmailAbuse(sessionId, analysis);
    }

    return analysis;
  } catch (error) {
    console.error('Error in analyzeEmailContent:', error);
    return createSafeEmailAnalysis(sessionId);
  }
}

/**
 * Calculate risk level based on email patterns
 */
function calculateEmailRiskLevel(data: {
  totalEmails: number;
  facebookEmails: number;
  confirmationCodes: number;
  emailToGenRatio: number;
  timeSpan: number;
  patterns: string[];
}): { riskLevel: 'low' | 'medium' | 'high' | 'critical'; confidence: number } {
  let riskScore = 0;
  let confidence = 0;

  // Factor 1: Facebook email percentage
  const facebookPercentage = data.totalEmails > 0 ? data.facebookEmails / data.totalEmails : 0;
  if (facebookPercentage >= 0.9) {
    riskScore += 0.4;
    confidence += 0.3;
  } else if (facebookPercentage >= 0.7) {
    riskScore += 0.3;
    confidence += 0.2;
  }

  // Factor 2: Email-to-generation ratio
  if (data.emailToGenRatio >= 1.5) {
    riskScore += 0.3;
    confidence += 0.25;
  } else if (data.emailToGenRatio >= 1.0) {
    riskScore += 0.2;
    confidence += 0.15;
  }

  // Factor 3: Confirmation codes
  if (data.confirmationCodes >= 10) {
    riskScore += 0.3;
    confidence += 0.2;
  } else if (data.confirmationCodes >= 5) {
    riskScore += 0.2;
    confidence += 0.15;
  }

  // Factor 4: Pattern diversity (bots often have consistent patterns)
  if (data.patterns.includes('facebook_confirmation') && data.patterns.includes('facebook_promotional')) {
    riskScore += 0.2;
    confidence += 0.1;
  }

  // Factor 5: Volume and timing
  if (data.totalEmails >= 20 && data.timeSpan < 60) {
    riskScore += 0.2;
    confidence += 0.1;
  }

  // Normalize confidence
  confidence = Math.min(confidence, 1.0);

  // Determine risk level
  let riskLevel: 'low' | 'medium' | 'high' | 'critical';
  if (riskScore < 0.3) {
    riskLevel = 'low';
  } else if (riskScore < 0.6) {
    riskLevel = 'medium';
  } else if (riskScore < 0.9) {
    riskLevel = 'high';
  } else {
    riskLevel = 'critical';
  }

  return { riskLevel, confidence };
}

/**
 * Create safe analysis for error cases
 */
function createSafeEmailAnalysis(sessionId: string): EmailAnalysis {
  return {
    sessionId,
    totalEmails: 0,
    facebookEmails: 0,
    confirmationCodes: 0,
    promotionalEmails: 0,
    emailToGenRatio: 0,
    riskLevel: 'low',
    confidence: 0,
    patterns: [],
    timeSpan: 0
  };
}

/**
 * Log email analysis to database
 */
async function logEmailAnalysis(analysis: EmailAnalysis): Promise<void> {
  try {
    await supabase
      .from('abuse_patterns')
      .insert({
        session_id: analysis.sessionId,
        pattern_type: 'facebook_abuse',
        confidence_score: analysis.confidence,
        evidence: {
          total_emails: analysis.totalEmails,
          facebook_emails: analysis.facebookEmails,
          confirmation_codes: analysis.confirmationCodes,
          email_to_gen_ratio: analysis.emailToGenRatio,
          patterns: analysis.patterns,
          time_span_minutes: analysis.timeSpan,
          risk_level: analysis.riskLevel
        },
        action_taken: analysis.confidence >= 0.85 ? 'auto_blocked' : 'logged'
      });

    // Log security event
    await supabase
      .from('security_events')
      .insert({
        event_type: 'facebook_abuse_detected',
        session_id: analysis.sessionId,
        severity: analysis.riskLevel === 'critical' ? 'high' : 'medium',
        description: `Facebook abuse detected: ${analysis.facebookEmails}/${analysis.totalEmails} Facebook emails, ratio: ${analysis.emailToGenRatio.toFixed(2)}`,
        metadata: {
          analysis,
          detection_time: new Date().toISOString()
        },
        action_taken: analysis.confidence >= 0.85 ? 'session_blocked' : 'monitored'
      });
  } catch (error) {
    console.error('Error logging email analysis:', error);
  }
}

/**
 * Auto-block session for email abuse
 */
async function autoBlockForEmailAbuse(sessionId: string, analysis: EmailAnalysis): Promise<void> {
  try {
    const blockDuration = 24 * 60 * 60 * 1000; // 24 hours
    const expiresAt = new Date(Date.now() + blockDuration);

    await supabase
      .from('blocked_sessions')
      .insert({
        session_id: sessionId,
        reason: `Facebook account creation abuse: ${analysis.facebookEmails} Facebook emails, ${analysis.confirmationCodes} confirmation codes`,
        blocked_by: 'Email Content Analyzer',
        expires_at: expiresAt.toISOString(),
        is_active: true
      });

    console.log(`🚨 AUTO-BLOCKED: Session ${sessionId} for Facebook abuse (${analysis.facebookEmails} emails)`);
  } catch (error) {
    console.error('Error auto-blocking for email abuse:', error);
  }
}

/**
 * Monitor all sessions for email abuse patterns
 */
export async function monitorEmailAbuse(timeWindowMinutes: number = 60): Promise<EmailAnalysis[]> {
  try {
    const timeWindow = new Date(Date.now() - timeWindowMinutes * 60 * 1000);

    // Get sessions with email activity
    const { data: sessions, error } = await supabase
      .from('analytics_events')
      .select('session_id')
      .eq('event_type', 'email_received')
      .gte('timestamp', timeWindow.toISOString());

    if (error || !sessions) {
      console.error('Error fetching sessions:', error);
      return [];
    }

    const uniqueSessions = [...new Set(sessions.map(s => s.session_id))];
    const analyses: EmailAnalysis[] = [];

    for (const sessionId of uniqueSessions) {
      const analysis = await analyzeEmailContent(sessionId, timeWindowMinutes);
      if (analysis.riskLevel !== 'low') {
        analyses.push(analysis);
      }
    }

    return analyses;
  } catch (error) {
    console.error('Error in monitorEmailAbuse:', error);
    return [];
  }
}

/**
 * Middleware to check for email abuse patterns
 */
export async function checkEmailAbuseMiddleware(sessionId: string): Promise<{
  allowed: boolean;
  analysis?: EmailAnalysis;
  reason?: string;
}> {
  try {
    if (!sessionId || sessionId === 'anonymous') {
      return { allowed: true };
    }

    const analysis = await analyzeEmailContent(sessionId);

    if (analysis.riskLevel === 'critical' && analysis.confidence >= 0.85) {
      return {
        allowed: false,
        analysis,
        reason: `Facebook abuse detected: ${analysis.facebookEmails} Facebook emails with ratio ${analysis.emailToGenRatio.toFixed(2)}`
      };
    }

    return { allowed: true, analysis };
  } catch (error) {
    console.error('Error in email abuse middleware:', error);
    return { allowed: true };
  }
}

export default {
  analyzeEmailContent,
  monitorEmailAbuse,
  checkEmailAbuseMiddleware,
  FACEBOOK_PATTERNS,
  SUSPICIOUS_PATTERNS
};
