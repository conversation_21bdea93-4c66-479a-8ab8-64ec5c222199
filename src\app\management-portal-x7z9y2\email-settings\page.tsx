'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, AdminCardHeader, AdminCardContent, AdminCardTitle, AdminCardDescription } from '@/components/admin/ui/AdminCard';
import { AdminButton } from '@/components/admin/ui/AdminButton';
import { AdminStatusIndicator } from '@/components/admin/ui/AdminStatusIndicator';
import { AdminTooltip } from '@/components/admin/ui/AdminTooltip';
import { AdminSkeleton } from '@/components/admin/ui/AdminSkeleton';
import { AdminSwitch } from '@/components/ui/Switch';

/**
 * Email Settings Page
 *
 * Allows admins to view and test SMTP configuration
 */
export default function EmailSettingsPage() {
  const [smtpConfig, setSmtpConfig] = useState<{
    host: string;
    port: string | number;
    secure: boolean;
    user: string;
    from: string;
  } | null>(null);

  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch current SMTP configuration
  useEffect(() => {
    const fetchSmtpConfig = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/management-portal-x7z9y2/email/test-smtp');
        const data = await response.json();

        if (data.success && data.config) {
          setSmtpConfig(data.config);
        } else {
          setError(data.message || 'Failed to fetch SMTP configuration');
        }
      } catch (err) {
        setError('An error occurred while fetching SMTP configuration');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchSmtpConfig();
  }, []);

  // Test SMTP connection
  const handleTestConnection = async () => {
    try {
      setLoading(true);
      setTestResult(null);

      const response = await fetch('/api/management-portal-x7z9y2/email/test-smtp');
      const data = await response.json();

      setTestResult(data);
    } catch (err) {
      console.error('Error testing SMTP connection:', err);
      setTestResult({
        success: false,
        message: 'An error occurred while testing the connection'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 sm:px-6 animate-fadeIn">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Email Settings</h1>

        {smtpConfig && (
          <div className="flex items-center">
            <AdminStatusIndicator
              status={smtpConfig ? 'online' : 'offline'}
              label={smtpConfig ? 'SMTP Configured' : 'SMTP Not Configured'}
            />
          </div>
        )}
      </div>

      <AdminCard className="mb-8">
        <AdminCardHeader>
          <div className="flex justify-between items-start">
            <div>
              <AdminCardTitle>SMTP Configuration</AdminCardTitle>
              <AdminCardDescription>
                Configure your SMTP server for sending email notifications.
              </AdminCardDescription>
            </div>
            {smtpConfig && (
              <div className="flex items-center space-x-2">
                <AdminButton
                  variant="outline"
                  size="sm"
                  onClick={handleTestConnection}
                  isLoading={loading}
                  disabled={!smtpConfig}
                  leftIcon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  }
                >
                  Test Connection
                </AdminButton>
              </div>
            )}
          </div>
        </AdminCardHeader>

        <AdminCardContent>
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg shadow-sm mb-6 animate-fadeIn">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {error}
              </div>
            </div>
          )}

          {loading && !smtpConfig ? (
            <div className="py-8">
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="relative w-16 h-16">
                  <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
                  <div className="absolute inset-0 rounded-full border-4 border-t-indigo-600 animate-spin"></div>
                </div>
                <p className="text-gray-500 font-medium">Loading SMTP configuration...</p>
              </div>
            </div>
          ) : smtpConfig ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 transition-all duration-200 hover:shadow-md">
                <div className="flex items-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <h3 className="font-medium text-gray-900">Server Configuration</h3>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Host:</span>
                    <span className="text-sm font-medium text-gray-900">{smtpConfig.host}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Port:</span>
                    <span className="text-sm font-medium text-gray-900">{smtpConfig.port}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Security:</span>
                    <span className="text-sm font-medium text-gray-900">
                      {smtpConfig.secure ? (
                        <span className="flex items-center text-green-600">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                          SSL
                        </span>
                      ) : (
                        <span className="flex items-center text-blue-600">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          TLS
                        </span>
                      )}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 transition-all duration-200 hover:shadow-md">
                <div className="flex items-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  <h3 className="font-medium text-gray-900">Email Configuration</h3>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">SMTP User:</span>
                    <span className="text-sm font-medium text-gray-900">{smtpConfig.user}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">From Email:</span>
                    <span className="text-sm font-medium text-gray-900">{smtpConfig.from}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Status:</span>
                    <AdminStatusIndicator
                      status={testResult?.success ? 'success' : testResult ? 'error' : 'warning'}
                      label={testResult?.success ? 'Connected' : testResult ? 'Connection Failed' : 'Not Tested'}
                      size="sm"
                    />
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-6 py-4 rounded-lg shadow-sm mb-6">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-600 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <div>
                  <h3 className="font-medium text-yellow-800 mb-1">SMTP Not Configured</h3>
                  <p className="text-yellow-700">
                    Email notifications require SMTP configuration. Please add the following variables to your .env.local file:
                  </p>
                  <ul className="mt-2 space-y-1 text-sm text-yellow-700 list-disc list-inside ml-2">
                    <li>SMTP_HOST</li>
                    <li>SMTP_PORT</li>
                    <li>SMTP_SECURE</li>
                    <li>SMTP_USER</li>
                    <li>SMTP_PASSWORD</li>
                    <li>SMTP_FROM</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {testResult && (
            <div className={`p-4 rounded-lg shadow-sm border ${testResult.success ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800'} animate-fadeIn`}>
              <div className="flex items-start">
                {testResult.success ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
                <div>
                  <h3 className="font-medium mb-1">{testResult.success ? 'Connection Successful' : 'Connection Failed'}</h3>
                  <p>{testResult.message}</p>
                </div>
              </div>
            </div>
          )}

          <div className="mt-6 text-sm text-gray-500 bg-gray-50 p-4 rounded-lg border border-gray-100">
            <p className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              SMTP configuration is managed through environment variables. To update these settings, modify your .env.local file.
            </p>
          </div>
        </AdminCardContent>
      </AdminCard>

      <AdminCard>
        <AdminCardHeader>
          <AdminCardTitle>Email Notification Settings</AdminCardTitle>
          <AdminCardDescription>
            Configure when email notifications are sent.
          </AdminCardDescription>
        </AdminCardHeader>

        <AdminCardContent>
          <div className="space-y-6">
            <AdminSwitch
              label="Send notification when an admin replies to a contact message"
              description="When an admin replies to a contact message, an email notification will be sent to the original sender. This setting is required and cannot be disabled."
              checked={true}
              disabled={true}
              status="enabled"
            />

            <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
              <h3 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
                Advanced Configuration
              </h3>
              <p className="text-sm text-gray-500">
                Additional notification settings can be configured by modifying the email service implementation in <code className="text-xs bg-gray-100 px-1 py-0.5 rounded">src/lib/services/emailService.ts</code>.
              </p>
            </div>
          </div>
        </AdminCardContent>
      </AdminCard>
    </div>
  );
}
