'use client';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className="min-h-screen bg-[#fbfaf8] flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-6">
        <h2 className="text-2xl font-bold text-[#1b130e] mb-4">
          Something went wrong!
        </h2>
        <p className="text-[#4a3728] mb-6">
          An error occurred while loading the DMARC Generator.
        </p>
        <button
          onClick={reset}
          className="bg-[#66b077] text-white px-6 py-2 rounded-full hover:bg-[#07880e] transition-colors"
        >
          Try again
        </button>
      </div>
    </div>
  );
}
