'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  getCookiePreferences,
  saveCookiePreferences,
  acceptAllCookies,
  acceptNecessaryCookies,
  type CookiePreferences
} from '@/lib/cookieConsent';

export default function CookieConsent() {
  const [showBanner, setShowBanner] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>(getCookiePreferences());

  // Load preferences from localStorage on component mount
  useEffect(() => {
    const currentPreferences = getCookiePreferences();
    setPreferences(currentPreferences);

    // Don't show banner if preferences are already set
    if (currentPreferences.preferences_set) {
      setShowBanner(false);
      return;
    }

    // Show banner if no preferences are set
    setShowBanner(true);
  }, []);

  // Save preferences to localStorage and apply them
  const savePreferences = (newPreferences: Partial<CookiePreferences>) => {
    const updatedPreferences = saveCookiePreferences(newPreferences);
    setPreferences(updatedPreferences);
    setShowBanner(false);
    setShowPreferences(false);
  };

  // Accept all cookies
  const acceptAll = () => {
    const updatedPreferences = acceptAllCookies();
    setPreferences(updatedPreferences);
    setShowBanner(false);
    setShowPreferences(false);
  };

  // Accept only necessary cookies
  const acceptNecessary = () => {
    const updatedPreferences = acceptNecessaryCookies();
    setPreferences(updatedPreferences);
    setShowBanner(false);
    setShowPreferences(false);
  };

  // Toggle individual preference
  const togglePreference = (key: keyof CookiePreferences) => {
    if (key === 'necessary') return; // Cannot toggle necessary cookies

    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  // Open preferences panel
  const openPreferences = () => {
    setShowPreferences(true);
  };

  // Save current preferences
  const saveCurrentPreferences = () => {
    savePreferences(preferences);
  };

  if (!showBanner) {
    return (
      <button
        onClick={() => setShowBanner(true)}
        className="fixed bottom-4 left-4 z-[100] bg-white px-2 py-1 rounded shadow-md hover:shadow-lg transition-all duration-300 hidden sm:flex items-center text-xs border border-gray-200"
        aria-label="Cookie Settings"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-[#ce601c] mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <span className="text-gray-700 font-medium">Cookie Settings</span>
      </button>
    );
  }

  return (
    <div className={`fixed z-[100] ${showPreferences ? 'animate-fadeIn hidden sm:flex' : 'animate-slideInBottom'} ${showPreferences ? 'bottom-[52px]' : 'bottom-4'} left-4 ${showPreferences ? 'hidden sm:flex' : 'flex'} items-start justify-start`}>
      <div className={`bg-white rounded-lg shadow-xl w-full max-w-[calc(100vw-32px)] sm:max-w-[360px] max-h-[80vh] overflow-y-auto border border-gray-200`}>
        {showPreferences ? (
          // Detailed preferences panel
          <div className="p-4">
            <div className="flex justify-between items-center mb-3">
              <h2 className="text-lg font-bold text-gray-900">Cookie Preferences</h2>
              <button
                onClick={() => setShowPreferences(false)}
                className="text-gray-500 hover:text-gray-700"
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            <div className="space-y-2 mb-4 mt-2">
              {/* Necessary cookies - always enabled */}
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div>
                  <h3 className="font-medium text-gray-900 text-sm">Necessary Cookies</h3>
                  <p className="text-xs text-gray-600">Required for the website to function properly</p>
                </div>
                <div className="flex items-center">
                  <span className="text-xs font-medium text-gray-500 mr-2">Always active</span>
                  <div className="w-8 h-4 bg-[#ce601c] rounded-full"></div>
                </div>
              </div>

              {/* Functional cookies */}
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div>
                  <h3 className="font-medium text-gray-900 text-sm">Functional Cookies</h3>
                  <p className="text-xs text-gray-600">Remember your preferences and settings</p>
                </div>
                <label className="flex items-center cursor-pointer">
                  <div className="relative">
                    <input
                      type="checkbox"
                      className="sr-only"
                      checked={preferences.functional}
                      onChange={() => togglePreference('functional')}
                    />
                    <div className={`w-8 h-4 ${preferences.functional ? 'bg-[#ce601c]' : 'bg-gray-300'} rounded-full transition-colors`}></div>
                    <div className={`absolute left-0.5 top-0.5 bg-white w-3 h-3 rounded-full transition-transform ${preferences.functional ? 'transform translate-x-4' : ''}`}></div>
                  </div>
                </label>
              </div>

              {/* Analytics cookies */}
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div>
                  <h3 className="font-medium text-gray-900 text-sm">Analytics Cookies</h3>
                  <p className="text-xs text-gray-600">Help us improve our website by collecting anonymous usage data</p>
                </div>
                <label className="flex items-center cursor-pointer">
                  <div className="relative">
                    <input
                      type="checkbox"
                      className="sr-only"
                      checked={preferences.analytics}
                      onChange={() => togglePreference('analytics')}
                    />
                    <div className={`w-8 h-4 ${preferences.analytics ? 'bg-[#ce601c]' : 'bg-gray-300'} rounded-full transition-colors`}></div>
                    <div className={`absolute left-0.5 top-0.5 bg-white w-3 h-3 rounded-full transition-transform ${preferences.analytics ? 'transform translate-x-4' : ''}`}></div>
                  </div>
                </label>
              </div>


            </div>

            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowPreferences(false)}
                className="px-2 py-1 text-xs text-gray-700 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={saveCurrentPreferences}
                className="px-2 py-1 text-xs bg-[#ce601c] text-white rounded hover:bg-[#b85518] transition-colors"
              >
                Save Preferences
              </button>
            </div>
          </div>
        ) : (
          // Simple banner
          <div className="p-4">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-lg font-bold text-gray-900">We Value Your Privacy</h2>
              <button
                onClick={() => setShowBanner(false)}
                className="text-gray-500 hover:text-gray-700"
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            <p className="text-gray-600 text-sm mb-3">
              We use cookies to improve your experience and analyze site traffic. By clicking "Accept All", you consent to our cookie use.
            </p>

            <div className="flex flex-col space-y-3">
              <div className="flex flex-wrap items-center sm:space-x-3">
                <button
                  onClick={openPreferences}
                  className="hidden sm:inline-block text-sm text-[#ce601c] hover:underline"
                >
                  Customize Preferences
                </button>

                <Link
                  href="/cookies"
                  className="text-sm text-[#ce601c] hover:underline"
                  onClick={() => setShowBanner(false)}
                >
                  Cookie Policy
                </Link>
              </div>

              <div className="flex w-full space-x-2">
                <button
                  onClick={acceptNecessary}
                  className="flex-1 px-2 py-1 text-xs text-gray-700 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                >
                  Necessary Only
                </button>
                <button
                  onClick={acceptAll}
                  className="flex-1 px-2 py-1 text-xs bg-[#ce601c] text-white rounded hover:bg-[#b85518] transition-colors"
                >
                  Accept All
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
