'use client';

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { XMarkIcon } from '@heroicons/react/24/solid';

// shadcn/ui Alert variants adapted for VanishPost theme
const alertVariants = cva(
  "relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",
  {
    variants: {
      variant: {
        default: "bg-white border-gray-200 text-gray-900",
        destructive: "bg-red-50 border-red-200 text-red-800 [&>svg]:text-red-600",
        success: "bg-green-50 border-green-200 text-green-800 [&>svg]:text-green-600",
        warning: "bg-yellow-50 border-yellow-200 text-yellow-800 [&>svg]:text-yellow-600",
        info: "bg-blue-50 border-blue-200 text-blue-800 [&>svg]:text-blue-600",
        // Legacy variants for backward compatibility
        error: "bg-red-50 border-red-200 text-red-800 [&>svg]:text-red-600",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

// Enhanced interface for backward compatibility with existing VanishPost usage
export interface AlertProps
  extends React.ComponentProps<"div">,
    VariantProps<typeof alertVariants> {
  title?: string;
  onDismiss?: () => void;
}

/**
 * shadcn/ui Alert Component adapted for VanishPost
 *
 * Maintains backward compatibility while providing enhanced accessibility and styling
 */
function Alert({
  className,
  variant,
  title,
  children,
  onDismiss,
  ...props
}: AlertProps) {
  return (
    <div
      data-slot="alert"
      role="alert"
      className={cn(alertVariants({ variant }), className)}
      {...props}
    >
      {/* Render children directly if no title, otherwise structure with title */}
      {title ? (
        <>
          <AlertTitle>{title}</AlertTitle>
          <AlertDescription>{children}</AlertDescription>
        </>
      ) : (
        children
      )}

      {/* Dismiss button for backward compatibility */}
      {onDismiss && (
        <button
          type="button"
          onClick={onDismiss}
          className="absolute top-2 right-2 p-1 rounded-md hover:bg-black/5 transition-colors"
          aria-label="Dismiss alert"
        >
          <XMarkIcon className="h-4 w-4" />
        </button>
      )}
    </div>
  );
}

Alert.displayName = "Alert";

/**
 * AlertTitle component from shadcn/ui
 */
function AlertTitle({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="alert-title"
      className={cn(
        "col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",
        className
      )}
      {...props}
    />
  );
}

AlertTitle.displayName = "AlertTitle";

/**
 * AlertDescription component from shadcn/ui
 */
function AlertDescription({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="alert-description"
      className={cn(
        "col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",
        className
      )}
      {...props}
    />
  );
}

AlertDescription.displayName = "AlertDescription";

export { Alert, AlertTitle, AlertDescription };
