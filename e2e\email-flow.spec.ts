import { test, expect } from '@playwright/test';

test.describe('Email Flow', () => {
  test('should generate a new email address and copy it to clipboard', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForSelector('button[aria-label="Generate new email address"]');
    
    // Click the generate button
    await page.click('button[aria-label="Generate new email address"]');
    
    // Wait for the email address to be generated
    await page.waitForFunction(() => {
      const input = document.querySelector('input[aria-label="Your temporary email address"]') as HTMLInputElement;
      return input && input.value && input.value.includes('@fademail.com');
    });
    
    // Get the generated email address
    const emailAddress = await page.inputValue('input[aria-label="Your temporary email address"]');
    
    // Verify that the email address is valid
    expect(emailAddress).toContain('@fademail.com');
    
    // Click the copy button
    await page.click('button[aria-label="Copy email address to clipboard"]');
    
    // Verify that the copy was successful (this is difficult to test directly in <PERSON><PERSON>)
    // Instead, we'll check that the button shows a success state or tooltip
    await page.waitForTimeout(500); // Wait for any visual feedback
    
    // Check that the email address is still displayed
    const emailAddressAfterCopy = await page.inputValue('input[aria-label="Your temporary email address"]');
    expect(emailAddressAfterCopy).toBe(emailAddress);
  });
  
  test('should show guide emails when a new address is generated', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForSelector('button[aria-label="Generate new email address"]');
    
    // Click the generate button
    await page.click('button[aria-label="Generate new email address"]');
    
    // Wait for the email address to be generated
    await page.waitForFunction(() => {
      const input = document.querySelector('input[aria-label="Your temporary email address"]') as HTMLInputElement;
      return input && input.value && input.value.includes('@fademail.com');
    });
    
    // Wait for guide emails to appear
    await page.waitForSelector('.email-list-item', { timeout: 10000 });
    
    // Check that guide emails are displayed
    const emailItems = await page.$$('.email-list-item');
    expect(emailItems.length).toBeGreaterThan(0);
    
    // Check that the first email is from the Fademail Team
    const firstEmailSender = await page.textContent('.email-list-item:first-child .email-sender');
    expect(firstEmailSender).toContain('Fademail Team');
  });
  
  test('should refresh the inbox and show new emails', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForSelector('button[aria-label="Generate new email address"]');
    
    // Click the generate button
    await page.click('button[aria-label="Generate new email address"]');
    
    // Wait for the email address to be generated
    await page.waitForFunction(() => {
      const input = document.querySelector('input[aria-label="Your temporary email address"]') as HTMLInputElement;
      return input && input.value && input.value.includes('@fademail.com');
    });
    
    // Wait for guide emails to appear
    await page.waitForSelector('.email-list-item', { timeout: 10000 });
    
    // Count the initial number of emails
    const initialEmailCount = await page.$$eval('.email-list-item', items => items.length);
    
    // Click the refresh button
    await page.click('button[aria-label="Refresh inbox"]');
    
    // Wait for the refresh to complete
    await page.waitForTimeout(1000);
    
    // Count the number of emails after refresh
    const afterRefreshEmailCount = await page.$$eval('.email-list-item', items => items.length);
    
    // The number of emails should be the same (since we're not actually receiving new emails in the test)
    expect(afterRefreshEmailCount).toBe(initialEmailCount);
  });
  
  test('should select an email and display its content', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForSelector('button[aria-label="Generate new email address"]');
    
    // Click the generate button
    await page.click('button[aria-label="Generate new email address"]');
    
    // Wait for the email address to be generated
    await page.waitForFunction(() => {
      const input = document.querySelector('input[aria-label="Your temporary email address"]') as HTMLInputElement;
      return input && input.value && input.value.includes('@fademail.com');
    });
    
    // Wait for guide emails to appear
    await page.waitForSelector('.email-list-item', { timeout: 10000 });
    
    // Click on the first email
    await page.click('.email-list-item:first-child');
    
    // Wait for the email content to load
    await page.waitForSelector('.email-viewer-content');
    
    // Check that the email content is displayed
    const emailContent = await page.isVisible('.email-viewer-content');
    expect(emailContent).toBe(true);
    
    // Check that the email subject is displayed
    const emailSubject = await page.isVisible('.email-viewer-subject');
    expect(emailSubject).toBe(true);
  });
  
  test('should delete an email', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForSelector('button[aria-label="Generate new email address"]');
    
    // Click the generate button
    await page.click('button[aria-label="Generate new email address"]');
    
    // Wait for the email address to be generated
    await page.waitForFunction(() => {
      const input = document.querySelector('input[aria-label="Your temporary email address"]') as HTMLInputElement;
      return input && input.value && input.value.includes('@fademail.com');
    });
    
    // Wait for guide emails to appear
    await page.waitForSelector('.email-list-item', { timeout: 10000 });
    
    // Count the initial number of emails
    const initialEmailCount = await page.$$eval('.email-list-item', items => items.length);
    
    // Click the delete button on the first email
    await page.click('.email-list-item:first-child button[aria-label*="Delete email"]');
    
    // Wait for the deletion to complete
    await page.waitForTimeout(1000);
    
    // Count the number of emails after deletion
    const afterDeletionEmailCount = await page.$$eval('.email-list-item', items => items.length);
    
    // The number of emails should be one less
    expect(afterDeletionEmailCount).toBe(initialEmailCount - 1);
  });
});
