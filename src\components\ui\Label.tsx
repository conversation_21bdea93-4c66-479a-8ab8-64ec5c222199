'use client';

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Define label variants using class-variance-authority
const labelVariants = cva(
  // Base styles applied to all labels
  "text-sm font-medium",
  {
    variants: {
      variant: {
        default: "text-neutral-700",
        error: "text-error-500",
        success: "text-success-500",
        muted: "text-neutral-500",
      },
      size: {
        sm: "text-xs",
        md: "text-sm",
        lg: "text-base",
      },
      required: {
        true: "after:content-['*'] after:ml-0.5 after:text-red-500",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

export interface LabelProps
  extends React.LabelHTMLAttributes<HTMLLabelElement>,
    VariantProps<typeof labelVariants> {
  htmlFor?: string;
  required?: boolean;
}

/**
 * Label Component
 *
 * A styled label component for form elements
 */
const Label = React.forwardRef<HTMLLabelElement, LabelProps>(
  ({ className, variant, size, required, children, ...props }, ref) => {
    return (
      <label
        className={cn(labelVariants({ variant, size, required, className }))}
        ref={ref}
        {...props}
      >
        {children}
      </label>
    );
  }
);

Label.displayName = "Label";

export { Label, labelVariants };
