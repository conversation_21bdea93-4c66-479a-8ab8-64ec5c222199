#!/usr/bin/env node

/**
 * Security initialization script for VanishPost
 * Migrates plain-text credentials to secure storage and validates security configuration
 */

require('dotenv').config({ path: '.env.local' });

async function initializeSecurity() {
  console.log('🔒 Initializing VanishPost Security System...\n');

  // Check for required environment variables
  const requiredEnvVars = [
    'JWT_SECRET',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];

  console.log('1. Checking required environment variables...');
  let missingVars = [];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missingVars.push(envVar);
    } else {
      console.log(`   ✅ ${envVar}: Set`);
    }
  }

  if (missingVars.length > 0) {
    console.error(`\n❌ Missing required environment variables:`);
    missingVars.forEach(varName => console.error(`   - ${varName}`));
    console.error('\nPlease set these variables in your .env.local file before continuing.\n');
    process.exit(1);
  }

  // Check JWT_SECRET strength
  console.log('\n2. Validating JWT_SECRET strength...');
  const jwtSecret = process.env.JWT_SECRET;

  if (jwtSecret.length < 32) {
    console.warn(`   ⚠️  JWT_SECRET is only ${jwtSecret.length} characters. Recommended: 32+ characters`);
  } else {
    console.log(`   ✅ JWT_SECRET length: ${jwtSecret.length} characters`);
  }

  if (jwtSecret === 'fallback-secret-change-in-production') {
    console.error('   ❌ JWT_SECRET is still using the fallback value!');
    console.error('   Please generate a secure JWT secret and update your .env.local file.\n');
    process.exit(1);
  }

  // Check admin credentials
  console.log('\n3. Checking admin credentials...');
  const adminUsername = process.env.ADMIN_USERNAME;
  const adminPassword = process.env.ADMIN_PASSWORD;
  const adminPasswordHash = process.env.ADMIN_PASSWORD_HASH;

  if (!adminUsername) {
    console.error('   ❌ ADMIN_USERNAME not set');
  } else {
    console.log(`   ✅ ADMIN_USERNAME: ${adminUsername}`);
  }

  if (!adminPasswordHash && !adminPassword) {
    console.error('   ❌ Neither ADMIN_PASSWORD_HASH nor ADMIN_PASSWORD is set');
    console.error('   Please set ADMIN_PASSWORD_HASH (recommended) or ADMIN_PASSWORD');
  } else if (adminPasswordHash) {
    console.log('   ✅ ADMIN_PASSWORD_HASH: Set (secure)');
    if (adminPassword) {
      console.warn('   ⚠️  Both ADMIN_PASSWORD_HASH and ADMIN_PASSWORD are set. ADMIN_PASSWORD_HASH will be used.');
    }
  } else if (adminPassword) {
    console.warn('   ⚠️  Using ADMIN_PASSWORD (plain text). Consider migrating to ADMIN_PASSWORD_HASH');
    if (adminPassword.length < 8) {
      console.error('   ❌ ADMIN_PASSWORD is too short. Minimum 8 characters required.');
    }
  }

  // Check database credentials
  console.log('\n4. Checking database credentials...');

  const dbConfigs = [
    {
      name: 'Guerrilla DB',
      prefix: 'GUERRILLA_DB_',
      required: ['HOST', 'USER', 'PASSWORD', 'NAME']
    },
    {
      name: 'Local DB',
      prefix: 'DB_',
      required: ['HOST', 'USER', 'PASSWORD', 'NAME']
    }
  ];

  for (const config of dbConfigs) {
    console.log(`\n   ${config.name}:`);
    let hasAllRequired = true;

    for (const field of config.required) {
      const envVar = `${config.prefix}${field}`;
      const value = process.env[envVar];

      if (!value) {
        console.log(`     ❌ ${envVar}: Not set`);
        hasAllRequired = false;
      } else if (field === 'PASSWORD') {
        console.log(`     ✅ ${envVar}: Set (${value.length} characters)`);
        if (value.length < 8) {
          console.warn(`     ⚠️  ${envVar} is short. Consider using a stronger password.`);
        }
      } else {
        console.log(`     ✅ ${envVar}: ${value}`);
      }
    }

    if (hasAllRequired) {
      console.log(`     ✅ ${config.name} configuration complete`);
    } else {
      console.warn(`     ⚠️  ${config.name} configuration incomplete`);
    }
  }

  // Check optional configuration variables
  console.log('\n5. Checking optional configuration...');

  const emailTesterDomain = process.env.EMAIL_TESTER_DOMAIN;
  if (emailTesterDomain) {
    console.log(`   ✅ EMAIL_TESTER_DOMAIN: ${emailTesterDomain}`);
  } else {
    console.log('   ℹ️  EMAIL_TESTER_DOMAIN: Not set (using default: fademail.site)');
  }

  // Security recommendations
  console.log('\n6. Security Recommendations:');
  console.log('   📋 Security Checklist:');
  console.log('   □ Use strong, unique passwords for all accounts');
  console.log('   □ Enable 2FA where possible');
  console.log('   □ Regularly rotate JWT secrets and passwords');
  console.log('   □ Monitor access logs for suspicious activity');
  console.log('   □ Keep dependencies updated');
  console.log('   □ Use HTTPS in production');
  console.log('   □ Implement proper backup procedures');
  console.log('   □ Review and audit permissions regularly');

  console.log('\n   🔐 Next Steps:');
  console.log('   1. Run: npm run build (to test configuration)');
  console.log('   2. Run: npm run dev (to start development server)');
  console.log('   3. Test admin login functionality');
  console.log('   4. Review security logs regularly');

  // Generate secure JWT secret if needed
  if (jwtSecret.length < 32) {
    console.log('\n7. Generating secure JWT secret...');
    const crypto = require('crypto');
    const newSecret = crypto.randomBytes(64).toString('hex');
    console.log('   Generated secure JWT secret:');
    console.log(`   JWT_SECRET=${newSecret}`);
    console.log('\n   Please update your .env.local file with this new secret.');
  }

  console.log('\n🎉 Security initialization complete!');
  console.log('\nIMPORTANT: Review all warnings above and address any security concerns before deploying to production.\n');
}

// Run the initialization
initializeSecurity().catch(error => {
  console.error('❌ Security initialization failed:', error);
  process.exit(1);
});
