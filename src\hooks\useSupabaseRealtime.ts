/**
 * Custom hook for Supabase real-time subscriptions
 */

import React, { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase';
import { Email, mapApiEmailToUiEmail } from '@/lib/emailProcessing';

interface UseSupabaseRealtimeOptions {
  /** Email address to subscribe to */
  emailAddress: string | null;

  /** Whether to enable the subscription */
  enabled: boolean;

  /** Callback when new emails are received */
  onNewEmails: (emails: Email[]) => void;

  /** Function to load read status from localStorage */
  loadReadStatus: () => Record<string, boolean>;

  /** Function to filter out deleted emails from a list */
  filterOutDeletedEmails: (emails: Email[]) => Email[];

  /** Function to filter out emails from a previous address during transition */
  filterDuringAddressTransition: (emails: Email[], currentAddress: string | null, previousAddress: string | null) => Email[];

  /** Flag indicating we're in an address transition period */
  inAddressTransition: boolean;

  /** Reference to the previous address */
  previousAddressRef: React.RefObject<string | null>;
}

/**
 * Custom hook for Supabase real-time subscriptions
 *
 * This hook subscribes to real-time updates for a specific email address
 * and calls the onNewEmails callback when new emails are received.
 */
export const useSupabaseRealtime = ({
  emailAddress,
  enabled,
  onNewEmails,
  loadReadStatus,
  filterOutDeletedEmails,
  filterDuringAddressTransition,
  inAddressTransition,
  previousAddressRef
}: UseSupabaseRealtimeOptions) => {
  const [isSubscribed, setIsSubscribed] = useState(false);

  useEffect(() => {
    // Only subscribe if enabled and we have an email address
    if (!enabled || !emailAddress) {
      setIsSubscribed(false);
      return;
    }

    // Create a Supabase client for the subscription
    const supabase = createClient();

    // Subscribe to changes in the temp_emails table for this email address
    const subscription = supabase
      .channel('temp_emails_changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'temp_emails',
          filter: `email_address=eq.${emailAddress}`
        },
        async (payload) => {
          console.log('New email received via Supabase real-time:', payload);

          try {
            // Fetch the full email details from the API
            // This is necessary because the real-time payload doesn't include the email content
            const response = await fetch(`/api/emails/${encodeURIComponent(emailAddress)}`);

            if (!response.ok) {
              throw new Error(`API error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if (data && data.success) {
              // Get read status from localStorage
              const readStatusMap = loadReadStatus();

              // Map the API response to match the Email interface
              const mappedEmails = data.emails.map((apiEmail: any) =>
                mapApiEmailToUiEmail(apiEmail, readStatusMap)
              );

              // If we're in an address transition, don't process realtime updates
              if (inAddressTransition) {
                console.log(`[${new Date().toISOString()}] Realtime update: In address transition, ignoring update`);
                return;
              }

              // First, filter out any deleted emails
              let filteredEmails = filterOutDeletedEmails(mappedEmails);
              console.log(`[${new Date().toISOString()}] Realtime update: Filtered out ${mappedEmails.length - filteredEmails.length} deleted emails`);

              // If we have a previous address, apply additional filtering
              if (previousAddressRef.current) {
                console.log(`[${new Date().toISOString()}] Realtime update: Applying additional filtering for previous address: ${previousAddressRef.current}`);

                // Apply the address transition filter
                filteredEmails = filterDuringAddressTransition(
                  filteredEmails,
                  emailAddress,
                  previousAddressRef.current
                );
              }

              // Call the callback with the filtered emails
              onNewEmails(filteredEmails);
            }
          } catch (error) {
            console.error('Error fetching emails after real-time update:', error);
          }
        }
      )
      .subscribe();

    setIsSubscribed(true);

    // Clean up the subscription when the component unmounts
    return () => {
      console.log('Unsubscribing from Supabase real-time updates');
      supabase.channel('temp_emails_changes').unsubscribe();
      setIsSubscribed(false);
    };
  }, [emailAddress, enabled, onNewEmails, loadReadStatus, filterOutDeletedEmails, filterDuringAddressTransition, inAddressTransition, previousAddressRef]);

  return { isSubscribed };
};
