/**
 * API routes for ad management
 */
import { NextRequest, NextResponse } from 'next/server';
import { getAllAdPlacements, addAdPlacement } from '@/lib/config/adService';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/admin/ads
 *
 * Get all ad placements
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const domain = searchParams.get('domain');

    // Get all ad placements
    const ads = await getAllAdPlacements();

    // Filter by domain if provided
    const filteredAds = domain
      ? ads.filter(ad => ad.domain === domain)
      : ads;

    return NextResponse.json({
      success: true,
      data: filteredAds
    });
  } catch (error) {
    logError('api', 'Error getting ad placements', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to get ad placements' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/ads
 *
 * Add a new ad placement
 */
export async function POST(request: NextRequest) {
  try {
    // Get ad data from request body
    const {
      placementId,
      domain,
      adUnitId,
      adClientId,
      isEnabled,
      deviceTypes,
      displayOptions
    } = await request.json();

    // Validate required fields
    if (!placementId || !domain || !adUnitId || !adClientId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Add ad placement
    const newAd = await addAdPlacement(
      placementId,
      domain,
      adUnitId,
      adClientId,
      isEnabled,
      deviceTypes,
      displayOptions
    );

    if (!newAd) {
      return NextResponse.json(
        { success: false, error: 'Failed to add ad placement' },
        { status: 500 }
      );
    }

    logInfo('api', 'Added ad placement', { ad: newAd });

    return NextResponse.json({
      success: true,
      data: newAd,
      message: 'Ad placement added successfully'
    });
  } catch (error) {
    logError('api', 'Error adding ad placement', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to add ad placement' },
      { status: 500 }
    );
  }
}
