/**
 * <PERSON><PERSON><PERSON> to generate a hashed password for the admin user
 * 
 * Usage: node scripts/generate-password-hash.js <password>
 */
require('dotenv').config({ path: '.env.local' });
const bcrypt = require('bcrypt');

async function generateHash() {
  // Get password from command line arguments or use the one from environment variables
  const password = process.argv[2] || process.env.ADMIN_PASSWORD;
  
  if (!password) {
    console.error('Error: No password provided. Please provide a password as a command line argument or set ADMIN_PASSWORD in .env.local');
    process.exit(1);
  }
  
  try {
    // Generate hash with 10 salt rounds
    const hash = await bcrypt.hash(password, 10);
    
    console.log('\nHashed Password:');
    console.log(hash);
    console.log('\nAdd this to your .env.local file as:');
    console.log('ADMIN_PASSWORD_HASH=' + hash);
    console.log('\nMake sure to keep your ADMIN_PASSWORD for reference, but it will no longer be used directly for authentication.');
  } catch (error) {
    console.error('Error generating hash:', error);
    process.exit(1);
  }
}

generateHash();
