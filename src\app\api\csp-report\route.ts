import { NextRequest, NextResponse } from 'next/server';

/**
 * CSP Report Handler
 * 
 * This endpoint receives Content Security Policy violation reports
 * and handles them gracefully to reduce console noise while maintaining security.
 */
export async function POST(request: NextRequest) {
  try {
    const report = await request.json();
    
    // Only log CSP violations in development for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log('CSP Violation Report:', {
        'blocked-uri': report['csp-report']?.['blocked-uri'],
        'violated-directive': report['csp-report']?.['violated-directive'],
        'original-policy': report['csp-report']?.['original-policy'],
        'document-uri': report['csp-report']?.['document-uri'],
        timestamp: new Date().toISOString()
      });
    }

    // In production, you might want to send these to a logging service
    // For now, we'll just acknowledge receipt
    return NextResponse.json({ status: 'received' }, { status: 200 });
    
  } catch (error) {
    // Silently handle malformed reports
    return NextResponse.json({ status: 'error' }, { status: 400 });
  }
}

// Handle GET requests (some browsers might send GET requests)
export async function GET() {
  return NextResponse.json({ 
    message: 'CSP Report endpoint is active',
    status: 'ok' 
  }, { status: 200 });
}
