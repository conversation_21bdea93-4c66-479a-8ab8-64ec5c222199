'use client';

import React, { useEffect, useState, useMemo } from 'react';
import dynamic from 'next/dynamic';
import DOMPurify from 'dompurify';

// Dynamically import IframeResizer to avoid SSR issues
const IframeResizer = dynamic(
  () => import('@open-iframe-resizer/react').then((mod) => mod.IframeResizer),
  { ssr: false }
);

interface OpenIframeRendererProps {
  /**
   * The HTML content to render in the iframe
   */
  html: string;

  /**
   * Optional className for the iframe container
   */
  className?: string;

  /**
   * Optional array of web fonts to load in the iframe
   * Each font should be a Google Fonts URL or similar
   */
  webFonts?: string[];

  /**
   * Optional style object for the iframe
   */
  style?: React.CSSProperties;
}

/**
 * Helper function to generate a simple content hash for memoization
 * This prevents unnecessary re-renders when the content hasn't changed
 */
function generateContentHash(content: string): string {
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString(36);
}

/**
 * OpenIframeRenderer Component
 *
 * A component for rendering email HTML content in an iframe using @open-iframe-resizer/react.
 * Features:
 * - Complete style isolation through iframe
 * - Automatic height adjustment
 * - Server-side rendering compatibility
 * - Cross-origin support
 */
export default function OpenIframeRenderer({
  html,
  className = '',
  webFonts = [],
  style = {}
}: OpenIframeRendererProps) {
  // State to track if the component is mounted (for SSR compatibility)
  const [isMounted, setIsMounted] = useState(false);

  // State to track loading status
  const [isLoading, setIsLoading] = useState(true);

  // Generate a content hash for memoization
  const contentHash = useMemo(() =>
    generateContentHash(html + webFonts.join('')),
    [html, webFonts]
  );

  // Create the HTML content with responsive styling
  const htmlContent = useMemo(() => {
    // Sanitize and normalize the HTML content with enhanced configuration
    const sanitizedHtml = DOMPurify.sanitize(html || '', {
      ADD_TAGS: ['iframe', 'style', 'link', 'center'],
      ADD_ATTR: [
        'allow', 'allowfullscreen', 'frameborder', 'scrolling', 'sandbox',
        'style', 'width', 'height', 'src', 'srcset', 'class', 'id', 'href',
        'target', 'align', 'valign', 'cellpadding', 'cellspacing', 'border',
        'bgcolor', 'colspan', 'rowspan'
      ],
      WHOLE_DOCUMENT: false, // We're wrapping the content in our own HTML structure
      FORCE_BODY: true, // Ensure there's a body tag
      RETURN_DOM_FRAGMENT: false, // Return HTML as string
      RETURN_DOM: false,
      ALLOW_DATA_ATTR: true, // Allow data attributes
      ALLOW_ARIA_ATTR: true // Allow ARIA attributes
    });

    // Debug output in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('Email HTML length:', html?.length || 0);
      console.log('Sanitized HTML length:', sanitizedHtml.length);

      // Check for duplicate images
      const imgCount = (sanitizedHtml.match(/<img/g) || []).length;
      console.log('Number of images in sanitized HTML:', imgCount);

      // Extract all image sources for debugging
      const imgSrcRegex = /<img[^>]*src="([^"]*)"[^>]*>/gi;
      const imgSrcs = [];
      let match;
      while ((match = imgSrcRegex.exec(sanitizedHtml)) !== null) {
        imgSrcs.push(match[1]);
      }
      console.log('Image sources:', imgSrcs);
    }

    // Process HTML to remove potential duplicate content
    // This is a common issue with forwarded emails
    let processedHtml = sanitizedHtml;

    // For ATAS emails, use a simpler approach to fix duplicate images
    if (sanitizedHtml.includes('ATAS') || sanitizedHtml.includes('PRICING CHANGES AND NEW FEATURES')) {
      // Use CSS to handle duplicate images instead of DOM manipulation
      // This is less aggressive and preserves the original structure
      if (process.env.NODE_ENV === 'development') {
        console.log('Detected ATAS email, applying specialized CSS fixes');
      }
    }

    let normalizedHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, minimum-scale=1, viewport-fit=cover">
          <meta name="format-detection" content="telephone=no">
          <base href="${typeof window !== 'undefined' ? window.location.origin : ''}">
          ${webFonts.map(font => `<link href="${font}" rel="stylesheet">`).join('')}
          <style>
            /* Reset styles */
            html, body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              line-height: 1.4;
              overflow: visible;
              -webkit-text-size-adjust: 100%;
              -webkit-font-smoothing: antialiased;
              height: auto;
              width: 100%;
            }

            /* Email wrapper */
            .email-wrapper {
              max-width: 100%;
              overflow: visible;
              position: relative;
              min-height: 100%;
              padding-bottom: 50px;
              margin-bottom: 50px;
            }

            /* Ensure images don't overflow and prevent duplication */
            img {
              max-width: 100%;
              height: auto !important;
              display: inline-block; /* Prevent stacking */
            }

            /* Universal fix for all images */
            img {
              max-width: 100% !important;
              height: auto !important;
            }

            /* Fix for ATAS email images - only hide exact duplicates */
            img[src*="ATAS"] + img[src*="ATAS"][src$="ATAS.png"],
            img[src*="PRICING"] + img[src*="PRICING"][src$="PRICING.png"] {
              display: none !important;
            }

            /* Preserve button styling */
            a[href], button, .button, [role="button"],
            a[class*="button"], a[class*="btn"],
            input[type="button"], input[type="submit"] {
              display: inline-block !important;
              width: auto !important;
              max-width: 100% !important;
            }

            /* Fix social media icons spacing */
            a[href*="facebook"] img, a[href*="twitter"] img,
            a[href*="linkedin"] img, a[href*="instagram"] img {
              display: inline-block !important;
              margin: 0 5px !important;
            }

            /* Ensure tables don't overflow */
            table {
              max-width: 100%;
            }

            /* Mobile scaling */
            @media (max-width: 640px) {
              /* Ensure tables are responsive */
              table {
                width: 100% !important;
                height: auto !important;
              }

              /* Ensure text is readable on small screens */
              body {
                font-size: 16px !important;
                -webkit-text-size-adjust: 100%;
              }

              /* Improve tap targets for links */
              a {
                padding: 2px 0;
                display: inline-block;
              }
            }

            /* Small mobile devices */
            @media (max-width: 375px) {
              body {
                font-size: 14px !important;
              }
            }

            /* Don't force display of hidden elements - this can break layouts */
            /* [style*="display: none"], [style*="display:none"] {
              display: block !important;
            } */

            /* Ensure footer elements are visible */
            .footer, footer, .email-footer, [id*="footer"], [class*="footer"] {
              display: block !important;
              visibility: visible !important;
            }
          </style>
        </head>
        <body>
          <div class="email-wrapper">
            ${processedHtml}
          </div>
        </body>
      </html>
    `;

    // Ensure a footer exists to force full height calculation
    if (!normalizedHtml.includes('</footer>')) {
      normalizedHtml = normalizedHtml.replace('</body>', '<footer style="height: 50px;"></footer></body>');
    }

    return normalizedHtml;
  }, [html, webFonts, contentHash]);

  // Effect to handle client-side rendering
  useEffect(() => {
    setIsMounted(true);

    // Set a timer to hide the loading state
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Loading state for server-side rendering and initial client render
  const loadingState = (
    <div className="animate-pulse flex flex-col space-y-4 p-4" style={{ minHeight: '200px' }}>
      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      <div className="h-4 bg-gray-200 rounded w-full"></div>
      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
    </div>
  );

  return (
    <div
      className={`open-iframe-container ${className}`}
      style={{
        width: '100%',
        overflow: 'hidden',
        position: 'relative',
        transition: 'opacity 0.3s ease-in-out'
      }}
    >
      {!isMounted || isLoading ? (
        // Server-side and initial client render - show loading state
        loadingState
      ) : (
        // Client-side render after hydration - use IframeResizer
        <IframeResizer
          srcDoc={htmlContent}
          style={{
            width: '100%',
            border: 'none',
            overflow: 'auto',
            minHeight: '600px',
            display: 'block',
            transition: 'opacity 0.3s ease-in-out, height 0.3s ease-in-out',
            transform: 'translateZ(0)',
            maxHeight: '2000px',
            ...style
          }}
          title="Email content"
          sandbox="allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-scripts"
        />
      )}
    </div>
  );
}
