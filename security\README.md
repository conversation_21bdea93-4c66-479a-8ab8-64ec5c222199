# Security Tests for Fademail

This directory contains security tests for the Fademail application.

## Prerequisites

1. Install OWASP ZAP: https://www.zaproxy.org/download/
2. Install the ZAP API: `npm install zaproxy`
3. Install node-fetch: `npm install node-fetch@2`

## Running the Tests

### ZAP Security Scan

This test runs a full security scan using OWASP ZAP, which checks for common security vulnerabilities such as XSS, SQL injection, and CSRF.

```bash
node security/zap-scan.js
```

### Authentication Security Tests

This test checks the security of the authentication system, including brute force protection, SQL injection protection, CSRF protection, and secure cookies.

```bash
node security/auth-security.js
```

## Test Configuration

### ZAP Security Scan

The ZAP security scan is configured to:

1. Spider the application to discover all pages
2. Run an active scan to check for vulnerabilities
3. Generate a report of the findings

### Authentication Security Tests

The authentication security tests check for:

1. Brute force protection: Does the application block multiple failed login attempts?
2. SQL injection protection: Is the login form protected against SQL injection?
3. CSRF protection: Are sensitive actions protected against CSRF?
4. Secure cookies: Are cookies set with the HttpOnly, Secure, and SameSite flags?

## Interpreting Results

### ZAP Security Scan

After running the ZAP security scan, a report will be generated in the `security/reports` directory. The report will contain a list of vulnerabilities found, categorized by risk level (High, Medium, Low, Informational).

### Authentication Security Tests

After running the authentication security tests, a summary will be printed to the console, indicating whether each security feature is enabled or disabled.

## Continuous Integration

These tests can be integrated into a CI/CD pipeline to ensure that security vulnerabilities are caught early. For example, you can run the tests after each deployment to staging and fail the pipeline if any high-risk vulnerabilities are found.
