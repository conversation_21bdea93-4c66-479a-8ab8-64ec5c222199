import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logInfo, logError } from '@/lib/logging';

/**
 * API route to directly initialize the app_config table
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Create the app_config table if it doesn't exist
    const createTableResult = await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS app_config (
          key VARCHAR(50) PRIMARY KEY,
          value JSONB NOT NULL,
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );
      `
    });

    if (createTableResult.error) {
      // Try to create the table directly with a query
      const createTableQuery = await supabase.from('app_config').insert({
        key: 'emailExpirationMinutes',
        value: 30,
        updated_at: new Date().toISOString()
      });

      if (createTableQuery.error && createTableQuery.error.code !== '23505') {
        return NextResponse.json({
          success: false,
          message: 'Failed to create app_config table',
          error: createTableQuery.error
        }, { status: 500 });
      }
    }

    // Insert default configuration values
    const configValues = [
      { key: 'emailExpirationMinutes', value: 30 },
      { key: 'autoRefreshInterval', value: 14 },
      { key: 'maxEmailsPerAddress', value: 100 },
      { key: 'maintenanceMode', value: false },
      { key: 'cleanupIntervalMinutes', value: 15 }
    ];

    for (const config of configValues) {
      const { error } = await supabase
        .from('app_config')
        .upsert({
          key: config.key,
          value: config.value,
          updated_at: new Date().toISOString()
        }, { onConflict: 'key' });

      if (error && error.code !== '23505') {
        return NextResponse.json({
          success: false,
          message: `Failed to insert config ${config.key}`,
          error
        }, { status: 500 });
      }
    }

    // Verify the configuration was inserted
    const { data, error } = await supabase
      .from('app_config')
      .select('key, value');

    if (error) {
      return NextResponse.json({
        success: false,
        message: 'Failed to verify configuration',
        error
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Configuration initialized successfully',
      data
    });
  } catch (error) {
    logError('admin', 'Error initializing configuration', { error });
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error
    }, { status: 500 });
  }
}
