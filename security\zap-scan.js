/**
 * This script runs a security scan using OWASP ZAP.
 * 
 * Prerequisites:
 * 1. Install OWASP ZAP: https://www.zaproxy.org/download/
 * 2. Install the ZAP API: npm install zaproxy
 * 
 * Usage:
 * node security/zap-scan.js
 */

const ZapClient = require('zaproxy');
const fs = require('fs');
const path = require('path');

// ZAP configuration
const zapOptions = {
  apiKey: 'zap-api-key', // Change this to your ZAP API key
  proxy: {
    host: 'localhost',
    port: 8080
  }
};

// Target URL
const target = 'http://localhost:3000';

// Initialize the ZAP client
const zaproxy = new ZapClient(zapOptions);

// Create a directory for the reports if it doesn't exist
const reportsDir = path.join(__dirname, 'reports');
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir);
}

// Function to generate a timestamp for the report filename
function getTimestamp() {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}-${String(now.getMinutes()).padStart(2, '0')}-${String(now.getSeconds()).padStart(2, '0')}`;
}

// Main function to run the security scan
async function runSecurityScan() {
  try {
    console.log('Starting ZAP security scan...');
    
    // Start a new session
    await zaproxy.core.newSession('', true);
    console.log('New session created');
    
    // Access the target URL to add it to the site tree
    console.log(`Accessing target: ${target}`);
    await zaproxy.core.accessUrl(target);
    
    // Spider the target
    console.log('Starting spider scan...');
    const scanId = await zaproxy.spider.scan(target, null, null, null, null);
    
    // Wait for the spider to complete
    let spiderProgress = 0;
    while (spiderProgress < 100) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const status = await zaproxy.spider.status(scanId);
      spiderProgress = parseInt(status.status);
      console.log(`Spider progress: ${spiderProgress}%`);
    }
    console.log('Spider scan completed');
    
    // Run an active scan
    console.log('Starting active scan...');
    const activeScanId = await zaproxy.ascan.scan(target, true, true, null, null, null);
    
    // Wait for the active scan to complete
    let activeScanProgress = 0;
    while (activeScanProgress < 100) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      const status = await zaproxy.ascan.status(activeScanId);
      activeScanProgress = parseInt(status.status);
      console.log(`Active scan progress: ${activeScanProgress}%`);
    }
    console.log('Active scan completed');
    
    // Get the alerts
    console.log('Retrieving alerts...');
    const alerts = await zaproxy.core.alerts(target, null, null, null);
    
    // Generate a report
    console.log('Generating report...');
    const timestamp = getTimestamp();
    const reportPath = path.join(reportsDir, `security-report-${timestamp}.html`);
    const reportData = await zaproxy.core.htmlreport();
    fs.writeFileSync(reportPath, reportData);
    
    // Print a summary of the alerts
    console.log('\nSecurity Scan Summary:');
    console.log(`Total alerts: ${alerts.alerts.length}`);
    
    // Group alerts by risk level
    const riskLevels = {
      High: 0,
      Medium: 0,
      Low: 0,
      Informational: 0
    };
    
    alerts.alerts.forEach(alert => {
      riskLevels[alert.risk]++;
    });
    
    console.log('Alerts by risk level:');
    Object.entries(riskLevels).forEach(([risk, count]) => {
      console.log(`  ${risk}: ${count}`);
    });
    
    console.log(`\nReport saved to: ${reportPath}`);
    
    // Return the results
    return {
      totalAlerts: alerts.alerts.length,
      riskLevels,
      reportPath
    };
  } catch (error) {
    console.error('Error running security scan:', error);
    throw error;
  }
}

// Run the security scan
runSecurityScan()
  .then(results => {
    console.log('Security scan completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Security scan failed:', error);
    process.exit(1);
  });
