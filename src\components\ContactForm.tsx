'use client';

import { useState } from 'react';

/**
 * ContactForm Component
 *
 * A client-side form component for submitting contact messages.
 */
export default function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Your message has been sent successfully. We will get back to you soon!');
        // Reset form
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: ''
        });
      } else {
        setError(data.message || 'Failed to send message. Please try again.');
      }
    } catch (err) {
      setError('An error occurred while sending your message. Please try again.');
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {success && (
        <div className="px-4 py-3 rounded-md" style={{ backgroundColor: 'var(--earth-green-success)', color: 'white', border: '1px solid var(--earth-green-success)' }}>
          {success}
        </div>
      )}

      {error && (
        <div className="px-4 py-3 rounded-md" style={{ backgroundColor: 'var(--earth-amber-accent)', color: 'white', border: '1px solid var(--earth-amber-accent)' }}>
          {error}
        </div>
      )}

      <div>
        <label htmlFor="name" className="block text-sm font-medium mb-1" style={{ color: 'var(--earth-brown-dark)' }}>
          Your Name
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          className="w-full px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:border-transparent"
          style={{
            border: '1px solid var(--earth-beige-secondary)',
            backgroundColor: 'var(--earth-beige-light)',
            color: 'var(--earth-brown-dark)'
          }}
          onFocus={(e) => {
            e.target.style.borderColor = 'var(--earth-brown-medium)';
            e.target.style.boxShadow = '0 0 0 2px var(--earth-beige-secondary)';
          }}
          onBlur={(e) => {
            e.target.style.borderColor = 'var(--earth-beige-secondary)';
            e.target.style.boxShadow = 'none';
          }}
          placeholder="John Doe"
          required
          disabled={isSubmitting}
        />
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium mb-1" style={{ color: 'var(--earth-brown-dark)' }}>
          Email Address
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          className="w-full px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:border-transparent"
          style={{
            border: '1px solid var(--earth-beige-secondary)',
            backgroundColor: 'var(--earth-beige-light)',
            color: 'var(--earth-brown-dark)'
          }}
          onFocus={(e) => {
            e.target.style.borderColor = 'var(--earth-brown-medium)';
            e.target.style.boxShadow = '0 0 0 2px var(--earth-beige-secondary)';
          }}
          onBlur={(e) => {
            e.target.style.borderColor = 'var(--earth-beige-secondary)';
            e.target.style.boxShadow = 'none';
          }}
          placeholder="<EMAIL>"
          required
          disabled={isSubmitting}
        />
      </div>

      <div>
        <label htmlFor="subject" className="block text-sm font-medium mb-1" style={{ color: 'var(--earth-brown-dark)' }}>
          Subject
        </label>
        <input
          type="text"
          id="subject"
          name="subject"
          value={formData.subject}
          onChange={handleChange}
          className="w-full px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:border-transparent"
          style={{
            border: '1px solid var(--earth-beige-secondary)',
            backgroundColor: 'var(--earth-beige-light)',
            color: 'var(--earth-brown-dark)'
          }}
          onFocus={(e) => {
            e.target.style.borderColor = 'var(--earth-brown-medium)';
            e.target.style.boxShadow = '0 0 0 2px var(--earth-beige-secondary)';
          }}
          onBlur={(e) => {
            e.target.style.borderColor = 'var(--earth-beige-secondary)';
            e.target.style.boxShadow = 'none';
          }}
          placeholder="How can we help you?"
          required
          disabled={isSubmitting}
        />
      </div>

      <div>
        <label htmlFor="message" className="block text-sm font-medium mb-1" style={{ color: 'var(--earth-brown-dark)' }}>
          Message
        </label>
        <textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleChange}
          rows={5}
          className="w-full px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:border-transparent"
          style={{
            border: '1px solid var(--earth-beige-secondary)',
            backgroundColor: 'var(--earth-beige-light)',
            color: 'var(--earth-brown-dark)'
          }}
          onFocus={(e) => {
            e.target.style.borderColor = 'var(--earth-brown-medium)';
            e.target.style.boxShadow = '0 0 0 2px var(--earth-beige-secondary)';
          }}
          onBlur={(e) => {
            e.target.style.borderColor = 'var(--earth-beige-secondary)';
            e.target.style.boxShadow = 'none';
          }}
          placeholder="Your message here..."
          required
          disabled={isSubmitting}
        ></textarea>
      </div>

      <button
        type="submit"
        className="w-full text-white px-6 py-3 rounded-md font-medium transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
        style={{ backgroundColor: 'var(--earth-brown-medium)' }}
        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--earth-brown-dark)'}
        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--earth-brown-medium)'}
        disabled={isSubmitting}
      >
        {isSubmitting ? 'Sending...' : 'Send Message'}
      </button>
    </form>
  );
}
