/**
 * Analytics Cleanup Management API Endpoint
 * 
 * This endpoint provides cleanup management functionality for analytics data.
 * Allows manual cleanup execution, statistics retrieval, and cleanup monitoring.
 */

import { NextRequest, NextResponse } from 'next/server';
import { analyticsCleanup, CLEANUP_CONFIG } from '@/lib/analytics/cleanup';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/management-portal-x7z9y2/analytics/cleanup
 * Get cleanup statistics and recommendations
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeRecommendations = searchParams.get('includeRecommendations') !== 'false';

    logInfo('CleanupAPI', 'Retrieving cleanup statistics');

    const stats = await analyticsCleanup.getCleanupStats();

    const result = {
      statistics: {
        eventsCount: stats.eventsCount,
        sessionsCount: stats.sessionsCount,
        incompleteSessionsCount: stats.incompleteSessionsCount,
        oldestEvent: stats.oldestEvent,
        oldestSession: stats.oldestSession
      },
      configuration: {
        eventsRetentionDays: CLEANUP_CONFIG.EVENTS_RETENTION_DAYS,
        sessionsRetentionDays: CLEANUP_CONFIG.SESSIONS_RETENTION_DAYS,
        incompleteSessionsRetentionHours: CLEANUP_CONFIG.INCOMPLETE_SESSIONS_RETENTION_HOURS,
        cleanupSchedule: CLEANUP_CONFIG.CLEANUP_SCHEDULE
      },
      recommendations: includeRecommendations ? stats.recommendations : undefined
    };

    // Calculate cleanup estimates
    if (stats.oldestEvent) {
      const oldestEventDate = new Date(stats.oldestEvent);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - CLEANUP_CONFIG.EVENTS_RETENTION_DAYS);
      
      if (oldestEventDate < cutoffDate) {
        const daysPastRetention = Math.floor((cutoffDate.getTime() - oldestEventDate.getTime()) / (1000 * 60 * 60 * 24));
        (result as any).estimates = {
          eventsPastRetention: true,
          daysPastRetention,
          recommendCleanup: daysPastRetention > 7
        };
      }
    }

    logInfo('CleanupAPI', 'Retrieved cleanup statistics', {
      eventsCount: stats.eventsCount,
      sessionsCount: stats.sessionsCount,
      incompleteSessionsCount: stats.incompleteSessionsCount
    });

    return NextResponse.json({
      success: true,
      data: result,
      meta: {
        includeRecommendations,
        queryTime: new Date().toISOString()
      }
    });

  } catch (error) {
    logError('CleanupAPI', 'Error retrieving cleanup statistics', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve cleanup statistics' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/management-portal-x7z9y2/analytics/cleanup
 * Execute cleanup operations
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      operation = 'full',
      dryRun = false,
      eventsRetentionDays,
      sessionsRetentionDays,
      incompleteSessionsRetentionHours
    } = body;

    logInfo('CleanupAPI', 'Starting cleanup operation', {
      operation,
      dryRun,
      customRetention: {
        events: eventsRetentionDays,
        sessions: sessionsRetentionDays,
        incomplete: incompleteSessionsRetentionHours
      }
    });

    let result: any = {};

    switch (operation) {
      case 'full':
        if (dryRun) {
          // For dry run, get statistics to show what would be deleted
          const stats = await analyticsCleanup.getCleanupStats();
          
          // Calculate what would be deleted
          const eventsCutoff = new Date();
          eventsCutoff.setDate(eventsCutoff.getDate() - (eventsRetentionDays || CLEANUP_CONFIG.EVENTS_RETENTION_DAYS));
          
          const sessionsCutoff = new Date();
          sessionsCutoff.setDate(sessionsCutoff.getDate() - (sessionsRetentionDays || CLEANUP_CONFIG.SESSIONS_RETENTION_DAYS));
          
          const incompleteCutoff = new Date();
          incompleteCutoff.setHours(incompleteCutoff.getHours() - (incompleteSessionsRetentionHours || CLEANUP_CONFIG.INCOMPLETE_SESSIONS_RETENTION_HOURS));

          result = {
            dryRun: true,
            estimatedDeletions: {
              events: 'Estimation requires database query',
              sessions: 'Estimation requires database query',
              incompleteSessions: stats.incompleteSessionsCount
            },
            cutoffDates: {
              events: eventsCutoff.toISOString(),
              sessions: sessionsCutoff.toISOString(),
              incompleteSessions: incompleteCutoff.toISOString()
            }
          };
        } else {
          result = await analyticsCleanup.runFullCleanup();
        }
        break;

      case 'events':
        result = await analyticsCleanup.cleanupAnalyticsEvents(
          eventsRetentionDays || CLEANUP_CONFIG.EVENTS_RETENTION_DAYS
        );
        break;

      case 'sessions':
        result = await analyticsCleanup.cleanupSessionData(
          sessionsRetentionDays || CLEANUP_CONFIG.SESSIONS_RETENTION_DAYS
        );
        break;

      case 'incomplete':
        result = await analyticsCleanup.cleanupIncompleteSessions(
          incompleteSessionsRetentionHours || CLEANUP_CONFIG.INCOMPLETE_SESSIONS_RETENTION_HOURS
        );
        break;

      default:
        return NextResponse.json(
          { success: false, error: `Unknown operation: ${operation}` },
          { status: 400 }
        );
    }

    logInfo('CleanupAPI', 'Cleanup operation completed', {
      operation,
      dryRun,
      result: dryRun ? 'dry-run-completed' : result
    });

    return NextResponse.json({
      success: true,
      data: result,
      meta: {
        operation,
        dryRun,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logError('CleanupAPI', 'Error executing cleanup operation', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to execute cleanup operation' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/management-portal-x7z9y2/analytics/cleanup
 * Emergency cleanup - delete all analytics data (with confirmation)
 */
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { confirmationToken } = body;

    // Require explicit confirmation for emergency cleanup
    if (confirmationToken !== 'EMERGENCY_CLEANUP_CONFIRMED') {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Emergency cleanup requires confirmation token: EMERGENCY_CLEANUP_CONFIRMED' 
        },
        { status: 400 }
      );
    }

    logInfo('CleanupAPI', 'Starting emergency cleanup - deleting ALL analytics data');

    const startTime = Date.now();
    
    // Get initial counts
    const initialStats = await analyticsCleanup.getCleanupStats();

    // Delete all analytics events
    const { error: eventsError } = await analyticsCleanup['supabase']
      .from('analytics_events')
      .delete()
      .neq('id', 0); // Delete all records

    if (eventsError) {
      logError('CleanupAPI', 'Failed to delete analytics events', { error: eventsError });
      throw eventsError;
    }

    // Delete all session analytics
    const { error: sessionsError } = await analyticsCleanup['supabase']
      .from('session_analytics')
      .delete()
      .neq('session_id', ''); // Delete all records

    if (sessionsError) {
      logError('CleanupAPI', 'Failed to delete session analytics', { error: sessionsError });
      throw sessionsError;
    }

    const duration = Date.now() - startTime;

    const result = {
      emergencyCleanup: true,
      deletedCounts: {
        events: initialStats.eventsCount,
        sessions: initialStats.sessionsCount
      },
      duration,
      timestamp: new Date().toISOString()
    };

    logInfo('CleanupAPI', 'Emergency cleanup completed', result);

    return NextResponse.json({
      success: true,
      data: result,
      meta: {
        operation: 'emergency-cleanup',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logError('CleanupAPI', 'Error in emergency cleanup', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to execute emergency cleanup' },
      { status: 500 }
    );
  }
}
