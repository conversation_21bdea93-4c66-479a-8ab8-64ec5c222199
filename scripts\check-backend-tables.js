/**
 * <PERSON><PERSON>t to check if the Backend Management System tables exist in Supabase
 */
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create a Supabase client with the anon key
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Tables to check
const tables = [
  'app_config',
  'domain_config',
  'ad_config',
  'system_logs'
];

/**
 * Check if a table exists
 */
async function checkTable(tableName) {
  try {
    console.log(`Checking if table ${tableName} exists...`);

    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);

    if (error) {
      if (error.code === '42P01') {
        console.log(`Table ${tableName} does not exist`);
        return false;
      } else {
        console.error(`Error checking table ${tableName}:`, error.message);
        return false;
      }
    }

    console.log(`Table ${tableName} exists`);
    return true;
  } catch (error) {
    console.error(`Error checking table ${tableName}:`, error.message);
    return false;
  }
}

/**
 * Main function to check all tables
 */
async function checkTables() {
  console.log('Checking Backend Management System tables in Supabase...');

  let allTablesExist = true;

  for (const table of tables) {
    const exists = await checkTable(table);
    if (!exists) {
      allTablesExist = false;
    }
  }

  if (allTablesExist) {
    console.log('All Backend Management System tables exist in Supabase');
  } else {
    console.log('Some Backend Management System tables are missing in Supabase');
  }
}

// Run the check function
checkTables();
