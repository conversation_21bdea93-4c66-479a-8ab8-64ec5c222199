'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function DebugMaintenancePage() {
  const [maintenanceMode, setMaintenanceMode] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [toggleResult, setToggleResult] = useState<any>(null);

  // Fetch maintenance mode status
  const fetchStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/debug/maintenance-status');
      const data = await response.json();

      if (data.success) {
        setMaintenanceMode(data.maintenanceMode);
      } else {
        setError(data.error || 'Failed to fetch maintenance mode status');
      }
    } catch (err) {
      setError('An error occurred while fetching maintenance mode status');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Toggle maintenance mode
  const toggleMaintenanceMode = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/debug/toggle-maintenance');
      const data = await response.json();

      if (data.success) {
        setToggleResult(data);
        setMaintenanceMode(data.confirmedStatus);
      } else {
        setError(data.error || 'Failed to toggle maintenance mode');
      }
    } catch (err) {
      setError('An error occurred while toggling maintenance mode');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch status on component mount
  useEffect(() => {
    fetchStatus();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <h1 className="text-lg font-medium text-gray-900">Maintenance Mode Debug</h1>

          {loading ? (
            <div className="mt-4 flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            </div>
          ) : error ? (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
              <button
                className="mt-2 text-sm text-indigo-600 hover:text-indigo-500"
                onClick={fetchStatus}
              >
                Retry
              </button>
            </div>
          ) : (
            <div className="mt-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Current Status:</p>
                  <p className="text-lg font-semibold">
                    {maintenanceMode ? (
                      <span className="text-orange-600">Maintenance Mode Enabled</span>
                    ) : (
                      <span className="text-green-600">Normal Operation</span>
                    )}
                  </p>
                </div>

                <button
                  className={`px-4 py-2 rounded-md text-sm font-medium text-white ${
                    maintenanceMode ? 'bg-green-600 hover:bg-green-700' : 'bg-orange-600 hover:bg-orange-700'
                  }`}
                  onClick={toggleMaintenanceMode}
                >
                  {maintenanceMode ? 'Disable Maintenance Mode' : 'Enable Maintenance Mode'}
                </button>
              </div>

              {toggleResult && (
                <div className="mt-4 p-4 bg-gray-50 rounded-md">
                  <p className="text-sm font-medium text-gray-500">Toggle Result:</p>
                  <pre className="mt-2 text-xs overflow-auto bg-gray-100 p-2 rounded">
                    {JSON.stringify(toggleResult, null, 2)}
                  </pre>
                </div>
              )}

              <div className="mt-6 border-t border-gray-200 pt-4">
                <p className="text-sm font-medium text-gray-500">Test Links:</p>
                <div className="mt-2 space-y-2">
                  <Link href="/" className="text-sm text-indigo-600 hover:text-indigo-500 block">
                    Visit Homepage
                  </Link>
                  <Link href="/maintenance" className="text-sm text-indigo-600 hover:text-indigo-500 block">
                    View Maintenance Page
                  </Link>
                  {/* Admin link removed for security reasons */}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
