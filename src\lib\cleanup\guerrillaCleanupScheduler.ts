/**
 * Guerrilla Email Cleanup Scheduler
 * 
 * Dedicated scheduler for cleaning up old Guerrilla emails from MySQL database
 * Operates independently from the general cleanup system
 */

import { getConfig, updateConfig } from '@/lib/config/configService';
import { logInfo, logError } from '@/lib/logging';

// Global variable to track the scheduler interval
let guerrillaCleanupIntervalId: NodeJS.Timeout | null = null;

/**
 * Start the Guerrilla email cleanup scheduler
 */
export async function startGuerrillaCleanupScheduler(): Promise<boolean> {
  try {
    logInfo('guerrilla-cleanup', 'Starting Guerrilla email cleanup scheduler');

    // If already running, stop it first
    if (guerrillaCleanupIntervalId) {
      await stopGuerrillaCleanupScheduler();
    }

    // Get cleanup interval from configuration
    const cleanupIntervalMinutes = await getConfig('guerrillaCleanupIntervalMinutes');

    if (!cleanupIntervalMinutes || cleanupIntervalMinutes <= 0) {
      logError('guerrilla-cleanup', 'Invalid Guerrilla cleanup interval', { cleanupIntervalMinutes });
      return false;
    }

    // Convert minutes to milliseconds
    const intervalMs = cleanupIntervalMinutes * 60 * 1000;
    
    // Record scheduler start time for consistent next run calculations
    const startTime = new Date().toISOString();
    await updateConfig('guerrillaCleanupSchedulerStartTime', startTime);

    // Schedule Guerrilla cleanup
    guerrillaCleanupIntervalId = setInterval(async () => {
      try {
        await runGuerrillaCleanup();
        // Update last run time
        await updateConfig('guerrillaCleanupLastRunTime', new Date().toISOString());
      } catch (error) {
        logError('guerrilla-cleanup', 'Error running scheduled Guerrilla cleanup', { error });
      }
    }, intervalMs);

    logInfo('guerrilla-cleanup', 'Guerrilla email cleanup scheduler started', {
      intervalMinutes: cleanupIntervalMinutes,
      startTime,
      nextRunAt: new Date(Date.now() + intervalMs).toISOString()
    });

    return true;
  } catch (error) {
    logError('guerrilla-cleanup', 'Failed to start Guerrilla cleanup scheduler', { error });
    return false;
  }
}

/**
 * Stop the Guerrilla email cleanup scheduler
 */
export async function stopGuerrillaCleanupScheduler(): Promise<void> {
  if (guerrillaCleanupIntervalId) {
    clearInterval(guerrillaCleanupIntervalId);
    guerrillaCleanupIntervalId = null;
    
    // Clear scheduler start time when stopping
    await updateConfig('guerrillaCleanupSchedulerStartTime', undefined);
    
    logInfo('guerrilla-cleanup', 'Guerrilla email cleanup scheduler stopped');
  }
}

/**
 * Check if the Guerrilla email cleanup scheduler is running
 */
export function isGuerrillaCleanupSchedulerRunning(): boolean {
  return guerrillaCleanupIntervalId !== null;
}

/**
 * Run Guerrilla email cleanup operation
 */
async function runGuerrillaCleanup(): Promise<void> {
  try {
    logInfo('guerrilla-cleanup', 'Running scheduled Guerrilla email cleanup');

    // Call the Guerrilla cleanup API
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001';
    const response = await fetch(`${baseUrl}/api/management-portal-x7z9y2/guerrilla-cleanup`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Guerrilla cleanup API returned ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.success) {
      logInfo('guerrilla-cleanup', 'Scheduled Guerrilla cleanup completed successfully', {
        guerrillaEmailsDeleted: result.guerrillaEmailsDeleted,
        duration: result.duration
      });
    } else {
      logError('guerrilla-cleanup', 'Scheduled Guerrilla cleanup failed', { 
        error: result.error 
      });
    }
  } catch (error) {
    logError('guerrilla-cleanup', 'Error during scheduled Guerrilla cleanup', { error });
    throw error;
  }
}
