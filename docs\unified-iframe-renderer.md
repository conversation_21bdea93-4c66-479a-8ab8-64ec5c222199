# Unified Iframe Renderer

This document explains the implementation of the unified iframe renderer in the Fademail application.

## Overview

The `UnifiedIframeRenderer` component provides a streamlined solution for rendering HTML emails in an iframe while ensuring they display properly on all devices. It consolidates the best features of our previous iframe implementations into a single, maintainable component.

## Key Features

- **Complete Style Isolation**: Emails are rendered in an isolated iframe to prevent style conflicts
- **Responsive Scaling**: Fixed-width emails are automatically scaled down on mobile devices
- **Dynamic Height Adjustment**: Uses ResizeObserver and message passing for accurate height calculation
- **Server-Side Rendering Compatibility**: Works seamlessly with Next.js
- **Minimal DOM Manipulation**: Reduces direct DOM access in favor of declarative approaches
- **Print Support**: Includes print-specific styles for proper printing of emails
- **Link Handling**: Intercepts link clicks to open them in new tabs
- **Performance Optimization**: Uses content hashing and memoization to prevent unnecessary re-renders

## Implementation Details

### Component Structure

The `UnifiedIframeRenderer` component is structured as follows:

1. **Props Interface**: Defines the component's API
   - `html`: The HTML content to render
   - `className`: Optional class name for styling
   - `webFonts`: Optional array of web font URLs to load

2. **State Management**:
   - Uses `useRef` for the iframe reference
   - Uses `useState` to track component mounting for SSR compatibility
   - Uses `useMemo` for content hashing to prevent unnecessary re-renders

3. **HTML Content Generation**:
   - Creates a complete HTML document with proper DOCTYPE and meta tags
   - Includes CSS for reset styles, responsive scaling, and print support
   - Embeds JavaScript for handling responsive scaling and link interception

4. **Height Adjustment**:
   - Uses ResizeObserver when available for monitoring content size changes
   - Falls back to message passing between iframe and parent for height communication
   - Applies appropriate scaling for fixed-width emails on mobile devices

### Responsive Scaling Approach

For fixed-width emails, the component:

1. Detects fixed-width elements (tables, divs, etc.)
2. Calculates the appropriate scaling factor based on viewport width
3. Applies CSS transforms to scale content proportionally
4. Adjusts container height to match scaled content

```javascript
// Calculate scale factor
var scale = viewportWidth / maxContentWidth;

// Apply minimum scale to prevent too small content
scale = Math.max(scale, minScale);

// Apply the scale transform
content.classList.add('email-mobile-scale');
content.style.transform = 'scale(' + scale + ')';
content.style.width = (maxContentWidth) + 'px';
```

### Height Calculation

The component uses a multi-layered approach to height calculation:

1. **ResizeObserver**: When available, monitors content size changes directly
2. **Message Passing**: Uses `postMessage` for communication between iframe content and parent
3. **Initial Calculation**: Sets a generous initial height during loading

```javascript
// Notify parent about height change
window.parent.postMessage({
  type: 'iframe-resize',
  height: scaledHeight
}, '*');
```

### Server-Side Rendering Compatibility

The component handles SSR by:

1. Using a state variable to track client-side mounting
2. Rendering a loading skeleton during SSR and initial hydration
3. Switching to the iframe after client-side hydration

```jsx
{!isMounted ? (
  // Server-side and initial client render - show loading state
  loadingState
) : (
  // Client-side render after hydration - use iframe
  <iframe ref={iframeRef} ... />
)}
```

## Usage

To use the unified iframe renderer in your components:

```tsx
import UnifiedIframeRenderer from '@/components/UnifiedIframeRenderer';

function EmailViewer({ email }) {
  return (
    <div className="email-container">
      <UnifiedIframeRenderer html={email.html} />
    </div>
  );
}
```

## Testing

A test page is available at `/test-unified-iframe` that demonstrates the renderer with different email templates.

The component includes comprehensive unit tests in `src/components/__tests__/UnifiedIframeRenderer.test.tsx`.

## Browser Compatibility

The implementation has been tested and works well in:
- Chrome
- Firefox
- Safari
- Edge

## Mobile Device Testing

For optimal results, test the implementation on actual mobile devices or using browser developer tools to simulate various device sizes.

## Performance Considerations

- Content hashing prevents unnecessary re-renders
- ResizeObserver provides efficient size monitoring
- Minimal DOM manipulation reduces layout thrashing
- Memoization optimizes HTML content generation

## Accessibility

The implementation maintains accessibility by:
- Preserving text scaling
- Ensuring links remain clickable
- Maintaining proper contrast ratios
- Using semantic HTML in the loading state

## Comparison with Previous Implementations

| Feature | Previous Implementations | Unified Implementation |
|---------|--------------------------|------------------------|
| Height Calculation | Multiple approaches with redundancy | Single, consistent approach |
| DOM Manipulation | Heavy direct manipulation | Minimal, with message passing |
| Code Complexity | High, with multiple components | Moderate, with clear structure |
| Mobile Support | Varied between implementations | Consistent responsive scaling |
| Print Support | Limited | Enhanced with print-specific styles |
| Performance | Multiple re-renders and layout calculations | Optimized with fewer reflows |
| Maintainability | Scattered across multiple components | Consolidated in one component |

## Future Improvements

Potential future improvements to consider:
- Add support for dark mode in emails
- Enhance print-specific styling
- Add options for customizing the scaling behavior
- Optimize performance for very large emails
