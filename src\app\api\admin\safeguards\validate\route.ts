import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '../../../../../lib/auth';
import productionSafeguards from '../../../../../lib/config/productionSafeguards';
import { logger } from '../../../../../lib/logging/dbLogger';

/**
 * POST /api/admin/safeguards/validate/config
 * Validate configuration changes
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { key, value } = body;

    if (!key) {
      return NextResponse.json({ error: 'Configuration key is required' }, { status: 400 });
    }

    // Validate configuration change
    const validationResult = await productionSafeguards.validateConfigChange(key, value);

    return NextResponse.json(validationResult);
  } catch (error) {
    await logger.error('API_SAFEGUARDS_VALIDATE_CONFIG', `Error validating configuration: ${error}`);
    return NextResponse.json({ success: false, message: 'Failed to validate configuration' }, { status: 500 });
  }
}
