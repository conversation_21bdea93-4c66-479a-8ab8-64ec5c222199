import { PostHog } from 'posthog-node'

/**
 * PostHogClient
 * 
 * Creates a server-side PostHog client for use in server components and API routes
 * Sets flushAt to 1 and flushInterval to 0 to ensure events are sent immediately
 * 
 * @returns PostHog client instance
 */
export default function PostHogClient() {
  const posthogClient = new PostHog(
    process.env.NEXT_PUBLIC_POSTHOG_KEY || 'phc_placeholder',
    {
      host: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://us.i.posthog.com',
      flushAt: 1, // Flush after each event
      flushInterval: 0, // Don't wait to flush
    }
  )
  
  return posthogClient
}

/**
 * Example usage in a server component:
 * 
 * ```
 * import PostHogClient from '@/lib/analytics/posthog-server'
 * 
 * export default async function MyServerComponent() {
 *   const posthog = PostHogClient()
 *   
 *   // Get feature flags for a user
 *   const flags = await posthog.getAllFlags('user_distinct_id')
 *   
 *   // Always call shutdown when done to ensure events are sent
 *   await posthog.shutdown()
 *   
 *   return (
 *     <div>
 *       {flags['my-feature'] && <p>Feature enabled!</p>}
 *     </div>
 *   )
 * }
 * ```
 */
