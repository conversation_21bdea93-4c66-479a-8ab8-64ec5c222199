# Performance Tests for Fademail

This directory contains performance tests for the Fademail application using k6, a modern load testing tool.

## Prerequisites

1. Install k6: https://k6.io/docs/getting-started/installation/

## Running the Tests

### Basic Load Test

This test simulates users generating email addresses, fetching emails, and deleting emails.

```bash
k6 run performance/load-test.js
```

### Admin Dashboard Load Test

This test simulates users accessing the admin dashboard and retrieving analytics data.

```bash
k6 run performance/admin-load-test.js
```

## Test Configuration

The tests are configured with the following stages:

### Basic Load Test

1. Ramp up to 10 virtual users over 30 seconds
2. Stay at 10 virtual users for 1 minute
3. Ramp up to 50 virtual users over 30 seconds
4. Stay at 50 virtual users for 1 minute
5. Ramp down to 0 virtual users over 30 seconds

### Admin Dashboard Load Test

1. Ramp up to 5 virtual users over 30 seconds
2. Stay at 5 virtual users for 1 minute
3. Ramp up to 20 virtual users over 30 seconds
4. Stay at 20 virtual users for 1 minute
5. Ramp down to 0 virtual users over 30 seconds

## Thresholds

The tests have the following thresholds:

1. The 95th percentile response time should be less than 500ms
2. The error rate should be less than 1%

## Custom Metrics

### Basic Load Test

1. `generate_email_calls`: Number of calls to the generate email API
2. `get_emails_calls`: Number of calls to the get emails API
3. `delete_email_calls`: Number of calls to the delete email API

### Admin Dashboard Load Test

1. `page_views_calls`: Number of calls to the page views API
2. `email_stats_calls`: Number of calls to the email stats API
3. `analytics_event_calls`: Number of calls to the analytics event API

## Interpreting Results

After running the tests, k6 will output a summary of the results, including:

1. HTTP request metrics (response time, request rate, etc.)
2. Custom metrics (number of API calls)
3. Checks (success rate of assertions)
4. Thresholds (whether the thresholds were met)

If any thresholds are not met, the test will exit with a non-zero exit code, indicating that the performance is not acceptable.

## Continuous Integration

These tests can be integrated into a CI/CD pipeline to ensure that performance regressions are caught early. For example, you can run the tests after each deployment to staging and fail the pipeline if the performance is not acceptable.
