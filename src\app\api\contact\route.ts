import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { logInfo, logError } from '@/lib/logging';
import { ContactMessageCreate } from '@/lib/types/contact';

/**
 * API route for handling contact form submissions
 */
export async function POST(request: NextRequest) {
  try {
    const { name, email, subject, message } = await request.json();

    // Validate input
    if (!name || !email || !subject || !message) {
      return NextResponse.json({
        success: false,
        message: 'All fields are required'
      }, { status: 400 });
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        success: false,
        message: 'Please provide a valid email address'
      }, { status: 400 });
    }

    // Create a new thread ID
    const threadId = uuidv4();

    // Create contact message
    const contactMessage: ContactMessageCreate = {
      name,
      email,
      subject,
      message,
      threadId,
      isAdminReply: false
    };

    // Get Supabase client
    const supabase = await createServerSupabaseClient();

    // Insert into database
    const { data, error } = await supabase
      .from('contact_messages')
      .insert([{
        name: contactMessage.name,
        email: contactMessage.email,
        subject: contactMessage.subject,
        message: contactMessage.message,
        thread_id: contactMessage.threadId,
        parent_id: contactMessage.parentId || null,
        is_admin_reply: contactMessage.isAdminReply
      }])
      .select();

    if (error) {
      logError('contact', 'Failed to save contact message', { error });
      return NextResponse.json({
        success: false,
        message: 'Failed to save your message. Please try again.'
      }, { status: 500 });
    }

    logInfo('contact', 'New contact message received', {
      id: data[0].id,
      email: contactMessage.email,
      subject: contactMessage.subject
    });

    return NextResponse.json({
      success: true,
      message: 'Your message has been sent successfully!'
    });
  } catch (error) {
    logError('contact', 'Error processing contact form submission', { error });
    return NextResponse.json({
      success: false,
      message: 'An error occurred while processing your request.'
    }, { status: 500 });
  }
}
