# Database Configuration System

This document describes the database configuration system implemented in Fademail, which allows for flexible management of database connections.

## Overview

The database configuration system allows administrators to:

1. Create and manage multiple database connection configurations
2. Test connections before activating them
3. Switch between different configurations without redeploying the application
4. Securely store connection credentials

## Configuration Structure

Each database configuration includes:

### General Information
- **Name**: A unique name for the configuration
- **Description**: Optional description of the configuration
- **Active Status**: Whether this configuration is currently active

### Guerrilla Database Configuration
- **Host**: The MySQL server hostname or IP address
- **Port**: The MySQL server port (default: 3306)
- **Database**: The name of the Guerrilla Mail database
- **User**: The MySQL username
- **Password**: The MySQL password
- **Connection Limit**: Maximum number of connections in the pool
- **SSL**: Whether to use SSL for the connection

### Supabase Configuration
- **URL**: The Supabase project URL
- **API Key**: The public (anon) API key for client-side operations
- **Service Role Key**: The secret key for server-side operations (optional)

## Managing Configurations

### Accessing the Configuration Page

The database configuration page is available at:
```
/management-portal-x7z9y2/database-config
```

### Creating a New Configuration

1. Click the "Add Configuration" button
2. Fill in the required fields in the form
3. Test the connection using the "Test Connection" button
4. Save the configuration

### Editing a Configuration

1. Click the "Edit" button next to the configuration
2. Modify the fields as needed
3. Test the connection
4. Save the changes

### Setting a Configuration as Active

1. Click the "Set Active" button next to the configuration
2. Confirm the action
3. The system will automatically deactivate any previously active configuration

### Deleting a Configuration

1. Click the "Delete" button next to the configuration
2. Confirm the action
3. Note: You cannot delete the active configuration

## Technical Implementation

### Database Schema

Configurations are stored in the `database_configurations` table in Supabase with the following structure:

- `id`: Serial primary key
- `name`: Unique name (VARCHAR)
- `description`: Optional description (TEXT)
- `is_active`: Boolean flag for active status
- `guerrilla_config`: JSONB object with Guerrilla MySQL connection parameters
- `supabase_config`: JSONB object with Supabase connection parameters
- `created_at`: Timestamp of creation
- `updated_at`: Timestamp of last update

### Connection Management

The system uses a connection manager that:

1. Retrieves the active configuration from the database
2. Creates and manages connection pools based on the configuration
3. Provides fallback to environment variables if no configuration is found
4. Implements caching to reduce database lookups

### Security Considerations

- Database credentials are stored securely in the database
- Access to the configuration page is restricted to authenticated administrators
- Sensitive operations require confirmation
- Connection testing is performed securely

## Fallback Mechanism

If no active configuration is found in the database, or if there's an error retrieving the configuration, the system falls back to using the environment variables:

- `GUERRILLA_DB_HOST`
- `GUERRILLA_DB_PORT`
- `GUERRILLA_DB_NAME`
- `GUERRILLA_DB_USER`
- `GUERRILLA_DB_PASSWORD`
- `GUERRILLA_DB_CONNECTION_LIMIT`
- `GUERRILLA_DB_SSL`
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`

## Troubleshooting

### Connection Issues

If you experience connection issues:

1. Test the configuration using the "Test Connection" button
2. Check the server logs for detailed error messages
3. Verify that the database servers are accessible from the application server
4. Ensure that the credentials are correct

### Performance Considerations

- The system caches the active configuration to reduce database lookups
- Connection pools are reused to minimize connection overhead
- The cache expires after 5 minutes to ensure changes are picked up

## Best Practices

1. Always test connections before setting them as active
2. Use descriptive names and descriptions for configurations
3. Regularly review and update configurations as needed
4. Consider creating separate configurations for different environments (development, staging, production)
5. Limit access to the configuration page to trusted administrators
