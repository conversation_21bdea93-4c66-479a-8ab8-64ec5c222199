/**
 * API Status Endpoint
 * 
 * This endpoint returns the current status of the API.
 * It's used for health checks and to determine if the site is in maintenance mode.
 */
import { NextRequest, NextResponse } from 'next/server';
import { getConfig } from '@/lib/config/configService';

/**
 * GET /api/status
 * 
 * Returns the current status of the API
 */
export async function GET(request: NextRequest) {
  try {
    // Check if the site is in maintenance mode
    const maintenanceMode = await getConfig('maintenanceMode');
    
    // If in maintenance mode, return 503 Service Unavailable
    if (maintenanceMode) {
      return NextResponse.json(
        {
          status: 'maintenance',
          message: 'Service is currently under maintenance',
          maintenanceMode: true
        },
        {
          status: 503,
          headers: {
            'X-Maintenance-Mode': 'true',
            'Retry-After': '3600'
          }
        }
      );
    }
    
    // Otherwise, return 200 OK
    return NextResponse.json(
      {
        status: 'ok',
        message: 'Service is operational',
        maintenanceMode: false
      },
      {
        status: 200,
        headers: {
          'X-Maintenance-Mode': 'false'
        }
      }
    );
  } catch (error) {
    console.error('Error checking API status:', error);
    
    // On error, return 500 Internal Server Error
    return NextResponse.json(
      {
        status: 'error',
        message: 'Error checking API status',
        maintenanceMode: false
      },
      { status: 500 }
    );
  }
}

/**
 * HEAD /api/status
 * 
 * Lightweight version of GET that only returns headers
 * Used for quick status checks without transferring a response body
 */
export async function HEAD(request: NextRequest) {
  try {
    // Check if the site is in maintenance mode
    const maintenanceMode = await getConfig('maintenanceMode');
    
    // If in maintenance mode, return 503 Service Unavailable
    if (maintenanceMode) {
      return new NextResponse(null, {
        status: 503,
        headers: {
          'X-Maintenance-Mode': 'true',
          'Retry-After': '3600'
        }
      });
    }
    
    // Otherwise, return 200 OK
    return new NextResponse(null, {
      status: 200,
      headers: {
        'X-Maintenance-Mode': 'false'
      }
    });
  } catch (error) {
    console.error('Error checking API status:', error);
    
    // On error, return 500 Internal Server Error
    return new NextResponse(null, { status: 500 });
  }
}
