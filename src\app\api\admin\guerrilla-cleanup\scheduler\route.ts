/**
 * Admin Guerrilla Email Cleanup Scheduler API
 * 
 * Admin version of the Guerrilla email cleanup scheduler API
 * Mirrors the management portal functionality
 */

import { NextRequest, NextResponse } from 'next/server';
import { 
  startGuerrillaCleanupScheduler, 
  stopGuerrillaCleanupScheduler, 
  isGuerrillaCleanupSchedulerRunning 
} from '@/lib/cleanup/guerrillaCleanupScheduler';
import { getConfig, updateConfig } from '@/lib/config/configService';
import { logInfo, logError } from '@/lib/logging';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, intervalMinutes, autoStart } = body;

    if (action === 'start') {
      if (!intervalMinutes || intervalMinutes <= 0) {
        return NextResponse.json({
          success: false,
          error: 'Invalid interval minutes'
        }, { status: 400 });
      }

      // Update configuration
      await updateConfig('guerrillaCleanupIntervalMinutes', intervalMinutes);
      if (autoStart !== undefined) {
        await updateConfig('guerrillaCleanupAutoStart', autoStart);
      }
      
      // Start scheduler
      const started = await startGuerrillaCleanupScheduler();
      
      if (!started) {
        return NextResponse.json({
          success: false,
          error: 'Failed to start Guerrilla cleanup scheduler'
        }, { status: 500 });
      }
      
      logInfo('guerrilla-cleanup', 'Admin Guerrilla cleanup scheduler started', { intervalMinutes });
      
      return NextResponse.json({
        success: true,
        message: 'Guerrilla cleanup scheduler started',
        intervalMinutes
      });
    } else if (action === 'stop') {
      // Stop scheduler
      await stopGuerrillaCleanupScheduler();
      
      logInfo('guerrilla-cleanup', 'Admin Guerrilla cleanup scheduler stopped');
      
      return NextResponse.json({
        success: true,
        message: 'Guerrilla cleanup scheduler stopped'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }
  } catch (error) {
    logError('guerrilla-cleanup', 'Error in admin scheduler API', { error });
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const running = isGuerrillaCleanupSchedulerRunning();
    const intervalMinutes = await getConfig('guerrillaCleanupIntervalMinutes');
    const autoStart = await getConfig('guerrillaCleanupAutoStart');
    const lastRunTime = await getConfig('guerrillaCleanupLastRunTime');
    const schedulerStartTime = await getConfig('guerrillaCleanupSchedulerStartTime');
    
    // Calculate next run time if running
    let nextRunAt = null;
    if (running && intervalMinutes && schedulerStartTime) {
      const startTime = new Date(schedulerStartTime).getTime();
      const intervalMs = intervalMinutes * 60 * 1000;
      const now = Date.now();
      
      // Calculate how many intervals have passed since start
      const elapsedTime = now - startTime;
      const intervalsPassed = Math.floor(elapsedTime / intervalMs);
      
      // Calculate the next run time based on the scheduler start time and intervals
      const nextRunTime = startTime + ((intervalsPassed + 1) * intervalMs);
      nextRunAt = new Date(nextRunTime).toISOString();
    }
    
    return NextResponse.json({
      success: true,
      status: {
        running,
        intervalMinutes,
        nextRunAt,
        autoStart: autoStart !== false // Default to true if not set
      }
    });
  } catch (error) {
    logError('guerrilla-cleanup', 'Error getting admin scheduler status', { error });
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}
