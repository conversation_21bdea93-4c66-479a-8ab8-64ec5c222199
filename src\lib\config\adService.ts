/**
 * Ad configuration service for Fademail
 *
 * This service provides functions to manage ad placements and their settings
 */
import { createServerSupabaseClient } from '@/lib/supabase';
import NodeCache from 'node-cache';
import { AdConfig, AdDisplayOptions, AdSchedule } from './types';
import { logInfo, logError } from '@/lib/logging';
import { shouldDisplayAd } from './adScheduleChecker';
import { Json } from '@/lib/database.types';
import {
  getAdFromCache,
  setAdInCache,
  getDomainAdsFromCache,
  setDomainAdsInCache,
  flushAllCaches
} from './adCacheService';

// Define the database record type
interface AdConfigDB {
  placement_id: string;
  domain: string;
  ad_unit_id: string;
  ad_client_id: string; // Required field with default value in database
  is_enabled: boolean;
  device_types: string[];
  display_options: Json;
  schedule: Json;
  created_at: string;
  updated_at: string;
}

// We'll use the adCacheService instead of this cache
// const adCache = new NodeCache({ stdTTL: 300, checkperiod: 60 });

/**
 * Get all ad placements
 *
 * @returns Array of ad configurations
 */
export async function getAllAdPlacements(): Promise<AdConfig[]> {
  try {
    // We don't use domain-specific caching for all ads, so we'll just query the database

    // Get from database
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ad_config')
      .select('*')
      .order('placement_id');

    if (error) {
      logError('ad', 'Error getting all ad placements', { error });
      return [];
    }

    // Map database results to AdConfig objects with proper type assertion
    const ads: AdConfig[] = data.map((item) => {
      // Cast the item to AdConfigDB to ensure type safety
      const dbItem = item as AdConfigDB;
      return {
        placementId: dbItem.placement_id,
        domain: dbItem.domain,
        adUnitId: dbItem.ad_unit_id,
        adClientId: dbItem.ad_client_id,
        isEnabled: dbItem.is_enabled,
        deviceTypes: dbItem.device_types,
        displayOptions: dbItem.display_options as AdDisplayOptions | undefined,
        schedule: dbItem.schedule as AdSchedule | undefined,
        createdAt: dbItem.created_at,
        updatedAt: dbItem.updated_at
      };
    });

    // We don't need to cache all ads since we use domain-specific caching
    return ads;
  } catch (error) {
    logError('ad', 'Error getting all ad placements', { error });
    return [];
  }
}

/**
 * Get ad placements for a domain
 *
 * @param domain The domain name
 * @returns Array of ad configurations for the domain
 */
export async function getAdPlacementsForDomain(domain: string): Promise<AdConfig[]> {
  try {
    // Check cache first
    const cachedValue = getDomainAdsFromCache(domain);
    if (cachedValue !== null) {
      return cachedValue;
    }

    // Get from database
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ad_config')
      .select('*')
      .eq('domain', domain)
      .order('placement_id');

    if (error) {
      logError('ad', `Error getting ad placements for domain: ${domain}`, { error });
      return [];
    }

    // Map database results to AdConfig objects
    const ads: AdConfig[] = data.map((item) => {
      // Cast the item to AdConfigDB to ensure type safety
      const dbItem = item as AdConfigDB;
      return {
        placementId: dbItem.placement_id,
        domain: dbItem.domain,
        adUnitId: dbItem.ad_unit_id,
        adClientId: dbItem.ad_client_id,
        isEnabled: dbItem.is_enabled,
        deviceTypes: dbItem.device_types,
        displayOptions: dbItem.display_options as AdDisplayOptions | undefined,
        schedule: dbItem.schedule as AdSchedule | undefined,
        createdAt: dbItem.created_at,
        updatedAt: dbItem.updated_at
      };
    });

    // Cache the ads
    setDomainAdsInCache(domain, ads);
    return ads;
  } catch (error) {
    logError('ad', `Error getting ad placements for domain: ${domain}`, { error });
    return [];
  }
}

/**
 * Get enabled ad placements for a domain
 *
 * @param domain The domain name
 * @returns Array of enabled ad configurations for the domain
 */
export async function getEnabledAdPlacementsForDomain(domain: string): Promise<AdConfig[]> {
  try {
    // We'll use the same cache as getAdPlacementsForDomain
    // and filter the results for enabled ads only
    const cachedValue = getDomainAdsFromCache(domain);
    if (cachedValue !== null) {
      return cachedValue.filter(ad => ad.isEnabled);
    }

    // Get from database
    const supabase = createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ad_config')
      .select('*')
      .eq('domain', domain)
      .eq('is_enabled', true)
      .order('placement_id');

    if (error) {
      logError('ad', `Error getting enabled ad placements for domain: ${domain}`, { error });
      return [];
    }

    // Map database results to AdConfig objects
    const ads: AdConfig[] = data.map((item) => {
      // Cast the item to AdConfigDB to ensure type safety
      const dbItem = item as AdConfigDB;
      return {
        placementId: dbItem.placement_id,
        domain: dbItem.domain,
        adUnitId: dbItem.ad_unit_id,
        adClientId: dbItem.ad_client_id,
        isEnabled: dbItem.is_enabled,
        deviceTypes: dbItem.device_types,
        displayOptions: dbItem.display_options as AdDisplayOptions | undefined,
        schedule: dbItem.schedule as AdSchedule | undefined,
        createdAt: dbItem.created_at,
        updatedAt: dbItem.updated_at
      };
    });

    // We don't need to cache separately since we're using the same cache
    // as getAdPlacementsForDomain
    return ads;
  } catch (error) {
    logError('ad', `Error getting enabled ad placements for domain: ${domain}`, { error });
    return [];
  }
}

/**
 * Get an ad placement
 *
 * @param placementId The placement ID
 * @param domain The domain name
 * @returns The ad configuration or null if not found
 */
export async function getAdPlacement(placementId: string, domain: string): Promise<AdConfig | null> {
  try {
    // Log the request parameters for debugging
    console.log(`getAdPlacement called with placementId: "${placementId}", domain: "${domain}"`);

    // Ensure the placementId is properly sanitized
    const sanitizedPlacementId = placementId.replace(/[^a-zA-Z0-9-_]/g, '_');
    if (sanitizedPlacementId !== placementId) {
      console.log(`Sanitized placementId from "${placementId}" to "${sanitizedPlacementId}"`);
    }

    // Check cache first
    const cachedValue = getAdFromCache(sanitizedPlacementId, domain);
    if (cachedValue !== null) {
      console.log(`Cache hit for ad placement: ${sanitizedPlacementId}, domain: ${domain}`);
      return cachedValue;
    }

    // Get from database
    const supabase = createServerSupabaseClient();
    console.log(`Querying database for ad placement: ${sanitizedPlacementId}, domain: ${domain}`);

    // First try to get all ad placements for the domain to see what's available
    const { data: allDomainAds, error: domainError } = await supabase
      .from('ad_config')
      .select('placement_id, domain')
      .eq('domain', domain);

    console.log(`All ad placements for domain ${domain}:`,
      domainError ? `Error: ${JSON.stringify(domainError)}` :
      `Found ${allDomainAds?.length || 0} placements: ${JSON.stringify(allDomainAds)}`);

    // Now try to get the specific ad placement
    const { data, error } = await supabase
      .from('ad_config')
      .select('*')
      .eq('placement_id', sanitizedPlacementId)
      .eq('domain', domain)
      .single();

    console.log(`Query for ad placement ${placementId} on domain ${domain}:`,
      error ? `Error: ${JSON.stringify(error)}` :
      `Success: ${data ? 'Found' : 'Not found'}`);


    if (error || !data) {
      console.log(`Error or no data found for ad placement: ${placementId}, domain: ${domain}`);
      if (error) {
        console.error(`Database error details:`, error);
      }
      return null;
    }

    console.log(`Successfully found ad placement in database: ${placementId}, domain: ${domain}`);

    // Cast the data to AdConfigDB type to ensure proper type safety
    const dbData = data as AdConfigDB;

    // Map database result to AdConfig object
    const adConfig: AdConfig = {
      placementId: dbData.placement_id,
      domain: dbData.domain,
      adUnitId: dbData.ad_unit_id,
      adClientId: dbData.ad_client_id,
      isEnabled: dbData.is_enabled,
      deviceTypes: dbData.device_types,
      displayOptions: dbData.display_options as AdDisplayOptions | undefined,
      schedule: dbData.schedule as AdSchedule | undefined,
      createdAt: dbData.created_at,
      updatedAt: dbData.updated_at
    };

    // Cache the ad
    setAdInCache(adConfig);
    return adConfig;
  } catch (error) {
    logError('ad', `Error getting ad placement: ${placementId} for domain: ${domain}`, { error });
    return null;
  }
}

/**
 * Add a new ad placement
 *
 * @param placementId The placement ID
 * @param domain The domain name
 * @param adUnitId The ad unit ID
 * @param isEnabled Whether the ad is enabled
 * @param deviceTypes The device types to show the ad on
 * @param displayOptions The display options
 * @returns The new ad configuration or null if failed
 * @throws Error if there's a database error
 */
export async function addAdPlacement(
  placementId: string,
  domain: string,
  adUnitId: string,
  adClientId: string,
  isEnabled: boolean = true,
  deviceTypes: string[] = ['desktop', 'tablet', 'mobile'],
  displayOptions?: AdDisplayOptions
): Promise<AdConfig | null> {
  // Ensure the placementId is properly sanitized
  const sanitizedPlacementId = placementId.replace(/[^a-zA-Z0-9-_]/g, '_');
  if (sanitizedPlacementId !== placementId) {
    console.log(`Sanitized placementId from "${placementId}" to "${sanitizedPlacementId}"`);
  }

  console.log(`Adding ad placement with placementId: "${sanitizedPlacementId}", domain: "${domain}"`);

  const supabase = createServerSupabaseClient();

  const now = new Date().toISOString();

  const { data, error } = await supabase
    .from('ad_config')
    .insert({
      placement_id: sanitizedPlacementId,
      domain,
      ad_unit_id: adUnitId,
      ad_client_id: adClientId,
      is_enabled: isEnabled,
      device_types: deviceTypes,
      display_options: displayOptions ? displayOptions as unknown as Json : null,
      created_at: now,
      updated_at: now
    })
    .select()
    .single();

  if (error) {
    logError('ad', `Error adding ad placement: ${placementId} for domain: ${domain}`, { error });
    // Throw the error so it can be caught and handled by the caller
    throw error;
  }

  if (!data) {
    logError('ad', `No data returned when adding ad placement: ${placementId} for domain: ${domain}`);
    return null;
  }

  // Cast the data to AdConfigDB type to ensure proper type safety
  const dbData = data as AdConfigDB;

  // Map database result to AdConfig object
  const adConfig: AdConfig = {
    placementId: dbData.placement_id,
    domain: dbData.domain,
    adUnitId: dbData.ad_unit_id,
    adClientId: dbData.ad_client_id,
    isEnabled: dbData.is_enabled,
    deviceTypes: dbData.device_types,
    displayOptions: dbData.display_options as AdDisplayOptions | undefined,
    schedule: dbData.schedule as AdSchedule | undefined,
    createdAt: dbData.created_at,
    updatedAt: dbData.updated_at
  };

  // Clear ad caches
  clearAdCache();

  logInfo('ad', `Added ad placement: ${placementId} for domain: ${domain}`, { adConfig });
  return adConfig;
}

/**
 * Update an ad placement
 *
 * @param placementId The placement ID
 * @param domain The domain name
 * @param updates The updates to apply
 * @returns The updated ad configuration or null if failed
 */
export async function updateAdPlacement(
  placementId: string,
  domain: string,
  updates: {
    adUnitId?: string;
    adClientId?: string;
    isEnabled?: boolean;
    deviceTypes?: string[];
    displayOptions?: AdDisplayOptions;
    schedule?: AdSchedule;
  }
): Promise<AdConfig> {
  console.log(`updateAdPlacement called with placementId: "${placementId}", domain: "${domain}"`);
  console.log(`Update data:`, JSON.stringify(updates, null, 2));

  // Ensure the placementId is properly sanitized
  const sanitizedPlacementId = placementId.replace(/[^a-zA-Z0-9-_]/g, '_');
  if (sanitizedPlacementId !== placementId) {
    console.log(`Sanitized placementId from "${placementId}" to "${sanitizedPlacementId}"`);
  }

  // Get the current ad placement
  const currentAd = await getAdPlacement(sanitizedPlacementId, domain);

  if (!currentAd) {
    console.error(`Ad placement not found for update: ${placementId} for domain: ${domain}`);

    // Try to get the ad placement directly from the database
    console.log(`Trying direct database query for ad placement: ${placementId}, domain: ${domain}`);

    const supabase = createServerSupabaseClient();

    // Query the database directly
    const { data, error } = await supabase
      .from('ad_config')
      .select('*')
      .eq('placement_id', placementId)
      .eq('domain', domain)
      .single();

    if (error || !data) {
      logError('ad', `Ad placement not found: ${placementId} for domain: ${domain}`);
      throw new Error(`Ad placement not found: ${placementId} for domain: ${domain}`);
    }

    // Cast the data to AdConfigDB type to ensure proper type safety
    const dbData = data as AdConfigDB;

    // Map database result to AdConfig object
    const adConfig: AdConfig = {
      placementId: dbData.placement_id,
      domain: dbData.domain,
      adUnitId: dbData.ad_unit_id,
      adClientId: dbData.ad_client_id ?? 'ca-pub-8397529755029714',
      isEnabled: dbData.is_enabled,
      deviceTypes: dbData.device_types,
      displayOptions: dbData.display_options as AdDisplayOptions | undefined,
      schedule: dbData.schedule as AdSchedule | undefined,
      createdAt: dbData.created_at,
      updatedAt: dbData.updated_at
    };

    // Cache the ad
    setAdInCache(adConfig);

    // Continue with the update using the ad we just found
    return await updateAdPlacementInternal(adConfig, updates);
  }

  console.log(`Found existing ad placement to update:`, JSON.stringify(currentAd, null, 2));

  // Use the internal update function
  return await updateAdPlacementInternal(currentAd, updates);
}

/**
 * Internal function to update an ad placement
 *
 * @param currentAd The current ad placement
 * @param updates The updates to apply
 * @returns The updated ad configuration
 */
async function updateAdPlacementInternal(
  currentAd: AdConfig,
  updates: {
    adUnitId?: string;
    adClientId?: string; // Add this property to match the type in updateAdPlacement
    isEnabled?: boolean;
    deviceTypes?: string[];
    displayOptions?: AdDisplayOptions;
    schedule?: AdSchedule;
  }
): Promise<AdConfig> {
  const placementId = currentAd.placementId;
  const domain = currentAd.domain;

  const supabase = createServerSupabaseClient();

  // Prepare update object
  const updateObj: any = {
    updated_at: new Date().toISOString()
  };

  if (updates.adUnitId !== undefined) {
    updateObj.ad_unit_id = updates.adUnitId;
  }

  if (updates.adClientId !== undefined) {
    updateObj.ad_client_id = updates.adClientId;
  }

  if (updates.isEnabled !== undefined) {
    updateObj.is_enabled = updates.isEnabled;
  }

  if (updates.deviceTypes !== undefined) {
    updateObj.device_types = updates.deviceTypes;
  }

  // Handle displayOptions explicitly, including the case where it's an empty object
  if (updates.displayOptions !== undefined) {
    // If displayOptions is an empty object, set it to null in the database
    // This effectively removes the display options
    if (updates.displayOptions && Object.keys(updates.displayOptions).length === 0) {
      updateObj.display_options = null;
    } else {
      updateObj.display_options = updates.displayOptions as unknown as Json;
    }
  }

  if (updates.schedule !== undefined) {
    updateObj.schedule = updates.schedule as unknown as Json;
  }

  console.log(`Executing update query for ad placement: ${placementId}, domain: ${domain}`);
  console.log(`Update object:`, JSON.stringify(updateObj, null, 2));

  const { data, error } = await supabase
    .from('ad_config')
    .update(updateObj)
    .eq('placement_id', placementId)
    .eq('domain', domain)
    .select()
    .single();

  console.log(`Update query result:`, error ? `Error: ${JSON.stringify(error)}` : `Success: ${JSON.stringify(data)}`);

  if (error) {
    logError('ad', `Error updating ad placement: ${placementId} for domain: ${domain}`, { error });
    throw error;
  }

  if (!data) {
    logError('ad', `No data returned when updating ad placement: ${placementId} for domain: ${domain}`);
    throw new Error(`No data returned when updating ad placement: ${placementId} for domain: ${domain}`);
  }

  // Cast the data to AdConfigDB type to ensure proper type safety
  const dbData = data as AdConfigDB;

  // Map database result to AdConfig object
  const adConfig: AdConfig = {
    placementId: dbData.placement_id,
    domain: dbData.domain,
    adUnitId: dbData.ad_unit_id,
    adClientId: dbData.ad_client_id,
    isEnabled: dbData.is_enabled,
    deviceTypes: dbData.device_types,
    displayOptions: dbData.display_options as AdDisplayOptions | undefined,
    schedule: dbData.schedule as AdSchedule | undefined,
    createdAt: dbData.created_at,
    updatedAt: dbData.updated_at
  };

  // Clear ad caches
  clearAdCache();

  logInfo('ad', `Updated ad placement: ${placementId} for domain: ${domain}`, { adConfig });
  return adConfig;
}

/**
 * Delete an ad placement
 *
 * @param placementId The placement ID
 * @param domain The domain name
 * @returns True if successful, false otherwise
 */
export async function deleteAdPlacement(placementId: string, domain: string): Promise<boolean> {
  try {
    // Ensure the placementId is properly sanitized
    const sanitizedPlacementId = placementId.replace(/[^a-zA-Z0-9-_]/g, '_');
    if (sanitizedPlacementId !== placementId) {
      console.log(`Sanitized placementId from "${placementId}" to "${sanitizedPlacementId}"`);
    }

    console.log(`Deleting ad placement with placementId: "${sanitizedPlacementId}", domain: "${domain}"`);

    const supabase = createServerSupabaseClient();

    const { error } = await supabase
      .from('ad_config')
      .delete()
      .eq('placement_id', sanitizedPlacementId)
      .eq('domain', domain);

    if (error) {
      logError('ad', `Error deleting ad placement: ${placementId} for domain: ${domain}`, { error });
      return false;
    }

    // Clear ad caches
    clearAdCache();

    logInfo('ad', `Deleted ad placement: ${placementId} for domain: ${domain}`);
    return true;
  } catch (error) {
    logError('ad', `Error deleting ad placement: ${placementId} for domain: ${domain}`, { error });
    return false;
  }
}

/**
 * Enable or disable all ad placements for a domain
 *
 * @param domain The domain name
 * @param enabled Whether to enable or disable ads
 * @returns True if successful, false otherwise
 */
export async function setAllAdPlacementsEnabled(domain: string, enabled: boolean): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();

    const { error } = await supabase
      .from('ad_config')
      .update({
        is_enabled: enabled,
        updated_at: new Date().toISOString()
      })
      .eq('domain', domain);

    if (error) {
      logError('ad', `Error ${enabled ? 'enabling' : 'disabling'} all ad placements for domain: ${domain}`, { error });
      return false;
    }

    // Clear ad caches
    clearAdCache();

    logInfo('ad', `${enabled ? 'Enabled' : 'Disabled'} all ad placements for domain: ${domain}`);
    return true;
  } catch (error) {
    logError('ad', `Error ${enabled ? 'enabling' : 'disabling'} all ad placements for domain: ${domain}`, { error });
    return false;
  }
}

/**
 * Get ad placements for client-side rendering
 *
 * This function returns only the necessary information for client-side rendering
 * to avoid exposing sensitive information
 *
 * @param domain The domain name
 * @param deviceType The device type
 * @returns Array of ad configurations for client-side rendering
 */
export async function getClientAdPlacements(
  domain: string,
  deviceType: 'desktop' | 'tablet' | 'mobile'
): Promise<Pick<AdConfig, 'placementId' | 'adUnitId' | 'displayOptions' | 'schedule'>[]> {
  try {
    // Get enabled ad placements for the domain
    const ads = await getEnabledAdPlacementsForDomain(domain);

    // Filter by device type and schedule
    const filteredAds = ads.filter(ad => {
      // Check device type
      const deviceTypeMatch = ad.deviceTypes.includes('all') || ad.deviceTypes.includes(deviceType);

      // Check schedule
      const scheduleMatch = shouldDisplayAd(ad.schedule);

      return deviceTypeMatch && scheduleMatch;
    });

    // Return only the necessary information
    return filteredAds.map(ad => ({
      placementId: ad.placementId,
      adUnitId: ad.adUnitId,
      displayOptions: ad.displayOptions,
      schedule: ad.schedule
    }));
  } catch (error) {
    logError('ad', `Error getting client ad placements for domain: ${domain}`, { error });
    return [];
  }
}

/**
 * Clear the ad cache
 */
export function clearAdCache(): void {
  flushAllCaches();
  logInfo('ad', 'Cleared ad cache');
}

/**
 * Track ad impression
 *
 * @param adId Ad ID to track impression for
 * @returns Promise<boolean> True if successful, false otherwise
 */
export async function trackAdImpression(adId: string): Promise<boolean> {
  try {
    logInfo('ad', `Tracked impression for ad: ${adId}`);
    // In a real implementation, this would store the impression in the database
    // For now, we just log it
    return true;
  } catch (error) {
    logError('ad', `Error tracking impression for ad: ${adId}`, { error });
    return false;
  }
}

/**
 * Track ad click
 *
 * @param adId Ad ID to track click for
 * @returns Promise<boolean> True if successful, false otherwise
 */
export async function trackAdClick(adId: string): Promise<boolean> {
  try {
    logInfo('ad', `Tracked click for ad: ${adId}`);
    // In a real implementation, this would store the click in the database
    // For now, we just log it
    return true;
  } catch (error) {
    logError('ad', `Error tracking click for ad: ${adId}`, { error });
    return false;
  }
}

/**
 * Get ad performance metrics
 *
 * @param placementId The placement ID
 * @param domain The domain name
 * @param startDate The start date for the metrics
 * @param endDate The end date for the metrics
 * @returns Object containing performance metrics
 */
export async function getAdPerformanceMetrics(
  placementId: string,
  domain: string,
  startDate: string,
  endDate: string
): Promise<{
  impressions: number;
  clicks: number;
  ctr: number;
  dailyData: Array<{
    date: string;
    impressions: number;
    clicks: number;
  }>;
}> {
  try {
    // In a real implementation, this would query the database for actual metrics
    // For now, we'll return mock data

    // Parse dates
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Generate mock daily data
    const dailyData = [];
    let totalImpressions = 0;
    let totalClicks = 0;

    // Create a copy of the start date to iterate through
    const currentDate = new Date(start);

    // Generate data for each day in the range
    while (currentDate <= end) {
      // Generate random metrics for this day
      const impressions = Math.floor(Math.random() * 1000) + 100;
      const clicks = Math.floor(Math.random() * impressions * 0.1);

      dailyData.push({
        date: currentDate.toISOString().split('T')[0],
        impressions,
        clicks
      });

      totalImpressions += impressions;
      totalClicks += clicks;

      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Calculate CTR (Click-Through Rate)
    const ctr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

    logInfo('ad', `Retrieved performance metrics for ad: ${placementId} on domain: ${domain}`, {
      startDate,
      endDate,
      totalImpressions,
      totalClicks,
      ctr
    });

    return {
      impressions: totalImpressions,
      clicks: totalClicks,
      ctr: parseFloat(ctr.toFixed(2)),
      dailyData
    };
  } catch (error) {
    logError('ad', `Error getting performance metrics for ad: ${placementId} on domain: ${domain}`, { error });

    // Return empty metrics on error
    return {
      impressions: 0,
      clicks: 0,
      ctr: 0,
      dailyData: []
    };
  }
}

// Export the service object for compatibility with productionSafeguards
export const adService = {
  getAllAdPlacements,
  getAdPlacementsForDomain,
  getEnabledAdPlacementsForDomain,
  getAdPlacement,
  addAdPlacement,
  updateAdPlacement,
  deleteAdPlacement,
  clearAdCache,
  getAdPerformanceMetrics
};

export default adService;
