/**
 * IP Range Blocking System
 * Provides functionality to block entire IP ranges using CIDR notation
 * Supports both IPv4 and IPv6 ranges with database persistence
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface BlockedIPRange {
  id: number;
  ip_range: string;
  reason: string;
  blocked_at: string;
  blocked_by: string | null;
  expires_at: string | null;
  is_active: boolean;
}

interface IPBlockResult {
  blocked: boolean;
  reason?: string;
  range?: string;
}

/**
 * Check if an IP address is within any blocked range
 */
export async function isIPBlocked(ipAddress: string): Promise<IPBlockResult> {
  try {
    // Query database for active blocked ranges that contain this IP
    const { data: blockedRanges, error } = await supabase
      .from('blocked_ip_ranges')
      .select('*')
      .eq('is_active', true)
      .or(`expires_at.is.null,expires_at.gt.${new Date().toISOString()}`);

    if (error) {
      console.error('Error checking blocked IP ranges:', error);
      return { blocked: false };
    }

    // Check each range using PostgreSQL's inet operators
    for (const range of blockedRanges || []) {
      const { data: isInRange, error: rangeError } = await supabase
        .rpc('is_ip_in_range', {
          ip_address: ipAddress,
          ip_range: range.ip_range
        });

      if (rangeError) {
        console.error('Error checking IP range:', rangeError);
        continue;
      }

      if (isInRange) {
        return {
          blocked: true,
          reason: range.reason,
          range: range.ip_range
        };
      }
    }

    return { blocked: false };
  } catch (error) {
    console.error('Error in isIPBlocked:', error);
    return { blocked: false };
  }
}

/**
 * Add a new IP range to the block list
 */
export async function blockIPRange(
  ipRange: string,
  reason: string,
  blockedBy: string,
  expiresAt?: Date
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('blocked_ip_ranges')
      .insert({
        ip_range: ipRange,
        reason,
        blocked_by: blockedBy,
        expires_at: expiresAt?.toISOString() || null,
        is_active: true
      });

    if (error) {
      console.error('Error blocking IP range:', error);
      return false;
    }

    // Log security event
    await logSecurityEvent('ip_range_blocked', {
      ip_range: ipRange,
      reason,
      blocked_by: blockedBy
    });

    return true;
  } catch (error) {
    console.error('Error in blockIPRange:', error);
    return false;
  }
}

/**
 * Remove an IP range from the block list
 */
export async function unblockIPRange(ipRange: string, unblockedBy: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('blocked_ip_ranges')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('ip_range', ipRange);

    if (error) {
      console.error('Error unblocking IP range:', error);
      return false;
    }

    // Log security event
    await logSecurityEvent('ip_range_unblocked', {
      ip_range: ipRange,
      unblocked_by: unblockedBy
    });

    return true;
  } catch (error) {
    console.error('Error in unblockIPRange:', error);
    return false;
  }
}

/**
 * Get all active blocked IP ranges
 */
export async function getBlockedIPRanges(): Promise<BlockedIPRange[]> {
  try {
    const { data, error } = await supabase
      .from('blocked_ip_ranges')
      .select('*')
      .eq('is_active', true)
      .order('blocked_at', { ascending: false });

    if (error) {
      console.error('Error fetching blocked IP ranges:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getBlockedIPRanges:', error);
    return [];
  }
}

/**
 * Log security events
 */
async function logSecurityEvent(eventType: string, metadata: any): Promise<void> {
  try {
    await supabase
      .from('security_events')
      .insert({
        event_type: eventType,
        severity: 'high',
        description: `IP range blocking action: ${eventType}`,
        metadata,
        action_taken: eventType
      });
  } catch (error) {
    console.error('Error logging security event:', error);
  }
}

/**
 * Middleware function to check IP blocking
 */
export async function checkIPBlocking(req: any, res: any, next: any) {
  try {
    const clientIP = getClientIP(req);
    
    if (!clientIP) {
      return next();
    }

    const blockResult = await isIPBlocked(clientIP);
    
    if (blockResult.blocked) {
      // Log the blocked attempt
      await logSecurityEvent('blocked_ip_access_attempt', {
        ip_address: clientIP,
        reason: blockResult.reason,
        range: blockResult.range,
        user_agent: req.headers['user-agent'],
        path: req.path
      });

      return res.status(403).json({
        error: 'Access denied',
        message: 'Your IP address has been blocked due to suspicious activity'
      });
    }

    next();
  } catch (error) {
    console.error('Error in IP blocking middleware:', error);
    next(); // Continue on error to avoid breaking the service
  }
}

/**
 * Extract client IP from request
 */
function getClientIP(req: any): string | null {
  return req.headers['x-forwarded-for']?.split(',')[0] ||
         req.headers['x-real-ip'] ||
         req.connection?.remoteAddress ||
         req.socket?.remoteAddress ||
         null;
}

/**
 * Emergency block function for immediate threat response
 */
export async function emergencyBlockIP(
  ipAddress: string,
  reason: string = 'Emergency security response'
): Promise<boolean> {
  try {
    // Block the specific IP
    const success = await blockIPRange(`${ipAddress}/32`, reason, 'Emergency System');
    
    if (success) {
      console.log(`🚨 EMERGENCY BLOCK: IP ${ipAddress} blocked immediately`);
      
      // Also log to security events with high priority
      await logSecurityEvent('emergency_ip_block', {
        ip_address: ipAddress,
        reason,
        timestamp: new Date().toISOString(),
        severity: 'critical'
      });
    }
    
    return success;
  } catch (error) {
    console.error('Error in emergency IP block:', error);
    return false;
  }
}

export default {
  isIPBlocked,
  blockIPRange,
  unblockIPRange,
  getBlockedIPRanges,
  checkIPBlocking,
  emergencyBlockIP
};
