#!/usr/bin/env node

/**
 * Analytics Data Cleanup Script
 * 
 * This script can be run manually or scheduled via cron to clean up
 * old analytics data according to retention policies.
 * 
 * Usage:
 *   node scripts/analytics-cleanup.js [options]
 * 
 * Options:
 *   --dry-run          Show what would be deleted without actually deleting
 *   --events-days      Days to retain analytics events (default: 90)
 *   --sessions-days    Days to retain session data (default: 30)
 *   --incomplete-hours Hours to retain incomplete sessions (default: 24)
 *   --verbose          Show detailed output
 *   --stats-only       Only show cleanup statistics
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

// Configuration
const config = {
  dryRun: process.argv.includes('--dry-run'),
  verbose: process.argv.includes('--verbose'),
  statsOnly: process.argv.includes('--stats-only'),
  eventsDays: getArgValue('--events-days', 90),
  sessionsDays: getArgValue('--sessions-days', 30),
  incompleteHours: getArgValue('--incomplete-hours', 24),
};

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Get command line argument value
 */
function getArgValue(argName, defaultValue) {
  const argIndex = process.argv.indexOf(argName);
  if (argIndex !== -1 && argIndex + 1 < process.argv.length) {
    const value = process.argv[argIndex + 1];
    return isNaN(value) ? defaultValue : parseInt(value);
  }
  return defaultValue;
}

/**
 * Log message with timestamp
 */
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level}] ${message}`);
}

/**
 * Get cleanup statistics
 */
async function getCleanupStats() {
  try {
    // Get analytics events count and oldest
    const { data: eventsData } = await supabase
      .from('analytics_events')
      .select('timestamp')
      .order('timestamp', { ascending: true })
      .limit(1);

    const { count: eventsCount } = await supabase
      .from('analytics_events')
      .select('*', { count: 'exact', head: true });

    // Get sessions count and oldest
    const { data: sessionsData } = await supabase
      .from('session_analytics')
      .select('session_start_time')
      .eq('is_active', false)
      .order('session_start_time', { ascending: true })
      .limit(1);

    const { count: sessionsCount } = await supabase
      .from('session_analytics')
      .select('*', { count: 'exact', head: true });

    // Get incomplete sessions count
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - config.incompleteHours);

    const { count: incompleteSessionsCount } = await supabase
      .from('session_analytics')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .lt('last_seen_at', cutoffDate.toISOString());

    return {
      eventsCount: eventsCount || 0,
      sessionsCount: sessionsCount || 0,
      incompleteSessionsCount: incompleteSessionsCount || 0,
      oldestEvent: eventsData?.[0]?.timestamp || null,
      oldestSession: sessionsData?.[0]?.session_start_time || null,
    };
  } catch (error) {
    log(`Error getting cleanup stats: ${error.message}`, 'ERROR');
    throw error;
  }
}

/**
 * Clean up analytics events
 */
async function cleanupAnalyticsEvents() {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - config.eventsDays);

  log(`Cleaning up analytics events older than ${config.eventsDays} days (before ${cutoffDate.toISOString()})`);

  if (config.dryRun) {
    const { count } = await supabase
      .from('analytics_events')
      .select('*', { count: 'exact', head: true })
      .lt('timestamp', cutoffDate.toISOString());

    log(`DRY RUN: Would delete ${count || 0} analytics events`);
    return { deletedCount: count || 0, dryRun: true };
  }

  const { data, error } = await supabase
    .from('analytics_events')
    .delete()
    .lt('timestamp', cutoffDate.toISOString())
    .select('id');

  if (error) {
    log(`Error cleaning up analytics events: ${error.message}`, 'ERROR');
    throw error;
  }

  const deletedCount = data?.length || 0;
  log(`Deleted ${deletedCount} analytics events`);
  return { deletedCount, dryRun: false };
}

/**
 * Clean up session data
 */
async function cleanupSessionData() {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - config.sessionsDays);

  log(`Cleaning up completed sessions older than ${config.sessionsDays} days (before ${cutoffDate.toISOString()})`);

  if (config.dryRun) {
    const { count } = await supabase
      .from('session_analytics')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', false)
      .lt('session_start_time', cutoffDate.toISOString());

    log(`DRY RUN: Would delete ${count || 0} completed sessions`);
    return { deletedCount: count || 0, dryRun: true };
  }

  const { data, error } = await supabase
    .from('session_analytics')
    .delete()
    .eq('is_active', false)
    .lt('session_start_time', cutoffDate.toISOString())
    .select('session_id');

  if (error) {
    log(`Error cleaning up session data: ${error.message}`, 'ERROR');
    throw error;
  }

  const deletedCount = data?.length || 0;
  log(`Deleted ${deletedCount} completed sessions`);
  return { deletedCount, dryRun: false };
}

/**
 * Clean up incomplete sessions
 */
async function cleanupIncompleteSessions() {
  const cutoffDate = new Date();
  cutoffDate.setHours(cutoffDate.getHours() - config.incompleteHours);

  log(`Cleaning up incomplete sessions older than ${config.incompleteHours} hours (before ${cutoffDate.toISOString()})`);

  if (config.dryRun) {
    const { count } = await supabase
      .from('session_analytics')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .lt('last_seen_at', cutoffDate.toISOString());

    log(`DRY RUN: Would delete ${count || 0} incomplete sessions`);
    return { deletedCount: count || 0, dryRun: true };
  }

  const { data, error } = await supabase
    .from('session_analytics')
    .delete()
    .eq('is_active', true)
    .lt('last_seen_at', cutoffDate.toISOString())
    .select('session_id');

  if (error) {
    log(`Error cleaning up incomplete sessions: ${error.message}`, 'ERROR');
    throw error;
  }

  const deletedCount = data?.length || 0;
  log(`Deleted ${deletedCount} incomplete sessions`);
  return { deletedCount, dryRun: false };
}

/**
 * Main cleanup function
 */
async function main() {
  const startTime = Date.now();
  
  log('Starting analytics cleanup script');
  log(`Configuration: ${JSON.stringify(config, null, 2)}`);

  try {
    // Get initial statistics
    const initialStats = await getCleanupStats();
    
    log('Initial Statistics:');
    log(`  Analytics Events: ${initialStats.eventsCount}`);
    log(`  Sessions: ${initialStats.sessionsCount}`);
    log(`  Incomplete Sessions: ${initialStats.incompleteSessionsCount}`);
    log(`  Oldest Event: ${initialStats.oldestEvent || 'None'}`);
    log(`  Oldest Session: ${initialStats.oldestSession || 'None'}`);

    if (config.statsOnly) {
      log('Stats-only mode, exiting');
      return;
    }

    // Run cleanup operations
    const results = {
      events: await cleanupAnalyticsEvents(),
      sessions: await cleanupSessionData(),
      incomplete: await cleanupIncompleteSessions(),
    };

    // Get final statistics
    const finalStats = await getCleanupStats();
    
    const totalDeleted = results.events.deletedCount + results.sessions.deletedCount + results.incomplete.deletedCount;
    const duration = Date.now() - startTime;

    log('Cleanup Summary:');
    log(`  Events deleted: ${results.events.deletedCount}`);
    log(`  Sessions deleted: ${results.sessions.deletedCount}`);
    log(`  Incomplete sessions deleted: ${results.incomplete.deletedCount}`);
    log(`  Total deleted: ${totalDeleted}`);
    log(`  Duration: ${duration}ms`);
    log(`  Dry run: ${config.dryRun}`);

    log('Final Statistics:');
    log(`  Analytics Events: ${finalStats.eventsCount}`);
    log(`  Sessions: ${finalStats.sessionsCount}`);
    log(`  Incomplete Sessions: ${finalStats.incompleteSessionsCount}`);

    log('Analytics cleanup completed successfully');

  } catch (error) {
    log(`Analytics cleanup failed: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    log(`Unhandled error: ${error.message}`, 'ERROR');
    process.exit(1);
  });
}
