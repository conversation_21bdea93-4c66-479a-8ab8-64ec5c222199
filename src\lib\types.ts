/**
 * Types and interfaces for the Fademail application
 */

/**
 * Represents a temporary email address in the local database
 */
export interface TempEmail {
  id: number;
  emailAddress: string;
  creationTime: Date;
  expirationDate: Date;
}

/**
 * Represents an email in the guerrilla database
 */
export interface GuerrillaEmail {
  mail_id: number;
  message_id: string;
  date: Date;
  from: string;
  to: string;
  reply_to: string;
  sender: string;
  subject: string;
  body: string;
  mail: string;
  spam_score: number;
  hash: string;
  content_type: string;
  recipient: string;
  has_attach: boolean;
  ip_addr: Buffer;
  return_path: string;
  is_tls: boolean;
}

/**
 * Represents a parsed email for the frontend
 */
export interface ParsedEmail {
  mail_id: string;
  from: string;
  fromName: string;
  fromEmail: string;
  to: string;
  subject: string;
  date: string;
  text: string;
  html: string;
  attachments: Attachment[];
}

/**
 * Represents an email attachment
 */
export interface Attachment {
  filename: string;
  contentType: string;
  content: Buffer;
  size: number;
}

/**
 * Represents the response from the email generation API
 */
export interface EmailGenerationResponse {
  emailAddress: string;
  expirationDate: Date;
  success: boolean;
  message?: string;
}

/**
 * Represents the response from the email retrieval API
 */
export interface EmailRetrievalResponse {
  emails: ParsedEmail[];
  totalCount: number;
  page: number;
  pageSize: number;
  success: boolean;
  message?: string;
}

/**
 * Represents the response from the cleanup API
 */
export interface CleanupResponse {
  deletedCount: number;
  success: boolean;
  message?: string;
}

/**
 * Represents the pagination parameters for the email retrieval API
 */
export interface PaginationParams {
  page: number;
  pageSize: number;
}

/**
 * Represents the sorting parameters for the email retrieval API
 */
export interface SortingParams {
  sortBy: 'date' | 'from' | 'subject';
  sortOrder: 'asc' | 'desc';
}

/**
 * Represents the filtering parameters for the email retrieval API
 */
export interface FilteringParams {
  search?: string;
  from?: string;
  to?: string;
  subject?: string;
  hasAttachments?: boolean;
  startDate?: Date;
  endDate?: Date;
}
