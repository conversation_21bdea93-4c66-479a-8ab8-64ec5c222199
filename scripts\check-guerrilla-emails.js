/**
 * <PERSON><PERSON><PERSON> to check if there are any emails for a specific address in the guerrilla database
 *
 * Usage: node scripts/check-guerrilla-emails.js <EMAIL>
 */
const mysql = require('mysql2/promise');

// Guerrilla database configuration
const guerrillaDbConfig = {
  host: process.env.GUERRILLA_DB_HOST || 'localhost',
  user: process.env.GUERRILLA_DB_USER || 'user',
  password: process.env.GUERRILLA_DB_PASSWORD || '',
  database: process.env.GUERRILLA_DB_NAME || 'guerrilla_db',
};

async function checkGuerrillaEmails() {
  // Get the email address from command line arguments
  const emailAddress = process.argv[2];

  if (!emailAddress) {
    console.error('Please provide an email address');
    console.error('Usage: node scripts/check-guerrilla-emails.js <EMAIL>');
    process.exit(1);
  }

  console.log(`Checking emails for: ${emailAddress}`);

  let connection;
  try {
    // Create connection
    connection = await mysql.createConnection(guerrillaDbConfig);

    // Check if there are any emails for this address
    const [rows] = await connection.execute(
      'SELECT mail_id, date, `from`, `to`, subject FROM guerrilla_mail WHERE recipient = ? OR `to` LIKE ? ORDER BY date DESC',
      [emailAddress, `%${emailAddress}%`]
    );

    if (rows.length === 0) {
      console.log('❌ No emails found for this address in the guerrilla database');

      // Check if there are any recent emails in the database
      const [recentEmails] = await connection.execute(
        'SELECT mail_id, date, `from`, `to`, recipient, subject FROM guerrilla_mail ORDER BY date DESC LIMIT 5'
      );

      if (recentEmails.length > 0) {
        console.log('\nMost recent emails in the database:');
        recentEmails.forEach((email, index) => {
          console.log(`\nEmail #${index + 1}:`);
          console.log(`  - ID: ${email.mail_id}`);
          console.log(`  - Date: ${email.date}`);
          console.log(`  - From: ${email.from}`);
          console.log(`  - To: ${email.to}`);
          console.log(`  - Recipient: ${email.recipient}`);
          console.log(`  - Subject: ${email.subject}`);
        });
      } else {
        console.log('No recent emails found in the database');
      }

      return;
    }

    console.log(`✅ Found ${rows.length} email(s) for this address`);
    console.log('\nEmails:');
    rows.forEach((email, index) => {
      console.log(`\nEmail #${index + 1}:`);
      console.log(`  - ID: ${email.mail_id}`);
      console.log(`  - Date: ${email.date}`);
      console.log(`  - From: ${email.from}`);
      console.log(`  - To: ${email.to}`);
      console.log(`  - Subject: ${email.subject}`);
    });

  } catch (error) {
    console.error('Error checking guerrilla emails:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the function
checkGuerrillaEmails().catch(console.error);
