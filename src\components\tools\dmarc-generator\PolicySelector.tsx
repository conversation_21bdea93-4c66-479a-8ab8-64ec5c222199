'use client';

// DMARC Policy Selector Component - Placeholder implementation
import React from 'react';
import { DmarcPolicy } from '@/types/dmarc';

interface PolicySelectorProps {
  value?: DmarcPolicy;
  onChange?: (value: DmarcPolicy) => void;
  disabled?: boolean;
}

export default function PolicySelector({
  value = 'none',
  onChange,
  disabled = false
}: PolicySelectorProps) {
  const policies = [
    {
      value: 'none' as DmarcPolicy,
      label: 'None (Monitor Only)',
      description: 'No action taken on failed emails, only reporting',
      recommended: 'Start here for new implementations',
      color: 'text-[#66b077]'
    },
    {
      value: 'quarantine' as DmarcPolicy,
      label: 'Quarantine',
      description: 'Failed emails sent to spam/junk folder',
      recommended: 'Intermediate step after monitoring',
      color: 'text-[#f59e0b]'
    },
    {
      value: 'reject' as DmarcPolicy,
      label: 'Reject',
      description: 'Failed emails are rejected and not delivered',
      recommended: 'Most secure, use after testing',
      color: 'text-[#dc2626]'
    }
  ];

  return (
    <div className="space-y-3">
      <label className="block text-sm font-semibold text-[#1b130e] mb-3">
        DMARC Policy *
      </label>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
        {policies.map((policy) => (
          <label key={policy.value} className={`relative flex items-start py-2 px-3 border-2 rounded-xl cursor-pointer transition-all duration-200 bg-transparent ${
            value === policy.value
              ? 'border-[#956b50] shadow-md'
              : 'border-[#4a3728]/20 hover:border-[#956b50]/50 hover:shadow-sm'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
            <input
              type="radio"
              name="dmarcPolicy"
              value={policy.value}
              checked={value === policy.value}
              onChange={() => onChange?.(policy.value)}
              disabled={disabled}
              className="sr-only"
            />
            <div className="flex flex-col w-full">
              <div className="flex items-center space-x-2 mb-1">
                <div className={`w-3 h-3 rounded-full border-2 flex items-center justify-center ${
                  value === policy.value
                    ? 'border-[#956b50] bg-[#956b50]'
                    : 'border-[#4a3728]/40'
                }`}>
                  {value === policy.value && (
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                  )}
                </div>
                <span className="font-semibold text-[#1b130e] text-xs">
                  {policy.label}
                </span>
              </div>
              <div className="mb-1">
                <span className={`px-1.5 py-0.5 text-xs font-medium rounded-full border ${
                  policy.value === 'none' ? 'border-[#66b077]/30 text-[#66b077] bg-transparent' :
                  policy.value === 'quarantine' ? 'border-[#f59e0b]/30 text-[#f59e0b] bg-transparent' :
                  'border-[#dc2626]/30 text-[#dc2626] bg-transparent'
                }`}>
                  {policy.recommended}
                </span>
              </div>
              <p className="text-xs text-[#4a3728] leading-tight">
                {policy.description}
              </p>
            </div>
          </label>
        ))}
      </div>

    </div>
  );
}
