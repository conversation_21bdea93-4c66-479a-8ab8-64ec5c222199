/**
 * API routes for domain management
 */
import { NextRequest, NextResponse } from 'next/server';
import { getAllDomains, addDomain } from '@/lib/config/domainService';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/management-portal-x7z9y2/domains
 * 
 * Get all domains
 */
export async function GET(request: NextRequest) {
  try {
    // Get all domains
    const domains = await getAllDomains();
    
    return NextResponse.json({
      success: true,
      data: domains
    });
  } catch (error) {
    logError('api', 'Error getting domains', { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to get domains' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/management-portal-x7z9y2/domains
 * 
 * Add a new domain
 */
export async function POST(request: NextRequest) {
  try {
    // Get domain data from request body
    const { domain, isActive, settings } = await request.json();
    
    // Validate domain
    if (!domain || typeof domain !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid domain' },
        { status: 400 }
      );
    }
    
    // Add domain
    const newDomain = await addDomain(domain, isActive, settings);
    
    if (!newDomain) {
      return NextResponse.json(
        { success: false, error: 'Failed to add domain' },
        { status: 500 }
      );
    }
    
    logInfo('api', 'Added domain', { domain: newDomain });
    
    return NextResponse.json({
      success: true,
      data: newDomain,
      message: 'Domain added successfully'
    });
  } catch (error) {
    logError('api', 'Error adding domain', { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to add domain' },
      { status: 500 }
    );
  }
}
