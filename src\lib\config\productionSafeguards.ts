import { configService, defaultAppConfig } from './configService';
import { domainService } from './domainService';
import { adService } from './adService';
import { logger } from '../logging/dbLogger';
import { AppConfig } from './types';

/**
 * Production safeguards to prevent issues during deployment
 */
export const productionSafeguards = {
  /**
   * Validate configuration changes before applying them
   * @param key Configuration key
   * @param value New configuration value
   * @returns Validation result with success flag and optional error message
   */
  validateConfigChange: async (key: string, value: any): Promise<{ success: boolean; message?: string }> => {
    try {
      // Get current configuration - use the appropriate method based on whether it's a standard config key
      const isStandardKey = Object.keys(defaultAppConfig).includes(key);
      const currentConfig = isStandardKey
        ? await configService.getConfig(key as keyof AppConfig)
        : await configService.getCustomConfig(key);

      // Validate based on configuration key
      switch (key) {
        case 'emailExpirationMinutes':
          // Ensure email expiration time is reasonable
          if (typeof value !== 'number' || value < 5 || value > 1440) {
            return {
              success: false,
              message: 'Email expiration time must be between 5 and 1440 minutes (24 hours)'
            };
          }
          break;

        case 'autoRefreshInterval':
          // Ensure auto-refresh interval is reasonable
          if (typeof value !== 'number' || value < 5 || value > 60) {
            return {
              success: false,
              message: 'Auto-refresh interval must be between 5 and 60 seconds'
            };
          }
          break;

        case 'maxEmailsPerAddress':
          // Ensure max emails per address is reasonable
          if (typeof value !== 'number' || value < 10 || value > 1000) {
            return {
              success: false,
              message: 'Maximum emails per address must be between 10 and 1000'
            };
          }
          break;

        case 'maintenanceMode':
          // Ensure maintenance mode is a boolean
          if (typeof value !== 'boolean') {
            return {
              success: false,
              message: 'Maintenance mode must be a boolean value'
            };
          }
          break;

        case 'cleanupIntervalMinutes':
          // Ensure cleanup interval is reasonable
          if (typeof value !== 'number' || value < 5 || value > 1440) {
            return {
              success: false,
              message: 'Cleanup interval must be between 5 and 1440 minutes (24 hours)'
            };
          }
          break;

        default:
          // For other configuration keys, just ensure the type matches
          if (currentConfig !== null && typeof value !== typeof currentConfig) {
            return {
              success: false,
              message: `Configuration value must be of type ${typeof currentConfig}`
            };
          }
      }

      // Log the configuration change
      await logger.info('CONFIG_CHANGE', `Configuration ${key} changed from ${JSON.stringify(currentConfig)} to ${JSON.stringify(value)}`);

      return { success: true };
    } catch (error) {
      await logger.error('CONFIG_VALIDATION', `Error validating configuration change: ${error}`);
      return {
        success: false,
        message: 'An error occurred while validating the configuration change'
      };
    }
  },

  /**
   * Validate domain changes before applying them
   * @param domain Domain name
   * @param isActive Whether the domain is active
   * @param settings Domain settings
   * @returns Validation result with success flag and optional error message
   */
  validateDomainChange: async (
    domain: string,
    isActive: boolean,
    settings: any
  ): Promise<{ success: boolean; message?: string }> => {
    try {
      // Validate domain name format
      const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
      if (!domainRegex.test(domain)) {
        return {
          success: false,
          message: 'Invalid domain name format'
        };
      }

      // Ensure at least one domain is active
      if (!isActive) {
        const domains = await domainService.getAllDomains();
        const activeDomains = domains.filter(d => d.isActive && d.domain !== domain);
        if (activeDomains.length === 0) {
          return {
            success: false,
            message: 'Cannot deactivate the last active domain'
          };
        }
      }

      // Validate domain settings
      if (!settings || typeof settings !== 'object') {
        return {
          success: false,
          message: 'Domain settings must be an object'
        };
      }

      // Validate weight
      if (settings.weight !== undefined && (typeof settings.weight !== 'number' || settings.weight < 0 || settings.weight > 100)) {
        return {
          success: false,
          message: 'Domain weight must be between 0 and 100'
        };
      }

      // Validate features
      if (settings.features && typeof settings.features !== 'object') {
        return {
          success: false,
          message: 'Domain features must be an object'
        };
      }

      // Log the domain change
      await logger.info('DOMAIN_CHANGE', `Domain ${domain} updated: isActive=${isActive}, settings=${JSON.stringify(settings)}`);

      return { success: true };
    } catch (error) {
      await logger.error('DOMAIN_VALIDATION', `Error validating domain change: ${error}`);
      return {
        success: false,
        message: 'An error occurred while validating the domain change'
      };
    }
  },

  /**
   * Validate ad placement changes before applying them
   * @param placement Ad placement object
   * @returns Validation result with success flag and optional error message
   */
  validateAdPlacementChange: async (
    placement: any
  ): Promise<{ success: boolean; message?: string }> => {
    try {
      // Validate required fields
      if (!placement.placement_id || !placement.domain || !placement.ad_unit_id) {
        return {
          success: false,
          message: 'Placement ID, domain, and ad unit ID are required'
        };
      }

      // Validate ad unit ID format
      const adUnitRegex = /^ca-pub-\w{16}$/;
      if (!adUnitRegex.test(placement.ad_unit_id)) {
        return {
          success: false,
          message: 'Invalid ad unit ID format (should be ca-pub-XXXXXXXXXXXXXXXX)'
        };
      }

      // Validate device types
      if (!Array.isArray(placement.device_types) || placement.device_types.length === 0) {
        return {
          success: false,
          message: 'At least one device type must be specified'
        };
      }

      const validDeviceTypes = ['desktop', 'tablet', 'mobile'];
      for (const deviceType of placement.device_types) {
        if (!validDeviceTypes.includes(deviceType)) {
          return {
            success: false,
            message: `Invalid device type: ${deviceType}. Must be one of: ${validDeviceTypes.join(', ')}`
          };
        }
      }

      // Validate display options if provided
      if (placement.display_options) {
        const { delay_seconds, display_duration, max_displays_per_session } = placement.display_options;

        if (delay_seconds !== undefined && (typeof delay_seconds !== 'number' || delay_seconds < 0)) {
          return {
            success: false,
            message: 'Delay seconds must be a non-negative number'
          };
        }

        if (display_duration !== undefined && (typeof display_duration !== 'number' || display_duration <= 0)) {
          return {
            success: false,
            message: 'Display duration must be a positive number'
          };
        }

        if (max_displays_per_session !== undefined && (typeof max_displays_per_session !== 'number' || max_displays_per_session <= 0)) {
          return {
            success: false,
            message: 'Max displays per session must be a positive number'
          };
        }
      }

      // Log the ad placement change
      await logger.info('AD_PLACEMENT_CHANGE', `Ad placement ${placement.placement_id} for domain ${placement.domain} updated: ${JSON.stringify(placement)}`);

      return { success: true };
    } catch (error) {
      await logger.error('AD_PLACEMENT_VALIDATION', `Error validating ad placement change: ${error}`);
      return {
        success: false,
        message: 'An error occurred while validating the ad placement change'
      };
    }
  },

  /**
   * Create a backup of the current configuration
   * @returns Backup ID
   */
  createConfigBackup: async (): Promise<string> => {
    try {
      // Get all configuration
      const config = await configService.getAllConfig();

      // Get all domains
      const domains = await domainService.getAllDomains();

      // Get all ad placements
      const adPlacements = await adService.getAllAdPlacements();

      // Create backup object
      const backup = {
        timestamp: new Date().toISOString(),
        config,
        domains,
        adPlacements,
        backupId: `backup-${Date.now()}`
      };

      // Store backup in database using custom config method
      await configService.setCustomConfig(`backup-${backup.backupId}`, backup);

      // Log backup creation
      await logger.info('CONFIG_BACKUP', `Configuration backup created: ${backup.backupId}`);

      return backup.backupId;
    } catch (error) {
      await logger.error('CONFIG_BACKUP', `Error creating configuration backup: ${error}`);
      throw new Error('Failed to create configuration backup');
    }
  },

  /**
   * Restore configuration from a backup
   * @param backupId Backup ID
   * @returns Success flag
   */
  restoreConfigBackup: async (backupId: string): Promise<boolean> => {
    try {
      // Get backup using custom config method
      const backup = await configService.getCustomConfig(`backup-${backupId}`);

      if (!backup) {
        await logger.error('CONFIG_RESTORE', `Backup not found: ${backupId}`);
        return false;
      }

      // Create a new backup before restoring (for safety)
      const newBackupId = await productionSafeguards.createConfigBackup();

      // Restore configuration
      for (const [key, value] of Object.entries(backup.config)) {
        if (!key.startsWith('backup-')) {
          // Use the appropriate method based on whether the key is a standard config key
          if (Object.keys(defaultAppConfig).includes(key)) {
            // Need to cast to the appropriate type for AppConfig
            const typedKey = key as keyof AppConfig;
            const typedValue = value as any;
            await configService.setConfig(typedKey, typedValue);
          } else {
            await configService.setCustomConfig(key, value);
          }
        }
      }

      // Restore domains
      for (const domain of backup.domains) {
        await domainService.updateDomain(domain.domain, domain.isActive, domain.settings);
      }

      // Restore ad placements
      for (const placement of backup.adPlacements) {
        // Extract the required parameters from the placement object
        const { placementId, domain, ...updates } = placement;
        await adService.updateAdPlacement(placementId, domain, updates);
      }

      // Log restoration
      await logger.info('CONFIG_RESTORE', `Configuration restored from backup: ${backupId}. New backup created: ${newBackupId}`);

      return true;
    } catch (error) {
      await logger.error('CONFIG_RESTORE', `Error restoring configuration from backup: ${error}`);
      return false;
    }
  },

  /**
   * List available configuration backups
   * @returns List of backup IDs with timestamps
   */
  listConfigBackups: async (): Promise<Array<{ id: string; timestamp: string }>> => {
    try {
      // Get all configuration keys
      const allConfig = await configService.getAllConfig();

      // Filter backup keys
      const backups = Object.entries(allConfig)
        .filter(([key]) => key.startsWith('backup-'))
        .map(([key, value]) => ({
          id: key.replace('backup-', ''),
          timestamp: value.timestamp
        }))
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      return backups;
    } catch (error) {
      await logger.error('CONFIG_BACKUP_LIST', `Error listing configuration backups: ${error}`);
      return [];
    }
  }
};

export default productionSafeguards;
