/**
 * SMTP Tester Validation API Route
 *
 * API endpoint for validating SMTP configuration without sending emails
 * POST /api/tools/smtp-tester/validate
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateSmtpConfig, sanitizeSmtpConfig } from '@/lib/tools/smtp-tester/validation';
import { SmtpConfig } from '@/types/smtp';
import { logError } from '@/lib/logging';
import nodemailer from 'nodemailer';
import { SMTP_TIMEOUTS } from '@/lib/tools/smtp-tester/constants';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const config: SmtpConfig = body.config;

    if (!config) {
      return NextResponse.json(
        {
          success: false,
          error: 'SMTP configuration is required'
        },
        { status: 400 }
      );
    }

    // Validate configuration
    const validation = validateSmtpConfig(config);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid SMTP configuration',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Sanitize configuration
    const sanitizedConfig = sanitizeSmtpConfig(config);

    console.log(`SMTP validation request for ${sanitizedConfig.server}:${sanitizedConfig.port}`);

    // Create transporter for validation
    const transportConfig: any = {
      host: sanitizedConfig.server,
      port: sanitizedConfig.port,
      connectionTimeout: SMTP_TIMEOUTS.connection,
      greetingTimeout: SMTP_TIMEOUTS.greeting,
      socketTimeout: SMTP_TIMEOUTS.socket,
      auth: {
        user: sanitizedConfig.username,
        pass: sanitizedConfig.password
      }
    };

    // Configure encryption
    if (sanitizedConfig.encryption === 'ssl') {
      transportConfig.secure = true;
    } else if (sanitizedConfig.encryption === 'tls') {
      transportConfig.secure = false;
      transportConfig.requireTLS = true;
    } else {
      transportConfig.secure = false;
      transportConfig.ignoreTLS = true;
    }

    const transporter = nodemailer.createTransport(transportConfig);

    try {
      // Verify connection without sending email
      const verified = await transporter.verify();

      // Connection will be closed automatically

      if (verified) {
        console.log(`SMTP validation successful for ${sanitizedConfig.server}`);
        return NextResponse.json({
          success: true,
          message: 'SMTP configuration is valid and connection successful'
        });
      } else {
        console.log(`SMTP validation failed for ${sanitizedConfig.server}`);
        return NextResponse.json(
          {
            success: false,
            error: 'SMTP connection verification failed'
          },
          { status: 400 }
        );
      }
    } catch (error) {
      // Connection will be closed automatically on error

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log(`SMTP validation error for ${sanitizedConfig.server}: ${errorMessage}`);

      return NextResponse.json(
        {
          success: false,
          error: `SMTP connection failed: ${errorMessage}`
        },
        { status: 400 }
      );
    }

  } catch (error) {
    logError('SMTP Tester Validation API', 'Unexpected error during SMTP validation', error);

    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred during SMTP validation'
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to validate SMTP configurations.' },
    { status: 405 }
  );
}
