/**
 * Secure API Client for VanishPost
 * 
 * This module provides a secure API client that:
 * 1. Automatically includes session IDs in request headers
 * 2. Handles session-based authentication
 * 3. Provides consistent error handling
 * 4. Supports rate limiting headers
 */

import { getCurrentSessionId, updateSessionActivity } from '@/lib/session/persistentSessionManager';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ApiRequestOptions extends RequestInit {
  includeSession?: boolean;
  timeout?: number;
}

/**
 * Secure API client that automatically includes session headers
 */
export class SecureApiClient {
  private baseUrl: string;
  private defaultTimeout: number;

  constructor(baseUrl: string = '', defaultTimeout: number = 30000) {
    this.baseUrl = baseUrl;
    this.defaultTimeout = defaultTimeout;
  }

  /**
   * Make a secure API request with session headers
   */
  async request<T = any>(
    endpoint: string,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const {
      includeSession = true,
      timeout = this.defaultTimeout,
      headers = {},
      ...fetchOptions
    } = options;

    try {
      // Prepare headers
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...(headers as Record<string, string>)
      };

      // Include session ID in headers if requested
      if (includeSession) {
        const sessionId = getCurrentSessionId();
        if (sessionId) {
          requestHeaders['x-session-id'] = sessionId;
          requestHeaders['session-id'] = sessionId; // Fallback header

          // Update session activity
          updateSessionActivity();
        }
      }

      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      // Make the request
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...fetchOptions,
        headers: requestHeaders,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // Parse response
      const responseData = await response.json();

      // Handle rate limiting headers
      this.handleRateLimitHeaders(response);

      if (!response.ok) {
        return {
          success: false,
          error: responseData.message || responseData.error || `HTTP ${response.status}`,
          message: responseData.message
        };
      }

      return {
        success: true,
        data: responseData,
        message: responseData.message
      };

    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return {
            success: false,
            error: 'Request timeout',
            message: 'The request took too long to complete'
          };
        }
        
        return {
          success: false,
          error: error.message,
          message: 'Network error occurred'
        };
      }

      return {
        success: false,
        error: 'Unknown error occurred',
        message: 'An unexpected error occurred'
      };
    }
  }

  /**
   * GET request
   */
  async get<T = any>(endpoint: string, options: ApiRequestOptions = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T = any>(
    endpoint: string,
    data?: any,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  /**
   * PUT request
   */
  async put<T = any>(
    endpoint: string,
    data?: any,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string, options: ApiRequestOptions = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Handle rate limiting headers from response
   */
  private handleRateLimitHeaders(response: Response): void {
    const rateLimitRemaining = response.headers.get('X-RateLimit-Remaining');
    const rateLimitReset = response.headers.get('X-RateLimit-Reset');
    
    if (rateLimitRemaining !== null) {
      const remaining = parseInt(rateLimitRemaining, 10);
      if (remaining <= 2) {
        console.warn('Rate limit warning: Only', remaining, 'requests remaining');
      }
    }

    if (response.status === 429) {
      const retryAfter = response.headers.get('Retry-After');
      console.warn('Rate limit exceeded. Retry after:', retryAfter || 'unknown');
    }
  }
}

// Create default instance
export const apiClient = new SecureApiClient();

/**
 * Email generation API with session support
 */
export async function generateEmailWithSession(): Promise<ApiResponse<{
  emailAddress: string;
  expirationDate: string;
  success: boolean;
}>> {
  return apiClient.post('/api/generate');
}

/**
 * Check email with session support
 */
export async function checkEmailWithSession(emailAddress: string): Promise<ApiResponse<{
  emailAddress: string;
  expirationDate: string;
  success: boolean;
}>> {
  return apiClient.post('/api/generate', { emailAddress });
}

/**
 * Analytics API with session support
 */
export async function trackEventWithSession(
  eventType: string,
  eventData: any
): Promise<ApiResponse<any>> {
  return apiClient.post('/api/analytics/events', {
    eventType,
    eventData,
    timestamp: new Date().toISOString()
  });
}

/**
 * Session management API
 */
export async function createSessionInDatabase(
  sessionId: string,
  metadata: any
): Promise<ApiResponse<any>> {
  return apiClient.post('/api/analytics/session', {
    action: 'create',
    sessionId,
    metadata
  });
}

export async function endSessionInDatabase(sessionId: string): Promise<ApiResponse<any>> {
  return apiClient.post('/api/analytics/session', {
    action: 'end',
    sessionId
  });
}

/**
 * Utility function to make any fetch request with session headers
 */
export async function fetchWithSession(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const sessionId = getCurrentSessionId();
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string>)
  };

  if (sessionId) {
    headers['x-session-id'] = sessionId;
    headers['session-id'] = sessionId;
    updateSessionActivity();
  }

  return fetch(url, {
    ...options,
    headers
  });
}

/**
 * Legacy support - wrapper for existing code
 */
export async function makeSecureRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await apiClient.request<T>(endpoint, options);
  
  if (!response.success) {
    throw new Error(response.error || 'Request failed');
  }
  
  return response.data as T;
}

export default apiClient;
