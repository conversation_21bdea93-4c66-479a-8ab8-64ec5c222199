/**
 * SVG Path Validation Utilities
 * Helps prevent malformed SVG path data that can cause rendering errors
 */

/**
 * Validates SVG path data for common issues
 */
export function validateSVGPath(pathData: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check for malformed arc commands where flags are concatenated
  // Look for patterns like "A6 6 0 1721 9z" where "1721" should be "1 1 21"
  // Only check uppercase A commands to avoid false positives with lowercase relative commands
  const malformedArcPattern = /A\s*([0-9.]+)\s+([0-9.]+)\s+([0-9.]+)\s+([0-9]{4,})\s+([0-9.-]+)/g;

  let match;
  while ((match = malformedArcPattern.exec(pathData)) !== null) {
    const flagsAndX = match[4];
    errors.push(`Malformed arc command at position ${match.index}: arc flags and coordinates appear to be concatenated "${flagsAndX}" - should be separated as "large-arc-flag sweep-flag x"`);
  }

  // Check for unclosed paths that should be closed
  if (pathData.includes('M') && !pathData.includes('z') && !pathData.includes('Z')) {
    // This is just a warning, not an error
    // errors.push('Path may need to be closed with "z" or "Z"');
  }

  // Check for invalid commands
  const validCommands = /^[MmLlHhVvCcSsQqTtAaZz0-9\s.,+-]+$/;
  if (!validCommands.test(pathData)) {
    errors.push('Path contains invalid characters or commands');
  }

  // Check for missing coordinates
  const commandPattern = /([MmLlHhVvCcSsQqTtAa])/g;
  const commands = pathData.match(commandPattern) || [];

  for (const command of commands) {
    const commandIndex = pathData.indexOf(command);
    const nextCommandIndex = pathData.indexOf(command, commandIndex + 1);
    const commandSection = nextCommandIndex === -1
      ? pathData.substring(commandIndex)
      : pathData.substring(commandIndex, nextCommandIndex);

    // Basic coordinate count validation for common commands
    const coords = commandSection.match(/[-+]?[0-9]*\.?[0-9]+/g) || [];

    switch (command.toUpperCase()) {
      case 'M':
      case 'L':
        if (coords.length < 2) {
          errors.push(`Command ${command} requires at least 2 coordinates, found ${coords.length}`);
        }
        break;
      case 'A':
        if (coords.length < 7) {
          errors.push(`Command ${command} requires 7 coordinates, found ${coords.length}`);
        }
        break;
      case 'C':
        if (coords.length < 6) {
          errors.push(`Command ${command} requires 6 coordinates, found ${coords.length}`);
        }
        break;
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Fixes common SVG path issues
 */
export function fixSVGPath(pathData: string): string {
  let fixed = pathData;

  // Fix malformed arc commands where flags and coordinates are concatenated
  // Pattern: A rx ry rotation concatenated_flags_and_x y
  // Look for cases like "A6 6 0 1721 9" where "1721" should be "1 1 21"
  // Only fix uppercase A commands to avoid false positives with lowercase relative commands
  fixed = fixed.replace(
    /A\s*([0-9.]+)\s+([0-9.]+)\s+([0-9.]+)\s+([0-9]{4,})\s+([0-9.-]+)/g,
    (match, rx, ry, rotation, flagsAndX, y) => {
      // Extract individual flags from concatenated string
      const flagsAndXStr = flagsAndX.toString();
      if (flagsAndXStr.length >= 3) {
        const largeArcFlag = flagsAndXStr[0];
        const sweepFlag = flagsAndXStr[1];
        const x = flagsAndXStr.substring(2);

        // Validate flags are 0 or 1
        if ((largeArcFlag === '0' || largeArcFlag === '1') &&
            (sweepFlag === '0' || sweepFlag === '1')) {
          return `A${rx} ${ry} ${rotation} ${largeArcFlag} ${sweepFlag} ${x} ${y}`;
        }
      }
      return match; // Return original if we can't fix it
    }
  );

  return fixed;
}

/**
 * Validates and fixes SVG path data
 */
export function validateAndFixSVGPath(pathData: string): {
  original: string;
  fixed: string;
  isValid: boolean;
  errors: string[];
  wasFixed: boolean;
} {
  const original = pathData;
  const fixed = fixSVGPath(pathData);
  const validation = validateSVGPath(fixed);

  return {
    original,
    fixed,
    isValid: validation.isValid,
    errors: validation.errors,
    wasFixed: original !== fixed
  };
}

/**
 * Development helper to check SVG paths in components
 */
export function logSVGPathValidation(pathData: string, componentName?: string): void {
  if (process.env.NODE_ENV === 'development') {
    const result = validateAndFixSVGPath(pathData);

    if (!result.isValid || result.wasFixed) {
      console.group(`SVG Path Validation${componentName ? ` - ${componentName}` : ''}`);

      if (result.wasFixed) {
        console.warn('SVG path was automatically fixed:');
        console.log('Original:', result.original);
        console.log('Fixed:', result.fixed);
      }

      if (!result.isValid) {
        console.error('SVG path validation errors:');
        result.errors.forEach(error => console.error('- ' + error));
      }

      console.groupEnd();
    }
  }
}
