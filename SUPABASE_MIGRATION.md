# Supabase Migration Guide

This document outlines the steps to migrate the Fademail application from using a local MySQL database to Supabase PostgreSQL, while keeping the remote email server setup unchanged.

## Overview

The migration involves:

1. Setting up Supabase tables
2. Updating the codebase to use Supabase
3. Migrating data from MySQL to Supabase
4. Testing the application with Supabase

## Prerequisites

- Supabase account and project set up
- Supabase URL and anon key
- Node.js and npm/pnpm installed
- MySQL database with existing data (for migration)

## Step 1: Update Environment Variables

Update your `.env.local` file to include Supabase credentials:

```
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-url.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Guerrilla Database Configuration
GUERRILLA_DB_HOST=your-guerrilla-db-host
GUERRILLA_DB_USER=your-guerrilla-db-user
GUERRILLA_DB_PASSWORD=your-guerrilla-db-password
GUERRILLA_DB_NAME=your-guerrilla-db-name
GUERRILLA_DB_PORT=3306
```

## Step 2: Set Up Supabase Tables

There are two ways to set up the required tables in Supabase:

### Option 1: Using the Supabase Dashboard (Recommended)

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Navigate to your project
3. Go to the SQL Editor
4. Create a new query and either:
   - Copy and paste the SQL from `scripts/create-supabase-tables.sql`
   - Or paste the following SQL:

```sql
-- Create temp_emails table
CREATE TABLE temp_emails (
  id SERIAL PRIMARY KEY,
  email_address VARCHAR(255) NOT NULL UNIQUE,
  creation_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  expiration_date TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Create index on expiration_date for cleanup queries
CREATE INDEX idx_expiration_date ON temp_emails(expiration_date);

-- Create analytics_events table
CREATE TABLE analytics_events (
  id SERIAL PRIMARY KEY,
  event_type VARCHAR(50) NOT NULL,
  page_path VARCHAR(255),
  referrer VARCHAR(255),
  country VARCHAR(50),
  browser VARCHAR(50),
  device_type VARCHAR(20),
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  additional_data JSONB
);

-- Create indexes for analytics queries
CREATE INDEX idx_event_type ON analytics_events(event_type);
CREATE INDEX idx_timestamp ON analytics_events(timestamp);
CREATE INDEX idx_page_path ON analytics_events(page_path);
```

5. Run the query

**Note:** You can also upload and run the `scripts/create-supabase-tables.sql` file directly in the SQL Editor.

### Option 2: Using the Setup Script

Run the setup script to check if the tables exist and get instructions for creating them:

```bash
npm run setup-supabase
```

This script will:
- Check if the required tables exist
- Provide SQL commands to create the tables if they don't exist

## Step 3: Test Supabase Connection

Verify that the connection to Supabase is working:

```bash
npm run test-supabase
```

This will test basic CRUD operations on the Supabase database.

## Step 4: Migrate Data from MySQL to Supabase

Run the migration script to transfer existing data:

```bash
npm run migrate-to-supabase
```

This script:
- Connects to both MySQL and Supabase
- Transfers all temporary emails
- Transfers all analytics events
- Handles data transformation between MySQL and PostgreSQL formats

## Step 5: Verify the Migration

After migration, verify that:

1. All temporary emails are accessible in the application
2. Analytics data is correctly displayed in the admin dashboard
3. New emails can be generated and stored in Supabase
4. Cleanup functionality works correctly

## Troubleshooting

### Common Issues

1. **Connection Issues**
   - Verify Supabase URL and anon key are correct
   - Check network connectivity to Supabase

2. **Data Migration Issues**
   - Check MySQL connection parameters
   - Ensure MySQL server is running and accessible
   - Look for data format incompatibilities

3. **Application Errors**
   - Check browser console for JavaScript errors
   - Check server logs for backend errors
   - Verify that all API endpoints are using the Supabase client

### Rollback Plan

If issues occur, you can roll back to MySQL by:

1. Reverting the code changes
2. Restoring the original `.env.local` file
3. Restarting the application

## Code Changes

The migration involves changes to:

1. Database client setup (`src/lib/supabase.ts`)
2. Database operations (`src/lib/supabase-db.ts`)
3. API routes (`src/app/api/*`)
4. Analytics functions (`src/lib/analytics/*`)

## Benefits of Supabase

- Managed PostgreSQL database
- Built-in authentication system
- Real-time subscriptions
- Row-level security
- Edge functions
- Storage capabilities
- Automatic backups and scaling

## Next Steps

After successful migration:

1. Implement additional Supabase features like real-time updates
2. Enhance security with row-level security policies
3. Consider using Supabase Auth for admin authentication
4. Explore Supabase Storage for attachment handling
