'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Progress } from '@/components/ui/Progress';
import { Skeleton } from '@/components/ui/Skeleton';
import { TrendingUp, Users, Mail, MousePointer, Clock, Eye, RefreshCw, Copy, Inbox } from 'lucide-react';

interface AnalyticsData {
  totalEvents: number;
  eventCounts: Record<string, number>;
  timeRange: {
    startDate: string;
    endDate: string;
  };
}

interface SessionData {
  summary: {
    totalSessions: number;
    avgSessionDuration: number;
    totalEmailsGenerated: number;
    totalEmailsReceived: number;
    totalEmailsViewed: number;
    totalEmailsDeleted: number;
    totalManualRefreshes: number;
    totalCopyActions: number;
  };
  engagement: {
    avgEmailsGeneratedPerSession: number;
    avgEmailsViewedPerSession: number;
    avgManualRefreshesPerSession: number;
    avgCopyActionsPerSession: number;
    emailViewRate: number;
    emailDeleteRate: number;
  };
}

interface AnalyticsOverviewProps {
  analyticsData: AnalyticsData;
  sessionData: { summary: SessionData['summary']; engagement: SessionData['engagement'] };
  isLoading: boolean;
}

/**
 * Analytics Overview Component
 * 
 * Displays key metrics and summary cards for the analytics dashboard.
 */
export default function AnalyticsOverview({ 
  analyticsData, 
  sessionData, 
  isLoading 
}: AnalyticsOverviewProps) {
  
  /**
   * Format duration in seconds to human readable format
   */
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)}m`;
    } else {
      return `${Math.round(seconds / 3600)}h`;
    }
  };

  /**
   * Format percentage with one decimal place
   */
  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`;
  };

  /**
   * Format large numbers with appropriate suffixes
   */
  const formatNumber = (value: number): string => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    } else {
      return value.toString();
    }
  };

  // Main analytics cards (excluding Total Events which will be moved to real-time section)
  const mainAnalyticsCards = [
    {
      title: 'Total Sessions',
      value: sessionData?.summary?.totalSessions || 0,
      icon: Users,
      description: 'Unique user sessions',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Emails Generated',
      value: sessionData?.summary?.totalEmailsGenerated || 0,
      icon: Mail,
      description: 'Temporary emails created',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Emails Received',
      value: sessionData?.summary?.totalEmailsReceived || 0,
      icon: Inbox,
      description: 'Emails delivered to temp addresses',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Emails Viewed',
      value: sessionData?.summary?.totalEmailsViewed || 0,
      icon: Eye,
      description: 'Emails opened by users',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Copy Actions',
      value: sessionData?.summary?.totalCopyActions || 0,
      icon: Copy,
      description: 'Email addresses copied',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Manual Refreshes',
      value: sessionData?.summary?.totalManualRefreshes || 0,
      icon: RefreshCw,
      description: 'Manual inbox refreshes',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
    {
      title: 'Avg Session Duration',
      value: formatDuration(sessionData?.summary?.avgSessionDuration || 0),
      icon: Clock,
      description: 'Average time per session',
      color: 'text-teal-600',
      bgColor: 'bg-teal-50',
      isString: true,
    },
    {
      title: 'Email View Rate',
      value: formatPercentage(sessionData?.engagement?.emailViewRate || 0),
      rawValue: sessionData?.engagement?.emailViewRate || 0,
      icon: TrendingUp,
      description: 'Emails viewed vs received',
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      isString: true,
      isPercentage: true,
    },
  ];



  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-8 w-8 rounded-lg" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-full mb-2" />
              <Skeleton className="h-2 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {mainAnalyticsCards.map((card, index) => {
        const Icon = card.icon;

        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <Icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {card.isString ? card.value : formatNumber(card.value as number)}
              </div>
              <p className="text-xs text-gray-500 mb-2">
                {card.description}
              </p>
              {card.isPercentage && (
                <Progress
                  value={Math.min(card.rawValue, 100)}
                  className="h-2"
                />
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}



/**
 * Engagement Metrics Component
 * 
 * Displays detailed engagement metrics in a separate section.
 */
export function EngagementMetrics({ sessionData }: { sessionData: SessionData }) {
  const engagementMetrics = [
    {
      label: 'Emails per Session',
      value: (sessionData?.engagement?.avgEmailsGeneratedPerSession || 0).toFixed(1),
      description: 'Average emails generated per session',
    },
    {
      label: 'Views per Session',
      value: (sessionData?.engagement?.avgEmailsViewedPerSession || 0).toFixed(1),
      description: 'Average emails viewed per session',
    },
    {
      label: 'Refreshes per Session',
      value: (sessionData?.engagement?.avgManualRefreshesPerSession || 0).toFixed(1),
      description: 'Average manual refreshes per session',
    },
    {
      label: 'Copies per Session',
      value: (sessionData?.engagement?.avgCopyActionsPerSession || 0).toFixed(1),
      description: 'Average copy actions per session',
    },
    {
      label: 'Email View Rate',
      value: `${(sessionData?.engagement?.emailViewRate || 0).toFixed(1)}%`,
      rawValue: sessionData?.engagement?.emailViewRate || 0,
      description: 'Percentage of received emails that were viewed',
      isPercentage: true,
    },
    {
      label: 'Email Delete Rate',
      value: `${(sessionData?.engagement?.emailDeleteRate || 0).toFixed(1)}%`,
      rawValue: sessionData?.engagement?.emailDeleteRate || 0,
      description: 'Percentage of received emails that were deleted',
      isPercentage: true,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Engagement Metrics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {engagementMetrics.map((metric, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {metric.value}
              </div>
              <div className="text-sm font-medium text-gray-700 mb-1">
                {metric.label}
              </div>
              <div className="text-xs text-gray-500 mb-2">
                {metric.description}
              </div>
              {metric.isPercentage && (
                <Progress
                  value={Math.min(metric.rawValue, 100)}
                  className="h-2"
                />
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
