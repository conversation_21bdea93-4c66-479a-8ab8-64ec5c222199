'use client';

// DKIM Generator Form Component - Full implementation
import React, { useState } from 'react';
import { DkimFormData, DkimFormErrors } from '@/types/dkim';
import { estimateGenerationTime } from '@/lib/tools/dkim-generator/keyGeneration';

interface DkimGeneratorFormProps {
  onSubmit: (data: DkimFormData) => void;
  loading?: boolean;
}

export default function DkimGeneratorForm({ onSubmit, loading = false }: DkimGeneratorFormProps) {
  const [formData, setFormData] = useState<DkimFormData>({
    domain: '',
    selector: 'dkim',
    keyStrength: 2048,
  });

  const [errors, setErrors] = useState<DkimFormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validateField = (name: keyof DkimFormData, value: string | number): string | undefined => {
    switch (name) {
      case 'domain':
        if (!value) return 'Domain is required';
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
        if (!domainRegex.test(value as string)) return 'Invalid domain format';
        break;
      case 'selector':
        if (!value) return 'Selector is required';
        const selectorRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,62}[a-zA-Z0-9]$|^[a-zA-Z0-9]$/;
        if (!selectorRegex.test(value as string)) return 'Invalid selector format. Use alphanumeric characters and hyphens only.';
        if ((value as string).length > 63) return 'Selector must be 63 characters or less';
        break;
      case 'keyStrength':
        if (value !== 1024 && value !== 2048) return 'Key strength must be 1024 or 2048 bits';
        break;
    }
    return undefined;
  };

  const handleInputChange = (name: keyof DkimFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleBlur = (name: keyof DkimFormData) => {
    setTouched(prev => ({ ...prev, [name]: true }));
    const error = validateField(name, formData[name]);
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const newErrors: DkimFormErrors = {};
    Object.keys(formData).forEach(key => {
      const fieldName = key as keyof DkimFormData;
      const error = validateField(fieldName, formData[fieldName]);
      if (error) newErrors[fieldName] = error;
    });

    setErrors(newErrors);
    setTouched({
      domain: true,
      selector: true,
      keyStrength: true,
    });

    // If no errors, submit the form
    if (Object.keys(newErrors).length === 0) {
      onSubmit(formData);
    }
  };

  const estimatedTime = estimateGenerationTime(formData.keyStrength);

  return (
    <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-8 shadow-sm">
      <div className="flex items-center space-x-4 mb-6">
        <div className="w-12 h-12 bg-gradient-to-br from-[#66b077] to-[#07880e] rounded-xl flex items-center justify-center shadow-lg flex-shrink-0" style={{ aspectRatio: '1' }}>
          <svg className="w-6 h-6 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
          </svg>
        </div>
        <div>
          <h2 className="text-2xl font-bold text-[#1b130e]">
            Generate DKIM Keys
          </h2>
          <p className="text-[#4a3728] mt-1">
            Create secure RSA key pairs and DNS records for email authentication
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-semibold text-[#1b130e] mb-3">
              Domain *
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="example.com"
                value={formData.domain}
                onChange={(e) => handleInputChange('domain', e.target.value)}
                onBlur={() => handleBlur('domain')}
                className={`w-full px-4 py-2.5 border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${
                  errors.domain && touched.domain
                    ? 'border-red-500 focus:ring-red-500 bg-red-50'
                    : 'border-[#4a3728]/20 focus:ring-[#66b077] focus:border-[#66b077] bg-white/60'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
                disabled={loading}
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-4">
                <svg className="w-5 h-5 text-[#4a3728]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                </svg>
              </div>
            </div>
            {errors.domain && touched.domain && (
              <div className="flex items-center mt-2 text-red-600">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <p className="text-sm">{errors.domain}</p>
              </div>
            )}
            <p className="text-xs text-[#4a3728] mt-2 flex items-center">
              <svg className="w-4 h-4 mr-1 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              The domain you want to configure DKIM for
            </p>
          </div>

          <div>
            <label className="block text-sm font-semibold text-[#1b130e] mb-3">
              Selector *
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="dkim"
                value={formData.selector}
                onChange={(e) => handleInputChange('selector', e.target.value)}
                onBlur={() => handleBlur('selector')}
                className={`w-full px-4 py-2.5 border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${
                  errors.selector && touched.selector
                    ? 'border-red-500 focus:ring-red-500 bg-red-50'
                    : 'border-[#4a3728]/20 focus:ring-[#66b077] focus:border-[#66b077] bg-white/60'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
                disabled={loading}
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-4">
                <svg className="w-5 h-5 text-[#4a3728]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a4 4 0 01-4-4V7a4 4 0 014-4z" />
                </svg>
              </div>
            </div>
            {errors.selector && touched.selector && (
              <div className="flex items-center mt-2 text-red-600">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <p className="text-sm">{errors.selector}</p>
              </div>
            )}
            <p className="text-xs text-[#4a3728] mt-2 flex items-center">
              <svg className="w-4 h-4 mr-1 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              A unique identifier for this DKIM key (e.g., "dkim", "mail", "default")
            </p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-semibold text-[#1b130e] mb-4">
            Key Strength *
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <label className={`relative flex items-center py-2.5 px-4 border-2 rounded-xl cursor-pointer transition-all duration-200 bg-transparent ${
              formData.keyStrength === 2048
                ? 'border-[#66b077] shadow-md'
                : 'border-[#4a3728]/20 hover:border-[#66b077]/50 hover:shadow-sm'
            } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}>
              <input
                type="radio"
                name="keyStrength"
                value="2048"
                checked={formData.keyStrength === 2048}
                onChange={(e) => handleInputChange('keyStrength', parseInt(e.target.value))}
                disabled={loading}
                className="sr-only"
              />
              <div className="flex items-center space-x-3 w-full">
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                  formData.keyStrength === 2048
                    ? 'border-[#66b077] bg-[#66b077]'
                    : 'border-[#4a3728]/40'
                }`}>
                  {formData.keyStrength === 2048 && (
                    <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-0.5">
                    <span className="font-semibold text-[#1b130e] text-sm">2048 bits</span>
                    <span className="px-1.5 py-0.5 border border-[#66b077]/30 text-[#66b077] bg-transparent text-xs font-medium rounded-full">
                      Recommended
                    </span>
                  </div>
                  <p className="text-xs text-[#4a3728]">
                    More secure • ~{Math.round(estimateGenerationTime(2048) / 1000)}s generation
                  </p>
                </div>
              </div>
            </label>

            <label className={`relative flex items-center py-2.5 px-4 border-2 rounded-xl cursor-pointer transition-all duration-200 bg-transparent ${
              formData.keyStrength === 1024
                ? 'border-[#66b077] shadow-md'
                : 'border-[#4a3728]/20 hover:border-[#66b077]/50 hover:shadow-sm'
            } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}>
              <input
                type="radio"
                name="keyStrength"
                value="1024"
                checked={formData.keyStrength === 1024}
                onChange={(e) => handleInputChange('keyStrength', parseInt(e.target.value))}
                disabled={loading}
                className="sr-only"
              />
              <div className="flex items-center space-x-3 w-full">
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                  formData.keyStrength === 1024
                    ? 'border-[#66b077] bg-[#66b077]'
                    : 'border-[#4a3728]/40'
                }`}>
                  {formData.keyStrength === 1024 && (
                    <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-0.5">
                    <span className="font-semibold text-[#1b130e] text-sm">1024 bits</span>
                    <span className="px-1.5 py-0.5 border border-[#f59e0b]/30 text-[#f59e0b] bg-transparent text-xs font-medium rounded-full">
                      Legacy
                    </span>
                  </div>
                  <p className="text-xs text-[#4a3728]">
                    Faster generation • ~{Math.round(estimateGenerationTime(1024) / 1000)}s • Less secure
                  </p>
                </div>
              </div>
            </label>
          </div>
          {errors.keyStrength && touched.keyStrength && (
            <div className="flex items-center mt-3 text-red-600">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-sm">{errors.keyStrength}</p>
            </div>
          )}
        </div>

        <div className="bg-gradient-to-r from-[#66b077]/10 to-[#07880e]/10 border border-[#66b077]/30 rounded-xl p-6">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-[#66b077]/10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ aspectRatio: '1' }}>
              <svg className="w-5 h-5 text-[#66b077] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div>
              <h4 className="font-semibold text-[#1b130e] mb-1">What happens next?</h4>
              <p className="text-sm text-[#4a3728]">
                We'll generate a secure RSA key pair, create the DNS record format, and provide step-by-step setup instructions.
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-center">
          <button
            type="submit"
            className="border border-[#ce601c] text-[#ce601c] bg-transparent py-3 px-6 rounded-xl hover:bg-[#ce601c] hover:text-white hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center font-semibold text-base"
            disabled={loading}
          >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current mr-3"></div>
              Generating Keys... (~{Math.round(estimatedTime / 1000)}s)
            </>
          ) : (
            <>
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
              </svg>
              Generate DKIM Keys
            </>
          )}
          </button>
        </div>
      </form>
    </div>
  );
}
