'use client';

// Shared DNS Record Display Component - Placeholder implementation
import React, { useState } from 'react';

interface DnsRecordDisplayProps {
  recordName: string;
  recordValue: string;
  recordType?: 'TXT' | 'MX' | 'A' | 'AAAA' | 'CNAME';
  title?: string;
  copyable?: boolean;
}

export default function DnsRecordDisplay({
  recordName,
  recordValue,
  recordType = 'TXT',
  title = 'DNS Record',
  copyable = true
}: DnsRecordDisplayProps) {
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const handleCopy = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2500);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-[#1b130e]">
        {title}
      </h3>

      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-[#1b130e] mb-2">
            Record Name
          </label>
          <div className="bg-[#f3ece8] rounded-md">
            <div className="flex items-center">
              <code className="text-sm text-[#1b130e] break-all p-3 flex-1 min-w-0">
                {recordName}
              </code>
              {copyable && (
                <div className="flex-shrink-0 p-3 pl-2">
                  <button
                    onClick={() => handleCopy(recordName, 'recordName')}
                    className={`transition-all duration-200 ${
                      copiedField === 'recordName'
                        ? 'text-[#1b130e] scale-110'
                        : 'text-[#4a3728] hover:text-[#1b130e] hover:scale-105'
                    }`}
                    title={copiedField === 'recordName' ? 'Copied!' : 'Copy Record Name'}
                  >
                    {copiedField === 'recordName' ? (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-[#1b130e] mb-2">
            Record Type
          </label>
          <div className="bg-[#f3ece8] p-3 rounded-md">
            <code className="text-sm text-[#1b130e]">
              {recordType}
            </code>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-[#1b130e] mb-2">
            Record Value
          </label>
          <div className="relative">
            <div className="bg-[#f3ece8] rounded-md max-h-32 overflow-y-auto">
              <div className="flex">
                <code className="text-sm text-[#1b130e] break-all whitespace-pre-wrap p-3 flex-1 min-w-0">
                  {recordValue}
                </code>
                {copyable && (
                  <div className="flex-shrink-0 p-3 pl-2">
                    <button
                      onClick={() => handleCopy(recordValue, 'recordValue')}
                      className={`transition-all duration-200 ${
                        copiedField === 'recordValue'
                          ? 'text-[#1b130e] scale-110'
                          : 'text-[#4a3728] hover:text-[#1b130e] hover:scale-105'
                      }`}
                      title={copiedField === 'recordValue' ? 'Copied!' : 'Copy Record Value'}
                    >
                      {copiedField === 'recordValue' ? (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      )}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}
