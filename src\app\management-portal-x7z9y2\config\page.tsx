'use client';

import { useState, useEffect } from 'react';
import { AppConfig } from '@/lib/config/types';
import { <PERSON>, CardHeader, CardContent, CardFooter, CardTitle, CardDescription } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Checkbox } from '@/components/ui/Checkbox';
import { Spinner } from '@/components/ui/Spinner';
import { AdminSwitch } from '@/components/ui/Switch';
import { SettingsGroup, ConfigSection, AdminAccordion } from '@/components/ui/Accordion';

/**
 * Admin Configuration Page
 */
export default function AdminConfigPage() {
  const [config, setConfig] = useState<AppConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch configuration on component mount
  useEffect(() => {
    fetchConfig();
  }, []);

  // Fetch configuration from the API
  const fetchConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/management-portal-x7z9y2/config');
      const data = await response.json();

      if (data.success) {
        setConfig(data.data);
      } else {
        setError(data.error || 'Failed to fetch configuration');
      }
    } catch (err) {
      setError('An error occurred while fetching configuration');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!config) return;

    try {
      setSaving(true);
      const response = await fetch('/api/management-portal-x7z9y2/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Configuration updated successfully');
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(data.error || 'Failed to update configuration');
      }
    } catch (err) {
      setError('An error occurred while updating the configuration');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof AppConfig, value: any) => {
    if (!config) return;

    setConfig({
      ...config,
      [field]: value,
    });
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
        <div className="flex flex-col justify-center items-center h-64">
          <Spinner size="lg" variant="primary" className="mb-4" />
          <p className="text-gray-500">Loading configuration...</p>
        </div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              Configuration Not Available
            </CardTitle>
            <CardDescription>
              The application configuration could not be loaded.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button
              variant="primary"
              onClick={fetchConfig}
              leftIcon={
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
              }
            >
              Retry
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-[#ce601c]" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
          </svg>
          <h1 className="text-2xl font-bold text-gray-900">Application Settings</h1>
        </div>
        <p className="mt-2 text-sm text-gray-600 ml-11">
          Configure global application settings and system parameters.
        </p>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md animate-slideIn flex justify-between items-start">
          <div>
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-red-600 font-medium">Error</p>
            </div>
            <p className="mt-1 text-red-600 ml-7">{error}</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setError(null)}
          >
            Dismiss
          </Button>
        </div>
      )}

      {/* Success message */}
      {success && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md animate-slideIn flex items-start">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <div>
            <p className="text-green-600 font-medium">Success</p>
            <p className="text-green-600">{success}</p>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <SettingsGroup
          title="Application Configuration"
          defaultValue="email-settings"
          sections={[
            {
              value: "email-settings",
              title: "Email Settings",
              description: "Configure email expiration, limits, and behavior",
              icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
              ),
              content: (
                <div className="space-y-6">
              <div>
                <label htmlFor="emailExpirationMinutes" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Expiration Time (minutes)
                </label>
                <Input
                  type="number"
                  id="emailExpirationMinutes"
                  className="w-full sm:w-1/3"
                  min="1"
                  max="1440"
                  value={config.emailExpirationMinutes.toString()}
                  onChange={(e) => handleInputChange('emailExpirationMinutes', parseInt(e.target.value))}
                  required
                  leftIcon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  }
                />
                <p className="mt-1 text-xs text-gray-500">
                  How long temporary email addresses remain active before expiring.
                </p>
              </div>

              <div>
                <label htmlFor="autoRefreshInterval" className="block text-sm font-medium text-gray-700 mb-1">
                  Auto-Refresh Interval (seconds)
                </label>
                <Input
                  type="number"
                  id="autoRefreshInterval"
                  className="w-full sm:w-1/3"
                  min="5"
                  max="60"
                  value={config.autoRefreshInterval.toString()}
                  onChange={(e) => handleInputChange('autoRefreshInterval', parseInt(e.target.value))}
                  required
                  leftIcon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                    </svg>
                  }
                />
                <p className="mt-1 text-xs text-gray-500">
                  How often the inbox automatically refreshes to check for new emails.
                </p>
              </div>

              <div>
                <label htmlFor="maxEmailsPerAddress" className="block text-sm font-medium text-gray-700 mb-1">
                  Maximum Emails Per Address
                </label>
                <Input
                  type="number"
                  id="maxEmailsPerAddress"
                  className="w-full sm:w-1/3"
                  min="1"
                  max="1000"
                  value={config.maxEmailsPerAddress.toString()}
                  onChange={(e) => handleInputChange('maxEmailsPerAddress', parseInt(e.target.value))}
                  required
                  leftIcon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  }
                />
                <p className="mt-1 text-xs text-gray-500">
                  Maximum number of emails that can be received by a single temporary address.
                </p>
              </div>

              <div>
                <label htmlFor="cleanupIntervalMinutes" className="block text-sm font-medium text-gray-700 mb-1">
                  Cleanup Interval (minutes)
                </label>
                <Input
                  type="number"
                  id="cleanupIntervalMinutes"
                  className="w-full sm:w-1/3"
                  min="1"
                  max="1440"
                  value={config.cleanupIntervalMinutes.toString()}
                  onChange={(e) => handleInputChange('cleanupIntervalMinutes', parseInt(e.target.value))}
                  required
                  leftIcon={
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  }
                />
                <p className="mt-1 text-xs text-gray-500">
                  How often the system checks for and removes expired email addresses.
                </p>
              </div>
                </div>
              )
            },
            {
              value: "system-settings",
              title: "System Settings",
              description: "Maintenance mode and system-wide configurations",
              icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
              ),
              content: (
                <div className="space-y-6">
                  <AdminSwitch
                    label="Maintenance Mode"
                    description="When enabled, the site will display a maintenance message to all users."
                  checked={config.maintenanceMode}
                  status={config.maintenanceMode ? 'warning' : 'disabled'}
                  onCheckedChange={async (checked) => {
                    // Update local state immediately for better UX
                    handleInputChange('maintenanceMode', checked);

                    // Also call the dedicated maintenance mode API for immediate effect
                    try {
                      const response = await fetch('/api/admin-portal/maintenance-mode', {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ enabled: checked }),
                      });

                      if (!response.ok) {
                        throw new Error('Failed to update maintenance mode');
                      }

                      // Show a temporary success message
                      setSuccess(`Maintenance mode ${checked ? 'enabled' : 'disabled'}`);
                      setTimeout(() => setSuccess(null), 3000);
                    } catch (err) {
                      console.error('Error toggling maintenance mode:', err);
                      setError('Failed to update maintenance mode. Changes will apply after saving.');
                    }
                  }}
                />
                {config.maintenanceMode && (
                  <div className="mt-2 p-3 bg-orange-50 border border-orange-200 rounded-md">
                    <div className="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-orange-500 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <div>
                        <p className="text-sm font-medium text-orange-800">Maintenance Mode Active</p>
                        <p className="text-sm text-orange-700 mt-1">
                          The site is currently in maintenance mode. All visitors will see a maintenance page.
                          <a href="/maintenance" target="_blank" rel="noopener noreferrer" className="ml-1 text-orange-800 underline">
                            View maintenance page
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                  <div className="mt-6">
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <div className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <div>
                          <p className="text-sm font-medium text-blue-800">Analytics Information</p>
                          <p className="text-sm text-blue-700 mt-1">
                            Analytics is now handled by PostHog. Configure PostHog settings in your environment variables.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            }
          ]}
        />

        <div className="mt-8 pt-5 border-t border-gray-200">
          <div className="flex justify-end space-x-3">

              <div className="pt-5 border-t border-gray-200">
                <div className="flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={fetchConfig}
                    leftIcon={
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                      </svg>
                    }
                  >
                    Reset
                  </Button>
                  <Button
                    type="submit"
                    variant="primary"
                    isLoading={saving}
                    disabled={saving}
                    leftIcon={
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    }
                  >
                    Save Changes
                  </Button>
                </div>
              </div>
            </form>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#ce601c]" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            System Information
          </CardTitle>
          <CardDescription>
            Information about the current system status and version.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 p-4 rounded-lg hover:shadow-md transition-all duration-300">
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#ce601c]" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                <h3 className="text-sm font-medium text-gray-500 uppercase">Server Time</h3>
              </div>
              <p className="text-lg font-medium text-gray-900">{new Date().toLocaleString()}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg hover:shadow-md transition-all duration-300">
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#ce601c]" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                </svg>
                <h3 className="text-sm font-medium text-gray-500 uppercase">Application Version</h3>
              </div>
              <p className="text-lg font-medium text-gray-900">1.0.0</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
