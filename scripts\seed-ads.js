/**
 * Seed script for ad placements
 *
 * This script creates sample ad placements for testing
 */
const { createClient } = require('@supabase/supabase-js');
const path = require('path');
require('dotenv').config({ path: path.resolve(process.cwd(), '.env.local') });

// Log environment variables for debugging
console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
console.log('SUPABASE_SERVICE_ROLE_KEY exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Sample ad placements
const adPlacements = [
  {
    placement_id: 'sidebar-top',
    domain: 'fademail.site',
    ad_unit_id: 'ca-pub-1234567890/1234567890',
    is_enabled: true,
    device_types: ['desktop', 'tablet', 'mobile'],
    display_options: {
      position: 'relative',
      margin: '10px 0',
      padding: '10px',
      backgroundColor: '#f9f9f9',
      borderRadius: '4px',
      maxWidth: '100%',
      overflow: 'hidden',
      showBorder: true,
      borderColor: '#e0e0e0',
      labelText: 'Advertisement',
      showLabel: true,
    },
    schedule: {
      useSchedule: true,
      monday: {
        enabled: true,
        startTime: '09:00',
        endTime: '17:00',
      },
      tuesday: {
        enabled: true,
        startTime: '09:00',
        endTime: '17:00',
      },
      wednesday: {
        enabled: true,
        startTime: '09:00',
        endTime: '17:00',
      },
      thursday: {
        enabled: true,
        startTime: '09:00',
        endTime: '17:00',
      },
      friday: {
        enabled: true,
        startTime: '09:00',
        endTime: '17:00',
      },
      saturday: {
        enabled: false,
        startTime: '00:00',
        endTime: '23:59',
      },
      sunday: {
        enabled: false,
        startTime: '00:00',
        endTime: '23:59',
      },
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    placement_id: 'sidebar-bottom',
    domain: 'fademail.site',
    ad_unit_id: 'ca-pub-1234567890/2345678901',
    is_enabled: true,
    device_types: ['desktop', 'tablet'],
    display_options: {
      position: 'relative',
      margin: '10px 0',
      padding: '10px',
      backgroundColor: '#f9f9f9',
      borderRadius: '4px',
      maxWidth: '100%',
      overflow: 'hidden',
      showBorder: true,
      borderColor: '#e0e0e0',
      labelText: 'Advertisement',
      showLabel: true,
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    placement_id: 'header',
    domain: 'fademail.site',
    ad_unit_id: 'ca-pub-1234567890/3456789012',
    is_enabled: false,
    device_types: ['desktop'],
    display_options: {
      position: 'relative',
      margin: '0 0 20px 0',
      padding: '15px',
      backgroundColor: '#f0f0f0',
      borderRadius: '4px',
      maxWidth: '100%',
      overflow: 'hidden',
      showBorder: true,
      borderColor: '#d0d0d0',
      labelText: 'Sponsored',
      showLabel: true,
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    placement_id: 'footer',
    domain: 'fademail.site',
    ad_unit_id: 'ca-pub-1234567890/4567890123',
    is_enabled: true,
    device_types: ['desktop', 'tablet', 'mobile'],
    display_options: {
      position: 'relative',
      margin: '20px 0 0 0',
      padding: '10px',
      backgroundColor: '#f9f9f9',
      borderRadius: '4px',
      maxWidth: '100%',
      overflow: 'hidden',
      showBorder: true,
      borderColor: '#e0e0e0',
      labelText: 'Advertisement',
      showLabel: true,
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    placement_id: 'sidebar-top',
    domain: 'tempmail.dev',
    ad_unit_id: 'ca-pub-1234567890/5678901234',
    is_enabled: true,
    device_types: ['desktop', 'tablet', 'mobile'],
    display_options: {
      position: 'relative',
      margin: '10px 0',
      padding: '10px',
      backgroundColor: '#f9f9f9',
      borderRadius: '4px',
      maxWidth: '100%',
      overflow: 'hidden',
      showBorder: true,
      borderColor: '#e0e0e0',
      labelText: 'Advertisement',
      showLabel: true,
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
];

async function seedAdPlacements() {
  try {
    console.log('Seeding ad placements...');

    // Delete existing ad placements
    const { error: deleteError } = await supabase
      .from('ad_config')
      .delete()
      .neq('placement_id', 'dummy');

    if (deleteError) {
      console.error('Error deleting existing ad placements:', deleteError);
      return;
    }

    console.log('Deleted existing ad placements');

    // Insert new ad placements
    const { data, error } = await supabase
      .from('ad_config')
      .insert(adPlacements)
      .select();

    if (error) {
      console.error('Error seeding ad placements:', error);
      return;
    }

    console.log(`Successfully seeded ${data.length} ad placements`);
  } catch (error) {
    console.error('Error seeding ad placements:', error);
  }
}

// Run the seed function
seedAdPlacements()
  .then(() => {
    console.log('Seed completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Seed failed:', error);
    process.exit(1);
  });
