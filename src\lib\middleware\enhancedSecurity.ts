/**
 * Enhanced Security Middleware
 * Integrates all security systems into a unified middleware stack
 * Provides comprehensive protection against various abuse patterns
 */

import { NextRequest, NextResponse } from 'next/server';
import { sessionRateLimitMiddleware } from './sessionRateLimiting';
import { createClient } from '@supabase/supabase-js';

import { checkIPRotationMiddleware } from '../detection/ipRotationDetector';
import { checkEmailAbuseMiddleware } from '../detection/emailContentAnalyzer';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// Get Supabase client instance
async function getSupabaseClient() {
  return await createServerSupabaseClient();
}

// Initialize direct Supabase client for IP range blocking (to avoid import issues)
const directSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// IP blocking interfaces
interface IPBlockResult {
  blocked: boolean;
  reason?: string;
  range?: string;
}

/**
 * Check if an IP address is within any blocked range
 * Self-contained implementation to avoid import issues during deployment
 */
async function checkIPRangeBlocking(ipAddress: string): Promise<IPBlockResult> {
  try {
    // Query database for active blocked ranges that contain this IP
    const { data: blockedRanges, error } = await directSupabase
      .from('blocked_ip_ranges')
      .select('*')
      .eq('is_active', true)
      .or(`expires_at.is.null,expires_at.gt.${new Date().toISOString()}`);

    if (error) {
      console.error('Error checking blocked IP ranges:', error);
      return { blocked: false };
    }

    // Check each range using PostgreSQL's inet operators
    for (const range of blockedRanges || []) {
      const { data: isInRange, error: rangeError } = await directSupabase
        .rpc('is_ip_in_range', {
          ip_address: ipAddress,
          ip_range: range.ip_range
        });

      if (rangeError) {
        console.error('Error checking IP range:', rangeError);
        continue;
      }

      if (isInRange) {
        return {
          blocked: true,
          reason: range.reason,
          range: range.ip_range
        };
      }
    }

    return { blocked: false };
  } catch (error) {
    console.error('Error in checkIPRangeBlocking:', error);
    return { blocked: false };
  }
}

interface SecurityCheckResult {
  allowed: boolean;
  reason?: string;
  statusCode?: number;
  headers?: Record<string, string>;
  metadata?: any;
}

interface SecurityContext {
  sessionId: string;
  ipAddress: string;
  userAgent: string;
  endpoint: string;
  method: string;
  path: string;
}

/**
 * Main enhanced security middleware
 */
export async function enhancedSecurityMiddleware(
  req: NextRequest,
  endpoint: string = 'emailGeneration'
): Promise<SecurityCheckResult> {
  try {
    // Extract security context
    const context = extractSecurityContext(req, endpoint);
    
    // Log security check start
    await logSecurityEvent('security_check_started', context);

    // Run all security checks in parallel for performance
    const [
      ipBlockCheck,
      sessionRateCheck,
      ipRotationCheck,
      emailAbuseCheck
    ] = await Promise.all([
      checkIPBlockingStatus(context),
      checkSessionRateLimit(context),
      checkIPRotation(context),
      checkEmailAbuse(context)
    ]);

    // Process results in order of severity
    const checks = [
      { name: 'ip_blocking', result: ipBlockCheck, priority: 1 },
      { name: 'session_rate_limit', result: sessionRateCheck, priority: 2 },
      { name: 'ip_rotation', result: ipRotationCheck, priority: 3 },
      { name: 'email_abuse', result: emailAbuseCheck, priority: 4 }
    ];

    // Find the first blocking check
    const blockingCheck = checks.find(check => !check.result.allowed);

    if (blockingCheck) {
      // Log security block
      await logSecurityEvent('security_block', {
        ...context,
        blocked_by: blockingCheck.name,
        reason: blockingCheck.result.reason,
        metadata: blockingCheck.result.metadata
      });

      return {
        allowed: false,
        reason: blockingCheck.result.reason || 'Security check failed',
        statusCode: getStatusCodeForBlock(blockingCheck.name),
        headers: getSecurityHeaders(blockingCheck.result),
        metadata: {
          blocked_by: blockingCheck.name,
          check_results: checks.map(c => ({
            name: c.name,
            allowed: c.result.allowed,
            reason: c.result.reason
          }))
        }
      };
    }

    // All checks passed - log success
    await logSecurityEvent('security_check_passed', context);

    return {
      allowed: true,
      headers: getSecurityHeaders(),
      metadata: {
        checks_passed: checks.length,
        session_id: context.sessionId
      }
    };

  } catch (error) {
    console.error('Error in enhanced security middleware:', error);
    
    // Log error but fail open to avoid breaking service
    await logSecurityEvent('security_check_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      endpoint
    });

    return { allowed: true };
  }
}

/**
 * Extract security context from request
 */
function extractSecurityContext(req: NextRequest, endpoint: string): SecurityContext {
  const ipAddress = req.headers.get('x-forwarded-for')?.split(',')[0] ||
                   req.headers.get('x-real-ip') ||
                   'unknown';

  const sessionId = req.headers.get('x-session-id') ||
                   req.cookies.get('session-id')?.value ||
                   'anonymous';

  const userAgent = req.headers.get('user-agent') || 'unknown';

  return {
    sessionId,
    ipAddress,
    userAgent,
    endpoint,
    method: req.method,
    path: req.nextUrl.pathname
  };
}

/**
 * Check IP blocking
 */
async function checkIPBlockingStatus(context: SecurityContext): Promise<SecurityCheckResult> {
  try {
    // Use self-contained IP range blocking function to avoid import issues
    const result = await checkIPRangeBlocking(context.ipAddress);

    if (result.blocked) {
      return {
        allowed: false,
        reason: `IP blocked: ${result.reason}`,
        metadata: { ip_range: result.range }
      };
    }

    return { allowed: true };
  } catch (error) {
    console.error('Error in IP blocking check:', error);
    return { allowed: true };
  }
}

/**
 * Check session rate limiting
 */
async function checkSessionRateLimit(context: SecurityContext): Promise<SecurityCheckResult> {
  try {
    const result = await sessionRateLimitMiddleware(
      { headers: new Map([['x-session-id', context.sessionId]]) } as any,
      context.endpoint
    );

    if (!result.allowed) {
      return {
        allowed: false,
        reason: `Session rate limit exceeded`,
        metadata: {
          remaining: result.remaining,
          reset_time: result.resetTime,
          blocked: result.blocked
        }
      };
    }

    return { allowed: true, metadata: { remaining: result.remaining } };
  } catch (error) {
    console.error('Error in session rate limit check:', error);
    return { allowed: true };
  }
}



/**
 * Check IP rotation
 */
async function checkIPRotation(context: SecurityContext): Promise<SecurityCheckResult> {
  try {
    const result = await checkIPRotationMiddleware(context.sessionId, context.ipAddress);

    if (!result.allowed) {
      return {
        allowed: false,
        reason: result.reason || 'IP rotation detected',
        metadata: { analysis: result.analysis }
      };
    }

    return { allowed: true, metadata: { analysis: result.analysis } };
  } catch (error) {
    console.error('Error in IP rotation check:', error);
    return { allowed: true };
  }
}

/**
 * Check email abuse patterns
 */
async function checkEmailAbuse(context: SecurityContext): Promise<SecurityCheckResult> {
  try {
    const result = await checkEmailAbuseMiddleware(context.sessionId);

    if (!result.allowed) {
      return {
        allowed: false,
        reason: result.reason || 'Email abuse detected',
        metadata: { analysis: result.analysis }
      };
    }

    return { allowed: true, metadata: { analysis: result.analysis } };
  } catch (error) {
    console.error('Error in email abuse check:', error);
    return { allowed: true };
  }
}

/**
 * Get appropriate status code for different block types
 */
function getStatusCodeForBlock(blockType: string): number {
  switch (blockType) {
    case 'ip_blocking':
      return 403; // Forbidden
    case 'burst_protection':
    case 'session_rate_limit':
      return 429; // Too Many Requests
    case 'ip_rotation':
    case 'email_abuse':
      return 403; // Forbidden
    default:
      return 403;
  }
}

/**
 * Get security headers for response
 */
function getSecurityHeaders(checkResult?: any): Record<string, string> {
  const headers: Record<string, string> = {
    'X-Security-Check': 'enhanced',
    'X-Security-Version': '1.0'
  };

  if (checkResult?.resetTime) {
    headers['X-RateLimit-Reset'] = Math.ceil(checkResult.resetTime.getTime() / 1000).toString();
  }

  if (checkResult?.remaining !== undefined) {
    headers['X-RateLimit-Remaining'] = checkResult.remaining.toString();
  }

  return headers;
}

/**
 * Log security events
 */
async function logSecurityEvent(eventType: string, data: any): Promise<void> {
  try {
    const supabase = await getSupabaseClient();
    await supabase
      .from('security_events')
      .insert({
        event_type: eventType,
        session_id: data.sessionId || null,
        ip_address: data.ipAddress || null,
        severity: getSeverityForEvent(eventType),
        description: getDescriptionForEvent(eventType, data),
        metadata: data,
        action_taken: getActionForEvent(eventType)
      });
  } catch (error) {
    console.error('Error logging security event:', error);
  }
}

/**
 * Get severity level for event type
 */
function getSeverityForEvent(eventType: string): string {
  switch (eventType) {
    case 'security_block':
      return 'high';
    case 'security_check_error':
      return 'medium';
    case 'security_check_started':
    case 'security_check_passed':
      return 'low';
    default:
      return 'medium';
  }
}

/**
 * Get description for event type
 */
function getDescriptionForEvent(eventType: string, data: any): string {
  switch (eventType) {
    case 'security_check_started':
      return `Security check started for ${data.endpoint}`;
    case 'security_check_passed':
      return `All security checks passed for ${data.endpoint}`;
    case 'security_block':
      return `Request blocked by ${data.blocked_by}: ${data.reason}`;
    case 'security_check_error':
      return `Security check error: ${data.error}`;
    default:
      return `Security event: ${eventType}`;
  }
}

/**
 * Get action taken for event type
 */
function getActionForEvent(eventType: string): string {
  switch (eventType) {
    case 'security_block':
      return 'request_blocked';
    case 'security_check_passed':
      return 'request_allowed';
    case 'security_check_error':
      return 'error_logged';
    default:
      return 'logged';
  }
}

/**
 * Express.js compatible middleware wrapper
 */
export function createExpressMiddleware(endpoint: string = 'emailGeneration') {
  return async (req: any, res: any, next: any) => {
    try {
      // Convert Express request to NextRequest-like object
      const nextReq = {
        headers: new Map(Object.entries(req.headers)),
        cookies: { get: (name: string) => ({ value: req.cookies?.[name] }) },
        method: req.method,
        nextUrl: { pathname: req.path },
        ip: req.ip
      } as any;

      const result = await enhancedSecurityMiddleware(nextReq, endpoint);

      if (!result.allowed) {
        // Set security headers
        if (result.headers) {
          Object.entries(result.headers).forEach(([key, value]) => {
            res.setHeader(key, value);
          });
        }

        return res.status(result.statusCode || 403).json({
          error: 'Access denied',
          message: result.reason,
          metadata: result.metadata
        });
      }

      // Set success headers
      if (result.headers) {
        Object.entries(result.headers).forEach(([key, value]) => {
          res.setHeader(key, value);
        });
      }

      next();
    } catch (error) {
      console.error('Error in Express security middleware:', error);
      next(); // Fail open
    }
  };
}

export default {
  enhancedSecurityMiddleware,
  createExpressMiddleware
};
