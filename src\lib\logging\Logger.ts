/**
 * Logger class for Fademail
 *
 * This module provides a Logger class for logging events and errors
 */
import { logToDatabase } from './dbLogger';
import { realTimeMonitor } from './realTimeMonitor';

export interface LogEntry {
  id?: number;
  level: string;
  category: string;
  message: string;
  timestamp: string;
  metadata?: any;
}

export type LogListener = (logEntry: LogEntry) => void;

export class Logger {
  private static instance: Logger;
  private listeners: LogListener[] = [];

  /**
   * Get the singleton instance
   */
  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}

  /**
   * Add a log listener
   * @param listener The listener function
   */
  public addListener(listener: LogListener): void {
    this.listeners.push(listener);
    console.log(`Added log listener. Total listeners: ${this.listeners.length}`);
  }

  /**
   * Remove a log listener
   * @param listener The listener function to remove
   */
  public removeListener(listener: LogListener): void {
    const index = this.listeners.indexOf(listener);
    if (index !== -1) {
      this.listeners.splice(index, 1);
      console.log(`Removed log listener. Total listeners: ${this.listeners.length}`);
    } else {
      console.log('Attempted to remove listener that was not found');
    }
  }

  /**
   * Notify all listeners of a new log entry
   * @param logEntry The log entry
   */
  private notifyListeners(logEntry: LogEntry): void {
    console.log(`Notifying ${this.listeners.length} listeners of new log entry:`, logEntry);
    for (const listener of this.listeners) {
      try {
        listener(logEntry);
      } catch (error) {
        console.error('Error in log listener:', error);
      }
    }
  }

  /**
   * Log a debug message
   * @param category The log category
   * @param message The log message
   * @param metadata Additional metadata
   */
  public async debug(category: string, message: string, metadata?: any): Promise<void> {
    const logEntry: LogEntry = {
      level: 'debug',
      category,
      message,
      timestamp: new Date().toISOString(),
      metadata
    };

    // Notify listeners
    this.notifyListeners(logEntry);

    // Log to database (debug logs are not stored in the database)
    console.debug(`[DEBUG] [${category}] ${message}`, metadata);
  }

  /**
   * Log an info message
   * @param category The log category
   * @param message The log message
   * @param metadata Additional metadata
   */
  public async info(category: string, message: string, metadata?: any): Promise<void> {
    const logEntry: LogEntry = {
      level: 'info',
      category,
      message,
      timestamp: new Date().toISOString(),
      metadata
    };

    // Notify listeners
    this.notifyListeners(logEntry);

    // Log to database
    await logToDatabase('INFO', category, message, metadata);
    console.info(`[INFO] [${category}] ${message}`, metadata);
  }

  /**
   * Log a warning message
   * @param category The log category
   * @param message The log message
   * @param metadata Additional metadata
   */
  public async warning(category: string, message: string, metadata?: any): Promise<void> {
    const logEntry: LogEntry = {
      level: 'warning',
      category,
      message,
      timestamp: new Date().toISOString(),
      metadata
    };

    // Notify listeners
    this.notifyListeners(logEntry);

    // Log to database
    await logToDatabase('WARNING', category, message, metadata);
    console.warn(`[WARNING] [${category}] ${message}`, metadata);
  }

  /**
   * Log an error message
   * @param category The log category
   * @param message The log message
   * @param metadata Additional metadata
   */
  public async error(category: string, message: string, metadata?: any): Promise<void> {
    const logEntry: LogEntry = {
      level: 'error',
      category,
      message,
      timestamp: new Date().toISOString(),
      metadata
    };

    // Notify listeners
    this.notifyListeners(logEntry);

    // Log to database
    await logToDatabase('ERROR', category, message, metadata);
    console.error(`[ERROR] [${category}] ${message}`, metadata);

    // Process for alerts
    await realTimeMonitor.processLogEntry('error', category, message, metadata);
  }

  /**
   * Log a critical message
   * @param category The log category
   * @param message The log message
   * @param metadata Additional metadata
   */
  public async critical(category: string, message: string, metadata?: any): Promise<void> {
    const logEntry: LogEntry = {
      level: 'critical',
      category,
      message,
      timestamp: new Date().toISOString(),
      metadata
    };

    // Notify listeners
    this.notifyListeners(logEntry);

    // Log to database
    await logToDatabase('CRITICAL', category, message, metadata);
    console.error(`[CRITICAL] [${category}] ${message}`, metadata);

    // Process for alerts
    await realTimeMonitor.processLogEntry('critical', category, message, metadata);
  }
}

// Export singleton instance
export const logger = Logger.getInstance();
