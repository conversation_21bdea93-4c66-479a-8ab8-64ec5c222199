/**
 * SMTP Tester Utility Functions
 *
 * Helper functions for SMTP testing functionality
 */

import { v4 as uuidv4 } from 'uuid';

export function generateTestEmailAddress(): string {
  const uuid = uuidv4();
  const emailDomain = process.env.EMAIL_TESTER_DOMAIN || 'fademail.site';
  return `test-${uuid}@${emailDomain}`;
}

/**
 * Extract UUID from test email address
 * @param testAddress The test email address (e.g., <EMAIL>)
 * @returns The UUID portion or null if not found
 */
export function extractTestAddressId(testAddress: string): string | null {
  try {
    // Validate input
    if (!testAddress || typeof testAddress !== 'string') {
      return null;
    }

    // Extract UUID from test address format: test-{uuid}@domain.com
    const match = testAddress.match(/^test-([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})@/i);
    return match ? match[1] : null;
  } catch (error) {
    console.error('Error extracting test address ID:', error);
    return null;
  }
}

export function formatSmtpLogs(logs: string[]): string {
  return logs.join('\n');
}

export function getEncryptionDisplayName(encryption: string): string {
  switch (encryption) {
    case 'none':
      return 'None (Plain)';
    case 'tls':
      return 'TLS (STARTTLS)';
    case 'ssl':
      return 'SSL/TLS';
    default:
      return encryption;
  }
}

export function getDefaultPortForEncryption(encryption: string): number {
  switch (encryption) {
    case 'none':
      return 25;
    case 'tls':
      return 587;
    case 'ssl':
      return 465;
    default:
      return 587;
  }
}

export function maskPassword(password: string): string {
  if (password.length <= 4) {
    return '*'.repeat(password.length);
  }
  return password.substring(0, 2) + '*'.repeat(password.length - 4) + password.substring(password.length - 2);
}

export function formatTestDuration(startTime: Date, endTime: Date): string {
  const duration = endTime.getTime() - startTime.getTime();
  return `${duration}ms`;
}

export function createTestEmailContent(testAddress: string): { subject: string; text: string; html: string } {
  const subject = `SMTP Test from VanishPost - ${new Date().toISOString()}`;

  const text = `
This is a test email sent from VanishPost's SMTP Tester Tool.

Test Details:
- Test Address: ${testAddress}
- Timestamp: ${new Date().toISOString()}
- Purpose: SMTP connectivity and email authentication testing

This email was sent to verify your SMTP server configuration and will be automatically analyzed for email authentication (SPF, DKIM, DMARC) compliance.

---
VanishPost Email Tools
https://vanishpost.com/tools/smtp-tester
  `.trim();

  const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>SMTP Test from VanishPost</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
  <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #66b077;">
    <h2 style="color: #1b130e; margin-top: 0;">SMTP Test from VanishPost</h2>
    <p>This is a test email sent from VanishPost's SMTP Tester Tool.</p>

    <div style="background: white; padding: 15px; border-radius: 6px; margin: 15px 0;">
      <h3 style="color: #4a3728; margin-top: 0;">Test Details:</h3>
      <ul style="margin: 0; padding-left: 20px;">
        <li><strong>Test Address:</strong> ${testAddress}</li>
        <li><strong>Timestamp:</strong> ${new Date().toISOString()}</li>
        <li><strong>Purpose:</strong> SMTP connectivity and email authentication testing</li>
      </ul>
    </div>

    <p>This email was sent to verify your SMTP server configuration and will be automatically analyzed for email authentication (SPF, DKIM, DMARC) compliance.</p>

    <hr style="border: none; border-top: 1px solid #ddd; margin: 20px 0;">
    <p style="font-size: 14px; color: #666;">
      <strong>VanishPost Email Tools</strong><br>
      <a href="https://vanishpost.com/tools/smtp-tester" style="color: #66b077;">https://vanishpost.com/tools/smtp-tester</a>
    </p>
  </div>
</body>
</html>
  `.trim();

  return { subject, text, html };
}
