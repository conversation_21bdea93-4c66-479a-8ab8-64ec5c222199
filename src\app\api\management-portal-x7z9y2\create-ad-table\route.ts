/**
 * API route to create the ad_config table
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/management-portal-x7z9y2/create-ad-table
 * 
 * Create the ad_config table if it doesn't exist
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();
    
    // Check if ad_config table exists
    const { data: checkData, error: checkError } = await supabase
      .from('ad_config')
      .select('*')
      .limit(1);
    
    // If table exists, return success
    if (!checkError) {
      return NextResponse.json({
        success: true,
        message: 'ad_config table already exists',
        data: checkData
      });
    }
    
    // Try to create the table using SQL
    try {
      const { error: createError } = await supabase.rpc('execute_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS ad_config (
            placement_id VARCHAR(50) NOT NULL,
            domain VARCHAR(100) NOT NULL,
            ad_unit_id VARCHAR(100) NOT NULL,
            is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
            device_types JSONB NOT NULL,
            display_options JSONB,
            schedule JSONB,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            PRIMARY KEY (placement_id, domain)
          );
        `
      });
      
      if (createError) {
        return NextResponse.json({
          success: false,
          message: 'Failed to create ad_config table using RPC',
          error: createError.message,
          sql: `
            CREATE TABLE IF NOT EXISTS ad_config (
              placement_id VARCHAR(50) NOT NULL,
              domain VARCHAR(100) NOT NULL,
              ad_unit_id VARCHAR(100) NOT NULL,
              is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
              device_types JSONB NOT NULL,
              display_options JSONB,
              schedule JSONB,
              created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
              updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
              PRIMARY KEY (placement_id, domain)
            );
          `
        }, { status: 500 });
      }
      
      // Check if table was created successfully
      const { data: verifyData, error: verifyError } = await supabase
        .from('ad_config')
        .select('*')
        .limit(1);
      
      if (verifyError) {
        return NextResponse.json({
          success: false,
          message: 'Failed to verify ad_config table creation',
          error: verifyError.message
        }, { status: 500 });
      }
      
      return NextResponse.json({
        success: true,
        message: 'ad_config table created successfully'
      });
    } catch (error) {
      return NextResponse.json({
        success: false,
        message: 'Exception occurred while creating ad_config table',
        error: error instanceof Error ? error.message : 'Unknown error',
        sql: `
          CREATE TABLE IF NOT EXISTS ad_config (
            placement_id VARCHAR(50) NOT NULL,
            domain VARCHAR(100) NOT NULL,
            ad_unit_id VARCHAR(100) NOT NULL,
            is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
            device_types JSONB NOT NULL,
            display_options JSONB,
            schedule JSONB,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            PRIMARY KEY (placement_id, domain)
          );
        `
      }, { status: 500 });
    }
  } catch (error) {
    logError('api', 'Error creating ad_config table', { error });
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create ad_config table',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
