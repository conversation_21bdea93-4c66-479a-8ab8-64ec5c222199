/**
 * <PERSON><PERSON><PERSON> to initialize the analytics database
 * Run this script once to create the necessary tables
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'tempmail_web_user',
  password: process.env.DB_PASSWORD || 'yiWTpFChtFW3neWCS',
  database: process.env.DB_NAME || 'tempmail_web',
};

async function initAnalyticsDb() {
  console.log('Initializing analytics database...');

  try {
    // Create a connection
    const connection = await mysql.createConnection(dbConfig);

    console.log('Connected to database successfully');

    // Create the analytics_events table if it doesn't exist
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS analytics_events (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event_type VARCHAR(50) NOT NULL,
        page_path VARCHAR(255),
        referrer VARCHAR(255),
        country VARCHAR(50),
        browser VARCHAR(50),
        device_type VARCHAR(20),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        additional_data JSON
      )
    `);

    console.log('Created analytics_events table');

    // Create indexes for faster queries
    try {
      await connection.execute(`CREATE INDEX idx_event_type ON analytics_events(event_type)`);
      console.log('Created index on event_type');
    } catch (error) {
      console.log('Index on event_type may already exist:', error.message);
    }

    try {
      await connection.execute(`CREATE INDEX idx_timestamp ON analytics_events(timestamp)`);
      console.log('Created index on timestamp');
    } catch (error) {
      console.log('Index on timestamp may already exist:', error.message);
    }

    try {
      await connection.execute(`CREATE INDEX idx_page_path ON analytics_events(page_path)`);
      console.log('Created index on page_path');
    } catch (error) {
      console.log('Index on page_path may already exist:', error.message);
    }

    console.log('Analytics database initialized successfully');

    // Close the connection
    await connection.end();

    return { success: true };
  } catch (error) {
    console.error('Failed to initialize analytics database:', error);
    return { success: false, error };
  }
}

// Run the initialization
initAnalyticsDb()
  .then(result => {
    if (result.success) {
      console.log('Analytics database setup complete');
      process.exit(0);
    } else {
      console.error('Analytics database setup failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
