// Shared DNS Validation Utilities - Isolated implementation
import { promises as dns } from 'dns';
import { DnsValidationResult, DnsValidationOptions } from '@/types/dns';

/**
 * Default DNS validation configuration
 */
const DEFAULT_OPTIONS: DnsValidationOptions = {
  timeout: 5000,
  retries: 3,
  retryDelay: 1000,
  dnsServers: ['8.8.8.8', '8.8.4.4', '1.1.1.1', '1.0.0.1'],
};

/**
 * Validate DNS TXT record
 * @param domain - Domain to query
 * @param recordName - Full record name to query
 * @param expectedValue - Expected record value (optional)
 * @param options - Validation options
 * @returns Promise<DnsValidationResult>
 */
export async function validateTxtRecord(
  domain: string,
  recordName: string,
  expectedValue?: string,
  options: Partial<DnsValidationOptions> = {}
): Promise<DnsValidationResult> {
  const config = { ...DEFAULT_OPTIONS, ...options };
  const errors: string[] = [];
  let records: string[] = [];
  let exists = false;
  let isValid = false;

  try {
    // Set DNS servers if specified
    if (config.dnsServers && config.dnsServers.length > 0) {
      dns.setServers(config.dnsServers);
    }

    // Query TXT records with timeout
    const txtRecords = await Promise.race([
      dns.resolveTxt(recordName),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('DNS query timeout')), config.timeout)
      ),
    ]);

    // Flatten TXT record arrays and join multi-part records
    records = txtRecords.map(record =>
      Array.isArray(record) ? record.join('') : record
    );

    exists = records.length > 0;

    // Validate against expected value if provided
    if (expectedValue && exists) {
      isValid = records.some(record =>
        record.trim() === expectedValue.trim()
      );
    } else {
      isValid = exists;
    }

    if (!exists) {
      errors.push('No TXT records found');
    } else if (expectedValue && !isValid) {
      errors.push('TXT record value does not match expected value');
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown DNS error';
    errors.push(`DNS resolution failed: ${errorMessage}`);

    // Retry logic
    if (config.retries > 0) {
      await new Promise(resolve => setTimeout(resolve, config.retryDelay));
      return validateTxtRecord(domain, recordName, expectedValue, {
        ...options,
        retries: config.retries - 1,
      });
    }
  }

  return {
    exists,
    records,
    isValid,
    errors,
    lastChecked: new Date().toISOString(),
    recordType: 'TXT',
    domain,
    recordName,
  };
}

/**
 * Validate DKIM DNS record
 * @param domain - Domain name
 * @param selector - DKIM selector
 * @param expectedPublicKey - Expected public key (optional)
 * @param options - Validation options
 * @returns Promise<DnsValidationResult>
 */
export async function validateDkimRecord(
  domain: string,
  selector: string,
  expectedPublicKey?: string,
  options: Partial<DnsValidationOptions> = {}
): Promise<DnsValidationResult> {
  const recordName = `${selector}._domainkey.${domain}`;

  try {
    const result = await validateTxtRecord(domain, recordName, undefined, options);

    // Additional DKIM-specific validation
    if (result.exists && result.records.length > 0) {
      const dkimRecord = result.records[0];

      // Check for DKIM format
      if (!dkimRecord.includes('v=DKIM1')) {
        result.errors.push('Record does not contain DKIM version identifier');
        result.isValid = false;
      }

      if (!dkimRecord.includes('k=rsa')) {
        result.errors.push('Record does not specify RSA key type');
        result.isValid = false;
      }

      if (!dkimRecord.includes('p=')) {
        result.errors.push('Record does not contain public key');
        result.isValid = false;
      }

      // Validate public key if provided
      if (expectedPublicKey) {
        const publicKeyMatch = dkimRecord.includes(`p=${expectedPublicKey}`);
        if (!publicKeyMatch) {
          result.errors.push('Public key does not match expected value');
          result.isValid = false;
        }
      }
    }

    return result;
  } catch (error) {
    return {
      exists: false,
      records: [],
      isValid: false,
      errors: [`DKIM validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      lastChecked: new Date().toISOString(),
      recordType: 'TXT',
      domain,
      recordName,
    };
  }
}

/**
 * Validate DMARC DNS record
 * @param domain - Domain name
 * @param expectedPolicy - Expected DMARC policy (optional)
 * @param options - Validation options
 * @returns Promise<DnsValidationResult>
 */
export async function validateDmarcRecord(
  domain: string,
  expectedPolicy?: string,
  options: Partial<DnsValidationOptions> = {}
): Promise<DnsValidationResult> {
  const recordName = `_dmarc.${domain}`;

  try {
    const result = await validateTxtRecord(domain, recordName, undefined, options);

    // Additional DMARC-specific validation
    if (result.exists && result.records.length > 0) {
      const dmarcRecord = result.records[0];

      // Check for DMARC format
      if (!dmarcRecord.includes('v=DMARC1')) {
        result.errors.push('Record does not contain DMARC version identifier');
        result.isValid = false;
      }

      if (!dmarcRecord.includes('p=')) {
        result.errors.push('Record does not contain policy directive');
        result.isValid = false;
      }

      // Validate policy if provided
      if (expectedPolicy) {
        const policyMatch = dmarcRecord.includes(`p=${expectedPolicy}`);
        if (!policyMatch) {
          result.errors.push(`Policy does not match expected value: ${expectedPolicy}`);
          result.isValid = false;
        }
      }

      // Check for required reporting address
      if (!dmarcRecord.includes('rua=')) {
        result.errors.push('Record should include reporting address (rua)');
        // This is a warning, not a validation failure
      }
    }

    return result;
  } catch (error) {
    return {
      exists: false,
      records: [],
      isValid: false,
      errors: [`DMARC validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      lastChecked: new Date().toISOString(),
      recordType: 'TXT',
      domain,
      recordName,
    };
  }
}

/**
 * Validate domain format
 * @param domain - Domain to validate
 * @returns boolean - True if valid domain format
 */
export function validateDomainFormat(domain: string): boolean {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
  return domainRegex.test(domain) && domain.length <= 253;
}

/**
 * Sanitize domain input
 * @param domain - Domain to sanitize
 * @returns Sanitized domain
 */
export function sanitizeDomain(domain: string): string {
  return domain.toLowerCase().trim();
}
