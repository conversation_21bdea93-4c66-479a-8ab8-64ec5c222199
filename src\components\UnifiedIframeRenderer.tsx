'use client';

import React, { useRef, useEffect, useState, useMemo } from 'react';

interface UnifiedIframeRendererProps {
  /**
   * The HTML content to render in the iframe
   */
  html: string;

  /**
   * Optional className for the iframe container
   */
  className?: string;

  /**
   * Optional array of web fonts to load in the iframe
   * Each font should be a Google Fonts URL or similar
   */
  webFonts?: string[];
}

/**
 * Helper function to generate a simple content hash for memoization
 * This prevents unnecessary re-renders when the content hasn't changed
 */
function generateContentHash(content: string): string {
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString(36);
}

/**
 * UnifiedIframeRenderer Component
 *
 * A streamlined, unified implementation for rendering email HTML content in an iframe.
 * Features:
 * - Complete style isolation through iframe
 * - Responsive scaling for mobile devices
 * - Automatic height adjustment using ResizeObserver
 * - Minimal direct DOM manipulation
 * - Server-side rendering compatibility
 */
export default function UnifiedIframeRenderer({
  html,
  className = '',
  webFonts = []
}: UnifiedIframeRendererProps) {
  // Create a ref for the iframe
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // State to track if the component is mounted (for SSR compatibility)
  const [isMounted, setIsMounted] = useState(false);

  // Generate a content hash for memoization
  const contentHash = useMemo(() =>
    generateContentHash(html + webFonts.join('')),
    [html, webFonts]
  );

  // Create the HTML content with responsive scaling for mobile
  const htmlContent = useMemo(() => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, minimum-scale=1">
          <meta name="format-detection" content="telephone=no">
          ${webFonts.map(font => `<link href="${font}" rel="stylesheet">`).join('')}
          <style>
            /* Reset styles */
            html, body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              line-height: 1.4;
              overflow: hidden;
              -webkit-text-size-adjust: 100%;
              -webkit-font-smoothing: antialiased;
              height: 100%;
              width: 100%;
            }

            /* Email wrapper */
            .email-wrapper {
              max-width: 100%;
              overflow: visible;
              position: relative;
              min-height: 100%;
              padding-bottom: 20px; /* Add padding to ensure content isn't cut off */
            }

            /* Ensure images don't overflow */
            img {
              max-width: 100%;
              height: auto !important;
            }

            /* Ensure tables don't overflow */
            table {
              max-width: 100%;
            }

            /* Default link styling */
            a:not([style]) {
              color: #0000EE;
              text-decoration: underline;
            }

            /* Mobile scaling */
            @media (max-width: 640px) {
              .email-mobile-scale {
                transform-origin: top left;
                transition: transform 0.2s ease-in-out;
              }
            }

            /* Print styles */
            @media print {
              body {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
              }

              .email-wrapper {
                width: 100% !important;
                max-width: 100% !important;
                transform: none !important;
              }
            }
          </style>
          <script>
            // This script handles responsive scaling for fixed-width emails
            document.addEventListener('DOMContentLoaded', function() {
              // Function to handle email scaling
              function handleEmailScaling() {
                var wrapper = document.querySelector('.email-wrapper');
                if (!wrapper || !wrapper.firstElementChild) return;

                var content = wrapper.firstElementChild;
                var viewportWidth = window.innerWidth;

                // Find all fixed-width elements
                var fixedWidthElements = document.querySelectorAll(
                  'table[width], table[style*="width"], div[style*="width"], td[width], ' +
                  'img[width], center > table, .container, [class*="container"], ' +
                  '[style*="margin-left: auto"][style*="margin-right: auto"]'
                );
                var maxContentWidth = 0;

                // Find the maximum content width
                fixedWidthElements.forEach(function(el) {
                  var elWidth = el.getAttribute('width') ||
                               (el.style.width ? parseInt(el.style.width, 10) : 0) ||
                               el.offsetWidth;

                  // Convert to number
                  elWidth = parseInt(elWidth, 10);

                  if (elWidth > maxContentWidth) {
                    maxContentWidth = elWidth;
                  }
                });

                // If no fixed width elements found, use the content width
                if (maxContentWidth === 0) {
                  maxContentWidth = content.offsetWidth;
                }

                // Only scale if content is wider than viewport and we're on mobile
                if (maxContentWidth > viewportWidth && viewportWidth < 640) {
                  var scale = viewportWidth / maxContentWidth;

                  // Minimum scale to prevent too small content
                  var minScale = viewportWidth < 375 ? 0.5 : 0.6;

                  // Apply minimum scale
                  scale = Math.max(scale, minScale);

                  // Apply the scale transform
                  content.classList.add('email-mobile-scale');
                  content.style.transform = 'scale(' + scale + ')';
                  content.style.width = (maxContentWidth) + 'px';
                  content.style.transformOrigin = 'top left';

                  // Calculate the scaled height with extra padding to ensure content isn't cut off
                  const scaledHeight = (content.offsetHeight * scale) + 30;

                  // Only adjust height if content is actually tall (more than 500px)
                  // This prevents unnecessary height adjustments for short content
                  if (content.offsetHeight > 500) {
                    // Set wrapper height to match scaled content
                    wrapper.style.height = scaledHeight + 'px';

                    // Notify parent about height change
                    window.parent.postMessage({
                      type: 'iframe-resize',
                      height: scaledHeight
                    }, '*');
                  } else {
                    // For short content, use a reasonable fixed height
                    wrapper.style.height = 'auto';

                    // Notify parent about minimal height change
                    window.parent.postMessage({
                      type: 'iframe-resize',
                      height: Math.max(400, scaledHeight) // Use at least 400px height
                    }, '*');
                  }
                } else {
                  // Reset scaling if not needed
                  if (content.classList.contains('email-mobile-scale')) {
                    content.classList.remove('email-mobile-scale');
                    content.style.transform = '';
                    content.style.width = '';
                  }

                  // Only adjust height if content is actually tall (more than 500px)
                  // This prevents unnecessary height adjustments for short content
                  if (content.offsetHeight > 500) {
                    // Notify parent about height with extra padding to ensure content isn't cut off
                    window.parent.postMessage({
                      type: 'iframe-resize',
                      height: content.offsetHeight + 30
                    }, '*');
                  } else {
                    // For short content, use a reasonable fixed height
                    // Notify parent about minimal height change
                    window.parent.postMessage({
                      type: 'iframe-resize',
                      height: Math.max(400, content.offsetHeight + 30) // Use at least 400px height
                    }, '*');
                  }
                }
              }

              // Initial scaling
              handleEmailScaling();

              // Re-scale on resize
              window.addEventListener('resize', handleEmailScaling);

              // Handle links to make them work properly
              document.querySelectorAll('a[href]').forEach(function(link) {
                link.addEventListener('click', function(e) {
                  e.preventDefault();
                  var href = this.getAttribute('href');

                  // Open links in new tab/window
                  if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
                    window.open(href, '_blank');
                  }
                });
              });

              // Create a ResizeObserver to monitor content size changes
              if (typeof ResizeObserver !== 'undefined') {
                const wrapper = document.querySelector('.email-wrapper');
                if (wrapper) {
                  const resizeObserver = new ResizeObserver(function() {
                    handleEmailScaling();
                  });
                  resizeObserver.observe(wrapper);
                }
              }
            });
          </script>
        </head>
        <body>
          <div class="email-wrapper">
            ${html}
          </div>
        </body>
      </html>
    `;
  }, [html, webFonts, contentHash]);

  // Effect to handle client-side rendering
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Effect to handle iframe content and message listening
  useEffect(() => {
    if (!isMounted || !iframeRef.current) return;

    const iframe = iframeRef.current;

    try {
      // Set initial height based on whether this is likely a guide email
      // Guide emails are typically shorter, so we use a smaller initial height
      const isLikelyGuideEmail = html.includes('Welcome to Fademail') ||
                                html.includes('Fademail Tips') ||
                                html.includes('Fademail Security');

      // Set a conservative initial height to prevent layout shifts
      iframe.style.height = isLikelyGuideEmail ? '400px' : '600px';

      // Write the HTML content
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) return;

      iframeDoc.open();
      // Cast to any to avoid the deprecated signature warning
      // This is a safe workaround as the functionality remains the same
      (iframeDoc as any).write(htmlContent);
      iframeDoc.close();

      // Listen for resize messages from the iframe content
      const handleMessage = (event: MessageEvent) => {
        if (event.data && event.data.type === 'iframe-resize' &&
            typeof event.data.height === 'number') {

          // Get the current height of the iframe
          const currentHeight = parseInt(iframe.style.height, 10) || 400;
          const newHeight = event.data.height;

          // Only update height if it's significantly different (more than 100px)
          // This prevents small height adjustments that can cause layout shifts
          if (Math.abs(currentHeight - newHeight) > 100) {
            iframe.style.height = `${newHeight}px`;
          } else if (newHeight > currentHeight) {
            // If new height is larger but not by much, still update but smoothly
            iframe.style.transition = 'height 0.3s ease-in-out';
            iframe.style.height = `${newHeight}px`;
          }
          // If new height is smaller but not by much, don't update to prevent jumpiness
        }
      };

      // Add message listener
      window.addEventListener('message', handleMessage);

      // Clean up
      return () => {
        window.removeEventListener('message', handleMessage);
      };
    } catch (error) {
      console.error('Error rendering email in iframe:', error);
    }
  }, [isMounted, htmlContent]);

  // Loading state for server-side rendering and initial client render
  const loadingState = (
    <div className="animate-pulse flex flex-col space-y-4 p-4" style={{ minHeight: '200px' }}>
      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      <div className="h-4 bg-gray-200 rounded w-full"></div>
      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
    </div>
  );

  return (
    <div
      className={`unified-iframe-container ${className}`}
      style={{
        width: '100%',
        overflow: 'hidden',
        position: 'relative',
        transition: 'opacity 0.3s ease-in-out'
      }}
    >
      {!isMounted ? (
        // Server-side and initial client render - show loading state
        loadingState
      ) : (
        // Client-side render after hydration - use iframe
        <iframe
          ref={iframeRef}
          title="Email content"
          style={{
            width: '100%',
            border: 'none',
            overflow: 'hidden',
            minHeight: '400px',
            display: 'block',
            transition: 'opacity 0.3s ease-in-out, height 0.3s ease-in-out',
            transform: 'translateZ(0)' // Force hardware acceleration for smoother transitions
          }}
          // Using CSS to prevent scrolling instead of deprecated scrolling attribute
          sandbox="allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-scripts"
        />
      )}
    </div>
  );
}
