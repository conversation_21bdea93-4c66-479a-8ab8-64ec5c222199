'use client';

import { useState } from 'react';
import AdSenseAd from '@/components/AdSenseAd';

export default function AdSenseDemoPage() {
  const [showAds, setShowAds] = useState(true);
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">AdSense Integration Demo</h1>
      
      <div className="mb-6">
        <button 
          onClick={() => setShowAds(!showAds)}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
        >
          {showAds ? 'Hide Ads' : 'Show Ads'}
        </button>
      </div>
      
      {showAds && (
        <div className="space-y-8">
          <section className="p-4 border border-gray-200 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Horizontal Banner Ad</h2>
            <div className="bg-gray-50 p-2 rounded">
              <AdSenseAd 
                adSlot="1234567890" 
                adFormat="horizontal"
                style={{ minHeight: '90px', maxHeight: '100px' }}
              />
            </div>
            <p className="mt-4 text-sm text-gray-600">
              This is a horizontal banner ad that adapts to the width of its container.
              Replace the adSlot with your actual AdSense ad slot ID.
            </p>
          </section>
          
          <section className="p-4 border border-gray-200 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Responsive Rectangle Ad</h2>
            <div className="bg-gray-50 p-2 rounded">
              <AdSenseAd 
                adSlot="0987654321" 
                adFormat="rectangle"
                style={{ minHeight: '250px' }}
              />
            </div>
            <p className="mt-4 text-sm text-gray-600">
              This is a responsive rectangle ad that works well in content.
              Replace the adSlot with your actual AdSense ad slot ID.
            </p>
          </section>
          
          <section className="p-4 border border-gray-200 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Auto-sized Responsive Ad</h2>
            <div className="bg-gray-50 p-2 rounded">
              <AdSenseAd 
                adSlot="1122334455" 
                adFormat="auto"
                fullWidthResponsive={true}
                style={{ minHeight: '200px' }}
              />
            </div>
            <p className="mt-4 text-sm text-gray-600">
              This is an auto-sized responsive ad that adapts to available space.
              Replace the adSlot with your actual AdSense ad slot ID.
            </p>
          </section>
        </div>
      )}
      
      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Implementation Notes</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>The AdSenseScript component is loaded once in the root layout</li>
          <li>Individual ad units are placed using the AdSenseAd component</li>
          <li>Each ad unit needs a unique ad slot ID from your AdSense account</li>
          <li>Use different ad formats based on placement location</li>
          <li>Always include fallback content or minimum heights to prevent layout shifts</li>
        </ul>
      </div>
    </div>
  );
}
