/**
 * API route for verifying if an email address is registered
 */
import { NextRequest, NextResponse } from 'next/server';
import { getTempEmailByAddress } from '@/lib/supabase-db';
import { logger } from '@/lib/logging/Logger';

/**
 * GET /api/verify
 *
 * Verify if an email address is registered in the database
 */
export async function GET(request: NextRequest) {
  try {
    // Get the email address from the query parameters
    const url = new URL(request.url);
    const address = url.searchParams.get('address');

    if (!address) {
      return NextResponse.json({
        success: false,
        message: 'Email address is required'
      }, { status: 400 });
    }

    // Log the verification attempt
    await logger.info('EMAIL_VERIFICATION', `Verifying email address: ${address}`);

    // Check if the email address exists in the database
    const tempEmail = await getTempEmailByAddress(address);

    if (!tempEmail) {
      await logger.info('EMAIL_VERIFICATION', `Email address not found: ${address}`);
      return NextResponse.json({
        success: false,
        message: 'Email address not found'
      }, { status: 404 });
    }

    // Check if the email address has expired
    const now = new Date();
    if (tempEmail.expirationDate < now) {
      await logger.info('EMAIL_VERIFICATION', `Email address has expired: ${address}`);
      return NextResponse.json({
        success: false,
        message: 'Email address has expired'
      }, { status: 410 });
    }

    // Return success
    await logger.info('EMAIL_VERIFICATION', `Email address verified successfully: ${address}`);
    return NextResponse.json({
      success: true,
      message: 'Email address is registered',
      emailAddress: tempEmail.emailAddress,
      expirationDate: tempEmail.expirationDate
    });
  } catch (error) {
    await logger.error('EMAIL_VERIFICATION', `Failed to verify email address: ${error instanceof Error ? error.message : String(error)}`, { error });

    return NextResponse.json({
      success: false,
      message: 'Failed to verify email address'
    }, { status: 500 });
  }
}
