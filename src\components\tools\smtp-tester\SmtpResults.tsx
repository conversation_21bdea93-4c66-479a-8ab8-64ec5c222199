/**
 * SMTP Results Component
 *
 * Displays SMTP test results with authentication analysis
 */

'use client';

import React, { useState } from 'react';
import { SmtpTestResult } from '@/types/smtp';

interface SmtpResultsProps {
  result: SmtpTestResult;
  testMode: 'auto' | 'custom';
}

export default function SmtpResults({ result, testMode }: SmtpResultsProps) {
  const [showLogs, setShowLogs] = useState(false);
  const [copiedLogs, setCopiedLogs] = useState(false);

  // State for accordion sections
  const [expandedProtocols, setExpandedProtocols] = useState<{
    spf: boolean;
    dkim: boolean;
    dmarc: boolean;
    mx: boolean;
    ptr: boolean;
  }>({
    spf: false,
    dkim: false,
    dmarc: false,
    mx: false,
    ptr: false,
  });

  // Toggle function for accordion sections
  const toggleProtocol = (protocol: keyof typeof expandedProtocols) => {
    setExpandedProtocols(prev => ({
      ...prev,
      [protocol]: !prev[protocol]
    }));
  };

  // Helper function to get preview text for collapsed state
  const getPreviewText = (protocolData: any, protocolName: string): string => {
    if (!protocolData) return 'No data available';

    const reason = protocolData.reason || protocolData.info || protocolData.hostname;
    const details = protocolData.details;

    if (reason) {
      return reason.length > 60 ? `${reason.substring(0, 60)}...` : reason;
    }
    if (details) {
      return details.length > 60 ? `${details.substring(0, 60)}...` : details;
    }

    return 'Authentication check completed';
  };

  const copyLogsToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(result.logs);
      setCopiedLogs(true);
      setTimeout(() => setCopiedLogs(false), 2000);
    } catch (error) {
      console.error('Failed to copy logs:', error);
    }
  };

  const getStatusIcon = (success: boolean) => {
    if (success) {
      return (
        <svg className="w-6 h-6 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    } else {
      return (
        <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    }
  };

  // Helper function specifically for PTR (Reverse DNS) status determination
  const getPtrStatus = (ptrData: any): 'pass' | 'warning' | 'fail' => {
    if (!ptrData) return 'fail';

    // Check direct result field
    const result = ptrData.result || ptrData.status;

    // Direct result check - Email Tester Tool uses these exact values
    if (result === 'pass') return 'pass';
    if (result === 'warning') return 'warning';
    if (result === 'fail') return 'fail';

    // Check for analysis structure
    if (ptrData.analysis?.reverseDns?.status === 'pass') return 'pass';
    if (ptrData.analysis?.reverseDns?.status === 'warning') return 'warning';
    if (ptrData.analysis?.reverseDns?.status === 'fail') return 'fail';

    // Special handling: If PTR has a hostname but has issues, it's likely a warning state
    // This matches the Email Tester Tool logic where PTR passes with warnings
    if (ptrData.hostname && ptrData.issues?.length > 0) {
      return 'warning';
    }

    // If PTR has a hostname but no explicit result, assume it's working (pass)
    if (ptrData.hostname) {
      return 'pass';
    }

    // Default to fail if we can't determine status
    return 'fail';
  };

  // Helper function to get status text
  const getStatusText = (status: 'pass' | 'warning' | 'fail'): string => {
    switch (status) {
      case 'pass': return 'Passed';
      case 'warning': return 'Warning';
      case 'fail': return 'Failed';
      default: return 'Failed';
    }
  };

  const getAuthStatusIcon = (status: 'pass' | 'warning' | 'fail') => {
    if (status === 'pass') {
      return (
        <svg className="w-5 h-5 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      );
    } else if (status === 'warning') {
      return (
        <svg className="w-5 h-5 text-[#f59e0b]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      );
    } else {
      return (
        <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      );
    }
  };

  return (
    <div className="space-y-4">
      {/* Main Result Status */}
      <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-4">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#f59e0b] to-[#ce601c] flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
            </svg>
          </div>
          <div>
            <h3 className="text-xl font-bold" style={{ color: '#1b130e' }}>Test Results</h3>
            <p className="text-sm" style={{ color: '#4a3728' }}>SMTP connection test results</p>
          </div>
        </div>

        <div className="flex items-center gap-3 p-4 rounded-lg" style={{ backgroundColor: result.success ? '#f0f9ff' : '#fef2f2' }}>
          {getStatusIcon(result.success)}
          <div className="flex-1">
            <h4 className="font-semibold" style={{ color: result.success ? '#166534' : '#dc2626' }}>
              {result.success ? 'SMTP Test Successful' : 'SMTP Test Failed'}
            </h4>
            <p className="text-sm" style={{ color: result.success ? '#15803d' : '#dc2626' }}>
              {result.success
                ? `Email sent successfully${result.messageId ? ` (ID: ${result.messageId})` : ''}`
                : result.error || 'SMTP connection failed'
              }
            </p>
            {result.testAddress && (
              <p className="text-sm mt-1" style={{ color: '#4a3728' }}>
                Test email sent to: <span className="font-mono">{result.testAddress}</span>
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Email Authentication Analysis (Auto Mode Only) */}
      {testMode === 'auto' && (
        <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#66b077] to-[#07880e] flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-bold" style={{ color: '#1b130e' }}>Email Authentication Analysis</h3>
              <p className="text-sm" style={{ color: '#4a3728' }}>SPF, DKIM, DMARC, MX Records, and Reverse DNS validation results</p>
            </div>
          </div>

          {result.analysisResults ? (
            <>
              {/* Vertical Accordion Layout */}
              <div className="space-y-3 mb-4">
                {/* SPF Protocol */}
                <div className="border border-[#4a3728]/20 rounded-lg overflow-hidden" style={{ backgroundColor: '#fbfaf8' }}>
                  <button
                    onClick={() => toggleProtocol('spf')}
                    className="w-full p-4 text-left hover:bg-white/50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#66b077]/20"
                    aria-expanded={expandedProtocols.spf}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {(() => {
                          const spfPassed = result.analysisResults.spf?.pass ||
                            result.analysisResults.spf?.result === 'pass' ||
                            result.analysisResults.analysis?.spf?.status === 'pass' ||
                            false;
                          return getAuthStatusIcon(spfPassed ? 'pass' : 'fail');
                        })()}
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <span className="font-semibold text-lg" style={{ color: '#1b130e' }}>SPF</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-bold uppercase tracking-wide text-white`} style={{
                              backgroundColor: (result.analysisResults.spf?.pass ||
                                result.analysisResults.spf?.result === 'pass' ||
                                result.analysisResults.analysis?.spf?.status === 'pass') ? '#66b077' : '#dc2626'
                            }}>
                              {(result.analysisResults.spf?.pass ||
                                result.analysisResults.spf?.result === 'pass' ||
                                result.analysisResults.analysis?.spf?.status === 'pass') ? 'Passed' : 'Failed'}
                            </span>
                          </div>
                          {!expandedProtocols.spf && (
                            <p className="text-sm mt-1" style={{ color: '#4a3728' }}>
                              {getPreviewText(result.analysisResults.spf || result.analysisResults.analysis?.spf, 'spf')}
                            </p>
                          )}
                        </div>
                      </div>
                      <svg
                        className={`w-5 h-5 transition-transform duration-200 ${expandedProtocols.spf ? 'rotate-180' : ''}`}
                        style={{ color: '#4a3728' }}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>

                  {expandedProtocols.spf && (
                    <div className="px-4 pb-4 border-t border-[#4a3728]/10">
                      <div className="pt-3">
                        <h4 className="font-semibold mb-2" style={{ color: '#1b130e' }}>SPF Details</h4>
                        <div className="space-y-2 text-sm" style={{ color: '#4a3728' }}>
                          <p>
                            <strong>Status:</strong> {(result.analysisResults.spf?.pass ||
                              result.analysisResults.spf?.result === 'pass' ||
                              result.analysisResults.analysis?.spf?.status === 'pass') ? 'Passed' : 'Failed'}
                          </p>
                          {(result.analysisResults.spf?.reason || result.analysisResults.analysis?.spf?.details) && (
                            <p>
                              <strong>Details:</strong> {result.analysisResults.spf?.reason || result.analysisResults.analysis?.spf?.details}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* DKIM Protocol */}
                <div className="border border-[#4a3728]/20 rounded-lg overflow-hidden" style={{ backgroundColor: '#fbfaf8' }}>
                  <button
                    onClick={() => toggleProtocol('dkim')}
                    className="w-full p-4 text-left hover:bg-white/50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#66b077]/20"
                    aria-expanded={expandedProtocols.dkim}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {(() => {
                          const dkimPassed = result.analysisResults.dkim?.pass ||
                            result.analysisResults.dkim?.result === 'pass' ||
                            result.analysisResults.analysis?.dkim?.status === 'pass' ||
                            false;
                          return getAuthStatusIcon(dkimPassed ? 'pass' : 'fail');
                        })()}
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <span className="font-semibold text-lg" style={{ color: '#1b130e' }}>DKIM</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-bold uppercase tracking-wide text-white`} style={{
                              backgroundColor: (result.analysisResults.dkim?.pass ||
                                result.analysisResults.dkim?.result === 'pass' ||
                                result.analysisResults.analysis?.dkim?.status === 'pass') ? '#66b077' : '#dc2626'
                            }}>
                              {(result.analysisResults.dkim?.pass ||
                                result.analysisResults.dkim?.result === 'pass' ||
                                result.analysisResults.analysis?.dkim?.status === 'pass') ? 'Passed' : 'Failed'}
                            </span>
                          </div>
                          {!expandedProtocols.dkim && (
                            <p className="text-sm mt-1" style={{ color: '#4a3728' }}>
                              {getPreviewText(result.analysisResults.dkim || result.analysisResults.analysis?.dkim, 'dkim')}
                            </p>
                          )}
                        </div>
                      </div>
                      <svg
                        className={`w-5 h-5 transition-transform duration-200 ${expandedProtocols.dkim ? 'rotate-180' : ''}`}
                        style={{ color: '#4a3728' }}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>

                  {expandedProtocols.dkim && (
                    <div className="px-4 pb-4 border-t border-[#4a3728]/10">
                      <div className="pt-3">
                        <h4 className="font-semibold mb-2" style={{ color: '#1b130e' }}>DKIM Details</h4>
                        <div className="space-y-2 text-sm" style={{ color: '#4a3728' }}>
                          <p>
                            <strong>Status:</strong> {(result.analysisResults.dkim?.pass ||
                              result.analysisResults.dkim?.result === 'pass' ||
                              result.analysisResults.analysis?.dkim?.status === 'pass') ? 'Passed' : 'Failed'}
                          </p>
                          {(result.analysisResults.dkim?.reason || result.analysisResults.analysis?.dkim?.details) && (
                            <p>
                              <strong>Details:</strong> {result.analysisResults.dkim?.reason || result.analysisResults.analysis?.dkim?.details}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* DMARC Protocol */}
                <div className="border border-[#4a3728]/20 rounded-lg overflow-hidden" style={{ backgroundColor: '#fbfaf8' }}>
                  <button
                    onClick={() => toggleProtocol('dmarc')}
                    className="w-full p-4 text-left hover:bg-white/50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#66b077]/20"
                    aria-expanded={expandedProtocols.dmarc}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {(() => {
                          const dmarcPassed = result.analysisResults.dmarc?.pass ||
                            result.analysisResults.dmarc?.result === 'pass' ||
                            result.analysisResults.analysis?.dmarc?.status === 'pass' ||
                            false;
                          return getAuthStatusIcon(dmarcPassed ? 'pass' : 'fail');
                        })()}
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <span className="font-semibold text-lg" style={{ color: '#1b130e' }}>DMARC</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-bold uppercase tracking-wide text-white`} style={{
                              backgroundColor: (result.analysisResults.dmarc?.pass ||
                                result.analysisResults.dmarc?.result === 'pass' ||
                                result.analysisResults.analysis?.dmarc?.status === 'pass') ? '#66b077' : '#dc2626'
                            }}>
                              {(result.analysisResults.dmarc?.pass ||
                                result.analysisResults.dmarc?.result === 'pass' ||
                                result.analysisResults.analysis?.dmarc?.status === 'pass') ? 'Passed' : 'Failed'}
                            </span>
                          </div>
                          {!expandedProtocols.dmarc && (
                            <p className="text-sm mt-1" style={{ color: '#4a3728' }}>
                              {getPreviewText(result.analysisResults.dmarc || result.analysisResults.analysis?.dmarc, 'dmarc')}
                            </p>
                          )}
                        </div>
                      </div>
                      <svg
                        className={`w-5 h-5 transition-transform duration-200 ${expandedProtocols.dmarc ? 'rotate-180' : ''}`}
                        style={{ color: '#4a3728' }}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>

                  {expandedProtocols.dmarc && (
                    <div className="px-4 pb-4 border-t border-[#4a3728]/10">
                      <div className="pt-3">
                        <h4 className="font-semibold mb-2" style={{ color: '#1b130e' }}>DMARC Details</h4>
                        <div className="space-y-2 text-sm" style={{ color: '#4a3728' }}>
                          <p>
                            <strong>Status:</strong> {(result.analysisResults.dmarc?.pass ||
                              result.analysisResults.dmarc?.result === 'pass' ||
                              result.analysisResults.analysis?.dmarc?.status === 'pass') ? 'Passed' : 'Failed'}
                          </p>
                          {(result.analysisResults.dmarc?.reason || result.analysisResults.analysis?.dmarc?.details) && (
                            <p>
                              <strong>Details:</strong> {result.analysisResults.dmarc?.reason || result.analysisResults.analysis?.dmarc?.details}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* MX Records Protocol */}
                <div className="border border-[#4a3728]/20 rounded-lg overflow-hidden" style={{ backgroundColor: '#fbfaf8' }}>
                  <button
                    onClick={() => toggleProtocol('mx')}
                    className="w-full p-4 text-left hover:bg-white/50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#66b077]/20"
                    aria-expanded={expandedProtocols.mx}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {(() => {
                          const mxPassed = result.analysisResults.mx?.result === 'pass' ||
                            result.analysisResults.enhancedAuthResults?.mx?.result === 'pass' ||
                            result.analysisResults.analysis?.mx?.status === 'pass' ||
                            false;
                          return getAuthStatusIcon(mxPassed ? 'pass' : 'fail');
                        })()}
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <span className="font-semibold text-lg" style={{ color: '#1b130e' }}>MX Records</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-bold uppercase tracking-wide text-white`} style={{
                              backgroundColor: (result.analysisResults.mx?.result === 'pass' ||
                                result.analysisResults.enhancedAuthResults?.mx?.result === 'pass' ||
                                result.analysisResults.analysis?.mx?.status === 'pass') ? '#66b077' : '#dc2626'
                            }}>
                              {(result.analysisResults.mx?.result === 'pass' ||
                                result.analysisResults.enhancedAuthResults?.mx?.result === 'pass' ||
                                result.analysisResults.analysis?.mx?.status === 'pass') ? 'Passed' : 'Failed'}
                            </span>
                          </div>
                          {!expandedProtocols.mx && (
                            <p className="text-sm mt-1" style={{ color: '#4a3728' }}>
                              {getPreviewText(
                                result.analysisResults.mx ||
                                result.analysisResults.enhancedAuthResults?.mx ||
                                result.analysisResults.analysis?.mx,
                                'mx'
                              )}
                            </p>
                          )}
                        </div>
                      </div>
                      <svg
                        className={`w-5 h-5 transition-transform duration-200 ${expandedProtocols.mx ? 'rotate-180' : ''}`}
                        style={{ color: '#4a3728' }}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>

                  {expandedProtocols.mx && (
                    <div className="px-4 pb-4 border-t border-[#4a3728]/10">
                      <div className="pt-3">
                        <h4 className="font-semibold mb-2" style={{ color: '#1b130e' }}>MX Records Details</h4>
                        <div className="space-y-2 text-sm" style={{ color: '#4a3728' }}>
                          <p>
                            <strong>Status:</strong> {(result.analysisResults.mx?.result === 'pass' ||
                              result.analysisResults.enhancedAuthResults?.mx?.result === 'pass' ||
                              result.analysisResults.analysis?.mx?.status === 'pass') ? 'Passed' : 'Failed'}
                          </p>
                          {(result.analysisResults.mx?.info ||
                            result.analysisResults.enhancedAuthResults?.mx?.info ||
                            result.analysisResults.analysis?.mx?.details) && (
                            <p>
                              <strong>Details:</strong> {result.analysisResults.mx?.info ||
                                result.analysisResults.enhancedAuthResults?.mx?.info ||
                                result.analysisResults.analysis?.mx?.details}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Reverse DNS (PTR) Protocol */}
                <div className="border border-[#4a3728]/20 rounded-lg overflow-hidden" style={{ backgroundColor: '#fbfaf8' }}>
                  <button
                    onClick={() => toggleProtocol('ptr')}
                    className="w-full p-4 text-left hover:bg-white/50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#66b077]/20"
                    aria-expanded={expandedProtocols.ptr}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {(() => {
                          const ptrStatus = getPtrStatus(
                            result.analysisResults.reverseDns ||
                            result.analysisResults.enhancedAuthResults?.reverseDns ||
                            result.analysisResults.analysis?.reverseDns
                          );
                          return getAuthStatusIcon(ptrStatus);
                        })()}
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <span className="font-semibold text-lg" style={{ color: '#1b130e' }}>Reverse DNS</span>
                            {(() => {
                              const ptrStatus = getPtrStatus(
                                result.analysisResults.reverseDns ||
                                result.analysisResults.enhancedAuthResults?.reverseDns ||
                                result.analysisResults.analysis?.reverseDns
                              );
                              const statusText = getStatusText(ptrStatus);
                              let backgroundColor = '#dc2626'; // red for fail
                              if (ptrStatus === 'pass') backgroundColor = '#66b077'; // green for pass
                              if (ptrStatus === 'warning') backgroundColor = '#f59e0b'; // amber for warning

                              return (
                                <span className={`px-2 py-1 rounded-full text-xs font-bold uppercase tracking-wide text-white`} style={{
                                  backgroundColor
                                }}>
                                  {statusText}
                                </span>
                              );
                            })()}
                          </div>
                          {!expandedProtocols.ptr && (
                            <p className="text-sm mt-1" style={{ color: '#4a3728' }}>
                              {getPreviewText(
                                result.analysisResults.reverseDns ||
                                result.analysisResults.enhancedAuthResults?.reverseDns ||
                                result.analysisResults.analysis?.reverseDns,
                                'ptr'
                              )}
                            </p>
                          )}
                        </div>
                      </div>
                      <svg
                        className={`w-5 h-5 transition-transform duration-200 ${expandedProtocols.ptr ? 'rotate-180' : ''}`}
                        style={{ color: '#4a3728' }}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>

                  {expandedProtocols.ptr && (
                    <div className="px-4 pb-4 border-t border-[#4a3728]/10">
                      <div className="pt-3">
                        <h4 className="font-semibold mb-2" style={{ color: '#1b130e' }}>Reverse DNS Details</h4>
                        <div className="space-y-2 text-sm" style={{ color: '#4a3728' }}>
                          <p>
                            <strong>Status:</strong> {(() => {
                              const ptrStatus = getPtrStatus(
                                result.analysisResults.reverseDns ||
                                result.analysisResults.enhancedAuthResults?.reverseDns ||
                                result.analysisResults.analysis?.reverseDns
                              );
                              return getStatusText(ptrStatus);
                            })()}
                          </p>
                          {(result.analysisResults.reverseDns?.info ||
                            result.analysisResults.reverseDns?.hostname ||
                            result.analysisResults.enhancedAuthResults?.reverseDns?.info ||
                            result.analysisResults.enhancedAuthResults?.reverseDns?.hostname ||
                            result.analysisResults.analysis?.reverseDns?.details) && (
                            <p>
                              <strong>Details:</strong> {result.analysisResults.reverseDns?.info ||
                                result.analysisResults.reverseDns?.hostname ||
                                result.analysisResults.enhancedAuthResults?.reverseDns?.info ||
                                result.analysisResults.enhancedAuthResults?.reverseDns?.hostname ||
                                result.analysisResults.analysis?.reverseDns?.details}
                            </p>
                          )}
                          {result.analysisResults.reverseDns?.hostname && (
                            <p>
                              <strong>Hostname:</strong> {result.analysisResults.reverseDns.hostname}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

          {/* Enhanced Overall Score Card */}
          {(result.analysisResults.score !== undefined || result.analysisResults.overallScore !== undefined) && (
            <div className="bg-gradient-to-r from-[#66b077]/5 to-[#07880e]/5 border border-[#66b077]/20 rounded-xl p-6">
              <div className="flex items-center justify-between">
                {/* Left side - Title and description */}
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-[#66b077] to-[#07880e] flex items-center justify-center shadow-sm">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold" style={{ color: '#1b130e' }}>Overall Score</h3>
                    <p className="text-sm" style={{ color: '#4a3728' }}>Email authentication analysis</p>
                  </div>
                </div>

                {/* Right side - Score display */}
                <div className="flex items-center gap-4">
                  {(() => {
                    const score = result.analysisResults.score ?? result.analysisResults.overallScore ?? 0;
                    const scoreColor = score >= 8 ? '#66b077' : score >= 6 ? '#f59e0b' : '#dc2626';
                    const scoreLabel = score >= 8 ? 'Excellent' : score >= 6 ? 'Good' : 'Needs Improvement';

                    return (
                      <>
                        <div className="text-right">
                          <div className="text-sm font-medium" style={{ color: '#4a3728' }}>Authentication Score</div>
                          <div className="text-xs mt-1" style={{ color: scoreColor }}>
                            {scoreLabel}
                          </div>
                        </div>
                        <div
                          className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg"
                          style={{ backgroundColor: scoreColor }}
                        >
                          <div className="text-center">
                            <div className="text-2xl font-bold text-white">
                              {score}
                            </div>
                            <div className="text-xs text-white/90 font-medium">
                              /10
                            </div>
                          </div>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>

              {/* Score breakdown */}
              <div className="mt-4 pt-4 border-t border-[#4a3728]/10">
                <div className="flex items-center justify-between text-sm">
                  <span style={{ color: '#4a3728' }}>Based on 5-protocol authentication analysis</span>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 rounded-full bg-[#66b077]"></div>
                      <span className="text-xs" style={{ color: '#4a3728' }}>SPF, DKIM, DMARC, MX, PTR</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Recommendations */}
          {result.analysisResults.recommendations && result.analysisResults.recommendations.length > 0 && (
            <div className="mt-4">
              <h4 className="font-semibold mb-2" style={{ color: '#1b130e' }}>Recommendations</h4>
              <ul className="space-y-2">
                {result.analysisResults.recommendations.map((recommendation, index) => {
                  // Handle both string recommendations and object recommendations
                  const isObject = typeof recommendation === 'object' && recommendation !== null;
                  const recommendationText = isObject
                    ? (recommendation as any).recommendation || (recommendation as any).issue || 'Recommendation available'
                    : recommendation;

                  return (
                    <li key={index} className="text-sm" style={{ color: '#4a3728' }}>
                      <div className="flex items-start gap-2">
                        <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: '#f59e0b' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div className="flex-1">
                          <p className="font-medium">{recommendationText}</p>
                          {isObject && (recommendation as any).category && (
                            <p className="text-xs mt-1" style={{ color: '#956b50' }}>
                              Category: {(recommendation as any).category}
                            </p>
                          )}
                          {isObject && (recommendation as any).priority && (
                            <p className="text-xs mt-1" style={{ color: '#956b50' }}>
                              Priority: {(recommendation as any).priority}
                            </p>
                          )}
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            </div>
          )}
            </>
          ) : (
            <div className="p-4 rounded-lg border border-[#f59e0b]/30" style={{ backgroundColor: '#fffbeb' }}>
              <div className="flex items-center gap-2 mb-2">
                <svg className="w-5 h-5 text-[#f59e0b]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="font-semibold text-[#1b130e]">Analysis Pending</span>
              </div>
              <p className="text-sm text-[#4a3728]">
                Email authentication analysis is still processing. Please check back in a few moments or try testing again.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Diagnostic Logs */}
      <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-[#956b50] to-[#4a3728] flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-bold" style={{ color: '#1b130e' }}>Diagnostic Logs</h3>
              <p className="text-sm" style={{ color: '#4a3728' }}>Detailed SMTP connection logs</p>
            </div>
          </div>
          <div className="flex gap-2">
            <button
              onClick={copyLogsToClipboard}
              className="px-3 py-1 text-sm border border-[#4a3728] text-[#4a3728] rounded hover:bg-[#4a3728] hover:text-white transition-colors duration-200"
            >
              {copiedLogs ? 'Copied!' : 'Copy'}
            </button>
            <button
              onClick={() => setShowLogs(!showLogs)}
              className="px-3 py-1 text-sm border border-[#4a3728] text-[#4a3728] rounded hover:bg-[#4a3728] hover:text-white transition-colors duration-200"
            >
              {showLogs ? 'Hide' : 'Show'}
            </button>
          </div>
        </div>

        {showLogs && (
          <div className="bg-[#1b130e] text-[#fbfaf8] p-4 rounded-lg font-mono text-sm overflow-x-auto">
            <pre className="whitespace-pre-wrap">{result.logs}</pre>
          </div>
        )}
      </div>
    </div>
  );
}
