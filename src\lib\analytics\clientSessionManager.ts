/**
 * Client-Side Session Management for VanishPost Analytics
 * 
 * This service provides frontend-safe session management functions that
 * communicate with the backend API instead of accessing the database directly.
 */

export interface SessionMetadata {
  deviceType?: string;
  browser?: string;
  country?: string;
  referrer?: string;
  userId?: string;
}

export interface SessionMetrics {
  emailsGeneratedCount?: number;
  emailsReceivedCount?: number;
  emailsViewedCount?: number;
  emailsDeletedCount?: number;
  manualRefreshCount?: number;
  copyActionsCount?: number;
  addressRegenerationCount?: number;
}

export interface SessionData {
  sessionId: string;
  userId?: string;
  sessionStartTime: Date;
  sessionEndTime?: Date;
  sessionDuration?: number;
  emailsGeneratedCount: number;
  emailsReceivedCount: number;
  emailsViewedCount: number;
  emailsDeletedCount: number;
  manualRefreshCount: number;
  copyActionsCount: number;
  addressRegenerationCount: number;
  deviceType?: string;
  browser?: string;
  country?: string;
  referrer?: string;
}

/**
 * Generate a unique session ID
 * Format: timestamp-random-suffix for uniqueness and sortability
 */
export function generateSessionId(): string {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 15);
  const additionalRandom = Math.random().toString(36).substring(2, 8);
  return `session_${timestamp}_${randomSuffix}_${additionalRandom}`;
}

/**
 * Create or get a session via API
 */
export async function getOrCreateSession(
  sessionId: string, 
  metadata?: SessionMetadata
): Promise<SessionData | null> {
  try {
    const response = await fetch('/api/analytics/session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'create',
        sessionId,
        metadata
      }),
    });

    if (!response.ok) {
      throw new Error(`Session API error: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to create session');
    }

    return result.data;

  } catch (error) {
    console.error('Failed to create/get session:', error);
    return null;
  }
}

/**
 * End a session via API
 */
export async function endSession(sessionId: string): Promise<boolean> {
  try {
    const response = await fetch('/api/analytics/session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'end',
        sessionId
      }),
    });

    if (!response.ok) {
      throw new Error(`Session API error: ${response.status}`);
    }

    const result = await response.json();
    return result.success || false;

  } catch (error) {
    console.error('Failed to end session:', error);
    return false;
  }
}

/**
 * Update session metrics via API
 */
export async function updateSessionMetrics(
  sessionId: string, 
  metrics: SessionMetrics
): Promise<boolean> {
  try {
    const response = await fetch('/api/analytics/session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'update',
        sessionId,
        metrics
      }),
    });

    if (!response.ok) {
      throw new Error(`Session API error: ${response.status}`);
    }

    const result = await response.json();
    return result.success || false;

  } catch (error) {
    console.error('Failed to update session metrics:', error);
    return false;
  }
}

/**
 * Get session data via API
 */
export async function getSession(sessionId: string): Promise<SessionData | null> {
  try {
    const response = await fetch(`/api/analytics/session?sessionId=${encodeURIComponent(sessionId)}`);

    if (!response.ok) {
      if (response.status === 404) {
        return null; // Session not found
      }
      throw new Error(`Session API error: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to get session');
    }

    return result.data;

  } catch (error) {
    console.error('Failed to get session:', error);
    return null;
  }
}

/**
 * Delete a session via API
 */
export async function deleteSession(sessionId: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/analytics/session?sessionId=${encodeURIComponent(sessionId)}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error(`Session API error: ${response.status}`);
    }

    const result = await response.json();
    return result.success || false;

  } catch (error) {
    console.error('Failed to delete session:', error);
    return false;
  }
}

/**
 * Check if a session exists
 */
export async function sessionExists(sessionId: string): Promise<boolean> {
  const session = await getSession(sessionId);
  return session !== null;
}

/**
 * Get session duration in seconds
 * If session is still active, calculates duration from start time to now
 */
export async function getSessionDuration(sessionId: string): Promise<number> {
  try {
    const session = await getSession(sessionId);
    
    if (!session) {
      return 0;
    }

    // If session has ended, return stored duration
    if (session.sessionEndTime && session.sessionDuration) {
      return session.sessionDuration;
    }

    // Calculate current duration for active session
    const sessionStartTime = new Date(session.sessionStartTime);
    const currentTime = new Date();
    const duration = Math.floor((currentTime.getTime() - sessionStartTime.getTime()) / 1000);

    return duration;

  } catch (error) {
    console.error('Failed to get session duration:', error);
    return 0;
  }
}
