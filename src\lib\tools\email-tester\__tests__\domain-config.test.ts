/**
 * Tests for EMAIL_TESTER_DOMAIN environment variable configuration
 */

// Mock the database functions
jest.mock('@/lib/supabase/server', () => ({
  createServerSupabaseClient: jest.fn(() => ({
    from: jest.fn(() => ({
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn().mockResolvedValue({
            data: { id: 'test-id', test_address: '<EMAIL>' },
            error: null
          })
        }))
      }))
    }))
  }))
}));

describe('EMAIL_TESTER_DOMAIN Environment Variable', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('generateEmailTesterAddress', () => {
    it('should use default domain when EMAIL_TESTER_DOMAIN is not set', async () => {
      // Ensure EMAIL_TESTER_DOMAIN is not set
      delete process.env.EMAIL_TESTER_DOMAIN;

      const { generateEmailTesterAddress } = require('../database');
      const result = await generateEmailTesterAddress();

      expect(result.testAddress).toMatch(/^test-[a-f0-9]{8}@fademail\.site$/);
    });

    it('should use custom domain when EMAIL_TESTER_DOMAIN is set', async () => {
      // Set custom domain
      process.env.EMAIL_TESTER_DOMAIN = 'vanishpost.com';

      const { generateEmailTesterAddress } = require('../database');
      const result = await generateEmailTesterAddress();

      expect(result.testAddress).toMatch(/^test-[a-f0-9]{8}@vanishpost\.com$/);
    });

    it('should use custom domain with subdomain when EMAIL_TESTER_DOMAIN is set', async () => {
      // Set custom domain with subdomain
      process.env.EMAIL_TESTER_DOMAIN = 'mail.example.org';

      const { generateEmailTesterAddress } = require('../database');
      const result = await generateEmailTesterAddress();

      expect(result.testAddress).toMatch(/^test-[a-f0-9]{8}@mail\.example\.org$/);
    });

    it('should handle empty EMAIL_TESTER_DOMAIN by using default', async () => {
      // Set empty domain
      process.env.EMAIL_TESTER_DOMAIN = '';

      const { generateEmailTesterAddress } = require('../database');
      const result = await generateEmailTesterAddress();

      expect(result.testAddress).toMatch(/^test-[a-f0-9]{8}@fademail\.site$/);
    });

    it('should generate unique addresses with custom domain', async () => {
      process.env.EMAIL_TESTER_DOMAIN = 'test.example.com';

      const { generateEmailTesterAddress } = require('../database');
      
      const result1 = await generateEmailTesterAddress();
      const result2 = await generateEmailTesterAddress();

      expect(result1.testAddress).toMatch(/^test-[a-f0-9]{8}@test\.example\.com$/);
      expect(result2.testAddress).toMatch(/^test-[a-f0-9]{8}@test\.example\.com$/);
      expect(result1.testAddress).not.toBe(result2.testAddress);
    });
  });

  describe('Domain extraction', () => {
    it('should extract correct domain from generated address', async () => {
      process.env.EMAIL_TESTER_DOMAIN = 'custom.domain.net';

      const { generateEmailTesterAddress } = require('../database');
      const result = await generateEmailTesterAddress();

      const domain = result.testAddress.split('@')[1];
      expect(domain).toBe('custom.domain.net');
    });

    it('should maintain test- prefix with custom domain', async () => {
      process.env.EMAIL_TESTER_DOMAIN = 'mycompany.email';

      const { generateEmailTesterAddress } = require('../database');
      const result = await generateEmailTesterAddress();

      const localPart = result.testAddress.split('@')[0];
      expect(localPart).toMatch(/^test-[a-f0-9]{8}$/);
    });
  });

  describe('Backward compatibility', () => {
    it('should maintain existing behavior when environment variable is not set', async () => {
      // Ensure no EMAIL_TESTER_DOMAIN
      delete process.env.EMAIL_TESTER_DOMAIN;

      const { generateEmailTesterAddress } = require('../database');
      const result = await generateEmailTesterAddress();

      // Should use the original default domain
      expect(result.testAddress).toContain('@fademail.site');
      expect(result.testAddress).toMatch(/^test-[a-f0-9]{8}@fademail\.site$/);
    });

    it('should work with all existing function parameters', async () => {
      process.env.EMAIL_TESTER_DOMAIN = 'test.domain.co';

      const { generateEmailTesterAddress } = require('../database');
      
      // Test with custom expiration hours
      const result = await generateEmailTesterAddress(48);

      expect(result.testAddress).toMatch(/^test-[a-f0-9]{8}@test\.domain\.co$/);
      expect(result.expirationDate).toBeDefined();
    });
  });
});
