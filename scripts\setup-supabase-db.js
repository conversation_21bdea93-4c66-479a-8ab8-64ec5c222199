/**
 * <PERSON><PERSON><PERSON> to set up the Supabase database tables
 *
 * This script creates the necessary tables in Supabase PostgreSQL database
 * for the Fademail application.
 */
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create a Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function setupDatabase() {
  console.log('Setting up Supabase database tables...');

  try {
    // Check if temp_emails table exists
    const { error: checkTempEmailsError } = await supabase
      .from('temp_emails')
      .select('id')
      .limit(1);

    if (checkTempEmailsError && checkTempEmailsError.code === '42P01') {
      console.log('Creating temp_emails table...');

      // Create temp_emails table
      const { error: createTempEmailsError } = await supabase
        .from('temp_emails')
        .insert([]); // This will fail with a specific error if the table doesn't exist

      if (createTempEmailsError && createTempEmailsError.code === '42P01') {
        console.log('The temp_emails table does not exist. Please create it manually in the Supabase dashboard with the following SQL:');
        console.log(`
          CREATE TABLE temp_emails (
            id SERIAL PRIMARY KEY,
            email_address VARCHAR(255) NOT NULL UNIQUE,
            creation_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            expiration_date TIMESTAMP WITH TIME ZONE NOT NULL
          );

          -- Create index on expiration_date for cleanup queries
          CREATE INDEX idx_expiration_date ON temp_emails(expiration_date);
        `);
      }
    } else {
      console.log('temp_emails table already exists');
    }

    // Check if analytics_events table exists
    const { error: checkAnalyticsError } = await supabase
      .from('analytics_events')
      .select('id')
      .limit(1);

    if (checkAnalyticsError && checkAnalyticsError.code === '42P01') {
      console.log('Creating analytics_events table...');

      // Create analytics_events table
      const { error: createAnalyticsError } = await supabase
        .from('analytics_events')
        .insert([]); // This will fail with a specific error if the table doesn't exist

      if (createAnalyticsError && createAnalyticsError.code === '42P01') {
        console.log('The analytics_events table does not exist. Please create it manually in the Supabase dashboard with the following SQL:');
        console.log(`
          CREATE TABLE analytics_events (
            id SERIAL PRIMARY KEY,
            event_type VARCHAR(50) NOT NULL,
            page_path VARCHAR(255),
            referrer VARCHAR(255),
            country VARCHAR(50),
            browser VARCHAR(50),
            device_type VARCHAR(20),
            timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            additional_data JSONB
          );

          -- Create indexes for analytics queries
          CREATE INDEX idx_event_type ON analytics_events(event_type);
          CREATE INDEX idx_timestamp ON analytics_events(timestamp);
          CREATE INDEX idx_page_path ON analytics_events(page_path);
        `);
      }
    } else {
      console.log('analytics_events table already exists');
    }

    console.log('\nDatabase setup check completed!');
    console.log('\nIf tables do not exist, please create them manually in the Supabase dashboard using the SQL commands provided above.');
    console.log('\nAlternatively, you can use the Supabase dashboard to create the tables with the following structure:');
    console.log('\n1. temp_emails table:');
    console.log('   - id: serial primary key');
    console.log('   - email_address: varchar(255) not null unique');
    console.log('   - creation_time: timestamptz not null default now()');
    console.log('   - expiration_date: timestamptz not null');
    console.log('\n2. analytics_events table:');
    console.log('   - id: serial primary key');
    console.log('   - event_type: varchar(50) not null');
    console.log('   - page_path: varchar(255)');
    console.log('   - referrer: varchar(255)');
    console.log('   - country: varchar(50)');
    console.log('   - browser: varchar(50)');
    console.log('   - device_type: varchar(20)');
    console.log('   - timestamp: timestamptz default now()');
    console.log('   - additional_data: jsonb');
  } catch (error) {
    console.error('Error setting up database:', error);
    process.exit(1);
  }
}

// Run the setup
setupDatabase().catch(console.error);
