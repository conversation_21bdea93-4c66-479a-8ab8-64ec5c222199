/**
 * Session Configuration Dashboard
 * 
 * Admin component for managing session and rate limiting settings
 */

'use client';

import React, { useState, useEffect } from 'react';

interface ConfigSummary {
  session: {
    duration: string;
    persistence: boolean;
    extensions: string;
    activityInterval: string;
  };
  rateLimits: {
    emailGeneration: {
      ipLimit: string;
      sessionLimit: string;
      anonymousLimit: string;
      strictMode: boolean;
    };
    burstProtection: {
      ipLimit: string;
      sessionLimit: string;
    };
    emergencyMode: boolean;
  };
}

export default function SessionConfigDashboard() {
  const [config, setConfig] = useState<ConfigSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [adminKey, setAdminKey] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Load configuration
  const loadConfig = async () => {
    if (!adminKey) return;

    try {
      setLoading(true);
      const response = await fetch('/api/admin/session-config?action=summary', {
        headers: {
          'Authorization': `Bearer ${adminKey}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setConfig(data.data);
        setIsAuthenticated(true);
        setError(null);
      } else {
        setError(data.error || 'Failed to load configuration');
        setIsAuthenticated(false);
      }
    } catch (err) {
      setError('Network error loading configuration');
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  // Apply preset
  const applyPreset = async (type: 'session' | 'rateLimit', preset: string) => {
    try {
      const action = type === 'session' ? 'applySessionPreset' : 'applyRateLimitPreset';
      
      const response = await fetch('/api/admin/session-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminKey}`
        },
        body: JSON.stringify({ action, preset })
      });

      const data = await response.json();

      if (data.success) {
        await loadConfig(); // Reload configuration
        alert(`${preset} preset applied successfully!`);
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (err) {
      alert('Network error applying preset');
    }
  };

  // Toggle emergency mode
  const toggleEmergencyMode = async () => {
    try {
      const response = await fetch('/api/admin/session-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminKey}`
        },
        body: JSON.stringify({ 
          action: 'emergencyMode', 
          enabled: !config?.rateLimits.emergencyMode 
        })
      });

      const data = await response.json();

      if (data.success) {
        await loadConfig();
        alert(`Emergency mode ${!config?.rateLimits.emergencyMode ? 'enabled' : 'disabled'}!`);
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (err) {
      alert('Network error toggling emergency mode');
    }
  };

  // Quick adjustments
  const quickAdjust = async (type: 'session' | 'rateLimit', adjustment: any) => {
    try {
      const response = await fetch('/api/admin/session-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminKey}`
        },
        body: JSON.stringify({ action: 'quickAdjust', type, adjustment })
      });

      const data = await response.json();

      if (data.success) {
        await loadConfig();
        alert('Quick adjustment applied!');
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (err) {
      alert('Network error applying adjustment');
    }
  };

  useEffect(() => {
    if (adminKey) {
      loadConfig();
    }
  }, [adminKey]);

  if (!isAuthenticated) {
    return (
      <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg">
        <h2 className="text-2xl font-bold mb-4">Admin Authentication</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Admin API Key
            </label>
            <input
              type="password"
              value={adminKey}
              onChange={(e) => setAdminKey(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter admin API key"
            />
          </div>
          <button
            onClick={loadConfig}
            disabled={!adminKey || loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Authenticating...' : 'Access Dashboard'}
          </button>
          {error && (
            <div className="text-red-600 text-sm mt-2">{error}</div>
          )}
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Loading configuration...</div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="text-center text-red-600 mt-8">
        Failed to load configuration
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-3xl font-bold mb-6">Session Configuration Dashboard</h1>
        
        {/* Emergency Mode Toggle */}
        <div className="mb-6 p-4 border-2 border-red-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-red-700">Emergency Mode</h3>
              <p className="text-sm text-gray-600">
                Activates strictest security settings (1 email/hour limits)
              </p>
            </div>
            <button
              onClick={toggleEmergencyMode}
              className={`px-4 py-2 rounded-md font-medium ${
                config.rateLimits.emergencyMode
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {config.rateLimits.emergencyMode ? 'EMERGENCY ACTIVE' : 'Activate Emergency'}
            </button>
          </div>
        </div>

        {/* Current Configuration Summary */}
        <div className="grid md:grid-cols-2 gap-6 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">Session Settings</h3>
            <div className="space-y-2 text-sm">
              <div><strong>Duration:</strong> {config.session.duration}</div>
              <div><strong>Persistence:</strong> {config.session.persistence ? 'Enabled' : 'Disabled'}</div>
              <div><strong>Max Extensions:</strong> {config.session.extensions}</div>
              <div><strong>Activity Update:</strong> {config.session.activityInterval}</div>
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">Rate Limits</h3>
            <div className="space-y-2 text-sm">
              <div><strong>IP Limit:</strong> {config.rateLimits.emailGeneration.ipLimit}</div>
              <div><strong>Session Limit:</strong> {config.rateLimits.emailGeneration.sessionLimit}</div>
              <div><strong>Anonymous Limit:</strong> {config.rateLimits.emailGeneration.anonymousLimit}</div>
              <div><strong>Strict Mode:</strong> {config.rateLimits.emailGeneration.strictMode ? 'Yes' : 'No'}</div>
            </div>
          </div>
        </div>

        {/* Preset Controls */}
        <div className="grid md:grid-cols-2 gap-6 mb-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">Session Presets</h3>
            <div className="space-y-2">
              <button
                onClick={() => applyPreset('session', 'PRODUCTION')}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
              >
                Production (24h)
              </button>
              <button
                onClick={() => applyPreset('session', 'HIGH_SECURITY')}
                className="w-full bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700"
              >
                High Security (4h)
              </button>
              <button
                onClick={() => applyPreset('session', 'RELAXED')}
                className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
              >
                Relaxed (48h)
              </button>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">Rate Limit Presets</h3>
            <div className="space-y-2">
              <button
                onClick={() => applyPreset('rateLimit', 'PRODUCTION')}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
              >
                Production (5/8 limits)
              </button>
              <button
                onClick={() => applyPreset('rateLimit', 'HIGH_SECURITY')}
                className="w-full bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700"
              >
                High Security (2/3 limits)
              </button>
              <button
                onClick={() => applyPreset('rateLimit', 'DEVELOPMENT')}
                className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
              >
                Development (20/25 limits)
              </button>
            </div>
          </div>
        </div>

        {/* Quick Adjustments */}
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold mb-3">Quick Adjustments</h3>
          <div className="grid md:grid-cols-3 gap-4">
            <button
              onClick={() => quickAdjust('session', { duration: 12 })}
              className="bg-yellow-600 text-white py-2 px-4 rounded hover:bg-yellow-700"
            >
              12h Sessions
            </button>
            <button
              onClick={() => quickAdjust('rateLimit', { emailLimit: 15 })}
              className="bg-yellow-600 text-white py-2 px-4 rounded hover:bg-yellow-700"
            >
              15 Emails/Hour
            </button>
            <button
              onClick={() => quickAdjust('rateLimit', { emailLimit: 3 })}
              className="bg-yellow-600 text-white py-2 px-4 rounded hover:bg-yellow-700"
            >
              3 Emails/Hour
            </button>
          </div>
        </div>

        {/* Refresh Button */}
        <div className="mt-6 text-center">
          <button
            onClick={loadConfig}
            className="bg-gray-600 text-white py-2 px-6 rounded hover:bg-gray-700"
          >
            Refresh Configuration
          </button>
        </div>
      </div>
    </div>
  );
}
