import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { logInfo, logError } from '@/lib/logging';

/**
 * API route for fetching all messages in a thread
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ threadId: string }> }
) {
  try {
    // Await the params object before accessing its properties
    const params = await context.params;
    const { threadId } = params;

    if (!threadId) {
      return NextResponse.json({
        success: false,
        message: 'Thread ID is required'
      }, { status: 400 });
    }

    // Get Supabase client
    const supabase = await createServerSupabaseClient();

    // Query all messages in the thread
    const { data: messages, error } = await supabase
      .from('contact_messages')
      .select('*')
      .eq('thread_id', threadId)
      .order('created_at', { ascending: true });

    if (error) {
      logError('messages', 'Failed to fetch thread messages', { threadId, error });
      return NextResponse.json({
        success: false,
        message: 'Failed to fetch thread messages'
      }, { status: 500 });
    }

    // Format dates to ensure consistency
    const formattedMessages = messages?.map(message => ({
      ...message,
      // Convert to ISO string format which is more reliable for JavaScript Date parsing
      created_at: message.created_at ? new Date(message.created_at).toISOString() : null,
      updated_at: message.updated_at ? new Date(message.updated_at).toISOString() : null
    }));

    return NextResponse.json({
      success: true,
      messages: formattedMessages
    });
  } catch (error) {
    logError('messages', 'Error fetching thread messages', { error });
    return NextResponse.json({
      success: false,
      message: 'An error occurred while fetching thread messages'
    }, { status: 500 });
  }
}
