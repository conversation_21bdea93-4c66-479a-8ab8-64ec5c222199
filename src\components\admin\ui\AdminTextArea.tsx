'use client';

import React, { useState, useRef, useEffect } from 'react';

interface AdminTextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  fullWidth?: boolean;
  resizable?: boolean;
  autoResize?: boolean;
  maxHeight?: number;
}

/**
 * A styled textarea component for admin interfaces
 */
export function AdminTextArea({
  label,
  error,
  helperText,
  fullWidth = true,
  resizable = true,
  autoResize = false,
  maxHeight = 400,
  className = '',
  ...props
}: AdminTextAreaProps) {
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  // Auto-resize functionality
  useEffect(() => {
    if (autoResize && textareaRef.current) {
      const textarea = textareaRef.current;
      
      const adjustHeight = () => {
        textarea.style.height = 'auto';
        const newHeight = Math.min(textarea.scrollHeight, maxHeight);
        textarea.style.height = `${newHeight}px`;
        
        // Add scrollbar if content exceeds maxHeight
        textarea.style.overflowY = textarea.scrollHeight > maxHeight ? 'auto' : 'hidden';
      };
      
      // Initial adjustment
      adjustHeight();
      
      // Adjust on input
      const handleInput = () => adjustHeight();
      textarea.addEventListener('input', handleInput);
      
      return () => {
        textarea.removeEventListener('input', handleInput);
      };
    }
  }, [autoResize, maxHeight, props.value]);
  
  // Base classes
  const containerClasses = `
    ${fullWidth ? 'w-full' : 'w-auto'}
    ${className}
  `;
  
  const textareaClasses = `
    w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-all duration-200
    ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-200'}
    ${isFocused ? 'bg-white' : 'bg-gray-50'}
    ${!resizable ? 'resize-none' : ''}
    ${props.disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : ''}
  `;
  
  return (
    <div className={containerClasses}>
      {label && (
        <label 
          htmlFor={props.id} 
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          {label}
        </label>
      )}
      
      <textarea
        ref={textareaRef}
        className={textareaClasses}
        onFocus={(e) => {
          setIsFocused(true);
          props.onFocus?.(e);
        }}
        onBlur={(e) => {
          setIsFocused(false);
          props.onBlur?.(e);
        }}
        {...props}
      />
      
      {(error || helperText) && (
        <div className="mt-1 text-sm">
          {error ? (
            <p className="text-red-600">{error}</p>
          ) : helperText ? (
            <p className="text-gray-500">{helperText}</p>
          ) : null}
        </div>
      )}
    </div>
  );
}
