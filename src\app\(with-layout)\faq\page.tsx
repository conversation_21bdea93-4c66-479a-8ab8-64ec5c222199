import React from 'react';
import Link from 'next/link';
import type { Metadata } from 'next';
import StructuredData from '@/components/StructuredData';

export const metadata: Metadata = {
  title: 'FAQ | VanishPost',
  description: 'Frequently asked questions about VanishPost, your secure temporary email service.',
};

export default function FAQPage() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-12 sm:py-16">
      <StructuredData type="faq" />
      <div className="text-center mb-12">
        <h1 className="text-3xl sm:text-4xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Frequently Asked Questions</h1>
        <p className="text-lg max-w-3xl mx-auto" style={{ color: 'var(--earth-brown-medium)' }}>
          Find answers to common questions about VanishPost and how it helps protect your online privacy.
        </p>
      </div>

      <div className="space-y-6">
        {/* FAQ Item 1 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>What is VanishPost?</h2>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            VanishPost is a free service that provides temporary, disposable email addresses that automatically expire after 15 minutes.
            It helps protect your privacy by allowing you to receive emails without revealing your personal email address.
          </p>
        </div>

        {/* FAQ Item 2 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>How long do VanishPost addresses last?</h2>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            All VanishPost addresses automatically expire after 15 minutes from the time they are created.
            After expiration, the address and all associated emails are permanently deleted from our servers.
          </p>
        </div>

        {/* FAQ Item 3 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>Do I need to create an account?</h2>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            No, VanishPost requires no registration, account creation, or personal information.
            Simply visit the website and generate a temporary email address with a single click.
          </p>
        </div>

        {/* FAQ Item 4 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>Is VanishPost completely free?</h2>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            Yes, VanishPost is completely free to use. We support the service through minimal, non-intrusive advertisements
            that don't track your activity or compromise your privacy.
          </p>
        </div>

        {/* FAQ Item 5 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>Can I send emails with VanishPost?</h2>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            Currently, VanishPost only supports receiving emails. The service is designed primarily for situations
            where you need to receive verification emails, newsletters, or other communications without using your
            personal email address.
          </p>
        </div>

        {/* FAQ Item 6 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>Are emails stored securely?</h2>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            Yes, all emails are stored securely during their 15-minute lifespan. We use industry-standard encryption
            and security practices to ensure your temporary communications remain private. After 15 minutes, all data
            is permanently deleted from our servers.
          </p>
        </div>

        {/* FAQ Item 7 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>Can I access VanishPost from my mobile device?</h2>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            Yes, VanishPost is fully responsive and works on all modern devices including smartphones, tablets, and desktop computers.
            No app installation is required—simply visit the website in your browser.
          </p>
        </div>

        {/* FAQ Item 8 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>What types of attachments can I receive?</h2>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            VanishPost supports all common file attachments including documents, images, and compressed files.
            For security reasons, executable files (.exe, .bat, etc.) are blocked to protect users from potential malware.
          </p>
        </div>

        {/* FAQ Item 9 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>How often does the inbox refresh?</h2>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            VanishPost automatically checks for new emails every 14 seconds. You can also manually refresh the inbox
            at any time by clicking the refresh button.
          </p>
        </div>

        {/* FAQ Item 10 */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>Can I extend the life of my temporary email address?</h2>
          <p style={{ color: 'var(--earth-brown-medium)' }}>
            No, all VanishPost addresses expire after 15 minutes without exception. This is by design to ensure maximum privacy
            and security. If you need a longer-term solution, we recommend using a permanent email service.
          </p>
        </div>
      </div>

      <div className="mt-12 text-center">
        <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
          Still have questions? We're here to help!
        </p>
        <Link href="/contact" className="inline-block bg-[#ce601c] text-white px-6 py-3 rounded-md font-medium hover:bg-[#b85518] transition-colors">
          Contact Us
        </Link>
      </div>
    </div>
  );
}
