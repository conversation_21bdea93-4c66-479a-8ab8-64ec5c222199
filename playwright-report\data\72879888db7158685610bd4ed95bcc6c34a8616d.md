# Test info

- Name: Admin Dashboard >> should require authentication to access the admin dashboard
- Location: C:\Users\<USER>\Documents\augment-projects\js_fademail\e2e\admin-dashboard.spec.ts:4:7

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at C:\Users\<USER>\Documents\augment-projects\js_fademail\e2e\admin-dashboard.spec.ts:13:23
```

# Page snapshot

```yaml
- paragraph: Checking authentication...
- alert
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Admin Dashboard', () => {
   4 |   test('should require authentication to access the admin dashboard', async ({ page }) => {
   5 |     // Navigate to the admin dashboard
   6 |     await page.goto('/admin');
   7 |     
   8 |     // Check that we're redirected to the login page
   9 |     await page.waitForURL('**/admin/login');
   10 |     
   11 |     // Check that the login form is displayed
   12 |     const loginForm = await page.isVisible('form');
>  13 |     expect(loginForm).toBe(true);
      |                       ^ Error: expect(received).toBe(expected) // Object.is equality
   14 |     
   15 |     // Check that the username and password fields are displayed
   16 |     const usernameField = await page.isVisible('input[name="username"]');
   17 |     const passwordField = await page.isVisible('input[name="password"]');
   18 |     expect(usernameField).toBe(true);
   19 |     expect(passwordField).toBe(true);
   20 |   });
   21 |   
   22 |   test('should show error message for invalid credentials', async ({ page }) => {
   23 |     // Navigate to the admin login page
   24 |     await page.goto('/admin/login');
   25 |     
   26 |     // Fill in invalid credentials
   27 |     await page.fill('input[name="username"]', 'invalid-user');
   28 |     await page.fill('input[name="password"]', 'invalid-password');
   29 |     
   30 |     // Submit the form
   31 |     await page.click('button[type="submit"]');
   32 |     
   33 |     // Check that an error message is displayed
   34 |     await page.waitForSelector('.error-message');
   35 |     const errorMessage = await page.textContent('.error-message');
   36 |     expect(errorMessage).toContain('Invalid credentials');
   37 |   });
   38 |   
   39 |   test('should login with valid credentials and show the dashboard', async ({ page }) => {
   40 |     // This test assumes that there's a valid test user with username 'test-admin' and password 'test-password'
   41 |     // In a real scenario, you would set up a test user in your database before running this test
   42 |     
   43 |     // Navigate to the admin login page
   44 |     await page.goto('/admin/login');
   45 |     
   46 |     // Fill in valid credentials
   47 |     await page.fill('input[name="username"]', 'test-admin');
   48 |     await page.fill('input[name="password"]', 'test-password');
   49 |     
   50 |     // Submit the form
   51 |     await page.click('button[type="submit"]');
   52 |     
   53 |     // Check that we're redirected to the dashboard
   54 |     await page.waitForURL('**/admin/dashboard');
   55 |     
   56 |     // Check that the dashboard is displayed
   57 |     const dashboard = await page.isVisible('.admin-dashboard');
   58 |     expect(dashboard).toBe(true);
   59 |     
   60 |     // Check that the analytics section is displayed
   61 |     const analytics = await page.isVisible('.analytics-section');
   62 |     expect(analytics).toBe(true);
   63 |   });
   64 |   
   65 |   test('should display analytics data in the dashboard', async ({ page }) => {
   66 |     // This test assumes that the user is already logged in
   67 |     // In a real scenario, you would log in before running this test
   68 |     
   69 |     // Navigate to the admin dashboard
   70 |     await page.goto('/admin/dashboard');
   71 |     
   72 |     // Check that the page views chart is displayed
   73 |     const pageViewsChart = await page.isVisible('.page-views-chart');
   74 |     expect(pageViewsChart).toBe(true);
   75 |     
   76 |     // Check that the email generation chart is displayed
   77 |     const emailGenerationChart = await page.isVisible('.email-generation-chart');
   78 |     expect(emailGenerationChart).toBe(true);
   79 |     
   80 |     // Check that the email view chart is displayed
   81 |     const emailViewChart = await page.isVisible('.email-view-chart');
   82 |     expect(emailViewChart).toBe(true);
   83 |     
   84 |     // Check that the email deletion chart is displayed
   85 |     const emailDeletionChart = await page.isVisible('.email-deletion-chart');
   86 |     expect(emailDeletionChart).toBe(true);
   87 |   });
   88 |   
   89 |   test('should allow changing the date range for analytics data', async ({ page }) => {
   90 |     // This test assumes that the user is already logged in
   91 |     // In a real scenario, you would log in before running this test
   92 |     
   93 |     // Navigate to the admin dashboard
   94 |     await page.goto('/admin/dashboard');
   95 |     
   96 |     // Check that the date range selector is displayed
   97 |     const dateRangeSelector = await page.isVisible('.date-range-selector');
   98 |     expect(dateRangeSelector).toBe(true);
   99 |     
  100 |     // Select a different date range
  101 |     await page.selectOption('.date-range-selector select', '7');
  102 |     
  103 |     // Wait for the charts to update
  104 |     await page.waitForTimeout(1000);
  105 |     
  106 |     // Check that the charts are still displayed
  107 |     const pageViewsChart = await page.isVisible('.page-views-chart');
  108 |     expect(pageViewsChart).toBe(true);
  109 |   });
  110 | });
  111 |
```