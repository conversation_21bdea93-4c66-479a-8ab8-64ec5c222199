/**
 * Rate Limit Statistics API
 * 
 * Provides real-time statistics for rate limiting monitoring
 */

import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth/adminAuth';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logger } from '@/lib/logging/Logger';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '24h'; // 1h, 24h, 7d, 30d

    // Calculate time range
    const now = new Date();
    let startTime: Date;
    
    switch (timeframe) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default: // 24h
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    const supabase = createServerSupabaseClient();

    // Get analytics events for total requests (email generation)
    const { data: analyticsEvents, error: analyticsError } = await supabase
      .from('analytics_events')
      .select('*')
      .eq('event_type', 'email_generated')
      .gte('timestamp', startTime.toISOString());

    if (analyticsError) {
      await logger.error('RATE_LIMIT_STATS', 'Error fetching analytics events', { analyticsError });
    }

    // Get rate limit violations
    const { data: violations, error: violationsError } = await supabase
      .from('rate_limit_violations')
      .select('*')
      .gte('last_violation', startTime.toISOString());

    if (violationsError) {
      await logger.error('RATE_LIMIT_STATS', 'Error fetching violations', { violationsError });
    }

    // Get blocked IP attempts from logs (this would typically come from a logging system)
    // For now, we'll estimate based on blocked IPs and violations
    const { data: blockedIPs, error: blockedError } = await supabase
      .from('blocked_ips')
      .select('*')
      .eq('is_active', true);

    if (blockedError) {
      await logger.error('RATE_LIMIT_STATS', 'Error fetching blocked IPs', { blockedError });
    }

    // Process the data
    const totalRequests = analyticsEvents?.length || 0;
    
    // Estimate blocked requests (this would be more accurate with proper logging)
    const blockedRequests = violations?.reduce((sum, v) => sum + v.violation_count, 0) || 0;

    // Group violations by endpoint
    const violationsByEndpoint: Record<string, number> = {};
    violations?.forEach(violation => {
      violationsByEndpoint[violation.endpoint] = 
        (violationsByEndpoint[violation.endpoint] || 0) + violation.violation_count;
    });

    // Get top violating IPs
    const ipViolationMap: Record<string, { violations: number; lastViolation: string }> = {};
    violations?.forEach(violation => {
      const ip = violation.ip_address;
      if (!ipViolationMap[ip]) {
        ipViolationMap[ip] = { violations: 0, lastViolation: violation.last_violation };
      }
      ipViolationMap[ip].violations += violation.violation_count;
      if (new Date(violation.last_violation) > new Date(ipViolationMap[ip].lastViolation)) {
        ipViolationMap[ip].lastViolation = violation.last_violation;
      }
    });

    const topViolatingIPs = Object.entries(ipViolationMap)
      .map(([ip, data]) => ({ ip, ...data }))
      .sort((a, b) => b.violations - a.violations)
      .slice(0, 10);

    // Generate recent activity (combining violations and blocks)
    const recentActivity: Array<{
      timestamp: string;
      ip: string;
      endpoint: string;
      action: 'blocked' | 'rate_limited' | 'violation';
      details?: string;
    }> = [];

    // Add violations to recent activity
    violations?.forEach(violation => {
      recentActivity.push({
        timestamp: violation.last_violation,
        ip: violation.ip_address,
        endpoint: violation.endpoint,
        action: 'violation',
        details: `${violation.violation_count} violations`
      });
    });

    // Add recent blocks to activity
    blockedIPs?.forEach(blocked => {
      if (blocked.blocked_at && new Date(blocked.blocked_at) >= startTime) {
        recentActivity.push({
          timestamp: blocked.blocked_at,
          ip: blocked.ip_address,
          endpoint: 'system',
          action: 'blocked',
          details: blocked.reason
        });
      }
    });

    // Sort recent activity by timestamp (most recent first)
    recentActivity.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Limit to last 50 activities
    const limitedRecentActivity = recentActivity.slice(0, 50);

    const stats = {
      totalRequests,
      blockedRequests,
      violationsByEndpoint,
      topViolatingIPs,
      recentActivity: limitedRecentActivity,
      timeframe,
      generatedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    await logger.error('RATE_LIMIT_STATS', 'Error generating rate limit statistics', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to generate statistics' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/rate-limit-stats
 * Trigger cleanup or maintenance operations
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'cleanup_old_violations':
        // Clean up violations older than 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const supabase2 = createServerSupabaseClient();
        const { data: cleanupResult, error: cleanupError } = await supabase2
          .from('rate_limit_violations')
          .delete()
          .lt('last_violation', thirtyDaysAgo.toISOString())
          .eq('is_resolved', true);

        if (cleanupError) {
          throw cleanupError;
        }

        await logger.info('RATE_LIMIT_STATS', 'Old violations cleaned up', { 
          cleanedBy: authResult.user?.username
        });

        return NextResponse.json({
          success: true,
          message: 'Old violations cleaned up successfully'
        });

      case 'reset_violation_counts':
        // Reset violation counts for resolved violations
        const supabase3 = createServerSupabaseClient();
        const { error: resetError } = await supabase3
          .from('rate_limit_violations')
          .update({ violation_count: 0 })
          .eq('is_resolved', true);

        if (resetError) {
          throw resetError;
        }

        await logger.info('RATE_LIMIT_STATS', 'Violation counts reset', { 
          resetBy: authResult.user?.username
        });

        return NextResponse.json({
          success: true,
          message: 'Violation counts reset successfully'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    await logger.error('RATE_LIMIT_STATS', 'Error performing maintenance action', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to perform action' },
      { status: 500 }
    );
  }
}
