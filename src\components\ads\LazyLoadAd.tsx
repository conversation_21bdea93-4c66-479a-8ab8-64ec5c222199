'use client';

import { useState, useEffect, useRef } from 'react';
import { trackAdImpression } from '@/lib/config/adService';
import { ClientOnly } from '@/lib/utils/suppressHydrationWarning';

// Add type declaration for window.adsbygoogle
declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

interface LazyLoadAdProps {
  adId: string;
  adUnitId: string;
  className?: string;
  threshold?: number; // Percentage of element visible to trigger loading (0-1)
  placeholderHeight?: number; // Height in pixels for placeholder
  loadingDelay?: number; // Delay in ms before loading the ad
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

export default function LazyLoadAd({
  adId,
  adUnitId,
  className = '',
  threshold = 0.1,
  placeholderHeight = 250,
  loadingDelay = 0,
  onLoad,
  onError
}: LazyLoadAdProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [impressionTracked, setImpressionTracked] = useState(false);
  const adRef = useRef<HTMLDivElement>(null);

  // Set up intersection observer to detect when ad is visible
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsVisible(entry.isIntersecting);
      },
      {
        root: null, // viewport
        rootMargin: '0px',
        threshold // percentage of element visible
      }
    );

    const currentRef = adRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [threshold]);

  // Load ad when visible
  useEffect(() => {
    if (isVisible && !isLoaded && !hasError) {
      const timer = setTimeout(() => {
        try {
          // Load the ad
          if (adUnitId.startsWith('ca-pub-')) {
            // For Google AdSense
            (window.adsbygoogle = window.adsbygoogle || []).push({});
          }

          setIsLoaded(true);
          if (onLoad) onLoad();

          // Track impression
          if (!impressionTracked) {
            trackAdImpression(adId).then(() => {
              setImpressionTracked(true);
            });
          }
        } catch (error) {
          setHasError(true);
          if (onError) onError(error instanceof Error ? error : new Error('Error loading ad'));
          console.error('Error loading ad:', error);
        }
      }, loadingDelay);

      return () => clearTimeout(timer);
    }
  }, [isVisible, isLoaded, hasError, adId, adUnitId, loadingDelay, impressionTracked, onLoad, onError]);

  // Wrap all return statements with ClientOnly to prevent hydration errors
  return (
    <ClientOnly>
      {(() => {
        // Render placeholder until ad is loaded
        if (!isVisible || (!isLoaded && !hasError)) {
          return (
            <div
              ref={adRef}
              className={`ad-placeholder ${className}`}
              style={{ height: `${placeholderHeight}px` }}
              data-ad-id={adId}
            >
              <div className="flex items-center justify-center h-full bg-gray-100 animate-pulse rounded">
                <span className="text-gray-400 text-sm">Ad loading...</span>
              </div>
            </div>
          );
        }

        // Render error state
        if (hasError) {
          return (
            <div
              ref={adRef}
              className={`ad-error ${className}`}
              data-ad-id={adId}
            >
              <div className="flex items-center justify-center h-full bg-gray-100 rounded p-4">
                <span className="text-gray-500 text-sm">Ad could not be loaded</span>
              </div>
            </div>
          );
        }

        // Render Google AdSense ad
        if (adUnitId.startsWith('ca-pub-')) {
          return (
            <div
              ref={adRef}
              className={`ad-container ${className}`}
              data-ad-id={adId}
            >
              <ins
                className="adsbygoogle"
                style={{ display: 'block' }}
                data-ad-client={adUnitId.split('/')[0]}
                data-ad-slot={adUnitId.split('/')[1] || ''}
                data-ad-format="auto"
                data-full-width-responsive="true"
              />
            </div>
          );
        }

        // Render custom ad
        return (
          <div
            ref={adRef}
            className={`ad-container ${className}`}
            data-ad-id={adId}
          >
            <div className="bg-gray-100 p-4 rounded text-center">
              <p className="text-sm text-gray-700 mb-2">Advertisement</p>
              <div className="bg-white p-4 rounded-md shadow-sm">
                <p className="font-medium text-gray-800">Custom Ad Content</p>
                <p className="text-sm text-gray-600 mt-1">Ad ID: {adUnitId}</p>
              </div>
            </div>
          </div>
        );
      })()}
    </ClientOnly>
  );
}
