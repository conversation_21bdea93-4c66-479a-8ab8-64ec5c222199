'use client';

import { useState, useEffect } from 'react';
import { ArrowPathIcon, ChartBarIcon, TableCellsIcon } from '@heroicons/react/24/solid';
import { format, subDays } from 'date-fns';
import { AdConfig } from '@/lib/config/types';
import AdPerformanceCharts from './AdPerformanceCharts';

interface AdPerformanceProps {
  adPlacement?: AdConfig;
}

interface PerformanceMetrics {
  date: string;
  impressions: number;
  clicks: number;
  ctr: number;
  revenue?: number;
}

export default function AdPerformance({ adPlacement }: AdPerformanceProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d'>('7d');
  const [viewMode, setViewMode] = useState<'table' | 'charts'>('charts');

  // Fetch performance metrics
  const fetchPerformanceMetrics = async () => {
    if (!adPlacement) return;

    try {
      setLoading(true);
      setError(null);

      // Calculate date range
      const endDate = format(new Date(), 'yyyy-MM-dd');
      let startDate: string;

      switch (dateRange) {
        case '30d':
          startDate = format(subDays(new Date(), 30), 'yyyy-MM-dd');
          break;
        case '90d':
          startDate = format(subDays(new Date(), 90), 'yyyy-MM-dd');
          break;
        default:
          startDate = format(subDays(new Date(), 7), 'yyyy-MM-dd');
          break;
      }

      // Use the mock API for testing (using secure admin path)
      const response = await fetch(
        `/api/management-portal-x7z9y2/ads/${adPlacement.placementId}/${adPlacement.domain}/performance/mock?startDate=${startDate}&endDate=${endDate}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch performance metrics: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setMetrics(data.data);
      } else {
        setError(data.error || 'Failed to fetch performance metrics');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching performance metrics');
      console.error('Error fetching performance metrics:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch and when adPlacement or dateRange changes
  useEffect(() => {
    if (adPlacement) {
      fetchPerformanceMetrics();
    }
  }, [adPlacement, dateRange]);

  // Calculate totals
  const totalImpressions = metrics.reduce((sum, metric) => sum + metric.impressions, 0);
  const totalClicks = metrics.reduce((sum, metric) => sum + metric.clicks, 0);
  const averageCtr = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
  const totalRevenue = metrics.reduce((sum, metric) => sum + (metric.revenue || 0), 0);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'MMM d, yyyy');
  };

  if (!adPlacement) {
    return (
      <div className="bg-white shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Ad Performance</h3>
        </div>
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-8">
            <p className="text-gray-500">Select an ad placement to view performance metrics.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden rounded-lg">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Ad Performance</h3>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label htmlFor="date-range" className="text-sm font-medium text-gray-700">
              Date Range:
            </label>
            <select
              id="date-range"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value as '7d' | '30d' | '90d')}
              className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </div>
          <div className="flex rounded-md shadow-sm" role="group">
            <button
              type="button"
              onClick={() => setViewMode('charts')}
              className={`inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-l-md ${viewMode === 'charts' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'}`}
            >
              <ChartBarIcon className="h-4 w-4 mr-1.5" />
              Charts
            </button>
            <button
              type="button"
              onClick={() => setViewMode('table')}
              className={`inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-r-md ${viewMode === 'table' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'}`}
            >
              <TableCellsIcon className="h-4 w-4 mr-1.5" />
              Table
            </button>
          </div>
          <button
            type="button"
            onClick={fetchPerformanceMetrics}
            disabled={loading}
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-1.5 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>
      <div className="px-4 py-5 sm:p-6">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <dt className="text-sm font-medium text-gray-500 truncate">Total Impressions</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">
                {totalImpressions.toLocaleString()}
              </dd>
            </div>
          </div>
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <dt className="text-sm font-medium text-gray-500 truncate">Total Clicks</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">
                {totalClicks.toLocaleString()}
              </dd>
            </div>
          </div>
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <dt className="text-sm font-medium text-gray-500 truncate">Average CTR</dt>
              <dd className="mt-1 text-3xl font-semibold text-gray-900">
                {averageCtr.toFixed(2)}%
              </dd>
            </div>
          </div>
          {totalRevenue > 0 && (
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                <dd className="mt-1 text-3xl font-semibold text-gray-900">
                  ${totalRevenue.toFixed(2)}
                </dd>
              </div>
            </div>
          )}
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <ArrowPathIcon className="h-8 w-8 text-indigo-500 animate-spin" />
          </div>
        ) : metrics.length > 0 ? (
          viewMode === 'table' ? (
            <div className="mt-8">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Daily Performance</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Impressions
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Clicks
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        CTR
                      </th>
                      {metrics.some(metric => metric.revenue !== undefined) && (
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Revenue
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {metrics.map((metric) => (
                      <tr key={metric.date}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {formatDate(metric.date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {metric.impressions.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {metric.clicks.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {metric.ctr.toFixed(2)}%
                        </td>
                        {metrics.some(metric => metric.revenue !== undefined) && (
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {metric.revenue !== undefined ? `$${metric.revenue.toFixed(2)}` : '-'}
                          </td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="mt-8">
              <AdPerformanceCharts
                adPlacement={adPlacement}
                dateRange={dateRange}
                metrics={metrics}
              />
            </div>
          )
        ) : (
          <div className="mt-8 text-center py-8">
            <p className="text-gray-500">No performance data available for the selected date range.</p>
          </div>
        )}
      </div>
    </div>
  );
}
