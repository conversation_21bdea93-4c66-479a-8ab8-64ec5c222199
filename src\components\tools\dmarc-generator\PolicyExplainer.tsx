'use client';

// DMARC Policy Explainer Component - Placeholder implementation
import React, { useState } from 'react';
import { DmarcPolicy } from '@/types/dmarc';

interface PolicyExplainerProps {
  policy?: DmarcPolicy;
  expanded?: boolean;
}

export default function PolicyExplainer({ policy = 'none', expanded = false }: PolicyExplainerProps) {
  const [isExpanded, setIsExpanded] = useState(expanded);

  const policyInfo = {
    none: {
      title: 'Monitor Mode (p=none)',
      description: 'Emails that fail DMARC authentication are delivered normally, but reports are generated.',
      impact: 'No impact on email delivery',
      useCase: 'Perfect for initial setup and monitoring',
      nextStep: 'Monitor reports for 1-2 weeks, then consider upgrading to Quarantine',
      color: 'border-[#66b077] bg-[#66b077] bg-opacity-10'
    },
    quarantine: {
      title: 'Quarantine Mode (p=quarantine)',
      description: 'Emails that fail DMARC authentication are sent to the spam/junk folder.',
      impact: 'Failed emails may be marked as spam',
      useCase: 'Intermediate step to test policy without blocking emails',
      nextStep: 'Monitor for issues, then consider upgrading to Reject for maximum security',
      color: 'border-[#f59e0b] bg-[#f59e0b] bg-opacity-10'
    },
    reject: {
      title: 'Reject Mode (p=reject)',
      description: 'Emails that fail DMARC authentication are rejected and not delivered.',
      impact: 'Failed emails are completely blocked',
      useCase: 'Maximum security for domains with properly configured SPF and DKIM',
      nextStep: 'Monitor reports to ensure legitimate emails are not being blocked',
      color: 'border-[#dc2626] bg-[#dc2626] bg-opacity-10'
    }
  };

  const info = policyInfo[policy];

  return (
    <div className={`border rounded-lg p-4 ${info.color}`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full text-left"
      >
        <h3 className="font-medium text-[#1b130e]">
          {info.title}
        </h3>
        <span className="text-[#1b130e]">
          {isExpanded ? '−' : '+'}
        </span>
      </button>
      
      {isExpanded && (
        <div className="mt-4 space-y-3">
          <div>
            <h4 className="font-medium text-[#1b130e] mb-1">Description</h4>
            <p className="text-sm text-[#4a3728]">{info.description}</p>
          </div>
          
          <div>
            <h4 className="font-medium text-[#1b130e] mb-1">Impact on Email Delivery</h4>
            <p className="text-sm text-[#4a3728]">{info.impact}</p>
          </div>
          
          <div>
            <h4 className="font-medium text-[#1b130e] mb-1">Best Use Case</h4>
            <p className="text-sm text-[#4a3728]">{info.useCase}</p>
          </div>
          
          <div>
            <h4 className="font-medium text-[#1b130e] mb-1">Next Step</h4>
            <p className="text-sm text-[#4a3728]">{info.nextStep}</p>
          </div>
          
          <div className="bg-white bg-opacity-50 p-3 rounded border">
            <h4 className="font-medium text-[#1b130e] mb-2">Implementation Timeline</h4>
            <div className="space-y-2 text-sm text-[#4a3728]">
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-[#66b077] rounded-full"></span>
                <span>Week 1-2: Start with "none" policy</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-[#f59e0b] rounded-full"></span>
                <span>Week 3-4: Upgrade to "quarantine" if reports look good</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-[#dc2626] rounded-full"></span>
                <span>Week 5+: Consider "reject" for maximum security</span>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <p className="text-xs text-[#4a3728] mt-3">
        Component implementation coming in Phase 3
      </p>
    </div>
  );
}
