'use client';

// Key Strength Selector Component - Placeholder implementation
import React from 'react';

interface KeyStrengthSelectorProps {
  value?: 1024 | 2048;
  onChange?: (value: 1024 | 2048) => void;
  disabled?: boolean;
}

export default function KeyStrengthSelector({ 
  value = 2048, 
  onChange, 
  disabled = false 
}: KeyStrengthSelectorProps) {
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-[#1b130e]">
        Key Strength
      </label>
      <div className="space-y-2">
        <label className="flex items-center">
          <input
            type="radio"
            name="keyStrength"
            value="2048"
            checked={value === 2048}
            onChange={() => onChange?.(2048)}
            disabled={disabled}
            className="mr-2 text-[#66b077] focus:ring-[#66b077]"
          />
          <span className="text-[#1b130e]">
            2048 bits (Recommended) - More secure, slower generation
          </span>
        </label>
        <label className="flex items-center">
          <input
            type="radio"
            name="keyStrength"
            value="1024"
            checked={value === 1024}
            onChange={() => onChange?.(1024)}
            disabled={disabled}
            className="mr-2 text-[#66b077] focus:ring-[#66b077]"
          />
          <span className="text-[#1b130e]">
            1024 bits - Faster generation, less secure
          </span>
        </label>
      </div>
      <p className="text-sm text-[#4a3728]">
        Component implementation coming in Phase 2
      </p>
    </div>
  );
}
