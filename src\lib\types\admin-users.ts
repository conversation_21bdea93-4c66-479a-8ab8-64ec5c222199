/**
 * Types for the admin user management system
 */

export type UserRole = 'admin' | 'editor';

export interface AdminUser {
  id: number;
  username: string;
  email: string;
  fullName: string | null;
  role: UserRole;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastLogin: string | null;
}

export interface AdminUserCreate {
  username: string;
  email: string;
  password: string;
  fullName?: string;
  role: UserRole;
  isActive?: boolean;
}

export interface AdminUserUpdate {
  id: number;
  email?: string;
  fullName?: string;
  role?: UserRole;
  isActive?: boolean;
  password?: string;
}

export interface AdminUserActivity {
  id: number;
  userId: number;
  username: string;
  actionType: string;
  resourceType: string | null;
  resourceId: string | null;
  details: any;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
}

export interface AdminUserActivityFilter {
  userId?: number;
  actionType?: string;
  resourceType?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}
