/**
 * Admin user management service
 */
import { createServerSupabaseClient } from '../supabase';
import { AdminUser, AdminUserCreate, AdminUserUpdate, AdminUserActivity, AdminUserActivityFilter } from '../types/admin-users';
import bcrypt from 'bcrypt';

/**
 * Get all admin users
 */
export async function getAdminUsers(): Promise<AdminUser[]> {
  try {
    const supabase = createServerSupabaseClient();

    const { data, error } = await supabase
      .from('admin_users' as any)
      .select('id, username, email, full_name, role, is_active, created_at, updated_at, last_login')
      .order('username');

    if (error) throw error;

    // Type assertion for data
    const userData = data as any[];

    return userData.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.full_name,
      role: user.role,
      isActive: user.is_active,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      lastLogin: user.last_login
    }));
  } catch (error) {
    console.error('Error getting admin users:', error);
    throw error;
  }
}

/**
 * Get admin user by ID
 */
export async function getAdminUserById(id: number): Promise<AdminUser | null> {
  try {
    const supabase = createServerSupabaseClient();

    const { data, error } = await supabase
      .from('admin_users' as any)
      .select('id, username, email, full_name, role, is_active, created_at, updated_at, last_login')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Record not found
      throw error;
    }

    // Type assertion for data
    const userData = data as any;

    return {
      id: userData.id,
      username: userData.username,
      email: userData.email,
      fullName: userData.full_name,
      role: userData.role,
      isActive: userData.is_active,
      createdAt: userData.created_at,
      updatedAt: userData.updated_at,
      lastLogin: userData.last_login
    };
  } catch (error) {
    console.error(`Error getting admin user with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new admin user
 */
export async function createAdminUser(user: AdminUserCreate): Promise<AdminUser> {
  try {
    const supabase = createServerSupabaseClient();

    // Hash the password
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(user.password, saltRounds);

    const { data, error } = await supabase
      .from('admin_users' as any)
      .insert({
        username: user.username,
        email: user.email,
        password_hash: passwordHash,
        full_name: user.fullName || null,
        role: user.role,
        is_active: user.isActive !== undefined ? user.isActive : true
      })
      .select('id, username, email, full_name, role, is_active, created_at, updated_at, last_login')
      .single();

    if (error) throw error;

    // Type assertion for data
    const userData = data as any;

    return {
      id: userData.id,
      username: userData.username,
      email: userData.email,
      fullName: userData.full_name,
      role: userData.role,
      isActive: userData.is_active,
      createdAt: userData.created_at,
      updatedAt: userData.updated_at,
      lastLogin: userData.last_login
    };
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
}

/**
 * Update an existing admin user
 */
export async function updateAdminUser(user: AdminUserUpdate): Promise<AdminUser> {
  try {
    const supabase = createServerSupabaseClient();

    const updates: any = {
      updated_at: new Date().toISOString()
    };

    if (user.email !== undefined) updates.email = user.email;
    if (user.fullName !== undefined) updates.full_name = user.fullName;
    if (user.role !== undefined) updates.role = user.role;
    if (user.isActive !== undefined) updates.is_active = user.isActive;

    // If password is provided, hash it
    if (user.password) {
      const saltRounds = 10;
      updates.password_hash = await bcrypt.hash(user.password, saltRounds);
    }

    const { data, error } = await supabase
      .from('admin_users' as any)
      .update(updates)
      .eq('id', user.id)
      .select('id, username, email, full_name, role, is_active, created_at, updated_at, last_login')
      .single();

    if (error) throw error;

    // Type assertion for data
    const userData = data as any;

    return {
      id: userData.id,
      username: userData.username,
      email: userData.email,
      fullName: userData.full_name,
      role: userData.role,
      isActive: userData.is_active,
      createdAt: userData.created_at,
      updatedAt: userData.updated_at,
      lastLogin: userData.last_login
    };
  } catch (error) {
    console.error(`Error updating admin user with ID ${user.id}:`, error);
    throw error;
  }
}

/**
 * Delete an admin user
 */
export async function deleteAdminUser(id: number): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();

    const { error } = await supabase
      .from('admin_users' as any)
      .delete()
      .eq('id', id);

    if (error) throw error;

    return true;
  } catch (error) {
    console.error(`Error deleting admin user with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Update user's last login timestamp
 */
export async function updateLastLogin(id: number): Promise<void> {
  try {
    const supabase = createServerSupabaseClient();

    const { error } = await supabase
      .from('admin_users' as any)
      .update({
        last_login: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) throw error;
  } catch (error) {
    console.error(`Error updating last login for user with ID ${id}:`, error);
    throw error;
  }
}

/**
 * Log admin user activity
 */
export async function logAdminActivity(
  userId: number,
  actionType: string,
  resourceType: string | null = null,
  resourceId: string | null = null,
  details: any = null,
  ipAddress: string = 'unknown',
  userAgent: string = 'unknown'
): Promise<void> {
  try {
    const supabase = createServerSupabaseClient();

    const { error } = await supabase
      .from('admin_activity_log' as any)
      .insert({
        user_id: userId,
        action_type: actionType,
        resource_type: resourceType,
        resource_id: resourceId,
        details: details,
        ip_address: ipAddress,
        user_agent: userAgent
      });

    if (error) throw error;
  } catch (error) {
    console.error('Error logging admin activity:', error);
    // Don't throw the error to prevent disrupting the main flow
  }
}

/**
 * Get admin user activity logs
 */
export async function getAdminActivityLogs(filter: AdminUserActivityFilter = {}): Promise<AdminUserActivity[]> {
  try {
    const supabase = createServerSupabaseClient();

    let query = supabase
      .from('admin_activity_log' as any)
      .select(`
        id,
        user_id,
        action_type,
        resource_type,
        resource_id,
        details,
        ip_address,
        user_agent,
        created_at,
        admin_users(username)
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (filter.userId) query = query.eq('user_id', filter.userId);
    if (filter.actionType) query = query.eq('action_type', filter.actionType);
    if (filter.resourceType) query = query.eq('resource_type', filter.resourceType);
    if (filter.startDate) query = query.gte('created_at', filter.startDate);
    if (filter.endDate) query = query.lte('created_at', filter.endDate);

    // Apply pagination
    if (filter.limit) query = query.limit(filter.limit);
    if (filter.offset) query = query.range(filter.offset, filter.offset + (filter.limit || 20) - 1);

    const { data, error } = await query;

    if (error) throw error;

    // Type assertion for data
    const logData = data as any[];

    return logData.map(log => ({
      id: log.id,
      userId: log.user_id,
      username: log.admin_users?.username || 'Unknown',
      actionType: log.action_type,
      resourceType: log.resource_type,
      resourceId: log.resource_id,
      details: log.details,
      ipAddress: log.ip_address,
      userAgent: log.user_agent,
      createdAt: log.created_at
    }));
  } catch (error) {
    console.error('Error getting admin activity logs:', error);
    throw error;
  }
}

/**
 * Verify admin user credentials
 */
export async function verifyAdminCredentials(username: string, password: string): Promise<{ user: AdminUser | null, valid: boolean }> {
  try {
    const supabase = createServerSupabaseClient();

    const { data, error } = await supabase
      .from('admin_users' as any)
      .select('id, username, email, full_name, role, is_active, created_at, updated_at, last_login, password_hash')
      .eq('username', username)
      .single();

    if (error) {
      return {
        user: null,
        valid: false
      };
    }

    // Type assertion for data
    const userData = data as any;

    // Check if user is active
    if (!userData.is_active) {
      return {
        user: null,
        valid: false
      };
    }

    // Verify password
    const valid = await bcrypt.compare(password, userData.password_hash);

    if (!valid) {
      return {
        user: null,
        valid: false
      };
    }

    // Update last login time
    await updateLastLogin(userData.id);

    return {
      user: {
        id: userData.id,
        username: userData.username,
        email: userData.email,
        fullName: userData.full_name,
        role: userData.role,
        isActive: userData.is_active,
        createdAt: userData.created_at,
        updatedAt: userData.updated_at,
        lastLogin: userData.last_login
      },
      valid: true
    };
  } catch (error) {
    console.error(`Error verifying credentials for user ${username}:`, error);
    return {
      user: null,
      valid: false
    };
  }
}
