'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

interface SystemInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SystemInfo {
  status: string;
  timestamp: string;
  version: string;
  components: {
    supabase: { status: string; responseTime?: number };
    guerrilla: { status: string; responseTime?: number };
    emailService: { status: string };
    config: { status: string };
  };
  metrics: {
    activeEmails: number;
    emailsReceivedLast24Hours: number;
    totalLogs: number;
    errorLogs: number;
    activeDomains: number;
  };
  system: {
    uptime: number;
    memory: {
      total: number;
      free: number;
      used: number;
    };
    cpu: {
      cores: number;
      load: number[];
    };
  };
}

export default function SystemInfoModal({ isOpen, onClose }: SystemInfoModalProps) {
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSystemInfo = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/management-portal-x7z9y2/health');

      if (!response.ok) {
        throw new Error(`Failed to fetch system info: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      // Handle both formats: direct data or {success: true, data: {...}}
      if (result.success && result.data) {
        setSystemInfo(result.data);
      } else if (result.status) {
        // Direct format from the health endpoint
        setSystemInfo(result);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch system information');
      console.error('Error fetching system info:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (isOpen) {
      fetchSystemInfo();
    }
  }, [isOpen, fetchSystemInfo]);

  // Format bytes to human-readable format
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format uptime to days, hours, minutes
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    return `${days}d ${hours}h ${minutes}m`;
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
        return 'bg-green-500';
      case 'degraded':
        return 'bg-yellow-500';
      case 'unhealthy':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            System Information
          </DialogTitle>
          <DialogDescription>
            Detailed information about the current system status and performance.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <p className="font-medium">Error loading system information</p>
            <p className="text-sm">{error}</p>
          </div>
        ) : systemInfo ? (
          <div className="space-y-6">
            {/* Overall Status */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Badge className={`${getStatusColor(systemInfo.status)} text-white mr-2`}>
                  {systemInfo.status.toUpperCase()}
                </Badge>
                <span className="text-sm text-gray-500">
                  Last updated: {new Date(systemInfo.timestamp).toLocaleString()}
                </span>
              </div>
              <div className="text-sm text-gray-500">
                Version: {systemInfo.version}
              </div>
            </div>

            {/* System Resources */}
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">System Resources</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Uptime:</span>
                      <span className="font-medium">{formatUptime(systemInfo.system.uptime)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">CPU Cores:</span>
                      <span className="font-medium">{systemInfo.system.cpu.cores}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">CPU Load (1m, 5m, 15m):</span>
                      <span className="font-medium">
                        {systemInfo.system.cpu.load.map(load => load.toFixed(2)).join(', ')}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Memory:</span>
                      <span className="font-medium">{formatBytes(systemInfo.system.memory.total)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Used Memory:</span>
                      <span className="font-medium">{formatBytes(systemInfo.system.memory.used)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Free Memory:</span>
                      <span className="font-medium">{formatBytes(systemInfo.system.memory.free)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Component Status */}
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">Component Status</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(systemInfo.components).map(([name, component]) => (
                    <div key={name} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                      <div className="flex items-center">
                        <Badge className={`${getStatusColor(component.status)} text-white mr-2`}>
                          {component.status.toUpperCase()}
                        </Badge>
                        <span className="capitalize">{name}</span>
                      </div>
                      {'responseTime' in component && component.responseTime && (
                        <span className="text-sm text-gray-500">{component.responseTime}ms</span>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Metrics */}
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">System Metrics</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-md text-center">
                    <div className="text-2xl font-bold text-indigo-600">
                      {systemInfo.metrics.activeEmails}
                    </div>
                    <div className="text-sm text-gray-600">Active Email Addresses</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-md text-center">
                    <div className="text-2xl font-bold text-indigo-600">
                      {systemInfo.metrics.emailsReceivedLast24Hours}
                    </div>
                    <div className="text-sm text-gray-600">Emails Received (24h)</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-md text-center">
                    <div className="text-2xl font-bold text-indigo-600">
                      {systemInfo.metrics.activeDomains}
                    </div>
                    <div className="text-sm text-gray-600">Active Domains</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-md text-center">
                    <div className="text-2xl font-bold text-indigo-600">
                      {systemInfo.metrics.totalLogs}
                    </div>
                    <div className="text-sm text-gray-600">Total Logs</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-md text-center">
                    <div className="text-2xl font-bold text-indigo-600">
                      {systemInfo.metrics.errorLogs}
                    </div>
                    <div className="text-sm text-gray-600">Error Logs</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="py-8 text-center text-gray-500">
            No system information available
          </div>
        )}

        <DialogFooter>
          <Button
            variant="secondary"
            onClick={fetchSystemInfo}
            disabled={isLoading}
            className="mr-2"
          >
            {isLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
