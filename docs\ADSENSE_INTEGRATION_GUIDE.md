# Google AdSense Integration Guide for Next.js 15.3.0 with App Router

This guide explains how to properly integrate Google AdSense into a Next.js application using the App Router, avoiding common issues like the `data-nscript` attribute error.

## Common Issues with AdSense in Next.js

When integrating Google AdSense with Next.js, you might encounter these issues:

1. **data-nscript attribute error**: Next.js adds a `data-nscript` attribute to scripts, which AdSense doesn't support.
2. **Hydration errors**: Server-side rendering can cause mismatches between server and client HTML.
3. **Script loading issues**: AdSense script needs to be loaded correctly to initialize properly.
4. **Ad display timing**: Ads need to be displayed after the AdSense script has loaded.

## Solution Overview

Our solution involves:

1. Creating a client component that injects the AdSense script directly into the DOM
2. Using a separate component for displaying individual ad units
3. Properly handling client-side rendering to avoid hydration errors

## Implementation Steps

### 1. Create the AdSenseScript Component

This component loads the AdSense script on the client side without the problematic `data-nscript` attribute:

```tsx
// src/components/AdSenseScript.tsx
'use client';

import { useEffect, useState } from 'react';

interface AdSenseScriptProps {
  clientId: string;
}

export default function AdSenseScript({ clientId }: AdSenseScriptProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    // Create and inject the AdSense script directly
    if (typeof document !== 'undefined' && !document.getElementById('google-adsense-script')) {
      const script = document.createElement('script');
      script.id = 'google-adsense-script';
      script.async = true;
      script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${clientId}`;
      script.crossOrigin = 'anonymous';
      
      document.head.appendChild(script);
    }

    // Cleanup function
    return () => {
      if (typeof document !== 'undefined') {
        const scriptElement = document.getElementById('google-adsense-script');
        if (scriptElement) {
          scriptElement.remove();
        }
        
        const noablateElements = document.querySelectorAll('.adsbygoogle-noablate');
        noablateElements.forEach(element => {
          element.remove();
        });
      }
    };
  }, [clientId]);

  return null;
}
```

### 2. Create a Client Component Wrapper

Since we can't use dynamic imports with `ssr: false` directly in Server Components, we need a client component wrapper:

```tsx
// src/components/AdSenseScriptLoader.tsx
'use client';

import { useEffect, useState } from 'react';
import AdSenseScript from './AdSenseScript';

interface AdSenseScriptLoaderProps {
  clientId: string;
}

export default function AdSenseScriptLoader({ clientId }: AdSenseScriptLoaderProps) {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted) {
    return null;
  }
  
  return <AdSenseScript clientId={clientId} />;
}
```

### 3. Create the AdSenseAd Component

This component renders individual ad units:

```tsx
// src/components/AdSenseAd.tsx
'use client';

import { useEffect, useRef } from 'react';

interface AdSenseAdProps {
  adSlot: string;
  adFormat?: 'auto' | 'fluid' | 'rectangle' | 'horizontal' | 'vertical';
  fullWidthResponsive?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export default function AdSenseAd({
  adSlot,
  adFormat = 'auto',
  fullWidthResponsive = true,
  className = '',
  style = {}
}: AdSenseAdProps) {
  const adRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (typeof window === 'undefined' || !adRef.current) return;
    
    try {
      adRef.current.innerHTML = '';
      
      const adElement = document.createElement('ins');
      adElement.className = 'adsbygoogle';
      adElement.style.display = 'block';
      adElement.dataset.adClient = 'ca-pub-8397529755029714'; // Your publisher ID
      adElement.dataset.adSlot = adSlot;
      adElement.dataset.adFormat = adFormat;
      
      if (fullWidthResponsive) {
        adElement.dataset.fullWidthResponsive = 'true';
      }
      
      adRef.current.appendChild(adElement);
      
      (window.adsbygoogle = window.adsbygoogle || []).push({});
    } catch (error) {
      console.error('Error setting up AdSense ad:', error);
    }
    
    return () => {
      if (adRef.current) {
        adRef.current.innerHTML = '';
      }
    };
  }, [adSlot, adFormat, fullWidthResponsive]);
  
  return (
    <div 
      ref={adRef}
      className={`adsense-container ${className}`}
      style={{
        minHeight: '100px',
        display: 'block',
        overflow: 'hidden',
        ...style
      }}
      data-ad-slot={adSlot}
    />
  );
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}
```

### 4. Add the AdSenseScriptLoader to Your Layout

Add the script loader to your root layout:

```tsx
// src/app/layout.tsx
import AdSenseScriptLoader from "@/components/AdSenseScriptLoader";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {/* Preconnect to AdSense domains to improve performance */}
        <link rel="preconnect" href="https://pagead2.googlesyndication.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://googleads.g.doubleclick.net" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://tpc.googlesyndication.com" crossOrigin="anonymous" />
      </head>
      <body>
        {children}
        
        {/* Load AdSense script client-side only */}
        <AdSenseScriptLoader clientId="ca-pub-8397529755029714" />
      </body>
    </html>
  );
}
```

### 5. Use the AdSenseAd Component in Your Pages

Now you can use the AdSenseAd component in any page:

```tsx
// src/app/some-page/page.tsx
'use client';

import AdSenseAd from '@/components/AdSenseAd';

export default function SomePage() {
  return (
    <div className="container mx-auto p-4">
      <h1>Page Content</h1>
      
      {/* Top banner ad */}
      <AdSenseAd 
        adSlot="1234567890" 
        adFormat="horizontal"
        style={{ minHeight: '90px', marginBottom: '20px' }}
      />
      
      <p>Your page content goes here...</p>
      
      {/* In-content ad */}
      <AdSenseAd 
        adSlot="0987654321" 
        adFormat="rectangle"
        style={{ minHeight: '250px', margin: '20px 0' }}
      />
      
      <p>More content...</p>
    </div>
  );
}
```

## Best Practices

1. **Avoid Layout Shifts**: Always set a minimum height for ad containers to prevent layout shifts.
2. **Lazy Load Ads**: Consider lazy loading ads that are below the fold.
3. **Respect User Preferences**: Honor user preferences for ads, especially if you have a cookie consent system.
4. **Test Thoroughly**: Test your implementation across different devices and browsers.
5. **Monitor Performance**: Keep an eye on performance metrics, as ads can impact page speed.
6. **Follow AdSense Policies**: Ensure your implementation follows Google AdSense policies.

## Troubleshooting

### Ad Not Showing

1. Check browser console for errors
2. Verify your AdSense account is approved and active
3. Ensure the ad slot ID is correct
4. Check if an ad blocker is active

### Script Loading Issues

1. Verify the client ID is correct
2. Check if the script is being injected correctly
3. Look for any CSP (Content Security Policy) issues

### Hydration Errors

1. Ensure all ad components are client-side only
2. Use proper mounting checks before rendering ads
3. Consider using a state to track when the component is mounted

## Conclusion

By following this guide, you should be able to integrate Google AdSense into your Next.js application without encountering the `data-nscript` attribute error or other common issues. The key is to manually inject the AdSense script on the client side and use client components for all ad-related functionality.
