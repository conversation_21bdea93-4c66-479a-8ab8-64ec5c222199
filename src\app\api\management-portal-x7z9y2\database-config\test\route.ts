import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';
import { createClient } from '@supabase/supabase-js';
import { DatabaseConfig, DatabaseTestResult } from '@/lib/types/databaseConfig';

/**
 * API endpoint to test database connections
 */
export async function POST(request: NextRequest) {
  try {
    const config: DatabaseConfig = await request.json();

    // Test Guerrilla connection
    const guerrillaResult = await testGuerrillaConnection(config.guerrillaConfig);

    // Test Supabase connection
    const supabaseResult = await testSupabaseConnection(config.supabaseConfig);

    const result: DatabaseTestResult = {
      success: guerrillaResult.success && supabaseResult.success,
      guerrilla: guerrillaResult,
      supabase: supabaseResult
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error testing database connections:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to test database connections',
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * Test connection to Guerrilla MySQL database
 */
async function testGuerrillaConnection(config: DatabaseConfig['guerrillaConfig']) {
  try {
    // Create a temporary connection
    const connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : undefined
    });

    // Test the connection with a simple query
    await connection.query('SELECT 1 AS connection_test');

    // Close the connection
    await connection.end();

    return {
      success: true,
      message: 'Successfully connected to Guerrilla database'
    };
  } catch (error) {
    console.error('Error testing Guerrilla connection:', error);

    return {
      success: false,
      message: 'Failed to connect to Guerrilla database',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test connection to Supabase
 */
async function testSupabaseConnection(config: DatabaseConfig['supabaseConfig']) {
  try {
    // Create a temporary Supabase client
    const supabase = createClient(
      config.url,
      config.apiKey
    );

    // Test the connection with a simple query
    const { error } = await supabase.from('database_configurations').select('id').limit(1);

    if (error) {
      throw error;
    }

    return {
      success: true,
      message: 'Successfully connected to Supabase'
    };
  } catch (error) {
    console.error('Error testing Supabase connection:', error);

    return {
      success: false,
      message: 'Failed to connect to Supabase',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
