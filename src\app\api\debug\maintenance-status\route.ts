/**
 * Debug API route for checking maintenance mode status
 * 
 * This API provides a way to check the current maintenance mode status
 * without authentication for debugging purposes.
 */
import { NextRequest, NextResponse } from 'next/server';
import { getConfig } from '@/lib/config/configService';

/**
 * GET /api/debug/maintenance-status
 * 
 * Get the current maintenance mode status
 */
export async function GET(request: NextRequest) {
  try {
    const maintenanceMode = await getConfig('maintenanceMode');
    
    return NextResponse.json({
      success: true,
      maintenanceMode,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting maintenance mode status:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
