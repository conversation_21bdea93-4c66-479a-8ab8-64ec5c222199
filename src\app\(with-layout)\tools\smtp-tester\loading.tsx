/**
 * SMTP Tester Loading Component
 * 
 * Loading state for SMTP Tester page
 */

import React from 'react';

export default function SmtpTesterLoading() {
  return (
    <div className="min-h-screen" style={{ backgroundColor: '#fbfaf8' }}>
      {/* Hero Section Skeleton */}
      <div className="bg-gradient-to-br from-[#f3ece8] to-[#fbfaf8] border-b border-[#4a3728]/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-[#66b077] to-[#07880e] flex items-center justify-center shadow-lg animate-pulse">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <div className="animate-pulse">
              <div className="h-12 bg-[#4a3728]/10 rounded-lg mb-4 max-w-md mx-auto"></div>
              <div className="h-6 bg-[#4a3728]/10 rounded-lg mb-6 max-w-lg mx-auto"></div>
              <div className="h-4 bg-[#4a3728]/10 rounded-lg max-w-3xl mx-auto"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Skeleton */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column Skeleton */}
          <div className="lg:col-span-2 space-y-6">
            {/* Test Mode Selector Skeleton */}
            <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-4 animate-pulse">
              <div className="h-6 bg-[#4a3728]/10 rounded mb-4 max-w-xs"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="h-32 bg-[#4a3728]/10 rounded-lg"></div>
                <div className="h-32 bg-[#4a3728]/10 rounded-lg"></div>
              </div>
            </div>

            {/* Form Skeleton */}
            <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-4 animate-pulse">
              <div className="h-6 bg-[#4a3728]/10 rounded mb-4 max-w-xs"></div>
              <div className="space-y-4">
                <div className="h-10 bg-[#4a3728]/10 rounded"></div>
                <div className="h-10 bg-[#4a3728]/10 rounded"></div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="h-10 bg-[#4a3728]/10 rounded"></div>
                  <div className="h-10 bg-[#4a3728]/10 rounded"></div>
                </div>
                <div className="h-10 bg-[#4a3728]/10 rounded"></div>
                <div className="h-10 bg-[#4a3728]/10 rounded"></div>
                <div className="h-10 bg-[#4a3728]/10 rounded"></div>
                <div className="h-10 bg-[#ce601c]/20 rounded"></div>
              </div>
            </div>
          </div>

          {/* Right Column Skeleton */}
          <div className="lg:col-span-1">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-4 animate-pulse">
              <div className="h-6 bg-[#4a3728]/10 rounded mb-4 max-w-xs"></div>
              <div className="space-y-3">
                <div className="h-4 bg-[#4a3728]/10 rounded"></div>
                <div className="h-4 bg-[#4a3728]/10 rounded max-w-3/4"></div>
                <div className="h-4 bg-[#4a3728]/10 rounded max-w-1/2"></div>
              </div>
              <div className="mt-6 space-y-3">
                <div className="h-12 bg-[#4a3728]/10 rounded"></div>
                <div className="h-12 bg-[#4a3728]/10 rounded"></div>
                <div className="h-12 bg-[#4a3728]/10 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* About Section Skeleton */}
      <div className="bg-gradient-to-br from-[#f3ece8] to-[#fbfaf8] border-t border-[#4a3728]/10 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-6 animate-pulse">
            <div className="h-6 bg-[#4a3728]/10 rounded mb-6 max-w-xs"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="h-5 bg-[#4a3728]/10 rounded max-w-xs"></div>
                <div className="h-4 bg-[#4a3728]/10 rounded"></div>
                <div className="h-4 bg-[#4a3728]/10 rounded max-w-3/4"></div>
                <div className="h-4 bg-[#4a3728]/10 rounded max-w-1/2"></div>
              </div>
              <div className="space-y-3">
                <div className="h-5 bg-[#4a3728]/10 rounded max-w-xs"></div>
                <div className="h-4 bg-[#4a3728]/10 rounded"></div>
                <div className="h-4 bg-[#4a3728]/10 rounded max-w-3/4"></div>
                <div className="h-4 bg-[#4a3728]/10 rounded max-w-1/2"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
