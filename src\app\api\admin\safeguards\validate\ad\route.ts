import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '../../../../../../lib/auth';
import productionSafeguards from '../../../../../../lib/config/productionSafeguards';
import { logger } from '../../../../../../lib/logging/dbLogger';

/**
 * POST /api/admin/safeguards/validate/ad
 * Validate ad placement changes
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const placement = body;

    if (!placement) {
      return NextResponse.json({ error: 'Ad placement is required' }, { status: 400 });
    }

    // Validate ad placement change
    const validationResult = await productionSafeguards.validateAdPlacementChange(placement);

    return NextResponse.json(validationResult);
  } catch (error) {
    await logger.error('API_SAFEGUARDS_VALIDATE_AD', `Error validating ad placement: ${error}`);
    return NextResponse.json({ success: false, message: 'Failed to validate ad placement' }, { status: 500 });
  }
}
