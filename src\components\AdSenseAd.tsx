'use client';

import { useEffect, useRef } from 'react';

interface AdSenseAdProps {
  adSlot: string;
  adFormat?: 'auto' | 'fluid' | 'rectangle' | 'horizontal' | 'vertical';
  fullWidthResponsive?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * AdSenseAd Component
 * 
 * A component that renders a Google AdSense advertisement unit.
 * This component should be used after the AdSenseScript has been loaded.
 * 
 * @param adSlot The AdSense ad slot ID
 * @param adFormat The format of the ad (default: 'auto')
 * @param fullWidthResponsive Whether the ad should be responsive (default: true)
 * @param className Additional CSS classes
 * @param style Additional inline styles
 */
export default function AdSenseAd({
  adSlot,
  adFormat = 'auto',
  fullWidthResponsive = true,
  className = '',
  style = {}
}: AdSenseAdProps) {
  const adRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined' || !adRef.current) return;
    
    try {
      // Clear any existing ad
      adRef.current.innerHTML = '';
      
      // Create the ad element
      const adElement = document.createElement('ins');
      adElement.className = 'adsbygoogle';
      adElement.style.display = 'block';
      adElement.dataset.adClient = 'ca-pub-8397529755029714'; // Your publisher ID
      adElement.dataset.adSlot = adSlot;
      adElement.dataset.adFormat = adFormat;
      
      if (fullWidthResponsive) {
        adElement.dataset.fullWidthResponsive = 'true';
      }
      
      // Append the ad to the container
      adRef.current.appendChild(adElement);
      
      // Push the ad to AdSense
      try {
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      } catch (error) {
        console.error('Error pushing ad to AdSense:', error);
      }
    } catch (error) {
      console.error('Error setting up AdSense ad:', error);
    }
    
    // Cleanup function
    return () => {
      if (adRef.current) {
        adRef.current.innerHTML = '';
      }
    };
  }, [adSlot, adFormat, fullWidthResponsive]);
  
  return (
    <div 
      ref={adRef}
      className={`adsense-container ${className}`}
      style={{
        minHeight: '100px',
        display: 'block',
        overflow: 'hidden',
        ...style
      }}
      data-ad-slot={adSlot}
    />
  );
}

// Add TypeScript declaration for window.adsbygoogle
declare global {
  interface Window {
    adsbygoogle: any[];
  }
}
