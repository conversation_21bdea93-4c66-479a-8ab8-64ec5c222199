# Vercel ignore file - ensure security modules are included in deployment

# Include all TypeScript files in src/lib/security
!src/lib/security/**/*.ts

# Include all source files
!src/**/*.ts
!src/**/*.tsx

# Exclude development files
node_modules
.env.local
.env.development
.env.test

# Exclude build artifacts that will be regenerated
.next
out

# Exclude development tools
.vscode
.idea

# Exclude logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Exclude temporary files
.DS_Store
Thumbs.db
