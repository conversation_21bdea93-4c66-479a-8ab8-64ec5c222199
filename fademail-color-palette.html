<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fademail Color Palette</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        
        body {
            padding: 40px;
            background-color: #f5f5f7;
            color: #1f2937;
        }
        
        h1 {
            margin-bottom: 30px;
            color: #1f2937;
        }
        
        h2 {
            margin: 30px 0 15px;
            color: #1f2937;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .color-section {
            margin-bottom: 40px;
        }
        
        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .color-card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            background-color: white;
        }
        
        .color-sample {
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .color-info {
            padding: 15px;
            background-color: white;
        }
        
        .color-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .color-hex {
            font-family: monospace;
            color: #6b7280;
        }
        
        .color-usage {
            margin-top: 8px;
            font-size: 14px;
            color: #4b5563;
        }
        
        .ui-example {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .ui-example h3 {
            margin-bottom: 15px;
            color: #1f2937;
        }
        
        .button-example {
            display: inline-block;
            padding: 12px 20px;
            border-radius: 10px;
            font-weight: 500;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
        }
        
        .email-card {
            border: 1px solid #f3f4f6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            background-color: white;
        }
        
        .email-card.selected {
            background-color: #f0f1fe;
            border-left: 4px solid #6366f1;
        }
        
        .email-sender {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .email-subject {
            font-weight: 500;
            color: #4b5563;
            margin-bottom: 5px;
        }
        
        .email-preview {
            color: #6b7280;
            font-size: 14px;
        }
        
        .unread-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #6366f1;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .logo-example {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background-color: #6366f1;
            border-radius: 8px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .logo-text {
            color: #6366f1;
            font-weight: 600;
            font-size: 20px;
        }
        
        .inbox-viewer {
            display: flex;
            border: 1px solid #f3f4f6;
            border-radius: 10px;
            overflow: hidden;
            height: 200px;
        }
        
        .inbox-section {
            width: 40%;
            background-color: white;
            border-right: 1px solid #f3f4f6;
            padding: 15px;
        }
        
        .viewer-section {
            width: 60%;
            background-color: white;
            padding: 15px;
        }
        
        .inbox-header {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .inbox-icon {
            color: #6366f1;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <h1>Fademail UI Color Palette</h1>
    
    <div class="color-section">
        <h2>Main Background</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #f5f5f7; color: #1f2937;">Background</div>
                <div class="color-info">
                    <div class="color-name">Background</div>
                    <div class="color-hex">#f5f5f7</div>
                    <div class="color-usage">Used for the main application background</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="color-section">
        <h2>Text Colors</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #1f2937;">Primary Text</div>
                <div class="color-info">
                    <div class="color-name">Primary Text</div>
                    <div class="color-hex">#1f2937</div>
                    <div class="color-usage">Used for headings and important text</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #4b5563;">Secondary Text</div>
                <div class="color-info">
                    <div class="color-name">Secondary Text</div>
                    <div class="color-hex">#4b5563</div>
                    <div class="color-usage">Used for body text and descriptions</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #6b7280;">Tertiary Text</div>
                <div class="color-info">
                    <div class="color-name">Tertiary Text</div>
                    <div class="color-hex">#6b7280</div>
                    <div class="color-usage">Used for less important text elements</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #9ca3af;">Light Text</div>
                <div class="color-info">
                    <div class="color-name">Light Text</div>
                    <div class="color-hex">#9ca3af</div>
                    <div class="color-usage">Used for subtle text elements like timestamps</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #ffffff; color: #1f2937;">White Text</div>
                <div class="color-info">
                    <div class="color-name">White Text</div>
                    <div class="color-hex">#ffffff</div>
                    <div class="color-usage">Used for text on dark backgrounds</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="color-section">
        <h2>Button Colors</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #f0f1fe; color: #4338ca;">Button Background</div>
                <div class="color-info">
                    <div class="color-name">Button Background</div>
                    <div class="color-hex">#f0f1fe</div>
                    <div class="color-usage">Used for button backgrounds</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #4338ca;">Button Text</div>
                <div class="color-info">
                    <div class="color-name">Button Text</div>
                    <div class="color-hex">#4338ca</div>
                    <div class="color-usage">Used for text on buttons</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #e2e3fc; color: #4338ca;">Button Border</div>
                <div class="color-info">
                    <div class="color-name">Button Border</div>
                    <div class="color-hex">#e2e3fc</div>
                    <div class="color-usage">Used for button borders</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #4f46e5;">Active Button</div>
                <div class="color-info">
                    <div class="color-name">Active Button</div>
                    <div class="color-hex">#4f46e5</div>
                    <div class="color-usage">Used for active/selected buttons</div>
                </div>
            </div>
        </div>
        
        <div class="ui-example">
            <h3>Button Examples</h3>
            <div class="button-example" style="background-color: #f0f1fe; color: #4338ca; border: 1px solid #e2e3fc;">
                Copy
            </div>
            <div class="button-example" style="background-color: #f0f1fe; color: #4338ca; border: 1px solid #e2e3fc;">
                New Address
            </div>
            <div class="button-example" style="background-color: #4f46e5; color: white;">
                Selected
            </div>
        </div>
    </div>
    
    <div class="color-section">
        <h2>Email Card Background</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #ffffff; color: #1f2937;">Card Background</div>
                <div class="color-info">
                    <div class="color-name">Card Background</div>
                    <div class="color-hex">#ffffff</div>
                    <div class="color-usage">Used for email card backgrounds</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #f3f4f6; color: #1f2937;">Card Border</div>
                <div class="color-info">
                    <div class="color-name">Card Border</div>
                    <div class="color-hex">#f3f4f6</div>
                    <div class="color-usage">Used for email card borders</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #f0f1fe; color: #1f2937;">Selected Email</div>
                <div class="color-info">
                    <div class="color-name">Selected Email Background</div>
                    <div class="color-hex">#f0f1fe</div>
                    <div class="color-usage">Used for selected email backgrounds</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #6366f1;">Selected Email Border</div>
                <div class="color-info">
                    <div class="color-name">Selected Email Border</div>
                    <div class="color-hex">#6366f1</div>
                    <div class="color-usage">Used for selected email left border</div>
                </div>
            </div>
        </div>
        
        <div class="ui-example">
            <h3>Email Card Examples</h3>
            <div class="email-card">
                <div class="email-sender"><span class="unread-indicator"></span>Fademail Team</div>
                <div class="email-subject">Welcome to Fademail!</div>
                <div class="email-preview">Welcome to Fademail, your temporary email service...</div>
            </div>
            <div class="email-card selected">
                <div class="email-sender">Fademail Tips</div>
                <div class="email-subject">Tips for using Fademail</div>
                <div class="email-preview">Here are some tips to get the most out of your Fademail...</div>
            </div>
        </div>
    </div>
    
    <div class="color-section">
        <h2>Logo Colors</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #6366f1;">Logo Color</div>
                <div class="color-info">
                    <div class="color-name">Logo Color</div>
                    <div class="color-hex">#6366f1</div>
                    <div class="color-usage">Used for the logo icon and text</div>
                </div>
            </div>
        </div>
        
        <div class="ui-example">
            <h3>Logo Example</h3>
            <div class="logo-example">
                <div class="logo-icon">F</div>
                <div class="logo-text">Fademail</div>
            </div>
        </div>
    </div>
    
    <div class="color-section">
        <h2>Inbox/Email Viewer Border</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #f3f4f6; color: #1f2937;">Border Color</div>
                <div class="color-info">
                    <div class="color-name">Border Color</div>
                    <div class="color-hex">#f3f4f6</div>
                    <div class="color-usage">Used for borders between inbox and email viewer</div>
                </div>
            </div>
        </div>
        
        <div class="ui-example">
            <h3>Inbox/Viewer Example</h3>
            <div class="inbox-viewer">
                <div class="inbox-section">
                    <div class="inbox-header">
                        <span class="inbox-icon">📥</span> Inbox
                    </div>
                    <div style="font-size: 12px; color: #6b7280;">Select an email to view</div>
                </div>
                <div class="viewer-section">
                    <div style="color: #6b7280; text-align: center; margin-top: 40px;">
                        No email selected
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="color-section">
        <h2>Additional UI Elements</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #6366f1;">Unread Indicator</div>
                <div class="color-info">
                    <div class="color-name">Unread Indicator</div>
                    <div class="color-hex">#6366f1</div>
                    <div class="color-usage">Used for unread email indicators</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #10b981;">Countdown Timer</div>
                <div class="color-info">
                    <div class="color-name">Countdown Timer</div>
                    <div class="color-hex">#10b981</div>
                    <div class="color-usage">Used for normal countdown timer</div>
                </div>
            </div>
            
            <div class="color-card">
                <div class="color-sample" style="background-color: #ef4444;">Expiring Timer</div>
                <div class="color-info">
                    <div class="color-name">Expiring Timer</div>
                    <div class="color-hex">#ef4444</div>
                    <div class="color-usage">Used for expiring countdown timer</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
