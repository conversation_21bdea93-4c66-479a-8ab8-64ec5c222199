/**
 * API route for database maintenance
 * 
 * This API provides functionality to perform database maintenance operations
 * to optimize performance after cleanup operations.
 */
import { NextRequest, NextResponse } from 'next/server';
import { logInfo, logError } from '@/lib/logging';
import { performMaintenance } from '@/lib/maintenance/dbMaintenance';

/**
 * GET /api/management-portal-x7z9y2/maintenance
 * 
 * Perform database maintenance
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    logInfo('maintenance', 'Starting database maintenance');
    
    // Perform maintenance
    const result = await performMaintenance();
    
    logInfo('maintenance', 'Database maintenance completed', {
      guerrillaOptimized: result.guerrillaOptimized,
      supabaseVacuumed: result.supabaseVacuumed,
      duration: result.duration
    });
    
    return NextResponse.json(result);
  } catch (error) {
    const duration = Date.now() - startTime;
    
    logError('maintenance', 'Error during database maintenance', { error, duration });
    
    return NextResponse.json({
      success: false,
      guerrillaOptimized: false,
      supabaseVacuumed: false,
      duration,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
