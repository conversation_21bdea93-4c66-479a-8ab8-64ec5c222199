/**
 * Analytics Data Retention and Cleanup System
 * 
 * Provides automated cleanup functions for analytics data to maintain
 * database performance and comply with data retention policies.
 * 
 * Retention Policies:
 * - Analytics Events: 90 days
 * - Session Data: 30 days for completed sessions
 * - Incomplete Sessions: 24 hours
 * - Cache Data: Automatic TTL-based cleanup
 */

import { createServerSupabaseClient } from '@/lib/supabase';
import { logInfo, logError, logWarn } from '@/lib/logging';
import { analyticsCache, CacheInvalidation } from './cache';

interface CleanupResult {
  success: boolean;
  deletedCount: number;
  error?: string;
  duration: number;
  timestamp: string;
}

interface CleanupSummary {
  totalDeleted: number;
  eventsDeleted: number;
  sessionsDeleted: number;
  incompleteSessionsDeleted: number;
  errors: string[];
  duration: number;
  timestamp: string;
}

/**
 * Analytics Data Cleanup Service
 */
export class AnalyticsCleanup {
  private supabase = createServerSupabaseClient();

  /**
   * Clean up analytics events older than specified days
   */
  async cleanupAnalyticsEvents(retentionDays: number = 90): Promise<CleanupResult> {
    const startTime = Date.now();
    
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      logInfo('AnalyticsCleanup', 'Starting analytics events cleanup', {
        retentionDays,
        cutoffDate: cutoffDate.toISOString()
      });

      const { data, error } = await this.supabase
        .from('analytics_events')
        .delete()
        .lt('timestamp', cutoffDate.toISOString())
        .select('id');

      if (error) {
        logError('AnalyticsCleanup', 'Failed to cleanup analytics events', { error });
        return {
          success: false,
          deletedCount: 0,
          error: error.message,
          duration: Date.now() - startTime,
          timestamp: new Date().toISOString()
        };
      }

      const deletedCount = data?.length || 0;

      logInfo('AnalyticsCleanup', 'Analytics events cleanup completed', {
        deletedCount,
        retentionDays,
        duration: Date.now() - startTime
      });

      return {
        success: true,
        deletedCount,
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logError('AnalyticsCleanup', 'Unexpected error during analytics events cleanup', { error });
      return {
        success: false,
        deletedCount: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Clean up completed session data older than specified days
   */
  async cleanupSessionData(retentionDays: number = 30): Promise<CleanupResult> {
    const startTime = Date.now();
    
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      logInfo('AnalyticsCleanup', 'Starting session data cleanup', {
        retentionDays,
        cutoffDate: cutoffDate.toISOString()
      });

      // Only delete completed sessions (where is_active = false)
      const { data, error } = await this.supabase
        .from('session_analytics')
        .delete()
        .eq('is_active', false)
        .lt('session_start_time', cutoffDate.toISOString())
        .select('session_id');

      if (error) {
        logError('AnalyticsCleanup', 'Failed to cleanup session data', { error });
        return {
          success: false,
          deletedCount: 0,
          error: error.message,
          duration: Date.now() - startTime,
          timestamp: new Date().toISOString()
        };
      }

      const deletedCount = data?.length || 0;

      logInfo('AnalyticsCleanup', 'Session data cleanup completed', {
        deletedCount,
        retentionDays,
        duration: Date.now() - startTime
      });

      return {
        success: true,
        deletedCount,
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logError('AnalyticsCleanup', 'Unexpected error during session data cleanup', { error });
      return {
        success: false,
        deletedCount: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Clean up incomplete sessions older than specified hours
   */
  async cleanupIncompleteSessions(retentionHours: number = 24): Promise<CleanupResult> {
    const startTime = Date.now();
    
    try {
      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - retentionHours);

      logInfo('AnalyticsCleanup', 'Starting incomplete sessions cleanup', {
        retentionHours,
        cutoffDate: cutoffDate.toISOString()
      });

      // Delete sessions that are still marked as active but haven't been seen recently
      const { data, error } = await this.supabase
        .from('session_analytics')
        .delete()
        .eq('is_active', true)
        .lt('last_seen_at', cutoffDate.toISOString())
        .select('session_id');

      if (error) {
        logError('AnalyticsCleanup', 'Failed to cleanup incomplete sessions', { error });
        return {
          success: false,
          deletedCount: 0,
          error: error.message,
          duration: Date.now() - startTime,
          timestamp: new Date().toISOString()
        };
      }

      const deletedCount = data?.length || 0;

      logInfo('AnalyticsCleanup', 'Incomplete sessions cleanup completed', {
        deletedCount,
        retentionHours,
        duration: Date.now() - startTime
      });

      return {
        success: true,
        deletedCount,
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logError('AnalyticsCleanup', 'Unexpected error during incomplete sessions cleanup', { error });
      return {
        success: false,
        deletedCount: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Run comprehensive cleanup with default retention policies
   */
  async runFullCleanup(): Promise<CleanupSummary> {
    const startTime = Date.now();
    const errors: string[] = [];
    let totalDeleted = 0;

    logInfo('AnalyticsCleanup', 'Starting full analytics cleanup');

    // Clean up analytics events (90 days)
    const eventsResult = await this.cleanupAnalyticsEvents(90);
    if (!eventsResult.success && eventsResult.error) {
      errors.push(`Events cleanup: ${eventsResult.error}`);
    }
    totalDeleted += eventsResult.deletedCount;

    // Clean up session data (30 days)
    const sessionsResult = await this.cleanupSessionData(30);
    if (!sessionsResult.success && sessionsResult.error) {
      errors.push(`Sessions cleanup: ${sessionsResult.error}`);
    }
    totalDeleted += sessionsResult.deletedCount;

    // Clean up incomplete sessions (24 hours)
    const incompleteResult = await this.cleanupIncompleteSessions(24);
    if (!incompleteResult.success && incompleteResult.error) {
      errors.push(`Incomplete sessions cleanup: ${incompleteResult.error}`);
    }
    totalDeleted += incompleteResult.deletedCount;

    // Invalidate cache after cleanup
    if (totalDeleted > 0) {
      CacheInvalidation.invalidateAll();
      logInfo('AnalyticsCleanup', 'Cache invalidated after cleanup');
    }

    const duration = Date.now() - startTime;
    const summary: CleanupSummary = {
      totalDeleted,
      eventsDeleted: eventsResult.deletedCount,
      sessionsDeleted: sessionsResult.deletedCount,
      incompleteSessionsDeleted: incompleteResult.deletedCount,
      errors,
      duration,
      timestamp: new Date().toISOString()
    };

    if (errors.length > 0) {
      logWarn('AnalyticsCleanup', 'Full cleanup completed with errors', summary);
    } else {
      logInfo('AnalyticsCleanup', 'Full cleanup completed successfully', summary);
    }

    return summary;
  }

  /**
   * Get cleanup statistics and recommendations
   */
  async getCleanupStats(): Promise<{
    eventsCount: number;
    sessionsCount: number;
    incompleteSessionsCount: number;
    oldestEvent: string | null;
    oldestSession: string | null;
    recommendations: string[];
  }> {
    try {
      // Get analytics events count and oldest
      const { data: eventsData } = await this.supabase
        .from('analytics_events')
        .select('timestamp')
        .order('timestamp', { ascending: true })
        .limit(1);

      const { count: eventsCount } = await this.supabase
        .from('analytics_events')
        .select('*', { count: 'exact', head: true });

      // Get sessions count and oldest
      const { data: sessionsData } = await this.supabase
        .from('session_analytics')
        .select('session_start_time')
        .eq('is_active', false)
        .order('session_start_time', { ascending: true })
        .limit(1);

      const { count: sessionsCount } = await this.supabase
        .from('session_analytics')
        .select('*', { count: 'exact', head: true });

      // Get incomplete sessions count
      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - 24);

      const { count: incompleteSessionsCount } = await this.supabase
        .from('session_analytics')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)
        .lt('last_seen_at', cutoffDate.toISOString());

      // Generate recommendations
      const recommendations: string[] = [];
      
      if ((eventsCount || 0) > 100000) {
        recommendations.push('High number of analytics events detected. Consider running cleanup.');
      }
      
      if ((incompleteSessionsCount || 0) > 100) {
        recommendations.push('Many incomplete sessions detected. Consider cleanup or session timeout adjustment.');
      }

      const oldestEventDate = eventsData?.[0]?.timestamp;
      if (oldestEventDate) {
        const daysSinceOldest = Math.floor((Date.now() - new Date(oldestEventDate).getTime()) / (1000 * 60 * 60 * 24));
        if (daysSinceOldest > 90) {
          recommendations.push(`Oldest event is ${daysSinceOldest} days old. Consider running events cleanup.`);
        }
      }

      return {
        eventsCount: eventsCount || 0,
        sessionsCount: sessionsCount || 0,
        incompleteSessionsCount: incompleteSessionsCount || 0,
        oldestEvent: oldestEventDate || null,
        oldestSession: sessionsData?.[0]?.session_start_time || null,
        recommendations
      };

    } catch (error) {
      logError('AnalyticsCleanup', 'Failed to get cleanup stats', { error });
      throw error;
    }
  }
}

// Export singleton instance
export const analyticsCleanup = new AnalyticsCleanup();

// Export cleanup configuration
export const CLEANUP_CONFIG = {
  EVENTS_RETENTION_DAYS: 90,
  SESSIONS_RETENTION_DAYS: 30,
  INCOMPLETE_SESSIONS_RETENTION_HOURS: 24,
  CLEANUP_SCHEDULE: '0 2 * * *', // Daily at 2 AM
} as const;
