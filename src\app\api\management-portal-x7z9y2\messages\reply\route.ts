import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { logInfo, logError } from '@/lib/logging';
import { sendContactReplyNotification } from '@/lib/services/emailService';
import crypto from 'crypto';

/**
 * API route for replying to a contact message
 */
export async function POST(request: NextRequest) {
  try {
    // Log the raw request body for debugging
    const requestBody = await request.text();
    console.log('Raw request body:', requestBody);

    // Parse the JSON manually to avoid potential parsing errors
    let parentId, threadId, message;
    try {
      const parsedBody = JSON.parse(requestBody);
      parentId = parsedBody.parentId;
      threadId = parsedBody.threadId;
      message = parsedBody.message;

      console.log('Parsed request body:', { parentId, threadId, message });
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return NextResponse.json({
        success: false,
        message: 'Invalid JSON in request body'
      }, { status: 400 });
    }

    // Validate input
    if (!parentId) {
      console.error('Missing parentId in request');
      return NextResponse.json({
        success: false,
        message: 'Missing parentId field'
      }, { status: 400 });
    }

    if (!message) {
      console.error('Missing message in request');
      return NextResponse.json({
        success: false,
        message: 'Missing message field'
      }, { status: 400 });
    }

    // threadId can be null for new threads

    // Get Supabase client
    const supabase = await createServerSupabaseClient();

    // First, get the parent message to get recipient details
    const { data: parentMessage, error: parentError } = await supabase
      .from('contact_messages')
      .select('*')
      .eq('id', parentId)
      .single();

    if (parentError || !parentMessage) {
      logError('messages', 'Failed to find parent message', { parentId, error: parentError });
      return NextResponse.json({
        success: false,
        message: 'Parent message not found'
      }, { status: 404 });
    }

    // Generate a thread ID if one doesn't exist
    const finalThreadId = threadId || parentMessage.thread_id || crypto.randomUUID();

    console.log('Using thread ID:', finalThreadId);

    // Prepare the reply data
    const replyPayload = {
      name: 'VanishPost Support',
      email: '<EMAIL>',
      subject: `Re: ${parentMessage.subject}`,
      message,
      status: 'read', // Admin replies are marked as read by default
      thread_id: finalThreadId,
      parent_id: parentId,
      is_admin_reply: true
    };

    console.log('Reply payload:', replyPayload);

    // Insert admin reply
    const { data: replyData, error: replyError } = await supabase
      .from('contact_messages')
      .insert([replyPayload])
      .select();

    // Log the error details if any
    if (replyError) {
      console.error('Reply insertion error details:', replyError);
    }

    if (replyError) {
      logError('messages', 'Failed to save reply', { error: replyError });
      return NextResponse.json({
        success: false,
        message: 'Failed to save reply'
      }, { status: 500 });
    }

    // Update parent message status to 'replied'
    const { error: updateError } = await supabase
      .from('contact_messages')
      .update({
        status: 'replied',
        updated_at: new Date().toISOString()
      })
      .eq('id', parentId);

    if (updateError) {
      logError('messages', 'Failed to update parent message status', { error: updateError });
      // Don't return an error here, as the reply was successfully created
    }

    logInfo('messages', 'Admin reply saved to database', {
      parentId,
      replyId: replyData?.[0]?.id,
      recipientEmail: parentMessage.email
    });

    // Send email notification to the user
    try {
      const emailResult = await sendContactReplyNotification(
        parentMessage.email,
        parentMessage.name,
        parentMessage.subject,
        parentMessage.message,
        message
      );

      if (emailResult.success) {
        logInfo('messages', 'Email notification sent successfully', {
          recipientEmail: parentMessage.email,
          messageId: replyData?.[0]?.id
        });
      } else {
        logError('messages', 'Failed to send email notification', {
          error: emailResult.message,
          recipientEmail: parentMessage.email
        });
      }

      return NextResponse.json({
        success: true,
        message: 'Reply sent successfully',
        emailSent: emailResult.success,
        emailStatus: emailResult.message
      });
    } catch (emailError) {
      logError('messages', 'Error sending email notification', {
        error: emailError,
        recipientEmail: parentMessage.email
      });

      // Still return success since the reply was saved to the database
      return NextResponse.json({
        success: true,
        message: 'Reply saved but email notification failed',
        emailSent: false,
        emailStatus: emailError instanceof Error ? emailError.message : 'Unknown error'
      });
    }
  } catch (error) {
    logError('messages', 'Error sending reply', { error });
    return NextResponse.json({
      success: false,
      message: 'An error occurred while sending the reply'
    }, { status: 500 });
  }
}
