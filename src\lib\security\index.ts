/**
 * Security module exports
 * Provides explicit exports for deployment compatibility
 */

// Export all functions from credentialManager
export {
  verifyAdminLogin,
  getAdminCredentials,
  storeSecureCredential,
  getSecureCredential,
  encryptCredential,
  verifyCredential,
  migrateCredentialsToSecureStorage,
  initializeSecureCredentials
} from './credentialManager';

// Export types from credentialManager
export type {
  SecureCredential,
  AdminCredentials
} from './credentialManager';

// Export all functions from databaseCredentials
export * from './databaseCredentials';

// Export all functions from ipBlocking (moved to lib root)
export {
  blockIP,
  unblockIP,
  whitelistIP,
  removeFromWhitelist,
  isIPBlocked,
  isIPWhitelisted,
  recordRateLimitViolation
} from '../ipBlocking';

// Export types from ipBlocking
export type {
  BlockedIP,
  IPWhitelist,
  RateLimitViolation
} from '../ipBlocking';
