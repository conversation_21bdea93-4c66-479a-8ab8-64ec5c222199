'use client';

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Define badge variants using class-variance-authority with earth-tone styling
const badgeVariants = cva(
  // Base styles applied to all badges
  "inline-flex items-center rounded-md px-2 py-1 text-xs font-medium shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-[#4a3728]/10 text-[#4a3728] hover:bg-[#4a3728]/20",
        secondary: "border-transparent bg-[#956b50]/10 text-[#956b50] hover:bg-[#956b50]/20",
        destructive: "border-transparent bg-red-100 text-red-800 hover:bg-red-200",
        outline: "text-[#4a3728] border border-[#4a3728]/20",
        success: "border-transparent bg-green-100 text-green-800 hover:bg-green-200",
        warning: "border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
        info: "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200",
        gray: "border-transparent bg-[#f3ece8] text-[#4a3728] hover:bg-[#e6d5cc]",
        online: "border-transparent bg-green-100 text-green-800",
        offline: "border-transparent bg-gray-100 text-gray-800",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2 py-1 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  children: React.ReactNode;
  icon?: React.ReactNode;
  outline?: boolean; // Support for outline prop
}

/**
 * Badge Component
 * 
 * A small status descriptor for UI elements.
 */
const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant, size, icon, outline, children, ...props }, ref) => {
    // If outline is true, use the outline variant
    const finalVariant = outline ? 'outline' : variant;

    return (
      <div
        ref={ref}
        className={cn(badgeVariants({ variant: finalVariant, size }), className)}
        {...props}
      >
        {icon && (
          <span className={cn("flex items-center", children && "mr-1")}>
            {icon}
          </span>
        )}
        {children}
      </div>
    );
  }
);

Badge.displayName = "Badge";

export { Badge, badgeVariants };
