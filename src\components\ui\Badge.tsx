'use client';

import React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

/**
 * Enhanced Badge variants combining shadcn/ui features with VanishPost styling
 *
 * Maintains all existing VanishPost variants while adding shadcn/ui enhancements
 */
const badgeVariants = cva(
  // Enhanced base styles with shadcn/ui features
  "inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",
  {
    variants: {
      variant: {
        // shadcn/ui standard variants adapted for VanishPost
        default: "border-transparent bg-blue-600 text-white hover:bg-blue-700",
        secondary: "border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200",
        destructive: "border-transparent bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500/20",
        outline: "text-gray-900 border-gray-200 hover:bg-gray-100 hover:text-gray-900",

        // VanishPost-specific variants (maintained for backward compatibility)
        success: "border-transparent bg-green-100 text-green-800 hover:bg-green-200",
        warning: "border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
        info: "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200",
        gray: "border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200",
        online: "border-transparent bg-green-100 text-green-800",
        offline: "border-transparent bg-gray-100 text-gray-800",

        // VanishPost earth-tone variants
        earth: "border-transparent bg-[#4a3728]/10 text-[#4a3728] hover:bg-[#4a3728]/20",
        earthSecondary: "border-transparent bg-[#956b50]/10 text-[#956b50] hover:bg-[#956b50]/20",
        earthGray: "border-transparent bg-[#f3ece8] text-[#4a3728] hover:bg-[#e6d5cc]",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-2 py-1 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

/**
 * Enhanced Badge Props with shadcn/ui features
 */
export interface BadgeProps
  extends React.ComponentProps<"span">,
    VariantProps<typeof badgeVariants> {
  children: React.ReactNode;
  icon?: React.ReactNode;
  outline?: boolean; // Support for outline prop (backward compatibility)
  asChild?: boolean; // shadcn/ui feature for composition
}

/**
 * Enhanced Badge Component
 *
 * Combines shadcn/ui features with VanishPost styling and backward compatibility.
 * Supports icons, composition via asChild, and all existing VanishPost variants.
 */
const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant, size, icon, outline, asChild = false, children, ...props }, ref) => {
    // If outline is true, use the outline variant (backward compatibility)
    const finalVariant = outline ? 'outline' : variant;

    // Use Slot for composition when asChild is true
    const Comp = asChild ? Slot : "span";

    return (
      <Comp
        ref={ref}
        data-slot="badge"
        className={cn(badgeVariants({ variant: finalVariant, size }), className)}
        {...props}
      >
        {icon && (
          <span className={cn("flex items-center", children && "mr-1")}>
            {icon}
          </span>
        )}
        {children}
      </Comp>
    );
  }
);

Badge.displayName = "Badge";

/**
 * Enhanced Badge variants for VanishPost admin use cases
 */

// Status Badge with predefined colors for common admin statuses
interface StatusBadgeProps extends Omit<BadgeProps, 'variant'> {
  status: 'active' | 'inactive' | 'pending' | 'error' | 'success' | 'warning';
}

export function StatusBadge({ status, className, ...props }: StatusBadgeProps) {
  const getVariant = () => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'gray';
      case 'pending':
        return 'warning';
      case 'error':
        return 'destructive';
      case 'success':
        return 'success';
      case 'warning':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getIcon = () => {
    switch (status) {
      case 'active':
        return (
          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'inactive':
        return (
          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case 'pending':
        return (
          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <Badge
      variant={getVariant()}
      icon={getIcon()}
      className={className}
      {...props}
    />
  );
}

// Count Badge for showing numbers (like message counts)
interface CountBadgeProps extends Omit<BadgeProps, 'children'> {
  count: number;
  max?: number;
  showZero?: boolean;
}

export function CountBadge({ count, max = 99, showZero = false, className, ...props }: CountBadgeProps) {
  if (count === 0 && !showZero) return null;

  const displayCount = count > max ? `${max}+` : count.toString();

  return (
    <Badge
      variant="destructive"
      size="sm"
      className={cn("min-w-[1.25rem] h-5 px-1", className)}
      {...props}
    >
      {displayCount}
    </Badge>
  );
}

// Dot Badge for simple status indicators
interface DotBadgeProps extends Omit<BadgeProps, 'children'> {
  color?: 'green' | 'red' | 'yellow' | 'blue' | 'gray';
}

export function DotBadge({ color = 'gray', className, ...props }: DotBadgeProps) {
  const getColorClasses = () => {
    switch (color) {
      case 'green':
        return 'bg-green-500';
      case 'red':
        return 'bg-red-500';
      case 'yellow':
        return 'bg-yellow-500';
      case 'blue':
        return 'bg-blue-500';
      default:
        return 'bg-gray-400';
    }
  };

  return (
    <span
      className={cn(
        "inline-block w-2 h-2 rounded-full",
        getColorClasses(),
        className
      )}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
