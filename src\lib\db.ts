import mysql from 'mysql2/promise';
import { TempEmail, GuerrillaEmail } from './types';
import { TIMEOUTS, DATABASE, API } from './constants';
import { logInfo, logError, logDbQuery, logDbError } from './logging';

// Custom error classes for better error handling
export class DatabaseConnectionError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'DatabaseConnectionError';
  }
}

export class DatabaseQueryError extends Error {
  constructor(message: string, public query?: string, public originalError?: Error) {
    super(message);
    this.name = 'DatabaseQueryError';
  }
}

export class EmailNotFoundError extends Error {
  constructor(emailAddress: string) {
    super(`Email address not found: ${emailAddress}`);
    this.name = 'EmailNotFoundError';
  }
}

// Local database configuration (for temporary email management)
const localDbConfig = {
  host: process.env.DB_HOST || DATABASE.DEFAULT_LOCAL_HOST,
  user: process.env.DB_USER || DATABASE.DEFAULT_LOCAL_USER,
  password: process.env.DB_PASSWORD || 'yiWTpFChtFW3neWCS',
  database: process.env.DB_NAME || DATABASE.DEFAULT_LOCAL_DB_NAME,
  port: DATABASE.DEFAULT_PORT,
  ssl: DATABASE.DEFAULT_SSL, // Using undefined instead of false to match the expected type
  connectTimeout: TIMEOUTS.CONNECTION_TIMEOUT,
  waitForConnections: true,
};

// Guerrilla database configuration (on droplet)
const guerrillaDbConfig = {
  host: process.env.GUERRILLA_DB_HOST || DATABASE.DEFAULT_GUERRILLA_HOST,
  user: process.env.GUERRILLA_DB_USER || DATABASE.DEFAULT_GUERRILLA_USER,
  password: process.env.GUERRILLA_DB_PASSWORD || '',
  database: process.env.GUERRILLA_DB_NAME || DATABASE.DEFAULT_GUERRILLA_DB_NAME,
  port: parseInt(process.env.GUERRILLA_DB_PORT || DATABASE.DEFAULT_PORT.toString(), 10),
  ssl: DATABASE.DEFAULT_SSL, // Using undefined instead of false to match the expected type
  connectTimeout: TIMEOUTS.CONNECTION_TIMEOUT,
  waitForConnections: true,
};

// Create connection pools for better performance
const localPool = mysql.createPool({
  host: localDbConfig.host,
  port: localDbConfig.port,
  user: localDbConfig.user,
  password: localDbConfig.password,
  database: localDbConfig.database,
  ssl: localDbConfig.ssl,
  connectTimeout: localDbConfig.connectTimeout,
  waitForConnections: localDbConfig.waitForConnections,
  connectionLimit: DATABASE.DEFAULT_CONNECTION_LIMIT,
  queueLimit: DATABASE.DEFAULT_QUEUE_LIMIT,
  enableKeepAlive: true, // Keep connections alive
  keepAliveInitialDelay: 10000, // 10 seconds
});

// Create a connection pool for the guerrilla database with proper handling of special characters
const guerrillaPool = mysql.createPool({
  host: guerrillaDbConfig.host,
  port: guerrillaDbConfig.port,
  user: guerrillaDbConfig.user,
  password: guerrillaDbConfig.password,
  database: guerrillaDbConfig.database,
  ssl: guerrillaDbConfig.ssl,
  connectTimeout: guerrillaDbConfig.connectTimeout,
  waitForConnections: guerrillaDbConfig.waitForConnections,
  connectionLimit: DATABASE.DEFAULT_CONNECTION_LIMIT,
  queueLimit: DATABASE.DEFAULT_QUEUE_LIMIT,
  enableKeepAlive: true, // Keep connections alive
  keepAliveInitialDelay: 10000, // 10 seconds
});

/**
 * Get a connection from the local database pool
 */
export async function getLocalDbConnection() {
  try {
    logInfo('DB', 'Connecting to local database');
    return await localPool.getConnection();
  } catch (error) {
    logDbError('connecting to local database', error);
    throw new DatabaseConnectionError(
      'Failed to connect to local database',
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * Get a connection from the guerrilla database pool
 */
export async function getGuerrillaDbConnection() {
  try {
    logInfo('DB', 'Attempting to connect to guerrilla database...');

    // Log the full configuration for debugging
    logInfo('DB', 'Guerrilla DB Config:', {
      host: guerrillaDbConfig.host,
      port: guerrillaDbConfig.port,
      user: guerrillaDbConfig.user,
      database: guerrillaDbConfig.database,
      // Don't log the password for security reasons
      passwordLength: guerrillaDbConfig.password ? guerrillaDbConfig.password.length : 0,
      connectTimeout: guerrillaDbConfig.connectTimeout,
      ssl: guerrillaDbConfig.ssl ? 'configured' : 'not configured'
    });

    // Check if environment variables are set
    const envHost = process.env.GUERRILLA_DB_HOST;
    const envUser = process.env.GUERRILLA_DB_USER;
    const envPassword = process.env.GUERRILLA_DB_PASSWORD;
    const envDatabase = process.env.GUERRILLA_DB_NAME;
    const envPort = process.env.GUERRILLA_DB_PORT;

    logInfo('DB', 'Environment variables:', {
      GUERRILLA_DB_HOST: envHost ? 'set' : 'not set',
      GUERRILLA_DB_USER: envUser ? 'set' : 'not set',
      GUERRILLA_DB_PASSWORD: envPassword ? 'set (length: ' + envPassword.length + ')' : 'not set',
      GUERRILLA_DB_NAME: envDatabase ? 'set' : 'not set',
      GUERRILLA_DB_PORT: envPort ? 'set' : 'not set'
    });

    // Try to get a connection with a timeout
    const connectionPromise = guerrillaPool.getConnection();
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Connection timeout')), guerrillaDbConfig.connectTimeout);
    });

    const connection = await Promise.race([connectionPromise, timeoutPromise]) as mysql.PoolConnection;
    logInfo('DB', 'Successfully connected to guerrilla database');
    return connection;
  } catch (error) {
    logDbError('connecting to guerrilla database', error);

    const errorMessage = `Failed to connect to guerrilla database: ${error instanceof Error ? error.message : 'Unknown error'}`;
    throw new DatabaseConnectionError(
      errorMessage,
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * Create a temporary email in the local database
 */
export async function createTempEmail(emailAddress: string, expirationDate: Date): Promise<TempEmail> {
  const connection = await getLocalDbConnection();
  try {
    const [result] = await connection.execute(
      'INSERT INTO TempEmail (emailAddress, expirationDate) VALUES (?, ?)',
      [emailAddress, expirationDate]
    );

    const insertId = (result as mysql.ResultSetHeader).insertId;

    const [rows] = await connection.execute(
      'SELECT * FROM TempEmail WHERE id = ?',
      [insertId]
    );

    return (rows as TempEmail[])[0];
  } finally {
    connection.release();
  }
}

/**
 * Get a temporary email from the local database by email address
 */
export async function getTempEmailByAddress(emailAddress: string): Promise<TempEmail | null> {
  let connection;
  try {
    connection = await getLocalDbConnection();

    try {
      const query = 'SELECT * FROM TempEmail WHERE emailAddress = ?';
      logDbQuery(query, [emailAddress]);

      const [rows] = await connection.execute(query, [emailAddress]);

      const tempEmails = rows as TempEmail[];
      return tempEmails.length > 0 ? tempEmails[0] : null;
    } catch (queryError) {
      logDbError('querying temp email', queryError);
      throw new DatabaseQueryError(
        `Failed to query temp email for address ${emailAddress}`,
        'SELECT * FROM TempEmail WHERE emailAddress = ?',
        queryError instanceof Error ? queryError : new Error(String(queryError))
      );
    }
  } catch (error) {
    // If it's already one of our custom errors, just rethrow it
    if (error instanceof DatabaseConnectionError || error instanceof DatabaseQueryError) {
      throw error;
    }

    // Otherwise wrap it in a more specific error
    logDbError('getTempEmailByAddress', error);
    throw new Error(`Failed to retrieve temp email for ${emailAddress}: ${error instanceof Error ? error.message : String(error)}`);
  } finally {
    if (connection) connection.release();
  }
}

/**
 * Get emails for a specific recipient from the guerrilla database
 */
export async function getEmailsForRecipient(
  recipient: string,
  page: number = 1,
  pageSize: number = API.DEFAULT_PAGE_SIZE,
  sortBy: string = API.DEFAULT_SORT_BY,
  sortOrder: string = API.DEFAULT_SORT_ORDER
): Promise<{ emails: GuerrillaEmail[], totalCount: number }> {
  let connection;
  try {
    logInfo('DB', `Getting emails for recipient: ${recipient}`);

    try {
      connection = await getGuerrillaDbConnection();
    } catch (connectionError) {
      // Handle connection errors gracefully
      logDbError('connecting to guerrilla database in getEmailsForRecipient', connectionError);

      // Return empty results instead of throwing an error
      // This allows the application to continue functioning even if the database is temporarily unavailable
      logInfo('DB', 'Returning empty results due to connection error');
      return {
        emails: [],
        totalCount: 0
      };
    }

    // Validate and sanitize sort parameters to prevent SQL injection
    const validSortColumns = API.VALID_SORT_COLUMNS;
    const validSortOrders = API.VALID_SORT_ORDERS;

    // Type assertion to handle the string type
    const actualSortBy = validSortColumns.includes(sortBy as any) ? sortBy : API.DEFAULT_SORT_BY;
    const actualSortOrder = validSortOrders.includes(sortOrder as any) ? sortOrder : API.DEFAULT_SORT_ORDER;

    // Calculate offset for pagination
    const offset = (page - 1) * pageSize;

    try {
      // Get total count - using exact match for recipient
      logInfo('DB', 'Executing count query');
      const [countResult] = await connection.execute(
        'SELECT COUNT(*) as count FROM guerrilla_mail WHERE recipient = ?',
        [recipient]
      );
      const totalCount = (countResult as any)[0].count;
      logInfo('DB', `Total count: ${totalCount}`);

      // Get emails with pagination and sorting
      // Using a different approach to avoid prepared statement issues
      // Using exact match for recipient to ensure we only get emails for this specific address
      const escapedRecipient = connection.escape(recipient);
      const query = `SELECT * FROM guerrilla_mail WHERE recipient = ${escapedRecipient}
         ORDER BY ${actualSortBy} ${actualSortOrder}
         LIMIT ${parseInt(pageSize.toString(), 10)} OFFSET ${parseInt(offset.toString(), 10)}`;

      logDbQuery(query);

      try {
        logInfo('DB', 'Executing email query');
        const [rows] = await connection.query(query);

        logInfo('DB', `Emails found: ${(rows as GuerrillaEmail[]).length}`);

        return {
          emails: rows as GuerrillaEmail[],
          totalCount
        };
      } catch (queryError) {
        logDbError('executing email query', queryError);

        // Return empty results instead of throwing an error
        logInfo('DB', 'Returning empty results due to query error');
        return {
          emails: [],
          totalCount: 0
        };
      }
    } catch (countError) {
      logDbError('getting email count', countError);

      // Return empty results instead of throwing an error
      logInfo('DB', 'Returning empty results due to count error');
      return {
        emails: [],
        totalCount: 0
      };
    }
  } catch (error) {
    // Log the error but don't throw it
    logDbError('getEmailsForRecipient', error);

    // Return empty results instead of throwing an error
    logInfo('DB', 'Returning empty results due to unexpected error');
    return {
      emails: [],
      totalCount: 0
    };
  } finally {
    if (connection) {
      try {
        connection.release();
      } catch (releaseError) {
        logDbError('releasing connection', releaseError);
      }
    }
  }
}

/**
 * Delete expired temporary emails from the local database
 */
export async function deleteExpiredEmails(): Promise<number> {
  const connection = await getLocalDbConnection();
  try {
    const [result] = await connection.execute(
      'DELETE FROM TempEmail WHERE expirationDate < NOW()'
    );

    return (result as mysql.ResultSetHeader).affectedRows;
  } finally {
    connection.release();
  }
}

/**
 * Delete a specific email from the guerrilla database
 *
 * @param emailId The ID of the email to delete
 * @param recipient The recipient email address (for verification)
 * @returns True if the email was deleted, false otherwise
 */
export async function deleteEmailFromGuerrilla(emailId: string, recipient: string): Promise<boolean> {
  let connection;
  try {
    connection = await getGuerrillaDbConnection();

    // Validate inputs to prevent SQL injection
    if (!emailId || !recipient) {
      throw new Error('Email ID and recipient are required');
    }

    // Make sure emailId is a number
    const mailId = parseInt(emailId, 10);
    if (isNaN(mailId)) {
      throw new Error('Invalid email ID');
    }

    // First verify that the email exists and belongs to this recipient
    const [rows] = await connection.execute(
      'SELECT mail_id FROM guerrilla_mail WHERE mail_id = ? AND recipient = ?',
      [mailId, recipient]
    );

    const emails = rows as { mail_id: number }[];
    if (emails.length === 0) {
      logInfo('DB', `Email with ID ${mailId} not found for recipient ${recipient}`);
      return false;
    }

    // Delete the email
    const query = 'DELETE FROM guerrilla_mail WHERE mail_id = ? AND recipient = ?';
    logDbQuery(query, [mailId, recipient]);

    const [result] = await connection.execute(query, [mailId, recipient]);

    const affectedRows = (result as mysql.ResultSetHeader).affectedRows;
    logInfo('DB', `Deleted ${affectedRows} email(s) with ID ${mailId} for recipient ${recipient}`);

    return affectedRows > 0;
  } catch (error) {
    logDbError('deleting email from guerrilla', error);
    throw new DatabaseQueryError(
      `Failed to delete email with ID ${emailId} for recipient ${recipient}`,
      'DELETE FROM guerrilla_mail',
      error instanceof Error ? error : new Error(String(error))
    );
  } finally {
    if (connection) connection.release();
  }
}

/**
 * Test the connection to both databases
 */
export async function testDatabaseConnections(): Promise<{ local: boolean, guerrilla: boolean }> {
  let localConnection = null;
  let guerrillaConnection = null;

  try {
    localConnection = await getLocalDbConnection();
    const localConnected = !!localConnection;

    guerrillaConnection = await getGuerrillaDbConnection();
    const guerrillaConnected = !!guerrillaConnection;

    return { local: localConnected, guerrilla: guerrillaConnected };
  } catch (error) {
    console.error('Error testing database connections:', error);
    return {
      local: !!localConnection,
      guerrilla: !!guerrillaConnection
    };
  } finally {
    if (localConnection) localConnection.release();
    if (guerrillaConnection) guerrillaConnection.release();
  }
}
