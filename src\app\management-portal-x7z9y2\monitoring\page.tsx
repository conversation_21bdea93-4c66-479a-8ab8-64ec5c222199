'use client';

import { useState } from 'react';
import RealTimeMonitor from '@/components/admin/RealTimeMonitor';
import AlertThresholds from '@/components/admin/AlertThresholds';
import SystemInfoModal from '@/components/admin/SystemInfoModal';
import { Card, CardHeader, CardContent, CardTitle, CardDescription } from '@/components/ui/Card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  AdminFilterSelect
} from '@/components/ui/Select';
import { Badge, StatusBadge, DotBadge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Spinner } from '@/components/ui/Spinner';
import { PageHeader } from '@/components/ui/PageHeader';
import { SkeletonStat, SkeletonText } from '@/components/ui/Skeleton';

export default function MonitoringPage() {
  const [level, setLevel] = useState<string | undefined>(undefined);
  const [category, setCategory] = useState<string | undefined>(undefined);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSystemInfoModalOpen, setIsSystemInfoModalOpen] = useState(false);
  const [isDownloadingLogs, setIsDownloadingLogs] = useState(false);
  const [systemStatus, setSystemStatus] = useState({
    status: 'healthy',
    uptime: '3d 12h 45m',
    cpuUsage: 23,
    memoryUsage: 45,
    diskUsage: 67,
    activeConnections: 12,
    lastUpdated: new Date().toLocaleTimeString()
  });

  // Log levels
  const logLevels = [
    { value: 'all', label: 'All Levels' },
    { value: 'debug', label: 'Debug' },
    { value: 'info', label: 'Info' },
    { value: 'warning', label: 'Warning' },
    { value: 'error', label: 'Error' },
    { value: 'critical', label: 'Critical' }
  ];

  // Common log categories
  const logCategories = [
    { value: 'all', label: 'All Categories' },
    { value: 'API', label: 'API' },
    { value: 'DATABASE', label: 'Database' },
    { value: 'AUTH', label: 'Authentication' },
    { value: 'EMAIL', label: 'Email' },
    { value: 'CLEANUP', label: 'Cleanup' },
    { value: 'CONFIG', label: 'Configuration' },
    { value: 'MONITOR', label: 'Monitoring' },
    { value: 'ALERT', label: 'Alerts' }
  ];

  // Simulate refreshing system status
  const refreshSystemStatus = () => {
    setIsRefreshing(true);

    // Simulate API call delay
    setTimeout(() => {
      setSystemStatus({
        ...systemStatus,
        cpuUsage: Math.floor(Math.random() * 40) + 10,
        memoryUsage: Math.floor(Math.random() * 30) + 30,
        diskUsage: Math.floor(Math.random() * 20) + 60,
        activeConnections: Math.floor(Math.random() * 20) + 5,
        lastUpdated: new Date().toLocaleTimeString()
      });
      setIsRefreshing(false);
    }, 1200);
  };

  // Get status color based on value
  const getStatusColor = (value: number) => {
    if (value < 50) return 'bg-green-500';
    if (value < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  // Get status indicator
  const getStatusIndicator = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'critical':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
          </svg>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Real-Time Monitoring</h1>
            <p className="mt-1 text-sm text-gray-600">Real-time monitoring and alerting system</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <StatusBadge status="active">Real-time</StatusBadge>
          <span className="text-sm text-gray-500">Last updated: {systemStatus.lastUpdated}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshSystemStatus}
            isLoading={isRefreshing}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
            </svg>
            Refresh All
          </Button>
        </div>
      </div>

      {/* System Status Overview Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                System Status
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-start">
              {isRefreshing ? (
                <div className="space-y-4 flex-1">
                  <div className="flex items-center space-x-2">
                    <div className="h-3 w-3 rounded-full bg-gray-200 animate-pulse"></div>
                    <SkeletonText width="60%" />
                  </div>
                  <SkeletonText width="40%" className="h-3" />
                  <SkeletonText width="50%" className="h-3" />
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <StatusBadge
                      status={systemStatus.status === 'healthy' ? 'active' :
                             systemStatus.status === 'warning' ? 'warning' : 'error'}
                    >
                      {systemStatus.status === 'healthy' ? 'All systems operational' :
                       systemStatus.status === 'warning' ? 'Some systems degraded' : 'Critical issues detected'}
                    </StatusBadge>
                  </div>
                  <div className="text-sm text-gray-500">System uptime: {systemStatus.uptime}</div>
                  <div className="text-sm text-gray-500">Active connections: {systemStatus.activeConnections}</div>
                </div>
              )}
              <Button
                variant="secondary"
                size="sm"
                onClick={refreshSystemStatus}
                isLoading={isRefreshing}
                className="flex-shrink-0"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
                Refresh
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Resource Usage
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">CPU Usage</span>
                  <span className="text-sm font-medium">{systemStatus.cpuUsage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className={`${getStatusColor(systemStatus.cpuUsage)} h-2 rounded-full`} style={{ width: `${systemStatus.cpuUsage}%` }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">Memory Usage</span>
                  <span className="text-sm font-medium">{systemStatus.memoryUsage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className={`${getStatusColor(systemStatus.memoryUsage)} h-2 rounded-full`} style={{ width: `${systemStatus.memoryUsage}%` }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">Disk Usage</span>
                  <span className="text-sm font-medium">{systemStatus.diskUsage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className={`${getStatusColor(systemStatus.diskUsage)} h-2 rounded-full`} style={{ width: `${systemStatus.diskUsage}%` }}></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                </svg>
                Quick Actions
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button
                variant="secondary"
                className="w-full justify-start"
                onClick={async () => {
                  try {
                    setIsRefreshing(true);
                    const response = await fetch('/api/management-portal-x7z9y2/cache/clear', {
                      method: 'POST',
                    });

                    if (response.ok) {
                      const data = await response.json();
                      alert(`Successfully cleared ${data.clearedCaches?.length || 0} cache(s)`);
                    } else {
                      alert('Failed to clear caches');
                    }
                  } catch (error) {
                    console.error('Error clearing caches:', error);
                    alert('Error clearing caches');
                  } finally {
                    setIsRefreshing(false);
                  }
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
                Clear Cache
              </Button>
              <Button
                variant="secondary"
                className="w-full justify-start"
                onClick={() => {
                  try {
                    setIsDownloadingLogs(true);
                    // Create a hidden link to download the logs
                    const downloadLink = document.createElement('a');
                    const levelParam = level && level !== 'all' ? `&level=${level}` : '';
                    const categoryParam = category && category !== 'all' ? `&category=${category}` : '';
                    downloadLink.href = `/api/management-portal-x7z9y2/logs/download?format=csv${levelParam}${categoryParam}`;
                    downloadLink.download = `fademail_logs_${new Date().toISOString().split('T')[0]}.csv`;
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);
                  } catch (error) {
                    console.error('Error downloading logs:', error);
                    alert('Error downloading logs');
                  } finally {
                    // Reset loading state after a short delay
                    setTimeout(() => setIsDownloadingLogs(false), 1000);
                  }
                }}
                disabled={isDownloadingLogs}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                {isDownloadingLogs ? 'Downloading...' : 'Download Logs'}
              </Button>
              <Button
                variant="secondary"
                className="w-full justify-start"
                onClick={() => setIsSystemInfoModalOpen(true)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                View System Info
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Log Filters and Alert Thresholds Section */}
      <div className="flex items-center mt-8 mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
        </svg>
        <h2 className="text-xl font-semibold text-gray-800">Log Configuration</h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
                </svg>
                Log Filters
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Log Level
                </label>
                <AdminFilterSelect
                  value={level || 'all'}
                  onValueChange={(value) => setLevel(value === 'all' ? undefined : value)}
                  placeholder="Select log level"
                  options={logLevels.map(option => {
                    // Add appropriate icons based on log level
                    let icon;
                    switch(option.value) {
                      case 'debug':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M9 3a1 1 0 012 0v5.5a.5.5 0 001 0V4a1 1 0 112 0v4.5a.5.5 0 001 0V6a1 1 0 112 0v5a7 7 0 11-14 0V9a1 1 0 012 0v2.5a.5.5 0 001 0V4a1 1 0 012 0v4.5a.5.5 0 001 0V3z" clipRule="evenodd" /></svg>;
                        break;
                      case 'info':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" /></svg>;
                        break;
                      case 'warning':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" /></svg>;
                        break;
                      case 'error':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" /></svg>;
                        break;
                      case 'critical':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-600" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" /></svg>;
                        break;
                      default:
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>;
                    }
                    return {
                      value: option.value,
                      label: option.label,
                      icon
                    };
                  })}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <AdminFilterSelect
                  value={category || 'all'}
                  onValueChange={(value) => setCategory(value === 'all' ? undefined : value)}
                  placeholder="Select category"
                  options={logCategories.map(option => {
                    // Add appropriate icons based on category
                    let icon;
                    switch(option.value) {
                      case 'API':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" /></svg>;
                        break;
                      case 'DATABASE':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-500" viewBox="0 0 20 20" fill="currentColor"><path d="M3 12v3c0 1.657 3.134 3 7 3s7-1.343 7-3v-3c0 1.657-3.134 3-7 3s-7-1.343-7-3z" /><path d="M3 7v3c0 1.657 3.134 3 7 3s7-1.343 7-3V7c0 1.657-3.134 3-7 3S3 8.657 3 7z" /><path d="M17 5c0 1.657-3.134 3-7 3S3 6.657 3 5s3.134-3 7-3 7 1.343 7 3z" /></svg>;
                        break;
                      case 'AUTH':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" /></svg>;
                        break;
                      case 'EMAIL':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-orange-500" viewBox="0 0 20 20" fill="currentColor"><path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" /><path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" /></svg>;
                        break;
                      case 'CLEANUP':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clipRule="evenodd" /><path fillRule="evenodd" d="M10 5a2 2 0 00-2 2v6a2 2 0 004 0V7a2 2 0 00-2-2zM8 7v6h4V7H8z" clipRule="evenodd" /></svg>;
                        break;
                      case 'CONFIG':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" /></svg>;
                        break;
                      case 'MONITOR':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-500" viewBox="0 0 20 20" fill="currentColor"><path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" /></svg>;
                        break;
                      case 'ALERT':
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" /></svg>;
                        break;
                      default:
                        icon = <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>;
                    }
                    return {
                      value: option.value,
                      label: option.label,
                      icon
                    };
                  })}
                  className="w-full"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <AlertThresholds />
      </div>

      {/* Real-Time Monitor Section */}
      <div className="mt-16 pt-8 border-t border-gray-200">
        <RealTimeMonitor maxLogs={100} level={level} category={category} />
      </div>

      {/* Monitoring Guide Section */}
      <div className="flex items-center mt-8 mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
        </svg>
        <h2 className="text-xl font-semibold text-gray-800">Documentation & Help</h2>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              Monitoring Guide
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-indigo-600 mb-1">Real-Time Monitoring</h3>
              <p className="text-sm text-gray-600">
                This page shows system logs and alerts in real-time.
                Use the filters to focus on specific log levels or categories.
              </p>
            </div>

            <div>
              <h3 className="font-medium text-indigo-600 mb-1">Alert Thresholds</h3>
              <p className="text-sm text-gray-600">
                Configure how many events of a specific category must occur
                within a 15-minute window to trigger an alert. Alerts will appear at the top of the monitor.
              </p>
            </div>

            <div>
              <h3 className="font-medium text-indigo-600 mb-1">Log Levels</h3>
              <ul className="list-disc pl-5 space-y-1 text-sm text-gray-600">
                <li><span className="font-medium">Debug:</span> Detailed information for debugging purposes</li>
                <li><span className="font-medium">Info:</span> Normal system operation information</li>
                <li><span className="font-medium">Warning:</span> Potential issues that don't affect core functionality</li>
                <li><span className="font-medium">Error:</span> Errors that affect specific operations but don't crash the system</li>
                <li><span className="font-medium">Critical:</span> Severe errors that may cause system failure</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Info Modal */}
      <SystemInfoModal
        isOpen={isSystemInfoModalOpen}
        onClose={() => setIsSystemInfoModalOpen(false)}
      />
    </div>
  );
}
