/**
 * <PERSON><PERSON><PERSON> to migrate admin pages from /admin to the new secure path
 * and update any hardcoded references to the old admin path.
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const OLD_ADMIN_PATH = 'src/app/admin';
const NEW_ADMIN_PATH = 'src/app/management-portal-x7z9y2';
const OLD_PATH_PATTERN = /\/admin\//g;
const NEW_PATH_REPLACEMENT = '/management-portal-x7z9y2/';
const OLD_PATH_PATTERN_HREF = /href="\/admin\//g;
const NEW_PATH_REPLACEMENT_HREF = 'href="/management-portal-x7z9y2/';
const OLD_PATH_PATTERN_ROUTE = /route="\/admin\//g;
const NEW_PATH_REPLACEMENT_ROUTE = 'route="/management-portal-x7z9y2/';

// Create the new admin directory if it doesn't exist
if (!fs.existsSync(NEW_ADMIN_PATH)) {
  fs.mkdirSync(NEW_ADMIN_PATH, { recursive: true });
}

// Function to copy a directory recursively
function copyDirectory(source, destination) {
  // Create the destination directory if it doesn't exist
  if (!fs.existsSync(destination)) {
    fs.mkdirSync(destination, { recursive: true });
  }

  // Get all files and directories in the source directory
  const entries = fs.readdirSync(source, { withFileTypes: true });

  // Process each entry
  for (const entry of entries) {
    const sourcePath = path.join(source, entry.name);
    const destinationPath = path.join(destination, entry.name);

    if (entry.isDirectory()) {
      // Recursively copy subdirectories
      copyDirectory(sourcePath, destinationPath);
    } else {
      // Read the file content
      let content = fs.readFileSync(sourcePath, 'utf8');

      // Replace hardcoded references to the old admin path
      content = content.replace(OLD_PATH_PATTERN, NEW_PATH_REPLACEMENT);
      content = content.replace(OLD_PATH_PATTERN_HREF, NEW_PATH_REPLACEMENT_HREF);
      content = content.replace(OLD_PATH_PATTERN_ROUTE, NEW_PATH_REPLACEMENT_ROUTE);

      // Write the updated content to the destination file
      fs.writeFileSync(destinationPath, content);
      console.log(`Copied and updated: ${destinationPath}`);
    }
  }
}

// Copy the admin directory
console.log(`Copying admin pages from ${OLD_ADMIN_PATH} to ${NEW_ADMIN_PATH}...`);
copyDirectory(OLD_ADMIN_PATH, NEW_ADMIN_PATH);

console.log('Migration completed successfully!');
