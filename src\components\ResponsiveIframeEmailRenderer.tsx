import React, { useMemo, useEffect, useState, useRef } from 'react';

// Create a simple iframe component
const SimpleIframe = ({
  html,
  style
}: {
  html: string;
  style?: React.CSSProperties
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    try {
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) return;

      // Write the HTML content safely
      iframeDoc.open();
      iframeDoc.write(html);
      iframeDoc.close();

      // Adjust height after content loads
      const adjustHeight = () => {
        if (!iframe.contentWindow) return;

        const body = iframeDoc.body;
        const html = iframeDoc.documentElement;

        const height = Math.max(
          body.scrollHeight,
          body.offsetHeight,
          html.clientHeight,
          html.scrollHeight,
          html.offsetHeight
        );

        iframe.style.height = `${height}px`;
      };

      // Adjust height initially
      adjustHeight();

      // Add load event listener safely
      if (iframe.contentWindow) {
        iframe.contentWindow.addEventListener('load', adjustHeight);

        // Clean up
        return () => {
          iframe.contentWindow?.removeEventListener('load', adjustHeight);
        };
      }
    } catch (error) {
      console.error('Error rendering email in iframe:', error);
    }
  }, [html]);

  return (
    <iframe
      ref={iframeRef}
      title="Email content"
      style={{
        width: '100%',
        border: 'none',
        overflow: 'hidden', // Prevent scrollbars
        minHeight: '200px',
        ...style
      }}
      // Use scrolling attribute for older browsers, even though it's deprecated
      scrolling="no"
      sandbox="allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-scripts"
    />
  );
};

interface ResponsiveIframeEmailRendererProps {
  /**
   * The HTML content to render in the iframe
   */
  html: string;

  /**
   * Optional className for the iframe container
   */
  className?: string;

  /**
   * Optional array of web fonts to load in the iframe
   * Each font should be a Google Fonts URL or similar
   */
  webFonts?: string[];
}

// Helper function to generate a simple content hash for memoization
function generateContentHash(content: string): string {
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString(36);
}

/**
 * ResponsiveIframeEmailRenderer Component
 *
 * Renders email HTML content in an isolated iframe with responsive scaling for mobile devices.
 * Uses iframe-resizer for dynamic height adjustment and implements Gmail-like scaling for fixed-width emails.
 */
export default function ResponsiveIframeEmailRenderer({
  html,
  className = '',
  webFonts = []
}: ResponsiveIframeEmailRendererProps) {
  // State to track client-side rendering
  const [isClient, setIsClient] = useState(false);

  // Generate a content hash for memoization
  const contentHash = useMemo(() => generateContentHash(html + webFonts.join('')), [html, webFonts]);

  // Create the HTML content with viewport scaling for mobile
  const htmlContent = useMemo(() => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, minimum-scale=1">
          <meta name="format-detection" content="telephone=no">
          ${webFonts.map(font => `<link href="${font}" rel="stylesheet">`).join('')}
          <style>
            /* Reset styles */
            html, body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              line-height: 1.4;
              overflow: hidden; /* Prevent all scrollbars */
              -webkit-text-size-adjust: 100%;
              -webkit-font-smoothing: antialiased;
              height: auto; /* Allow height to adjust naturally */
            }

            /* Mobile-specific wrapper for scaling */
            .email-wrapper {
              max-width: 100%;
              overflow: hidden; /* Prevent all scrollbars */
              position: relative;
              margin: 0;
              padding: 0;
            }

            /* Ensure images don't overflow */
            img {
              max-width: 100%;
              height: auto !important;
            }

            /* Ensure tables don't overflow */
            table {
              max-width: 100%;
            }

            /* Default link styling */
            a:not([style]) {
              color: #0000EE;
              text-decoration: underline;
            }

            /* Gmail-like mobile scaling */
            @media (max-width: 640px) {
              /* Add mobile-specific styles */
              .email-mobile-scale {
                transform-origin: top left;
                transition: transform 0.2s ease-in-out;
              }

              /* Handle fixed-width tables */
              table[width], table[style*="width"] {
                max-width: 100% !important;
              }
            }
          </style>
          <script>
            // This script handles responsive scaling for fixed-width emails
            window.addEventListener('load', function() {
              // Function to handle email scaling
              function handleEmailScaling() {
                var wrapper = document.querySelector('.email-wrapper');
                var content = wrapper.firstElementChild;
                var viewportWidth = window.innerWidth;

                // Find all fixed-width elements (enhanced selector)
                var fixedWidthElements = document.querySelectorAll(
                  'table[width], table[style*="width"], div[style*="width"], td[width], ' +
                  'img[width], center > table, .container, [class*="container"], ' +
                  '[style*="margin-left: auto"][style*="margin-right: auto"]'
                );
                var maxContentWidth = 0;

                // Find the maximum content width
                fixedWidthElements.forEach(function(el) {
                  var elWidth = el.getAttribute('width') ||
                               (el.style.width ? parseInt(el.style.width, 10) : 0) ||
                               el.offsetWidth;

                  // Convert to number
                  elWidth = parseInt(elWidth, 10);

                  if (elWidth > maxContentWidth) {
                    maxContentWidth = elWidth;
                  }
                });

                // If no fixed width elements found, use the content width
                if (maxContentWidth === 0) {
                  maxContentWidth = content.offsetWidth;
                }

                // Only scale if content is wider than viewport and we're on mobile
                if (maxContentWidth > viewportWidth && viewportWidth < 640) {
                  var scale = viewportWidth / maxContentWidth;

                  // Dynamic minimum scale based on device width
                  var minScale = 0.6; // Default in optimal range (0.5-0.7)

                  // For very small devices, allow more aggressive scaling
                  if (viewportWidth < 375) {
                    minScale = 0.5; // More aggressive scaling for small phones
                  } else if (viewportWidth >= 375 && viewportWidth < 480) {
                    minScale = 0.55; // Medium scaling for medium phones
                  }

                  // Check if email has small text and adjust scaling if needed
                  var hasSmallText = document.querySelectorAll('font[size="1"], [style*="font-size: 10px"], [style*="font-size: 11px"], [style*="font-size:10px"], [style*="font-size:11px"]').length > 0;
                  if (hasSmallText) {
                    // Less aggressive scaling for emails with already small text
                    minScale = Math.min(Math.max(minScale + 0.05, 0.55), 0.7);
                  }

                  // Apply minimum scale to prevent too small content
                  scale = Math.max(scale, minScale);

                  // Apply the scale transform
                  content.classList.add('email-mobile-scale');
                  content.style.transform = 'scale(' + scale + ')';
                  content.style.width = (maxContentWidth) + 'px';
                  content.style.transformOrigin = 'top left';

                  // Set wrapper height to match scaled content
                  wrapper.style.height = (content.offsetHeight * scale) + 'px';

                  // Notify parent about height change
                  if (window.parentIFrame) {
                    window.parentIFrame.size();
                  }
                } else {
                  // Reset scaling if not needed
                  if (content.classList.contains('email-mobile-scale')) {
                    content.classList.remove('email-mobile-scale');
                    content.style.transform = '';
                    content.style.width = '';
                    wrapper.style.height = '';

                    // Notify parent about height change
                    if (window.parentIFrame) {
                      window.parentIFrame.size();
                    }
                  }
                }
              }

              // Initial scaling
              handleEmailScaling();

              // Re-scale on resize
              window.addEventListener('resize', handleEmailScaling);

              // Handle links to make them work properly
              document.querySelectorAll('a[href]').forEach(function(link) {
                link.addEventListener('click', function(e) {
                  e.preventDefault();

                  var href = this.getAttribute('href');

                  // Open links in new tab/window
                  if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
                    window.open(href, '_blank');
                  }
                });
              });
            });
          </script>
        </head>
        <body>
          <div class="email-wrapper">
            ${html}
          </div>
        </body>
      </html>
    `;
  }, [html, webFonts, contentHash]);

  // Effect to handle client-side rendering
  useEffect(() => {
    setIsClient(true);
  }, []);

  // We don't need this effect anymore as we're using the SimpleIframe component
  // This is just a placeholder to maintain the component structure
  useEffect(() => {
    // No-op
  }, []);

  // Use a consistent loading state for both server and client initial render
  const loadingState = (
    <div className="animate-pulse flex flex-col space-y-4 p-4" style={{ minHeight: '200px' }}>
      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      <div className="h-4 bg-gray-200 rounded w-full"></div>
      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
    </div>
  );

  return (
    <div
      className={`iframe-email-container ${className}`}
      style={{
        width: '100%',
        overflow: 'hidden',
        position: 'relative'
      }}
    >
      {!isClient ? (
        // Server-side and initial client render - show loading state
        loadingState
      ) : (
        // Client-side render after hydration - use SimpleIframe
        <SimpleIframe
          html={htmlContent}
          style={{
            width: '100%',
            border: 'none',
            overflow: 'hidden',
            display: 'block' // Ensure block display
          }}
        />
      )}
    </div>
  );
}
