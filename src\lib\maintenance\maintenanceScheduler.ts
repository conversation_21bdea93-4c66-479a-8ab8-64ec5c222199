/**
 * Database maintenance scheduler
 * 
 * This module provides functionality to schedule regular database maintenance
 * to ensure optimal performance after cleanup operations.
 */

import { logInfo, logError } from '@/lib/logging';
import { getConfig, updateConfig } from '@/lib/config/configService';
import { performMaintenance } from './dbMaintenance';

// Store the interval ID for the maintenance scheduler
let maintenanceIntervalId: NodeJS.Timeout | null = null;

/**
 * Check if the maintenance scheduler is running
 * 
 * @returns boolean Whether the scheduler is running
 */
export function isMaintenanceSchedulerRunning(): boolean {
  return maintenanceIntervalId !== null;
}

/**
 * Stop the maintenance scheduler
 */
export function stopMaintenanceScheduler(): void {
  if (maintenanceIntervalId) {
    clearInterval(maintenanceIntervalId);
    maintenanceIntervalId = null;
    logInfo('maintenance', 'Maintenance scheduler stopped');
  }
}

/**
 * Start the maintenance scheduler
 * 
 * @returns Promise<boolean> Whether the scheduler was started successfully
 */
export async function startMaintenanceScheduler(): Promise<boolean> {
  try {
    // If already running, stop it first
    if (maintenanceIntervalId) {
      stopMaintenanceScheduler();
    }
    
    // Get maintenance interval from configuration
    const maintenanceIntervalHours = await getConfig('maintenanceIntervalHours');
    
    if (!maintenanceIntervalHours || maintenanceIntervalHours <= 0) {
      logError('maintenance', 'Invalid maintenance interval', { maintenanceIntervalHours });
      return false;
    }
    
    // Convert hours to milliseconds
    const intervalMs = maintenanceIntervalHours * 60 * 60 * 1000;
    
    // Schedule maintenance
    maintenanceIntervalId = setInterval(async () => {
      try {
        await runMaintenance();
      } catch (error) {
        logError('maintenance', 'Error running scheduled maintenance', { error });
      }
    }, intervalMs);
    
    logInfo('maintenance', 'Maintenance scheduler started', {
      intervalHours: maintenanceIntervalHours,
      nextRunAt: new Date(Date.now() + intervalMs).toISOString()
    });
    
    return true;
  } catch (error) {
    logError('maintenance', 'Error starting maintenance scheduler', { error });
    return false;
  }
}

/**
 * Run the maintenance process
 * 
 * @returns Promise<void>
 */
export async function runMaintenance(): Promise<void> {
  try {
    logInfo('maintenance', 'Running maintenance process');
    
    // Call the maintenance function
    const result = await performMaintenance();
    
    logInfo('maintenance', 'Maintenance process completed', {
      guerrillaOptimized: result.guerrillaOptimized,
      supabaseVacuumed: result.supabaseVacuumed,
      duration: result.duration
    });
  } catch (error) {
    logError('maintenance', 'Error running maintenance', { error });
  }
}
