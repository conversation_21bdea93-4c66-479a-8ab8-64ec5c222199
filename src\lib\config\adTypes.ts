/**
 * Ad Configuration Types
 * 
 * This file defines the types for ad configurations in the Fademail application.
 */

/**
 * Ad Position - Where the ad is displayed in the UI
 */
export enum AdPosition {
  TOP = 'top',
  BOTTOM = 'bottom',
  SIDEBAR = 'sidebar',
  INLINE = 'inline',
  MODAL = 'modal'
}

/**
 * Ad Type - The type of ad content
 */
export enum AdType {
  BANNER = 'banner',
  NATIVE = 'native',
  TEXT = 'text',
  CUSTOM = 'custom'
}

/**
 * Ad Display Mode - How the ad is displayed
 */
export enum AdDisplayMode {
  ALWAYS = 'always',
  TIMED = 'timed',
  IDLE = 'idle',
  ACTION = 'action'
}

/**
 * Ad Provider - The source of the ad
 */
export enum AdProvider {
  GOOGLE = 'google',
  CUSTOM = 'custom',
  AFFILIATE = 'affiliate'
}

/**
 * Device Type - The type of device for targeting
 */
export enum DeviceType {
  ALL = 'all',
  DESKTOP = 'desktop',
  MOBILE = 'mobile',
  TABLET = 'tablet'
}

/**
 * Ad Configuration - Defines how an ad should be displayed
 */
export interface AdConfiguration {
  id: string;
  name: string;
  description?: string;
  position: AdPosition;
  type: AdType;
  displayMode: AdDisplayMode;
  provider: AdProvider;
  content: string;
  scriptUrl?: string;
  styleUrl?: string;
  isActive: boolean;
  priority: number;
  startDate?: string;
  endDate?: string;
  deviceTargeting: DeviceType[];
  displayFrequency?: number;
  idleTimeSeconds?: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Ad Performance Metrics - Tracks ad performance
 */
export interface AdPerformanceMetrics {
  adId: string;
  impressions: number;
  clicks: number;
  ctr: number;
  revenue?: number;
  date: string;
}

/**
 * Ad Preview - Used for previewing ads in the admin interface
 */
export interface AdPreview {
  configuration: AdConfiguration;
  previewHtml: string;
}
