'use client';

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Define tabs variants using class-variance-authority
const tabsVariants = cva(
  // Base styles applied to all tabs containers
  "w-full",
  {
    variants: {
      variant: {
        default: "",
        card: "bg-white rounded-lg shadow-sm",
        outline: "border border-gray-200 rounded-lg",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

// Define tabs list variants
const tabsListVariants = cva(
  // Base styles applied to all tabs lists
  "flex",
  {
    variants: {
      variant: {
        default: "border-b border-gray-200",
        pills: "space-x-2",
        underline: "border-b border-gray-200",
        enclosed: "border border-gray-200 rounded-lg overflow-hidden",
      },
      fullWidth: {
        true: "w-full",
      },
    },
    defaultVariants: {
      variant: "default",
      fullWidth: false,
    },
  }
);

// Define tabs trigger variants
const tabsTriggerVariants = cva(
  // Base styles applied to all tabs triggers
  "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "border-b-2 border-transparent px-4 py-2 hover:border-gray-300 data-[state=active]:border-indigo-500 data-[state=active]:text-indigo-600",
        pills: "rounded-md px-3 py-1.5 hover:bg-gray-100 data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700",
        underline: "border-b-2 border-transparent px-4 py-2 hover:text-gray-700 data-[state=active]:border-indigo-500 data-[state=active]:text-indigo-600",
        enclosed: "px-4 py-2 bg-gray-50 first:rounded-l-md last:rounded-r-md hover:bg-gray-100 data-[state=active]:bg-white",
      },
      fullWidth: {
        true: "flex-1",
      },
    },
    defaultVariants: {
      variant: "default",
      fullWidth: false,
    },
  }
);

// Define tabs content variants
const tabsContentVariants = cva(
  // Base styles applied to all tabs contents
  "mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2",
  {
    variants: {
      variant: {
        default: "",
        card: "p-4",
        outline: "p-4",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

// Tabs component props
export interface TabsProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof tabsVariants> {
  value: string;
  onValueChange: (value: string) => void;
  defaultValue?: string;
}

// Tabs component
const Tabs = React.forwardRef<HTMLDivElement, TabsProps>(
  ({ className, variant, value, onValueChange, defaultValue, children, ...props }, ref) => {
    return (
      <div
        className={cn(tabsVariants({ variant, className }))}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Tabs.displayName = "Tabs";

// TabsList component props
export interface TabsListProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof tabsListVariants> {}

// TabsList component
const TabsList = React.forwardRef<HTMLDivElement, TabsListProps>(
  ({ className, variant, fullWidth, children, ...props }, ref) => {
    return (
      <div
        className={cn(tabsListVariants({ variant, fullWidth, className }))}
        role="tablist"
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);

TabsList.displayName = "TabsList";

// TabsTrigger component props
export interface TabsTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof tabsTriggerVariants> {
  value: string;
}

// TabsTrigger component
const TabsTrigger = React.forwardRef<HTMLButtonElement, TabsTriggerProps>(
  ({ className, variant, fullWidth, value, children, ...props }, ref) => {
    // Get the current value from the closest Tabs component
    const tabsContext = React.useContext(TabsContext);
    const isActive = tabsContext?.value === value;

    return (
      <button
        className={cn(tabsTriggerVariants({ variant, fullWidth, className }))}
        role="tab"
        aria-selected={isActive}
        data-state={isActive ? "active" : "inactive"}
        onClick={() => tabsContext?.onValueChange(value)}
        ref={ref}
        {...props}
      >
        {children}
      </button>
    );
  }
);

TabsTrigger.displayName = "TabsTrigger";

// TabsContent component props
export interface TabsContentProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof tabsContentVariants> {
  value: string;
}

// TabsContent component
const TabsContent = React.forwardRef<HTMLDivElement, TabsContentProps>(
  ({ className, variant, value, children, ...props }, ref) => {
    // Get the current value from the closest Tabs component
    const tabsContext = React.useContext(TabsContext);
    const isActive = tabsContext?.value === value;

    if (!isActive) return null;

    return (
      <div
        className={cn(tabsContentVariants({ variant, className }))}
        role="tabpanel"
        data-state={isActive ? "active" : "inactive"}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);

TabsContent.displayName = "TabsContent";

// Create a context to share the current tab value
interface TabsContextValue {
  value: string;
  onValueChange: (value: string) => void;
}

const TabsContext = React.createContext<TabsContextValue | null>(null);

// Wrap the Tabs component to provide context
const TabsWithContext = React.forwardRef<HTMLDivElement, TabsProps>(
  (props, ref) => {
    return (
      <TabsContext.Provider value={{ value: props.value, onValueChange: props.onValueChange }}>
        <Tabs {...props} ref={ref} />
      </TabsContext.Provider>
    );
  }
);

TabsWithContext.displayName = "Tabs";

export { TabsWithContext as Tabs, TabsList, TabsTrigger, TabsContent };
