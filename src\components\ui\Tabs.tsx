'use client';

import React from 'react';
import * as TabsPrimitive from '@radix-ui/react-tabs';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

/**
 * shadcn/ui Tabs Component adapted for VanishPost
 *
 * Modern tab navigation component with enhanced accessibility and styling.
 * Maintains backward compatibility with existing VanishPost usage.
 */

// Modern shadcn/ui Tabs components
const ModernTabs = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Root>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Root
    ref={ref}
    data-slot="tabs"
    className={cn("flex flex-col gap-2", className)}
    {...props}
  />
));

ModernTabs.displayName = TabsPrimitive.Root.displayName;

const ModernTabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    data-slot="tabs-list"
    className={cn(
      // VanishPost admin theme styling
      "inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",
      "bg-gray-100 text-gray-600",
      "dark:bg-gray-800 dark:text-gray-400",
      className
    )}
    {...props}
  />
));

ModernTabsList.displayName = TabsPrimitive.List.displayName;

const ModernTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    data-slot="tabs-trigger"
    className={cn(
      // Base styles
      "inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-3 py-1 text-sm font-medium whitespace-nowrap transition-all",
      // Focus and disabled states
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",
      "disabled:pointer-events-none disabled:opacity-50",
      // VanishPost theme colors
      "text-gray-600 hover:text-gray-900",
      "data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm",
      "dark:text-gray-400 dark:hover:text-gray-100",
      "dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-gray-100",
      // Icon support
      "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
      className
    )}
    {...props}
  />
));

ModernTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const ModernTabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    data-slot="tabs-content"
    className={cn(
      "flex-1 outline-none",
      // Add some padding and styling for VanishPost
      "mt-2",
      className
    )}
    {...props}
  />
));

ModernTabsContent.displayName = TabsPrimitive.Content.displayName;

/**
 * Enhanced Tabs variants for VanishPost admin use cases
 */

// Admin Tabs with badge support
interface AdminTabsTriggerProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> {
  badge?: string | number;
  icon?: React.ReactNode;
}

export const AdminTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  AdminTabsTriggerProps
>(({ className, badge, icon, children, ...props }, ref) => (
  <ModernTabsTrigger
    ref={ref}
    className={cn("relative", className)}
    {...props}
  >
    <div className="flex items-center gap-2">
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <span>{children}</span>
      {badge && (
        <span className="ml-1 inline-flex items-center justify-center px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full min-w-[1.25rem] h-5">
          {badge}
        </span>
      )}
    </div>
  </ModernTabsTrigger>
));

AdminTabsTrigger.displayName = "AdminTabsTrigger";

// Legacy interface for backward compatibility
export interface LegacyTabsProps {
  value: string;
  onValueChange: (value: string) => void;
  defaultValue?: string;
  className?: string;
  children?: React.ReactNode;
}

// Legacy Tabs component for backward compatibility
const LegacyTabs = React.forwardRef<HTMLDivElement, LegacyTabsProps>(
  ({ className, value, onValueChange, defaultValue, children }, ref) => {
    return (
      <ModernTabs
        value={value}
        onValueChange={onValueChange}
        defaultValue={defaultValue}
        className={cn("w-full", className)}
        ref={ref}
      >
        {children}
      </ModernTabs>
    );
  }
);

LegacyTabs.displayName = "Tabs";

// Legacy TabsList for backward compatibility
export interface LegacyTabsListProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'pills' | 'underline' | 'enclosed';
  fullWidth?: boolean;
}

const LegacyTabsList = React.forwardRef<HTMLDivElement, LegacyTabsListProps>(
  ({ className, variant = 'default', fullWidth, children, ...props }, ref) => {
    const getVariantClasses = () => {
      switch (variant) {
        case 'pills':
          return 'bg-transparent p-0 gap-1';
        case 'underline':
          return 'bg-transparent p-0 border-b border-gray-200';
        case 'enclosed':
          return 'border border-gray-200 rounded-lg overflow-hidden p-0';
        default:
          return 'bg-gray-100 p-[3px]';
      }
    };

    return (
      <ModernTabsList
        className={cn(
          getVariantClasses(),
          fullWidth && 'w-full',
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </ModernTabsList>
    );
  }
);

LegacyTabsList.displayName = "TabsList";

// Legacy TabsTrigger for backward compatibility
export interface LegacyTabsTriggerProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> {
  variant?: 'default' | 'pills' | 'underline' | 'enclosed';
  fullWidth?: boolean;
}

const LegacyTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  LegacyTabsTriggerProps
>(({ className, variant = 'default', fullWidth, children, ...props }, ref) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'pills':
        return 'rounded-md px-3 py-1.5 hover:bg-gray-100 data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700';
      case 'underline':
        return 'border-b-2 border-transparent px-4 py-2 hover:text-gray-700 data-[state=active]:border-blue-500 data-[state=active]:text-blue-600 bg-transparent shadow-none';
      case 'enclosed':
        return 'px-4 py-2 bg-gray-50 first:rounded-l-md last:rounded-r-md hover:bg-gray-100 data-[state=active]:bg-white shadow-none';
      default:
        return 'border-b-2 border-transparent px-4 py-2 hover:border-gray-300 data-[state=active]:border-blue-500 data-[state=active]:text-blue-600 bg-transparent shadow-none';
    }
  };

  return (
    <ModernTabsTrigger
      ref={ref}
      className={cn(
        getVariantClasses(),
        fullWidth && 'flex-1',
        className
      )}
      {...props}
    >
      {children}
    </ModernTabsTrigger>
  );
});

LegacyTabsTrigger.displayName = "TabsTrigger";

// Legacy TabsContent for backward compatibility
export interface LegacyTabsContentProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content> {
  variant?: 'default' | 'card' | 'outline';
}

const LegacyTabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  LegacyTabsContentProps
>(({ className, variant = 'default', ...props }, ref) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'card':
        return 'p-4 bg-white rounded-lg shadow-sm';
      case 'outline':
        return 'p-4 border border-gray-200 rounded-lg';
      default:
        return '';
    }
  };

  return (
    <ModernTabsContent
      ref={ref}
      className={cn(
        getVariantClasses(),
        className
      )}
      {...props}
    />
  );
});

LegacyTabsContent.displayName = "TabsContent";

// Export both modern and legacy versions
export {
  LegacyTabs as Tabs,
  LegacyTabsList as TabsList,
  LegacyTabsTrigger as TabsTrigger,
  LegacyTabsContent as TabsContent,
  ModernTabs,
  ModernTabsList,
  ModernTabsTrigger,
  ModernTabsContent
};
