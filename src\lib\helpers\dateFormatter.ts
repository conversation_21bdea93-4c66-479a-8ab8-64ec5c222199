import { formatDistanceToNow } from 'date-fns';

/**
 * Safely formats a date string to a relative time string (e.g., "2 hours ago")
 * 
 * @param dateString The date string to format
 * @param options Options for formatting
 * @returns A formatted string or a fallback string if the date is invalid
 */
export function formatRelativeTime(
  dateString: string | null | undefined,
  options: { addSuffix?: boolean } = { addSuffix: true },
  fallback: string = 'Date unavailable'
): string {
  if (!dateString) {
    return fallback;
  }

  try {
    const date = new Date(dateString);
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date:', dateString);
      return fallback;
    }
    
    return formatDistanceToNow(date, options);
  } catch (error) {
    console.error('Error formatting date:', error);
    return fallback;
  }
}

/**
 * Safely formats a date string to a locale string (e.g., "5/19/2025, 7:33:16 AM")
 * 
 * @param dateString The date string to format
 * @param options Options for formatting
 * @returns A formatted string or a fallback string if the date is invalid
 */
export function formatLocaleTime(
  dateString: string | null | undefined,
  options: Intl.DateTimeFormatOptions = {},
  fallback: string = 'Date unavailable'
): string {
  if (!dateString) {
    return fallback;
  }

  try {
    const date = new Date(dateString);
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date:', dateString);
      return fallback;
    }
    
    return date.toLocaleString(undefined, options);
  } catch (error) {
    console.error('Error formatting date:', error);
    return fallback;
  }
}
