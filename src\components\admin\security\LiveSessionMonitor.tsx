'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { AdminCard, AdminCardHeader, AdminCardContent, AdminCardTitle } from '../ui/AdminCard';
import { AdminButton } from '../ui/AdminButton';
import { AdminStatusIndicator } from '../ui/AdminStatusIndicator';
import {
  RefreshCw,
  Pause,
  Play,
  AlertTriangle,
  BarChart3,
  Info,
  Clock,
  Globe,
  Shield
} from 'lucide-react';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { Badge } from '@/components/ui/Badge';
import { Separator } from '@/components/ui/Separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/Tooltip';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/Pagination';

// Simple wrapper component for AdminCard with title support
function AdminCardWithTitle({ title, children, className }: { title: string | React.ReactNode; children: React.ReactNode; className?: string }) {
  return (
    <AdminCard className={className}>
      <AdminCardHeader>
        <AdminCardTitle>{title}</AdminCardTitle>
      </AdminCardHeader>
      <AdminCardContent>
        {children}
      </AdminCardContent>
    </AdminCard>
  );
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

interface LiveSession {
  session_id: string;
  emails_generated: number;
  emails_received: number;
  last_activity: string;
  ip_addresses: string[];
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  is_blocked: boolean;
  user_agent: string;
  country: string;
  generation_rate: number; // emails per hour
}

interface SecurityAlert {
  id: string;
  session_id: string;
  alert_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  created_at: string;
  is_resolved: boolean;
}

export function LiveSessionMonitor() {
  const [sessions, setSessions] = useState<LiveSession[]>([]);
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalSessions, setTotalSessions] = useState(0);
  const [sessionsPerPage] = useState(10); // Reduced from 50 for better UX

  // Filter state
  const [riskFilter, setRiskFilter] = useState<string>('all');

  // Fetch live sessions with pagination
  const fetchLiveSessions = async (page: number = currentPage) => {
    try {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();

      // First, get total count of unique sessions for pagination
      const { data: allSessionData, error: countError } = await supabase
        .from('analytics_events')
        .select('session_id')
        .gte('timestamp', oneHourAgo);

      if (countError) {
        console.error('Error fetching session count:', countError);
        return;
      }

      // Calculate unique sessions for total count
      const uniqueSessionIds = new Set(allSessionData?.map(event => event.session_id) || []);
      setTotalSessions(uniqueSessionIds.size);

      // Get active sessions with their activity (all data for processing)
      const { data: sessionData, error } = await supabase
        .from('analytics_events')
        .select(`
          session_id,
          additional_data,
          timestamp,
          event_type
        `)
        .gte('timestamp', oneHourAgo)
        .order('timestamp', { ascending: false });

      if (error) {
        console.error('Error fetching sessions:', error);
        return;
      }

      // Process session data
      const sessionMap = new Map<string, any>();
      
      sessionData?.forEach(event => {
        const sessionId = event.session_id;
        if (!sessionMap.has(sessionId)) {
          sessionMap.set(sessionId, {
            session_id: sessionId,
            emails_generated: 0,
            emails_received: 0,
            last_activity: event.timestamp,
            ip_addresses: new Set(),
            user_agent: event.additional_data?.userAgent || 'Unknown',
            country: event.additional_data?.country || 'Unknown'
          });
        }

        const session = sessionMap.get(sessionId);
        
        // Update activity
        if (new Date(event.timestamp) > new Date(session.last_activity)) {
          session.last_activity = event.timestamp;
        }

        // Count events
        if (event.event_type === 'email_address_generated') {
          session.emails_generated++;
        } else if (event.event_type === 'email_received') {
          session.emails_received++;
        }

        // Collect IP addresses
        if (event.additional_data?.clientIP) {
          session.ip_addresses.add(event.additional_data.clientIP);
        }
      });

      // Convert to array and calculate risk levels
      const processedSessions = Array.from(sessionMap.values()).map(session => {
        const ipCount = session.ip_addresses.size;
        const emailRatio = session.emails_generated > 0 ? session.emails_received / session.emails_generated : 0;
        const timeSpan = (Date.now() - new Date(session.last_activity).getTime()) / (1000 * 60 * 60);
        const generationRate = timeSpan > 0 ? session.emails_generated / timeSpan : 0;

        // Calculate risk level
        let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
        if (ipCount > 5 || emailRatio > 1.5 || generationRate > 10) {
          riskLevel = 'critical';
        } else if (ipCount > 3 || emailRatio > 1.0 || generationRate > 8) {
          riskLevel = 'high';
        } else if (ipCount > 2 || emailRatio > 0.5 || generationRate > 5) {
          riskLevel = 'medium';
        }

        return {
          ...session,
          ip_addresses: Array.from(session.ip_addresses),
          risk_level: riskLevel,
          is_blocked: false, // TODO: Check against blocked_sessions table
          generation_rate: generationRate
        };
      });

      // Apply risk filter
      const filteredSessions = riskFilter === 'all'
        ? processedSessions
        : processedSessions.filter(session => session.risk_level === riskFilter);

      // Update total count for filtered sessions
      setTotalSessions(filteredSessions.length);

      // Apply pagination to filtered sessions
      const startIndex = (page - 1) * sessionsPerPage;
      const endIndex = startIndex + sessionsPerPage;
      const paginatedSessions = filteredSessions.slice(startIndex, endIndex);

      setSessions(paginatedSessions);
    } catch (error) {
      console.error('Error in fetchLiveSessions:', error);
    }
  };

  // Fetch security alerts
  const fetchSecurityAlerts = async () => {
    try {
      const { data, error } = await supabase
        .from('security_events')
        .select('*')
        .eq('severity', 'high')
        .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        console.error('Error fetching alerts:', error);
        return;
      }

      const formattedAlerts: SecurityAlert[] = data?.map(event => ({
        id: event.id,
        session_id: event.session_id || 'N/A',
        alert_type: event.event_type,
        severity: event.severity as 'low' | 'medium' | 'high' | 'critical',
        message: event.description,
        created_at: event.created_at,
        is_resolved: false
      })) || [];

      setAlerts(formattedAlerts);
    } catch (error) {
      console.error('Error in fetchSecurityAlerts:', error);
    }
  };

  // Block session
  const blockSession = async (sessionId: string, reason: string) => {
    try {
      const { error } = await supabase
        .from('blocked_sessions')
        .insert({
          session_id: sessionId,
          reason: `Manual block: ${reason}`,
          blocked_by: 'Admin Dashboard',
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          is_active: true
        });

      if (error) {
        console.error('Error blocking session:', error);
        alert('Failed to block session');
        return;
      }

      alert('Session blocked successfully');
      fetchLiveSessions(); // Refresh data
    } catch (error) {
      console.error('Error in blockSession:', error);
      alert('Failed to block session');
    }
  };

  // Auto-refresh effect
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      await Promise.all([fetchLiveSessions(currentPage), fetchSecurityAlerts()]);
      setLoading(false);
    };

    fetchData();

    if (autoRefresh) {
      const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh, currentPage, riskFilter]); // Add currentPage and riskFilter dependencies

  // Pagination handlers
  const totalPages = Math.ceil(totalSessions / sessionsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setSelectedSession(null); // Clear selected session when changing pages
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  const handleRiskFilterChange = (filter: string) => {
    setRiskFilter(filter);
    setCurrentPage(1); // Reset to first page when changing filter
    setSelectedSession(null); // Clear selected session
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-green-600 bg-green-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      default: return 'text-blue-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Live Session Monitor</h2>
        <div className="flex gap-2">
          <AdminButton
            onClick={() => setAutoRefresh(!autoRefresh)}
            variant={autoRefresh ? 'primary' : 'secondary'}
            size="sm"
          >
            {autoRefresh ? (
              <>
                <RefreshCw className="h-4 w-4 mr-1" />
                Auto-Refresh ON
              </>
            ) : (
              <>
                <Pause className="h-4 w-4 mr-1" />
                Auto-Refresh OFF
              </>
            )}
          </AdminButton>
          <AdminButton
            onClick={() => {
              fetchLiveSessions();
              fetchSecurityAlerts();
            }}
            variant="secondary"
            size="sm"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh Now
          </AdminButton>
        </div>
      </div>

      {/* Security Alerts */}
      <AdminCardWithTitle title={<><AlertTriangle className="h-4 w-4 mr-1 inline text-red-600" />Recent Security Alerts</>} className="border-red-200">
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {alerts.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No recent security alerts</p>
          ) : (
            alerts.map(alert => (
              <div key={alert.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className={`font-medium ${getSeverityColor(alert.severity)}`}>
                      {alert.severity.toUpperCase()}
                    </span>
                    <span className="text-sm text-gray-600">
                      {alert.alert_type.replace(/_/g, ' ')}
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 mt-1">{alert.message}</p>
                  <p className="text-xs text-gray-500">
                    Session: {alert.session_id} • {new Date(alert.created_at).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </AdminCardWithTitle>

      {/* Live Sessions */}
      <AdminCardWithTitle
        title={
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-blue-600" />
            <span>Active Sessions</span>
            {totalSessions > 0 && (
              <Badge variant="secondary" className="ml-1">
                {totalSessions}
              </Badge>
            )}
          </div>
        }
        className="border-blue-200"
      >
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading sessions...</p>
          </div>
        ) : (
          <>
            {/* Risk Level Filter */}
            <div className="mb-4">
              <div className="flex items-center gap-3 mb-3">
                <span className="text-sm font-medium text-gray-700 flex items-center gap-1">
                  <Shield className="w-4 h-4" />
                  Filter by Risk Level:
                </span>
                <div className="flex gap-2">
                  {[
                    { value: 'all', label: 'All', icon: null },
                    { value: 'low', label: 'Low', icon: <Shield className="w-3 h-3" /> },
                    { value: 'medium', label: 'Medium', icon: <Shield className="w-3 h-3" /> },
                    { value: 'high', label: 'High', icon: <Shield className="w-3 h-3" /> },
                    { value: 'critical', label: 'Critical', icon: <AlertTriangle className="w-3 h-3" /> }
                  ].map((level) => (
                    <Badge
                      key={level.value}
                      variant={riskFilter === level.value ? (level.value === 'all' ? 'default' : level.value as any) : 'outline'}
                      className="cursor-pointer hover:bg-opacity-80 transition-colors"
                      onClick={() => handleRiskFilterChange(level.value)}
                    >
                      {level.icon}
                      {level.label}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Session Count and Pagination Info */}
            <div className="flex justify-between items-center mb-4 pb-2 border-b border-gray-200">
              <div className="text-sm text-gray-600">
                Showing {sessions.length > 0 ? ((currentPage - 1) * sessionsPerPage) + 1 : 0} - {Math.min(currentPage * sessionsPerPage, totalSessions)} of {totalSessions}
                {riskFilter !== 'all' ? ` ${riskFilter} risk` : ''} sessions
              </div>
              {totalPages > 1 && (
                <Pagination className="justify-end">
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          handlePrevPage();
                        }}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>

                    {/* Show page numbers */}
                    {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              handlePageChange(pageNum);
                            }}
                            isActive={currentPage === pageNum}
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}

                    <PaginationItem>
                      <PaginationNext
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          handleNextPage();
                        }}
                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              )}
            </div>

            {/* Sessions List */}
            <ScrollArea className="h-80 w-full rounded-md border">
              <div className="space-y-2 p-4">
              {sessions.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No active sessions</p>
              ) : (
                sessions.map(session => (
                <div key={session.session_id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="font-mono text-sm text-gray-600 cursor-help">
                              {session.session_id.slice(-12)}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Session ID: {session.session_id}</p>
                          </TooltipContent>
                        </Tooltip>

                        <Badge variant={session.risk_level as any}>
                          <Shield className="w-3 h-3 mr-1" />
                          {session.risk_level.toUpperCase()}
                        </Badge>

                        {session.is_blocked && (
                          <Badge variant="destructive">
                            BLOCKED
                          </Badge>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <span className="text-gray-500">Generated:</span>
                              <span className="ml-1 font-medium">{session.emails_generated}</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Emails generated in the last hour</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <span className="text-gray-500">Received:</span>
                              <span className="ml-1 font-medium">{session.emails_received}</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Emails received in the last hour</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <span className="text-gray-500">IPs:</span>
                              <span className="ml-1 font-medium">{session.ip_addresses.length}</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>IP Addresses: {session.ip_addresses.join(', ')}</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <span className="text-gray-500">Rate:</span>
                              <span className="ml-1 font-medium">{session.generation_rate.toFixed(1)}/hr</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Email generation rate per hour</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>

                      <Separator className="my-3" />

                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center gap-1 cursor-help">
                              <Clock className="w-3 h-3" />
                              <span>Last: {new Date(session.last_activity).toLocaleTimeString()}</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Last activity: {new Date(session.last_activity).toLocaleString()}</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center gap-1 cursor-help">
                              <Globe className="w-3 h-3" />
                              <span>{session.country || 'Unknown'}</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Country: {session.country || 'Unknown location'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </div>

                    <div className="flex gap-2 ml-4">
                      <AdminButton
                        onClick={() => setSelectedSession(
                          selectedSession === session.session_id ? null : session.session_id
                        )}
                        variant="secondary"
                        size="sm"
                      >
                        {selectedSession === session.session_id ? 'Hide' : 'Details'}
                      </AdminButton>
                      {!session.is_blocked && session.risk_level !== 'low' && (
                        <AdminButton
                          onClick={() => blockSession(session.session_id, `High risk session (${session.risk_level})`)}
                          variant="danger"
                          size="sm"
                        >
                          Block
                        </AdminButton>
                      )}
                    </div>
                  </div>

                  {/* Session Details */}
                  {selectedSession === session.session_id && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <h4 className="font-medium text-gray-700 mb-2">IP Addresses</h4>
                          <div className="space-y-1">
                            {session.ip_addresses.map((ip, index) => (
                              <div key={index} className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                                {ip}
                              </div>
                            ))}
                          </div>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-700 mb-2">User Agent</h4>
                          <div className="text-xs bg-gray-100 px-2 py-1 rounded break-all">
                            {session.user_agent}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
              </div>
            </ScrollArea>
          </>
        )}
      </AdminCardWithTitle>
    </div>
  );
}
