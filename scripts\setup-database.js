/**
 * <PERSON><PERSON><PERSON> to set up the TempEmail table in the local database
 */
const mysql = require('mysql2/promise');

// Local database configuration
const localDbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'tempmail_web_user',
  password: process.env.DB_PASSWORD || 'yiWTpFChtFW3neWCS',
  database: process.env.DB_NAME || 'tempmail_web',
};

async function setupDatabase() {
  console.log('Setting up the TempEmail table in the local database...');
  
  let connection;
  try {
    // Create connection
    connection = await mysql.createConnection(localDbConfig);
    
    // Check if the table already exists
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'TempEmail'"
    );
    
    if (tables.length > 0) {
      console.log('TempEmail table already exists. Checking structure...');
      
      // Check if the table structure matches our requirements
      const [columns] = await connection.execute(
        "SHOW COLUMNS FROM TempEmail"
      );
      
      const columnNames = columns.map(col => col.Field);
      const requiredColumns = ['id', 'emailAddress', 'creationTime', 'expirationDate'];
      
      const missingColumns = requiredColumns.filter(col => !columnNames.includes(col));
      
      if (missingColumns.length > 0) {
        console.log(`Table exists but is missing columns: ${missingColumns.join(', ')}`);
        console.log('Dropping and recreating the table...');
        
        await connection.execute("DROP TABLE TempEmail");
        await createTable(connection);
      } else {
        console.log('Table structure is correct.');
      }
    } else {
      console.log('TempEmail table does not exist. Creating...');
      await createTable(connection);
    }
    
    console.log('Database setup completed successfully!');
  } catch (error) {
    console.error('Error setting up database:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function createTable(connection) {
  // Create the TempEmail table with appropriate indexes
  await connection.execute(`
    CREATE TABLE TempEmail (
      id INT NOT NULL AUTO_INCREMENT,
      emailAddress VARCHAR(255) NOT NULL,
      creationTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      expirationDate DATETIME NOT NULL,
      PRIMARY KEY (id),
      UNIQUE INDEX idx_emailAddress (emailAddress),
      INDEX idx_expirationDate (expirationDate)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `);
  
  console.log('TempEmail table created successfully with indexes.');
}

// Run the setup
setupDatabase().catch(console.error);
