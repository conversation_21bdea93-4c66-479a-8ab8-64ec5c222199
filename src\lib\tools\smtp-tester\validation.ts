/**
 * SMTP Tester Validation Utilities
 * 
 * Input validation and sanitization functions
 */

import { SmtpConfig, SmtpTestRequest } from '@/types/smtp';

export function validateEmailAddress(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
}

export function validateSmtpServer(server: string): boolean {
  const serverRegex = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return serverRegex.test(server.trim());
}

export function validatePort(port: number): boolean {
  return Number.isInteger(port) && port >= 1 && port <= 65535;
}

export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

export function validateSmtpConfig(config: SmtpConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate server
  if (!config.server || !validateSmtpServer(config.server)) {
    errors.push('Invalid SMTP server address');
  }

  // Validate port
  if (!validatePort(config.port)) {
    errors.push('Port must be between 1 and 65535');
  }

  // Validate encryption
  if (!['none', 'tls', 'ssl'].includes(config.encryption)) {
    errors.push('Invalid encryption type');
  }

  // Validate username
  if (!config.username || config.username.trim().length === 0) {
    errors.push('Username is required');
  }

  // Validate password
  if (!config.password || config.password.length === 0) {
    errors.push('Password is required');
  }

  // Validate sender email
  if (!config.sender || !validateEmailAddress(config.sender)) {
    errors.push('Valid sender email address is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function validateSmtpTestRequest(request: SmtpTestRequest): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate config
  const configValidation = validateSmtpConfig(request.config);
  if (!configValidation.isValid) {
    errors.push(...configValidation.errors);
  }

  // Validate test mode
  if (!['auto', 'custom'].includes(request.testMode)) {
    errors.push('Invalid test mode');
  }

  // Validate recipient for custom mode
  if (request.testMode === 'custom') {
    if (!request.recipient || !validateEmailAddress(request.recipient)) {
      errors.push('Valid recipient email address is required for custom mode');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function sanitizeSmtpConfig(config: SmtpConfig): SmtpConfig {
  return {
    server: sanitizeString(config.server),
    port: config.port,
    encryption: config.encryption,
    username: sanitizeString(config.username),
    password: config.password, // Don't sanitize password as it might contain special chars
    sender: sanitizeString(config.sender)
  };
}

export function sanitizeSmtpTestRequest(request: SmtpTestRequest): SmtpTestRequest {
  return {
    config: sanitizeSmtpConfig(request.config),
    testMode: request.testMode,
    recipient: request.recipient ? sanitizeString(request.recipient) : undefined
  };
}
