/**
 * Tools Landing Page
 *
 * This page displays all available tools in the VanishPost application
 * using the established earth-tone design system.
 */

import Link from 'next/link';
import Breadcrumbs from '@/components/seo/Breadcrumbs';
import { SoftwareApplicationSchema } from '@/components/seo/StructuredData';

export const metadata = {
  title: 'VanishPost Tools - Free Email Testing & Privacy Tools',
  description: 'Professional email tools and utilities. Free email deliverability testing, SPF/DKIM/DMARC validators, temporary email addresses, and privacy utilities to enhance your online experience.',
  keywords: 'email tools, email tester, deliverability test, SPF checker, DKIM validator, DMARC tester, email authentication, privacy tools, free email utilities, temporary email',
};

export default function ToolsPage() {
  // Breadcrumb items
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Tools', url: '/tools', current: true },
  ];

  // Tool features for structured data
  const toolFeatures = [
    'Email Deliverability Testing',
    'SMTP Server Connectivity Testing',
    'SPF/DKIM/DMARC Validation',
    'Temporary Email Generation',
    'Email Authentication Analysis',
    'Privacy Protection Tools',
    'AI-Powered Recommendations'
  ];

  return (
    <>
      {/* Structured Data */}
      <SoftwareApplicationSchema
        name="VanishPost Tools"
        description="Professional email tools and utilities for testing, privacy, and deliverability improvement"
        url="https://vanishpost.com/tools"
        applicationCategory="BusinessApplication"
        features={toolFeatures}
      />

      <div className="min-h-screen" style={{ backgroundColor: '#fbfaf8' }}>
        <div className="max-w-5xl mx-auto px-4 py-8">
          {/* Breadcrumb Navigation */}
          <Breadcrumbs items={breadcrumbItems} />

          {/* Hero Section */}
          <div className="text-center mb-12 mt-8 sm:mt-10 lg:mt-12">
            <h1 className="text-4xl font-bold mb-4" style={{ color: '#1b130e' }}>
              Professional Email Tools & Utilities
            </h1>
            <p className="text-lg max-w-3xl mx-auto leading-relaxed" style={{ color: '#4a3728' }}>
              Explore our collection of professional email tools designed to enhance your online experience.
              From deliverability testing to privacy protection, we provide enterprise-grade utilities for free.
            </p>
          </div>

          {/* Tools Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 gap-8 mb-16">

            {/* Temporary Email Tool */}
            <Link
              href="/"
              className="group block bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
            >
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-[#66b077]/10 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-label="Temporary email icon">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h2 className="text-lg font-bold text-[#1b130e]">
                    Temporary Email Generator
                  </h2>
                  <p className="text-sm font-medium text-[#956b50]">
                    Core VanishPost Service
                  </p>
                </div>
              </div>

              <p className="text-sm text-[#4a3728] mb-4">
                Generate secure temporary email addresses that automatically expire in 15 minutes.
                Perfect for protecting your privacy when signing up for services, testing applications,
                or avoiding spam in your primary inbox.
              </p>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Expiry</div>
                  <div className="font-semibold text-[#1b130e] text-sm">15 minutes</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Registration</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Not required</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Privacy</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Protected</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Access</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Instant</div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-2">
                  <span className="text-xs px-2 py-1 rounded-full bg-[#66b077]/10 text-[#66b077] border border-[#66b077]/20">
                    Privacy
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-[#956b50]/10 text-[#956b50] border border-[#956b50]/20">
                    Security
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-[#f59e0b]/10 text-[#f59e0b] border border-[#f59e0b]/20">
                    Free
                  </span>
                </div>
                <div className="flex items-center text-sm font-medium group-hover:translate-x-1 transition-transform duration-200 text-[#1b130e]">
                  <span>Get Started</span>
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>

            {/* Email Tester Tool */}
            <Link
              href="/tools/email-tester"
              className="group block bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
            >
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-[#956b50]/10 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-[#956b50]" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-label="Email testing icon">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h2 className="text-lg font-bold text-[#1b130e]">
                    Email Tester Tool
                  </h2>
                  <p className="text-sm font-medium text-[#956b50]">
                    Deliverability & Authentication Testing
                  </p>
                </div>
              </div>

              <p className="text-sm text-[#4a3728] mb-4">
                Professional email deliverability testing tool. Validate SPF, DKIM, DMARC authentication,
                analyze email server configuration, and receive AI-powered recommendations to improve
                your email delivery rates and sender reputation.
              </p>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">SPF</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Validation</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">DKIM</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Verification</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">DMARC</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Analysis</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Records</div>
                  <div className="font-semibold text-[#1b130e] text-sm">MX & PTR</div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-2">
                  <span className="text-xs px-2 py-1 rounded-full bg-[#956b50]/10 text-[#956b50] border border-[#956b50]/20">
                    Testing
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-[#66b077]/10 text-[#66b077] border border-[#66b077]/20">
                    Analysis
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-[#f59e0b]/10 text-[#f59e0b] border border-[#f59e0b]/20">
                    AI-Powered
                  </span>
                </div>
                <div className="flex items-center text-sm font-medium group-hover:translate-x-1 transition-transform duration-200 text-[#1b130e]">
                  <span>Test Email</span>
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>

            {/* SMTP Tester Tool */}
            <Link
              href="/tools/smtp-tester"
              className="group block bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
            >
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-[#ce601c]/10 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-[#ce601c]" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-label="SMTP server icon">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h2 className="text-lg font-bold text-[#1b130e]">
                    SMTP Tester Tool
                  </h2>
                  <p className="text-sm font-medium text-[#956b50]">
                    Server Connectivity & Authentication
                  </p>
                </div>
              </div>

              <p className="text-sm text-[#4a3728] mb-4">
                Test SMTP server connectivity and email authentication protocols. Verify your email
                server configuration, test delivery capabilities, and analyze SPF, DKIM, DMARC
                authentication with comprehensive diagnostic logging.
              </p>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">SMTP</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Connectivity</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Auth</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Testing</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Logs</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Diagnostic</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Providers</div>
                  <div className="font-semibold text-[#1b130e] text-sm">All Supported</div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-2">
                  <span className="text-xs px-2 py-1 rounded-full bg-[#ce601c]/10 text-[#ce601c] border border-[#ce601c]/20">
                    SMTP
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-[#66b077]/10 text-[#66b077] border border-[#66b077]/20">
                    Authentication
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-[#f59e0b]/10 text-[#f59e0b] border border-[#f59e0b]/20">
                    Free
                  </span>
                </div>
                <div className="flex items-center text-sm font-medium group-hover:translate-x-1 transition-transform duration-200 text-[#1b130e]">
                  <span>Test SMTP</span>
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>

            {/* DKIM Generator Tool */}
            <Link
              href="/tools/dkim-generator"
              className="group block bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
            >
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-[#4a3728]/10 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-[#4a3728]" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-label="DKIM key icon">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h2 className="text-lg font-bold text-[#1b130e]">
                    DKIM Generator
                  </h2>
                  <p className="text-sm font-medium text-[#956b50]">
                    Email Authentication Keys
                  </p>
                </div>
              </div>

              <p className="text-sm text-[#4a3728] mb-4">
                Generate RSA key pairs and DNS records for DKIM email authentication.
                Secure your emails with cryptographic signatures to improve deliverability
                and protect against spoofing attacks.
              </p>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Keys</div>
                  <div className="font-semibold text-[#1b130e] text-sm">RSA Generation</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">DNS</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Record Format</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Security</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Encryption</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Validation</div>
                  <div className="font-semibold text-[#1b130e] text-sm">DNS Check</div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-2">
                  <span className="text-xs px-2 py-1 rounded-full bg-[#4a3728]/10 text-[#4a3728] border border-[#4a3728]/20">
                    Security
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-[#66b077]/10 text-[#66b077] border border-[#66b077]/20">
                    Authentication
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-[#f59e0b]/10 text-[#f59e0b] border border-[#f59e0b]/20">
                    Free
                  </span>
                </div>
                <div className="flex items-center text-sm font-medium group-hover:translate-x-1 transition-transform duration-200 text-[#1b130e]">
                  <span>Generate Keys</span>
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>

            {/* DMARC Generator Tool */}
            <Link
              href="/tools/dmarc-generator"
              className="group block bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
            >
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-[#956b50]/10 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-[#956b50]" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-label="DMARC shield icon">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h2 className="text-lg font-bold text-[#1b130e]">
                    DMARC Generator
                  </h2>
                  <p className="text-sm font-medium text-[#956b50]">
                    Email Policy Protection
                  </p>
                </div>
              </div>

              <p className="text-sm text-[#4a3728] mb-4">
                Create DMARC policy records for email authentication and reporting.
                Protect your domain from spoofing attacks and gain visibility into
                email authentication failures with comprehensive policy management.
              </p>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Policy</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Configuration</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Reports</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Setup</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Alignment</div>
                  <div className="font-semibold text-[#1b130e] text-sm">Advanced</div>
                </div>
                <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-[#4a3728]/10">
                  <div className="text-xs font-medium text-[#4a3728] uppercase tracking-wide mb-1">Validation</div>
                  <div className="font-semibold text-[#1b130e] text-sm">DNS Check</div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-2">
                  <span className="text-xs px-2 py-1 rounded-full bg-[#956b50]/10 text-[#956b50] border border-[#956b50]/20">
                    Protection
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-[#66b077]/10 text-[#66b077] border border-[#66b077]/20">
                    Reporting
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-[#f59e0b]/10 text-[#f59e0b] border border-[#f59e0b]/20">
                    Free
                  </span>
                </div>
                <div className="flex items-center text-sm font-medium group-hover:translate-x-1 transition-transform duration-200 text-[#1b130e]">
                  <span>Create Policy</span>
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>
          </div>

          {/* Features Overview Section */}
          <div className="mb-16 bg-[#66b077]/5 backdrop-blur-sm border border-[#66b077]/30 rounded-2xl p-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4" style={{ color: '#1b130e' }}>
                Why Choose VanishPost Tools?
              </h2>
              <p className="text-lg max-w-2xl mx-auto leading-relaxed" style={{ color: '#4a3728' }}>
                Professional-grade email tools designed for privacy, security, and deliverability optimization.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Privacy First */}
              <div className="text-center bg-white/80 backdrop-blur-sm border border-[#66b077]/30 rounded-2xl p-4 shadow-sm hover:shadow-lg transition-all duration-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-[#66b077]/20 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-label="Privacy icon">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-[#1b130e]">Privacy First</h3>
                </div>
                <p className="text-sm leading-relaxed text-[#4a3728]">
                  No registration required. Your data is automatically deleted after use.
                  We prioritize your privacy and security above all else.
                </p>
              </div>

              {/* Professional Grade */}
              <div className="text-center bg-white/80 backdrop-blur-sm border border-[#66b077]/30 rounded-2xl p-4 shadow-sm hover:shadow-lg transition-all duration-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-[#66b077]/15 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-label="Professional icon">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-[#1b130e]">Professional Grade</h3>
                </div>
                <p className="text-sm leading-relaxed text-[#4a3728]">
                  Enterprise-level tools with industry-standard protocols.
                  Trusted by developers, marketers, and system administrators worldwide.
                </p>
              </div>

              {/* Always Free */}
              <div className="text-center bg-white/80 backdrop-blur-sm border border-[#66b077]/30 rounded-2xl p-4 shadow-sm hover:shadow-lg transition-all duration-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-[#66b077]/15 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-label="Free icon">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-[#1b130e]">Always Free</h3>
                </div>
                <p className="text-sm leading-relaxed text-[#4a3728]">
                  All tools are completely free to use with no hidden costs,
                  subscriptions, or limitations. Professional tools for everyone.
                </p>
              </div>
            </div>
          </div>

          {/* SEO Content Section */}
          <div className="mb-16">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold mb-8 text-center" style={{ color: '#1b130e' }}>
                About Our Email Tools & Services
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm hover:shadow-lg transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-8 h-8 bg-[#956b50]/10 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-[#956b50]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-[#1b130e]">
                      Email Deliverability Testing
                    </h3>
                  </div>
                  <p className="text-sm leading-relaxed mb-4 text-[#4a3728]">
                    Our Email Tester Tool provides comprehensive analysis of your email server's deliverability
                    by validating critical authentication protocols including SPF (Sender Policy Framework),
                    DKIM (DomainKeys Identified Mail), and DMARC (Domain-based Message Authentication, Reporting, and Conformance).
                  </p>
                  <ul className="text-sm space-y-3 text-[#4a3728]">
                    <li className="flex items-start">
                      <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 bg-[#66b077]"></span>
                      <span><strong>SPF Records:</strong> Verify authorized sending servers and IP addresses</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 bg-[#66b077]"></span>
                      <span><strong>DKIM Signatures:</strong> Validate email integrity and authenticity</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 bg-[#66b077]"></span>
                      <span><strong>DMARC Policy:</strong> Protect against email spoofing and phishing attacks</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 bg-[#66b077]"></span>
                      <span><strong>AI Recommendations:</strong> Get personalized improvement suggestions</span>
                    </li>
                  </ul>
                </div>
                <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm hover:shadow-lg transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-8 h-8 bg-[#66b077]/10 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-[#1b130e]">
                      Temporary Email Privacy Protection
                    </h3>
                  </div>
                  <p className="text-sm leading-relaxed mb-4 text-[#4a3728]">
                    VanishPost's core temporary email service provides instant privacy protection by generating
                    secure email addresses that automatically expire in 15 minutes. Perfect for protecting your
                    primary inbox from spam, testing applications, or maintaining anonymity online.
                  </p>
                  <ul className="text-sm space-y-3 text-[#4a3728]">
                    <li className="flex items-start">
                      <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 bg-[#66b077]"></span>
                      <span><strong>Instant Generation:</strong> Create email addresses in seconds</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 bg-[#66b077]"></span>
                      <span><strong>Auto-Expiry:</strong> Addresses automatically delete after 15 minutes</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 bg-[#66b077]"></span>
                      <span><strong>No Registration:</strong> Use immediately without creating accounts</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0 bg-[#66b077]"></span>
                      <span><strong>Privacy First:</strong> No tracking, no data collection, no storage</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action Section */}
          <div className="text-center bg-[#66b077]/5 backdrop-blur-sm border border-[#66b077]/30 rounded-2xl p-8 shadow-sm">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-[#66b077]/20 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-[#66b077]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-[#1b130e]">
                Ready to Get Started?
              </h2>
            </div>
            <p className="text-sm mb-6 max-w-2xl mx-auto text-[#4a3728]">
              Choose the tool that best fits your needs. All our services are free, secure, and ready to use immediately.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center flex-wrap">
              <Link
                href="/"
                className="inline-flex items-center justify-center px-4 py-2 border-2 border-[#66b077] text-[#66b077] font-semibold rounded-full transition-all duration-200 hover:bg-[#66b077] hover:text-white hover:scale-105 hover:shadow-lg"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                Temporary Email
              </Link>
              <Link
                href="/tools/email-tester"
                className="inline-flex items-center justify-center px-4 py-2 border-2 border-[#66b077] text-[#66b077] font-semibold rounded-full transition-all duration-200 hover:bg-[#66b077] hover:text-white hover:scale-105 hover:shadow-lg"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Email Tester
              </Link>
              <Link
                href="/tools/dkim-generator"
                className="inline-flex items-center justify-center px-4 py-2 border-2 border-[#66b077] text-[#66b077] font-semibold rounded-full transition-all duration-200 hover:bg-[#66b077] hover:text-white hover:scale-105 hover:shadow-lg"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
                DKIM Generator
              </Link>
              <Link
                href="/tools/dmarc-generator"
                className="inline-flex items-center justify-center px-4 py-2 border-2 border-[#66b077] text-[#66b077] font-semibold rounded-full transition-all duration-200 hover:bg-[#66b077] hover:text-white hover:scale-105 hover:shadow-lg"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                DMARC Generator
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
