'use client';

/**
 * EmailSkeleton Component
 *
 * Displays a loading skeleton for emails while content is being loaded
 */
export default function EmailSkeleton() {
  return (
    <div className="animate-pulse">
      {/* Email Card Skeleton */}
      {[...Array(5)].map((_, index) => (
        <div key={index} className="p-4 mx-1.5 my-1.5 rounded-xl bg-white border border-neutral-100">
          <div className="flex justify-between items-start">
            <div className="flex items-center">
              <div className="w-2.5 h-2.5 bg-neutral-200 rounded-full mr-2"></div>
              <div className="h-4 bg-neutral-200 rounded-lg w-32"></div>
            </div>
            <div className="h-3 bg-neutral-200 rounded-lg w-16"></div>
          </div>
          <div className="h-4 bg-neutral-200 rounded-lg w-3/4 mt-2"></div>
          <div className="h-3 bg-neutral-200 rounded-lg w-full mt-2"></div>
          <div className="h-3 bg-neutral-200 rounded-lg w-1/4 mt-2"></div>
        </div>
      ))}
    </div>
  );
}

/**
 * EmailContentSkeleton Component
 *
 * Displays a loading skeleton for email content while it's being loaded
 */
export function EmailContentSkeleton() {
  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Mobile back button skeleton */}
      <div className="md:hidden p-3 border-b border-neutral-100">
        <div className="h-8 bg-neutral-200 rounded-lg w-24"></div>
      </div>

      {/* Email header skeleton */}
      <div className="px-6 sm:px-8 py-4 sm:py-6 border-b border-neutral-100">
        <div className="flex justify-between items-start mb-2">
          <div className="h-6 bg-neutral-200 rounded-lg w-48 mb-3"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-24"></div>
        </div>
        <div className="space-y-2">
          <div className="h-4 bg-neutral-200 rounded-lg w-32"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-40"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-36"></div>
        </div>
      </div>

      {/* Email content skeleton */}
      <div className="px-6 sm:px-8 py-5 sm:py-6 animate-pulse">
        {/* Content */}
        <div className="space-y-4">
          <div className="h-4 bg-neutral-200 rounded-lg w-full"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-full"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-3/4"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-5/6"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-full"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-full"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-4/5"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-full"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-3/4"></div>
        </div>

        {/* Attachments skeleton */}
        <div className="mt-8">
          <div className="h-5 bg-neutral-200 rounded-lg w-40 mb-4"></div>
          <div className="flex items-center p-4 border border-neutral-200 rounded-xl mb-2 bg-neutral-50/50">
            <div className="h-10 w-10 bg-neutral-200 rounded-lg mr-4"></div>
            <div>
              <div className="h-4 bg-neutral-200 rounded-lg w-32 mb-2"></div>
              <div className="h-3 bg-neutral-200 rounded-lg w-16"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
