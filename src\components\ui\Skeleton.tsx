'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * shadcn/ui Skeleton Component adapted for VanishPost
 * 
 * A modern skeleton loader component that provides better loading UX
 * compared to traditional spinners. Adapted to work with VanishPost's theme.
 */
function Skeleton({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="skeleton"
      className={cn(
        "bg-gray-200 animate-pulse rounded-md",
        "dark:bg-gray-700", // Dark mode support
        className
      )}
      {...props}
    />
  );
}

Skeleton.displayName = "Skeleton";

/**
 * Skeleton variants for common use cases in VanishPost admin
 */

// Avatar skeleton for user profiles
export function SkeletonAvatar({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <Skeleton
      className={cn("h-10 w-10 rounded-full", className)}
      {...props}
    />
  );
}

// Text line skeleton
export function SkeletonText({ 
  className, 
  width = "100%",
  ...props 
}: React.ComponentProps<"div"> & { width?: string }) {
  return (
    <Skeleton
      className={cn("h-4", className)}
      style={{ width }}
      {...props}
    />
  );
}

// Button skeleton
export function SkeletonButton({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <Skeleton
      className={cn("h-10 w-24 rounded-md", className)}
      {...props}
    />
  );
}

// Card skeleton for admin cards
export function SkeletonCard({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div className={cn("space-y-3 p-4 border rounded-lg", className)} {...props}>
      <div className="space-y-2">
        <SkeletonText width="60%" />
        <SkeletonText width="80%" />
        <SkeletonText width="40%" />
      </div>
    </div>
  );
}

// Table row skeleton for admin tables
export function SkeletonTableRow({ 
  columns = 4, 
  className, 
  ...props 
}: React.ComponentProps<"div"> & { columns?: number }) {
  return (
    <div className={cn("flex space-x-4 p-4", className)} {...props}>
      {Array.from({ length: columns }).map((_, i) => (
        <SkeletonText key={i} width={i === 0 ? "30%" : "20%"} />
      ))}
    </div>
  );
}

// Loading state for admin dashboard stats
export function SkeletonStat({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div className={cn("space-y-2 p-4 border rounded-lg", className)} {...props}>
      <SkeletonText width="50%" className="h-3" />
      <Skeleton className="h-8 w-16" />
      <SkeletonText width="70%" className="h-3" />
    </div>
  );
}

export { Skeleton };
