import { NextRequest, NextResponse } from 'next/server';
import {
  getAllDatabaseConfigurations,
  setActiveConfiguration,
  deleteDatabaseConfiguration,
  createDatabaseConfiguration,
  updateDatabaseConfiguration
} from '@/lib/services/databaseConfigService';
import { resetConnections } from '@/lib/db/connectionManager';

/**
 * GET /api/management-portal-x7z9y2/database-config
 *
 * Get all database configurations
 */
export async function GET(request: NextRequest) {
  try {
    const configs = await getAllDatabaseConfigurations();
    return NextResponse.json(configs);
  } catch (error) {
    console.error('Error fetching database configurations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/management-portal-x7z9y2/database-config
 *
 * Create a new database configuration
 */
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const result = await createDatabaseConfiguration(data);

    if (result.success) {
      return NextResponse.json({ success: true, id: result.id });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error creating database configuration:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/management-portal-x7z9y2/database-config
 *
 * Update an existing database configuration
 */
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json();
    const result = await updateDatabaseConfiguration(data);

    if (result.success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error updating database configuration:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/management-portal-x7z9y2/database-config
 *
 * Delete a database configuration
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'ID is required' },
        { status: 400 }
      );
    }

    const result = await deleteDatabaseConfiguration(parseInt(id));

    if (result.success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error deleting database configuration:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
