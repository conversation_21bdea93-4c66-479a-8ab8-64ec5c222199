/**
 * Tests for SVG Path Validation Utilities
 */
import { validateSVGPath, fixSVGPath, validateAndFixSVGPath } from '../svgValidator';

describe('SVG Path Validator', () => {
  describe('validateSVGPath', () => {
    it('should detect malformed arc commands', () => {
      // This is the original broken path from DetailedResults.tsx
      const brokenPath = "M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z";

      const result = validateSVGPath(brokenPath);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(error => error.includes('1721'))).toBe(true);
    });

    it('should validate correct arc commands', () => {
      // This is the fixed path
      const correctPath = "M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 1 21 9z";

      const result = validateSVGPath(correctPath);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate simple paths without arc commands', () => {
      const simplePath = "M10 10L20 20L30 10Z";

      const result = validateSVGPath(simplePath);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid characters', () => {
      const invalidPath = "M10 10X20 20";

      const result = validateSVGPath(invalidPath);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('invalid characters'))).toBe(true);
    });
  });

  describe('fixSVGPath', () => {
    it('should attempt to fix malformed arc commands with valid flags', () => {
      // Use a path with valid flags that are just concatenated (4+ digits to match pattern)
      const brokenPath = "M10 10A6 6 0 11021 9z";

      const fixed = fixSVGPath(brokenPath);

      expect(fixed).toContain('A6 6 0 1 1 021 9z');
      expect(fixed).not.toContain('A6 6 0 11021 9z');
    });

    it('should not fix malformed arc commands with invalid flags', () => {
      // The original broken path has invalid flag '7' so it cannot be auto-fixed
      const brokenPath = "M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z";

      const fixed = fixSVGPath(brokenPath);

      // Should remain unchanged because '7' is not a valid flag
      expect(fixed).toContain('A6 6 0 1721 9z');
    });

    it('should not modify correct paths', () => {
      const correctPath = "M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 1 21 9z";

      const fixed = fixSVGPath(correctPath);

      expect(fixed).toBe(correctPath);
    });
  });

  describe('validateAndFixSVGPath', () => {
    it('should validate and fix paths with valid concatenated flags', () => {
      const brokenPath = "M10 10A6 6 0 11021 9z";

      const result = validateAndFixSVGPath(brokenPath);

      expect(result.original).toBe(brokenPath);
      expect(result.fixed).toContain('A6 6 0 1 1 021 9z');
      expect(result.wasFixed).toBe(true);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect but not fix paths with invalid flags', () => {
      const brokenPath = "M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z";

      const result = validateAndFixSVGPath(brokenPath);

      expect(result.original).toBe(brokenPath);
      expect(result.fixed).toBe(brokenPath); // Should remain unchanged
      expect(result.wasFixed).toBe(false);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle already correct paths', () => {
      const correctPath = "M10 10L20 20Z";

      const result = validateAndFixSVGPath(correctPath);

      expect(result.original).toBe(correctPath);
      expect(result.fixed).toBe(correctPath);
      expect(result.wasFixed).toBe(false);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });
});
