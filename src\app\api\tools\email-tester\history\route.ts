/**
 * API Route for Retrieving Email Test History
 *
 * This route retrieves the history of email tests.
 *
 * GET /api/tools/email-tester/history
 */

import { NextRequest, NextResponse } from 'next/server';
import { getEmailTesterHistory } from '@/lib/tools/email-tester/database';
import { getCurrentSessionId } from '@/lib/tools/email-tester/sessionManager';
import { logError } from '@/lib/logging';

export async function GET(request: NextRequest) {
  try {
    // Get current session ID for access control
    const sessionId = await getCurrentSessionId();

    if (!sessionId) {
      return NextResponse.json({
        success: true,
        history: [] // Return empty history if no session
      });
    }

    // Get limit from query parameters, default to 20
    const limit = parseInt(request.nextUrl.searchParams.get('limit') || '20', 10);

    // Get the test history from Supabase filtered by session
    const history = await getEmailTesterHistory(limit, sessionId);

    return NextResponse.json({
      success: true,
      history
    });
  } catch (error) {
    logError('API', 'Failed to retrieve test history', error);
    return NextResponse.json(
      { success: false, message: 'Failed to retrieve test history' },
      { status: 500 }
    );
  }
}
