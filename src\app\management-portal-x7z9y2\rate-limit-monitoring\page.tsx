'use client';

import RateLimitMonitoring from '@/components/admin/RateLimitMonitoring';

/**
 * Rate Limit Monitoring Page for Secure Admin Portal
 */
export default function RateLimitMonitoringPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Rate Limit Monitoring</h1>
        <p className="mt-2 text-sm text-gray-600">
          Real-time monitoring of rate limits, violations, and security threats across all VanishPost endpoints.
        </p>
      </div>

      <RateLimitMonitoring />
    </div>
  );
}
