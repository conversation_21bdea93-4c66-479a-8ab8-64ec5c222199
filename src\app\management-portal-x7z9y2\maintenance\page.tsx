'use client';

import MaintenanceManager from '@/components/admin/MaintenanceManager';
import { Card, CardContent } from '@/components/ui/Card';

/**
 * Admin Database Maintenance Page
 */
export default function MaintenancePage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z" clipRule="evenodd" />
          </svg>
          <h1 className="text-2xl font-bold text-gray-900">Database Maintenance</h1>
        </div>
        <p className="mt-2 text-sm text-gray-600 ml-11">
          Optimize database performance by running maintenance operations to reclaim space and update statistics.
        </p>
      </div>

      <MaintenanceManager />
    </div>
  );
}
