# Fademail Admin Interface Troubleshooting Guide

This guide provides solutions to common issues you might encounter when using the Fademail admin interface.

## Table of Contents

1. [Authentication Issues](#authentication-issues)
2. [Configuration Management Issues](#configuration-management-issues)
3. [Domain Management Issues](#domain-management-issues)
4. [Ad Management Issues](#ad-management-issues)
5. [Analytics Issues](#analytics-issues)
6. [Cleanup Issues](#cleanup-issues)
7. [Database Connection Issues](#database-connection-issues)
8. [API Errors](#api-errors)
9. [Rollback and Recovery](#rollback-and-recovery)

## Authentication Issues

### Cannot Log In to Admin Interface

**Symptoms:**
- Login form submission fails
- "Invalid credentials" error message appears

**Possible Causes:**
1. Incorrect username or password
2. Authentication service is down
3. Database connection issues

**Solutions:**
1. Double-check your username and password
2. Check the system logs for authentication service errors
3. Verify database connectivity using the Health Dashboard
4. Reset your password using the admin password reset tool:
   ```bash
   node scripts/reset-admin-password.js --username=admin --new-password=newpassword
   ```

### Session Expires Too Quickly

**Symptoms:**
- Frequent redirects to the login page
- "Session expired" messages

**Possible Causes:**
1. Short session timeout setting
2. Browser cookie issues
3. Server time synchronization issues

**Solutions:**
1. Increase the session timeout in the configuration:
   ```
   sessionTimeoutMinutes: 60
   ```
2. Clear browser cookies and cache
3. Check server time synchronization

## Configuration Management Issues

### Configuration Changes Not Saving

**Symptoms:**
- Success message appears, but changes don't persist
- Changes revert after page refresh

**Possible Causes:**
1. Database write permission issues
2. Validation errors
3. Cache inconsistency

**Solutions:**
1. Check database permissions
2. Review validation rules in the production safeguards
3. Clear the configuration cache:
   ```
   POST /api/admin/config/clear-cache
   ```
4. Check system logs for specific error messages

### Invalid Configuration Values

**Symptoms:**
- Error messages when saving configuration
- "Invalid value" warnings

**Possible Causes:**
1. Value outside allowed range
2. Incorrect data type
3. Missing required fields

**Solutions:**
1. Review the validation rules for each configuration field
2. Use the validation API to pre-validate values:
   ```
   POST /api/admin/safeguards/validate/config
   {
     "key": "emailExpirationMinutes",
     "value": 30
   }
   ```
3. Reset to default values if needed

## Domain Management Issues

### Cannot Add New Domain

**Symptoms:**
- Error when submitting the domain form
- Domain not appearing in the list after addition

**Possible Causes:**
1. Invalid domain format
2. Domain already exists
3. Database connection issues

**Solutions:**
1. Ensure the domain follows the correct format (e.g., example.com)
2. Check if the domain already exists in the list
3. Verify database connectivity
4. Use the validation API to check domain validity:
   ```
   POST /api/admin/safeguards/validate/domain
   {
     "domain": "example.com",
     "isActive": true,
     "settings": {
       "weight": 50,
       "features": {
         "adsEnabled": true
       }
     }
   }
   ```

### Domain Rotation Not Working

**Symptoms:**
- Always getting the same domain for new email addresses
- Some domains never being used

**Possible Causes:**
1. Incorrect weight settings
2. Only one domain is active
3. Domain rotation logic issue

**Solutions:**
1. Check domain weights in the settings
2. Ensure multiple domains are marked as active
3. Verify domain rotation logic in the logs
4. Reset domain weights to be more balanced

## Ad Management Issues

### Ads Not Displaying

**Symptoms:**
- Ad placements are empty
- No ad content loading

**Possible Causes:**
1. Ad units disabled
2. Incorrect ad unit IDs
3. Device type mismatch
4. Ad blocker interference

**Solutions:**
1. Check if ads are enabled for the domain
2. Verify ad unit IDs are correct
3. Ensure device types are properly configured
4. Test in an incognito window without ad blockers
5. Check browser console for ad-related errors

### Ad Performance Data Missing

**Symptoms:**
- Empty charts in the ad performance dashboard
- "No data available" messages

**Possible Causes:**
1. No ad impressions or clicks recorded
2. Analytics service issues
3. Date range too narrow

**Solutions:**
1. Ensure ads are being displayed and tracked
2. Check analytics service connectivity
3. Expand the date range for the performance data
4. Clear the ad performance cache:
   ```
   POST /api/admin/ad-performance/flush-cache
   ```

## Analytics Issues

### Missing Analytics Data

**Symptoms:**
- Empty charts or tables
- "No data" messages

**Possible Causes:**
1. Analytics tracking not enabled
2. Database connection issues
3. Date range selection issues

**Solutions:**
1. Verify analytics tracking is enabled
2. Check database connectivity
3. Adjust the date range selection
4. Reset analytics data collection:
   ```
   POST /api/admin/analytics/reset-collection
   ```

### Incorrect Analytics Numbers

**Symptoms:**
- Unrealistic visitor counts
- Mismatched metrics between different reports

**Possible Causes:**
1. Double-counting issues
2. Bot traffic included
3. Caching problems

**Solutions:**
1. Enable bot filtering in the analytics settings
2. Clear the analytics cache
3. Verify tracking code implementation
4. Compare with external analytics if available

## Cleanup Issues

### Expired Emails Not Being Cleaned Up

**Symptoms:**
- Expired email addresses still accessible
- Growing database size

**Possible Causes:**
1. Cleanup scheduler not running
2. Incorrect expiration time setting
3. Database permission issues

**Solutions:**
1. Check cleanup scheduler status:
   ```
   GET /api/admin/cleanup/status
   ```
2. Verify email expiration time setting
3. Manually trigger cleanup:
   ```
   POST /api/admin/cleanup
   ```
4. Check system logs for cleanup errors

### Cleanup Taking Too Long

**Symptoms:**
- Cleanup operations timing out
- High server load during cleanup

**Possible Causes:**
1. Too many expired emails
2. Inefficient cleanup query
3. Database performance issues

**Solutions:**
1. Reduce the cleanup batch size:
   ```
   cleanupBatchSize: 1000
   ```
2. Schedule cleanup during off-peak hours
3. Optimize database indexes
4. Consider more frequent, smaller cleanups

## Database Connection Issues

### Database Connection Failures

**Symptoms:**
- "Database connection error" messages
- Features not working across the admin interface

**Possible Causes:**
1. Database server down
2. Incorrect connection credentials
3. Network issues

**Solutions:**
1. Check database server status
2. Verify connection credentials in environment variables
3. Test database connection:
   ```
   GET /api/admin/health
   ```
4. Check system logs for specific database errors

### Slow Database Performance

**Symptoms:**
- Admin interface loading slowly
- Operations timing out

**Possible Causes:**
1. Missing database indexes
2. Large tables without proper pagination
3. Inefficient queries

**Solutions:**
1. Review and optimize database indexes
2. Implement pagination for large data sets
3. Optimize query patterns
4. Consider database scaling if needed

## API Errors

### API Returning Errors

**Symptoms:**
- Error messages in the admin interface
- Failed operations

**Possible Causes:**
1. Invalid request parameters
2. Server-side errors
3. Authentication issues

**Solutions:**
1. Check browser console for detailed error messages
2. Review API request parameters
3. Verify authentication status
4. Check system logs for API errors

### Rate Limiting Issues

**Symptoms:**
- "Too many requests" errors
- Operations failing during high activity

**Possible Causes:**
1. Exceeding API rate limits
2. DDoS protection triggered
3. Server resource constraints

**Solutions:**
1. Implement request throttling in the client
2. Increase rate limits if appropriate
3. Optimize API usage patterns
4. Consider server scaling if needed

## Rollback and Recovery

### Recovering from Bad Configuration

**Symptoms:**
- System not working after configuration changes
- Critical features broken

**Solutions:**
1. View available configuration backups:
   ```
   GET /api/admin/safeguards/backups
   ```
2. Restore from a backup:
   ```
   PUT /api/admin/safeguards/restore
   {
     "backupId": "backup-1234567890"
   }
   ```
3. If no backups are available, reset to default configuration:
   ```
   POST /api/admin/config/reset-defaults
   ```

### Creating Manual Backups

It's a good practice to create manual backups before making significant changes:

1. Create a backup:
   ```
   POST /api/admin/safeguards/backup
   ```
2. Note the backup ID returned in the response
3. Make your changes
4. If issues occur, restore from the backup

## Additional Help

If you continue to experience issues not covered in this guide:

1. Check the system logs for detailed error messages
2. Review the full documentation
3. Contact support with the following information:
   - Detailed description of the issue
   - Steps to reproduce
   - Error messages
   - System logs
   - Browser and operating system information
