// DMARC Generator Test Suite
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { 
  generateDmarcRecord, 
  parseDmarcRecord, 
  validateDmarcRecordFormat,
  generateDmarcInstructions 
} from '@/lib/tools/dmarc-generator/recordGeneration';
import { validateDmarcRecord } from '@/lib/tools/shared/dnsValidation';
import { DmarcPolicyConfig, DmarcPolicy, DmarcAlignment } from '@/types/dmarc';

// Mock DNS resolution for testing
jest.mock('dns', () => ({
  promises: {
    resolveTxt: jest.fn(),
  },
}));

describe('DMARC Generator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Record Generation', () => {
    it('should generate basic DMARC record with none policy', () => {
      const config: DmarcPolicyConfig = {
        domain: 'example.com',
        policy: 'none' as DmarcPolicy,
        rua: 'mailto:<EMAIL>',
        pct: 100
      };

      const record = generateDmarcRecord(config);
      
      expect(record.recordName).toBe('_dmarc.example.com');
      expect(record.dnsRecord).toContain('v=DMARC1');
      expect(record.dnsRecord).toContain('p=none');
      expect(record.dnsRecord).toContain('rua=mailto:<EMAIL>');
      expect(record.dnsRecord).toContain('pct=100');
    });

    it('should generate DMARC record with quarantine policy', () => {
      const config: DmarcPolicyConfig = {
        domain: 'example.com',
        policy: 'quarantine' as DmarcPolicy,
        rua: 'mailto:<EMAIL>',
        pct: 50,
        sp: 'reject' as DmarcPolicy,
        adkim: 's' as DmarcAlignment,
        aspf: 'r' as DmarcAlignment
      };

      const record = generateDmarcRecord(config);
      
      expect(record.dnsRecord).toContain('p=quarantine');
      expect(record.dnsRecord).toContain('sp=reject');
      expect(record.dnsRecord).toContain('adkim=s');
      expect(record.dnsRecord).toContain('aspf=r');
      expect(record.dnsRecord).toContain('pct=50');
    });

    it('should generate DMARC record with reject policy', () => {
      const config: DmarcPolicyConfig = {
        domain: 'secure.example.com',
        policy: 'reject' as DmarcPolicy,
        rua: 'mailto:<EMAIL>',
        pct: 100,
        ruf: 'mailto:<EMAIL>'
      };

      const record = generateDmarcRecord(config);
      
      expect(record.dnsRecord).toContain('p=reject');
      expect(record.dnsRecord).toContain('ruf=mailto:<EMAIL>');
      expect(record.dnsRecord).toContain('pct=100');
    });

    it('should handle multiple RUA addresses', () => {
      const config: DmarcPolicyConfig = {
        domain: 'example.com',
        policy: 'none' as DmarcPolicy,
        rua: 'mailto:<EMAIL>,mailto:<EMAIL>',
        pct: 100
      };

      const record = generateDmarcRecord(config);
      
      expect(record.dnsRecord).toContain('rua=mailto:<EMAIL>,mailto:<EMAIL>');
    });
  });

  describe('Record Parsing', () => {
    it('should parse basic DMARC record correctly', () => {
      const dmarcRecord = 'v=DMARC1; p=none; rua=mailto:<EMAIL>; pct=100';
      
      const parsed = parseDmarcRecord(dmarcRecord);
      
      expect(parsed.policy).toBe('none');
      expect(parsed.rua).toBe('mailto:<EMAIL>');
      expect(parsed.pct).toBe(100);
    });

    it('should parse complex DMARC record with all tags', () => {
      const dmarcRecord = 'v=DMARC1; p=quarantine; sp=reject; adkim=s; aspf=r; pct=50; rua=mailto:<EMAIL>; ruf=mailto:<EMAIL>';
      
      const parsed = parseDmarcRecord(dmarcRecord);
      
      expect(parsed.policy).toBe('quarantine');
      expect(parsed.sp).toBe('reject');
      expect(parsed.adkim).toBe('s');
      expect(parsed.aspf).toBe('r');
      expect(parsed.pct).toBe(50);
      expect(parsed.rua).toBe('mailto:<EMAIL>');
      expect(parsed.ruf).toBe('mailto:<EMAIL>');
    });

    it('should handle malformed DMARC records gracefully', () => {
      const malformedRecord = 'v=DMARC1; p=invalid; malformed=tag';
      
      const parsed = parseDmarcRecord(malformedRecord);
      
      expect(parsed.policy).toBeUndefined(); // Invalid policy should be undefined
      expect(parsed.domain).toBeUndefined();
    });

    it('should parse records with extra whitespace', () => {
      const dmarcRecord = ' v=DMARC1 ;  p=none  ; rua=mailto:<EMAIL> ; pct=100 ';
      
      const parsed = parseDmarcRecord(dmarcRecord);
      
      expect(parsed.policy).toBe('none');
      expect(parsed.rua).toBe('mailto:<EMAIL>');
      expect(parsed.pct).toBe(100);
    });
  });

  describe('Record Validation', () => {
    it('should validate correct DMARC record format', () => {
      const validRecord = 'v=DMARC1; p=none; rua=mailto:<EMAIL>; pct=100';
      
      const isValid = validateDmarcRecordFormat(validRecord);
      
      expect(isValid).toBe(true);
    });

    it('should reject records without version tag', () => {
      const invalidRecord = 'p=none; rua=mailto:<EMAIL>; pct=100';
      
      const isValid = validateDmarcRecordFormat(invalidRecord);
      
      expect(isValid).toBe(false);
    });

    it('should reject records without policy tag', () => {
      const invalidRecord = 'v=DMARC1; rua=mailto:<EMAIL>; pct=100';
      
      const isValid = validateDmarcRecordFormat(invalidRecord);
      
      expect(isValid).toBe(false);
    });

    it('should reject records with invalid policy values', () => {
      const invalidRecord = 'v=DMARC1; p=invalid; rua=mailto:<EMAIL>; pct=100';
      
      const isValid = validateDmarcRecordFormat(invalidRecord);
      
      expect(isValid).toBe(false);
    });

    it('should validate records with optional tags', () => {
      const validRecord = 'v=DMARC1; p=quarantine; sp=reject; adkim=s; aspf=r; pct=50; rua=mailto:<EMAIL>; ruf=mailto:<EMAIL>';
      
      const isValid = validateDmarcRecordFormat(validRecord);
      
      expect(isValid).toBe(true);
    });
  });

  describe('DNS Validation', () => {
    it('should validate existing DMARC records', async () => {
      const mockDnsResponse = [
        ['v=DMARC1; p=none; rua=mailto:<EMAIL>; pct=100']
      ];
      
      const dns = require('dns');
      dns.promises.resolveTxt.mockResolvedValue(mockDnsResponse);

      const result = await validateDmarcRecord('example.com', 'none');
      
      expect(result.exists).toBe(true);
      expect(result.isValid).toBe(true);
      expect(result.records).toEqual(['v=DMARC1; p=none; rua=mailto:<EMAIL>; pct=100']);
    });

    it('should handle DNS resolution failures gracefully', async () => {
      const dns = require('dns');
      dns.promises.resolveTxt.mockRejectedValue(new Error('NXDOMAIN'));

      const result = await validateDmarcRecord('nonexistent.com', 'none');
      
      expect(result.exists).toBe(false);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('DNS record not found');
    });

    it('should detect policy mismatches', async () => {
      const mockDnsResponse = [
        ['v=DMARC1; p=reject; rua=mailto:<EMAIL>; pct=100']
      ];
      
      const dns = require('dns');
      dns.promises.resolveTxt.mockResolvedValue(mockDnsResponse);

      const result = await validateDmarcRecord('example.com', 'none');
      
      expect(result.exists).toBe(true);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Policy mismatch');
    });

    it('should validate records without expected policy', async () => {
      const mockDnsResponse = [
        ['v=DMARC1; p=quarantine; rua=mailto:<EMAIL>; pct=100']
      ];
      
      const dns = require('dns');
      dns.promises.resolveTxt.mockResolvedValue(mockDnsResponse);

      const result = await validateDmarcRecord('example.com');
      
      expect(result.exists).toBe(true);
      expect(result.isValid).toBe(true);
      expect(result.records).toEqual(['v=DMARC1; p=quarantine; rua=mailto:<EMAIL>; pct=100']);
    });
  });

  describe('Instructions Generation', () => {
    it('should generate comprehensive setup instructions', () => {
      const config: DmarcPolicyConfig = {
        domain: 'example.com',
        policy: 'none' as DmarcPolicy,
        rua: 'mailto:<EMAIL>',
        pct: 100
      };

      const record = generateDmarcRecord(config);
      const instructions = generateDmarcInstructions(record);
      
      expect(instructions).toBeInstanceOf(Array);
      expect(instructions.length).toBeGreaterThan(0);
      expect(instructions.some(instruction => instruction.includes('DNS'))).toBe(true);
      expect(instructions.some(instruction => instruction.includes('_dmarc'))).toBe(true);
    });

    it('should include policy-specific recommendations', () => {
      const rejectConfig: DmarcPolicyConfig = {
        domain: 'example.com',
        policy: 'reject' as DmarcPolicy,
        rua: 'mailto:<EMAIL>',
        pct: 100
      };

      const record = generateDmarcRecord(rejectConfig);
      const instructions = generateDmarcInstructions(record);
      
      const instructionText = instructions.join(' ');
      expect(instructionText.toLowerCase()).toContain('reject');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid domain formats', () => {
      expect(() => {
        const config: DmarcPolicyConfig = {
          domain: 'invalid..domain',
          policy: 'none' as DmarcPolicy,
          rua: 'mailto:<EMAIL>',
          pct: 100
        };
        generateDmarcRecord(config);
      }).toThrow();
    });

    it('should handle invalid email formats in RUA', () => {
      expect(() => {
        const config: DmarcPolicyConfig = {
          domain: 'example.com',
          policy: 'none' as DmarcPolicy,
          rua: 'invalid-email',
          pct: 100
        };
        generateDmarcRecord(config);
      }).toThrow();
    });

    it('should handle invalid percentage values', () => {
      expect(() => {
        const config: DmarcPolicyConfig = {
          domain: 'example.com',
          policy: 'none' as DmarcPolicy,
          rua: 'mailto:<EMAIL>',
          pct: 150 // Invalid percentage
        };
        generateDmarcRecord(config);
      }).toThrow();
    });
  });

  describe('Performance', () => {
    it('should generate records quickly', () => {
      const config: DmarcPolicyConfig = {
        domain: 'example.com',
        policy: 'none' as DmarcPolicy,
        rua: 'mailto:<EMAIL>',
        pct: 100
      };

      const startTime = Date.now();
      generateDmarcRecord(config);
      const endTime = Date.now();
      
      // Record generation should be nearly instantaneous
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('should handle multiple concurrent generations', () => {
      const configs = Array(10).fill(null).map((_, i) => ({
        domain: `example${i}.com`,
        policy: 'none' as DmarcPolicy,
        rua: `mailto:dmarc@example${i}.com`,
        pct: 100
      }));

      const startTime = Date.now();
      const records = configs.map(config => generateDmarcRecord(config));
      const endTime = Date.now();
      
      expect(records).toHaveLength(10);
      expect(endTime - startTime).toBeLessThan(500);
    });
  });
});

describe('DMARC Integration Tests', () => {
  it('should complete full DMARC generation workflow', () => {
    const domain = 'test.example.com';
    
    // 1. Create configuration
    const config: DmarcPolicyConfig = {
      domain,
      policy: 'quarantine' as DmarcPolicy,
      rua: 'mailto:<EMAIL>',
      pct: 50,
      sp: 'reject' as DmarcPolicy
    };
    
    // 2. Generate DNS record
    const record = generateDmarcRecord(config);
    expect(record.recordName).toBe(`_dmarc.${domain}`);
    expect(record.dnsRecord).toContain('v=DMARC1');
    
    // 3. Validate record format
    const isValid = validateDmarcRecordFormat(record.dnsRecord);
    expect(isValid).toBe(true);
    
    // 4. Parse generated record
    const parsed = parseDmarcRecord(record.dnsRecord);
    expect(parsed.policy).toBe('quarantine');
    expect(parsed.sp).toBe('reject');
    expect(parsed.pct).toBe(50);
    
    // 5. Generate instructions
    const instructions = generateDmarcInstructions(record);
    expect(instructions.length).toBeGreaterThan(0);
  });
});
