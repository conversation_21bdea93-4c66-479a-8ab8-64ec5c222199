// Mock Next.js server components
jest.mock('next/server', () => {
  return {
    NextRequest: jest.fn().mockImplementation(() => ({
      json: jest.fn().mockResolvedValue({}),
      headers: {
        get: jest.fn().mockImplementation((name) => {
          if (name === 'x-forwarded-for') return '127.0.0.1';
          if (name === 'user-agent') return 'Jest Test Agent';
          return null;
        }),
      },
      nextUrl: {
        searchParams: new URLSearchParams(),
      },
    })),
    NextResponse: {
      json: jest.fn().mockImplementation((body, options) => ({
        status: options?.status || 200,
        headers: new Map([['Content-Type', 'application/json']]),
        json: async () => body,
      })),
      redirect: jest.fn().mockImplementation((url) => ({
        status: 307,
        headers: new Map([['Location', url]]),
      })),
    },
  };
});

// Mock Next.js cookies
jest.mock('next/headers', () => {
  return {
    cookies: jest.fn().mockImplementation(() => ({
      get: jest.fn().mockImplementation((name) => {
        if (name === 'admin-token') {
          return { value: 'valid-token' };
        }
        return null;
      }),
      getAll: jest.fn().mockReturnValue([]),
      set: jest.fn(),
      delete: jest.fn(),
    })),
  };
});

// Mock bcrypt
jest.mock('bcrypt', () => ({
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
}));

// Mock date-fns
jest.mock('date-fns', () => ({
  format: jest.fn().mockImplementation(() => 'Jan 1, 2023, 12:00 AM'),
  parseISO: jest.fn().mockImplementation(() => new Date('2023-01-01T00:00:00.000Z')),
}));

// Mock EventSource for SSE
class MockEventSource {
  onmessage = null;
  onerror = null;

  constructor(url) {
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage({
          data: JSON.stringify({
            id: 1,
            level: 'info',
            category: 'API',
            message: 'Test log message',
            timestamp: new Date().toISOString(),
          }),
        });
      }
    }, 100);
  }

  close() {}
}

// Replace the global EventSource with our mock
global.EventSource = MockEventSource;

// Mock ReadableStream for SSE
global.ReadableStream = jest.fn().mockImplementation(() => ({
  getReader: jest.fn().mockReturnValue({
    read: jest.fn().mockResolvedValue({ done: true }),
  }),
}));

// Mock UI components
jest.mock('@/components/ui/Card', () => ({
  Card: jest.fn().mockImplementation(({ children, className }) => (
    <div data-testid="mock-card" className={className}>{children}</div>
  )),
  CardHeader: jest.fn().mockImplementation(({ children }) => (
    <div data-testid="mock-card-header">{children}</div>
  )),
  CardTitle: jest.fn().mockImplementation(({ children, className }) => (
    <div data-testid="mock-card-title" className={className}>{children}</div>
  )),
  CardContent: jest.fn().mockImplementation(({ children }) => (
    <div data-testid="mock-card-content">{children}</div>
  )),
}));

jest.mock('@/components/ui/Button', () => ({
  Button: jest.fn().mockImplementation(({ children, onClick, type, disabled, className, leftIcon }) => (
    <button
      data-testid="mock-button"
      onClick={onClick}
      type={type}
      disabled={disabled}
      className={className}
    >
      {leftIcon && <span data-testid="mock-button-icon">{leftIcon}</span>}
      {children}
    </button>
  )),
}));

jest.mock('@/components/ui/Spinner', () => ({
  Spinner: jest.fn().mockImplementation(({ size }) => (
    <div data-testid="mock-spinner" data-size={size}>Loading...</div>
  )),
}));

jest.mock('@/components/ui/Badge', () => ({
  Badge: jest.fn().mockImplementation(({ children, className }) => (
    <span data-testid="mock-badge" className={className}>{children}</span>
  )),
}));

jest.mock('@/components/ui/Input', () => ({
  Input: jest.fn().mockImplementation(({ id, type, value, onChange, disabled, error, leftIcon }) => (
    <input
      data-testid="mock-input"
      id={id}
      type={type}
      value={value}
      onChange={onChange}
      disabled={disabled}
      aria-invalid={!!error}
    />
  )),
}));

jest.mock('@/components/ui/Select', () => ({
  Select: jest.fn().mockImplementation(({ id, value, onChange, disabled, children }) => (
    <select
      data-testid="mock-select"
      id={id}
      value={value}
      onChange={onChange}
      disabled={disabled}
    >
      {children}
    </select>
  )),
}));

jest.mock('@/components/ui/Checkbox', () => ({
  Checkbox: jest.fn().mockImplementation(({ id, checked, onChange, disabled, label }) => (
    <div data-testid="mock-checkbox">
      <input
        type="checkbox"
        id={id}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
      />
      {label && <label htmlFor={id}>{label}</label>}
    </div>
  )),
}));

// Mock the auth service
jest.mock('@/lib/auth', () => ({
  getCurrentUser: jest.fn().mockReturnValue({
    userId: 1,
    username: 'admin',
    role: 'admin',
  }),
  isAdmin: jest.fn().mockReturnValue(true),
  authenticateUser: jest.fn().mockResolvedValue({
    success: true,
    user: {
      userId: 1,
      username: 'admin',
      role: 'admin',
    },
  }),
  logout: jest.fn().mockResolvedValue({ success: true }),
  verifyAdminAuth: jest.fn().mockResolvedValue({
    success: true,
    user: {
      userId: 1,
      username: 'admin',
      role: 'admin',
    },
  }),
}));

// We don't need to mock the routes directly, as we're testing them

// Mock the dbLogger
jest.mock('@/lib/logging/dbLogger', () => ({
  logger: {
    addListener: jest.fn(),
    removeListener: jest.fn(),
    info: jest.fn().mockResolvedValue(undefined),
    error: jest.fn().mockResolvedValue(undefined),
    warn: jest.fn().mockResolvedValue(undefined),
    debug: jest.fn().mockResolvedValue(undefined),
    critical: jest.fn().mockResolvedValue(undefined),
  }
}));

// Mock the monitoring service
jest.mock('@/lib/logging/realTimeMonitor', () => {
  // Default thresholds
  const defaultThresholds = {
    API: 10,
    DATABASE: 5,
    AUTH: 3,
    EMAIL: 8,
    CLEANUP: 5,
    CONFIG: 3,
    MONITOR: 3,
    ALERT: 3,
  };

  // Default alerts
  const defaultAlerts = [
    {
      category: 'API',
      count: 15,
      threshold: 10,
      timestamp: new Date().toISOString(),
    },
  ];

  // Default logs
  const defaultLogs = [
    {
      id: 1,
      level: 'info',
      category: 'API',
      message: 'Test log message 1',
      timestamp: new Date().toISOString(),
    },
    {
      id: 2,
      level: 'error',
      category: 'DATABASE',
      message: 'Test log message 2',
      timestamp: new Date().toISOString(),
    },
  ];

  // Mock instance
  const mockInstance = {
    cache: {
      get: jest.fn((key) => {
        if (key === 'alertThresholds') return { ...defaultThresholds };
        if (key === 'categoryCounters') return { API: 5, DATABASE: 2, AUTH: 1 };
        if (key === 'alerts') return [...defaultAlerts];
        return null;
      }),
      set: jest.fn(),
      del: jest.fn(),
      flushAll: jest.fn(),
    },
    subscribers: new Map(),
    logs: [...defaultLogs],
    checkAlerts: jest.fn(),
    notifySubscribers: jest.fn(),
    getLatestLogs: jest.fn().mockReturnValue(defaultLogs),
  };

  return {
    getInstance: jest.fn().mockReturnValue(mockInstance),
    getAlertThresholds: jest.fn().mockReturnValue({ ...defaultThresholds }),
    updateAlertThresholds: jest.fn().mockImplementation((newThresholds) => ({
      ...defaultThresholds,
      ...newThresholds,
    })),
    logEvent: jest.fn().mockImplementation((event) => ({
      id: 3,
      ...event,
      timestamp: new Date().toISOString(),
    })),
    getAlerts: jest.fn().mockReturnValue([...defaultAlerts]),
    clearAlerts: jest.fn(),
    getAlertCounts: jest.fn().mockReturnValue({ API: 5, DATABASE: 2, AUTH: 1 }),
    subscribe: jest.fn().mockReturnValue('subscription-id'),
    unsubscribe: jest.fn(),
  };
});
