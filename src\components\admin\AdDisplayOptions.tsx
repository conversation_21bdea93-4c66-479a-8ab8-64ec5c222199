'use client';

import { useState } from 'react';
import { AdConfig, AdDisplayOptions as AdDisplayOptionsType } from '@/lib/config/types';

interface AdDisplayOptionsProps {
  adPlacement: AdConfig;
  onUpdate: (placementId: string, domain: string, updates: any) => Promise<void>;
}

export default function AdDisplayOptions({ adPlacement, onUpdate }: AdDisplayOptionsProps) {
  const [displayMode, setDisplayMode] = useState<string>(
    adPlacement.displayOptions?.displayMode || 'always'
  );
  const [delaySeconds, setDelaySeconds] = useState<number>(
    adPlacement.displayOptions?.delaySeconds || 0
  );
  const [idleTimeSeconds, setIdleTimeSeconds] = useState<number>(
    adPlacement.displayOptions?.idleThresholdSeconds || 30
  );
  const [dismissible, setDismissible] = useState<boolean>(
    adPlacement.displayOptions?.dismissible || false
  );
  const [actionTrigger, setActionTrigger] = useState<string>(
    adPlacement.displayOptions?.triggerAction || 'none'
  );
  const [displayOptions, setDisplayOptions] = useState<any>(
    adPlacement.displayOptions || {}
  );
  const [saving, setSaving] = useState(false);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);

      // Build display options based on display mode
      const updatedDisplayOptions: any = {
        displayMode
      };

      // Add mode-specific options
      switch (displayMode) {
        case 'timed':
          updatedDisplayOptions.delaySeconds = delaySeconds;
          break;
        case 'idle':
          updatedDisplayOptions.idleThresholdSeconds = idleTimeSeconds;
          break;
        case 'action':
          updatedDisplayOptions.triggerAction = actionTrigger;
          break;
        case 'collapsible':
          // Add collapsible-specific options
          if (displayOptions.initiallyExpanded !== undefined) {
            updatedDisplayOptions.initiallyExpanded = displayOptions.initiallyExpanded;
          }
          if (displayOptions.rememberState !== undefined) {
            updatedDisplayOptions.rememberState = displayOptions.rememberState;
          }
          if (displayOptions.expandAfterMinutes !== undefined) {
            updatedDisplayOptions.expandAfterMinutes = displayOptions.expandAfterMinutes;
          }
          if (displayOptions.position) {
            updatedDisplayOptions.position = displayOptions.position;
          }
          break;
      }

      // Add common options
      updatedDisplayOptions.dismissible = dismissible;

      // Update ad placement
      await onUpdate(adPlacement.placementId, adPlacement.domain, {
        displayOptions: updatedDisplayOptions
      });

      // Show success message or handle success
    } catch (error) {
      console.error('Error updating display options:', error);
      // Show error message
    } finally {
      setSaving(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="display-mode" className="block text-sm font-medium text-gray-700">
          Display Mode
        </label>
        <select
          id="display-mode"
          value={displayMode}
          onChange={(e) => setDisplayMode(e.target.value)}
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        >
          <option value="always">Always (Standard)</option>
          <option value="timed">Timed (After Delay)</option>
          <option value="idle">Idle Time (User Inactive)</option>
          <option value="action">Action-Based (After User Action)</option>
          <option value="collapsible">Collapsible (User Expandable/Collapsible)</option>
        </select>
        <p className="mt-2 text-sm text-gray-500">
          {displayMode === 'always' && 'Display the ad immediately and continuously.'}
          {displayMode === 'timed' && 'Display the ad after a specified delay.'}
          {displayMode === 'idle' && 'Display the ad when the user is inactive for a specified time.'}
          {displayMode === 'action' && 'Display the ad after a specific user action.'}
          {displayMode === 'collapsible' && 'Display the ad in a collapsible container that users can expand or collapse.'}
        </p>
      </div>

      {displayMode === 'timed' && (
        <div>
          <label htmlFor="delay-seconds" className="block text-sm font-medium text-gray-700">
            Delay (seconds)
          </label>
          <input
            type="number"
            id="delay-seconds"
            value={delaySeconds}
            onChange={(e) => setDelaySeconds(parseInt(e.target.value))}
            min="0"
            max="300"
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
          <p className="mt-2 text-sm text-gray-500">
            The number of seconds to wait before displaying the ad.
          </p>
        </div>
      )}

      {displayMode === 'idle' && (
        <div>
          <label htmlFor="idle-time-seconds" className="block text-sm font-medium text-gray-700">
            Idle Time (seconds)
          </label>
          <input
            type="number"
            id="idle-time-seconds"
            value={idleTimeSeconds}
            onChange={(e) => setIdleTimeSeconds(parseInt(e.target.value))}
            min="5"
            max="300"
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
          <p className="mt-2 text-sm text-gray-500">
            The number of seconds of user inactivity before displaying the ad.
          </p>
        </div>
      )}

      {displayMode === 'action' && (
        <div>
          <label htmlFor="action-trigger" className="block text-sm font-medium text-gray-700">
            Action Trigger
          </label>
          <select
            id="action-trigger"
            value={actionTrigger}
            onChange={(e) => setActionTrigger(e.target.value)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
          >
            <option value="none">Select an action...</option>
            <option value="email-view">After Viewing Email</option>
            <option value="email-delete">After Deleting Email</option>
            <option value="address-generate">After Generating Address</option>
            <option value="page-change">After Page Navigation</option>
          </select>
          <p className="mt-2 text-sm text-gray-500">
            The user action that will trigger the display of the ad.
          </p>
        </div>
      )}

      {displayMode === 'collapsible' && (
        <div className="space-y-4">
          <div>
            <label htmlFor="initially-expanded" className="block text-sm font-medium text-gray-700">
              Initially Expanded
            </label>
            <div className="mt-1 flex items-center">
              <input
                type="checkbox"
                id="initially-expanded"
                checked={displayOptions?.initiallyExpanded !== false}
                onChange={(e) => {
                  const updatedOptions = { ...displayOptions, initiallyExpanded: e.target.checked };
                  setDisplayOptions(updatedOptions);
                }}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-500">
                Show the ad expanded by default
              </span>
            </div>
          </div>

          <div>
            <label htmlFor="remember-state" className="block text-sm font-medium text-gray-700">
              Remember State
            </label>
            <div className="mt-1 flex items-center">
              <input
                type="checkbox"
                id="remember-state"
                checked={displayOptions?.rememberState !== false}
                onChange={(e) => {
                  const updatedOptions = { ...displayOptions, rememberState: e.target.checked };
                  setDisplayOptions(updatedOptions);
                }}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-500">
                Remember if the user expanded or collapsed the ad
              </span>
            </div>
          </div>

          <div>
            <label htmlFor="expand-after-minutes" className="block text-sm font-medium text-gray-700">
              Auto-Expand After (minutes)
            </label>
            <input
              type="number"
              id="expand-after-minutes"
              value={displayOptions?.expandAfterMinutes || 10}
              onChange={(e) => {
                const updatedOptions = { ...displayOptions, expandAfterMinutes: parseInt(e.target.value) };
                setDisplayOptions(updatedOptions);
              }}
              min="1"
              max="60"
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
            <p className="mt-2 text-sm text-gray-500">
              Automatically expand the ad after it has been collapsed for this many minutes
            </p>
          </div>

          <div>
            <label htmlFor="position" className="block text-sm font-medium text-gray-700">
              Position
            </label>
            <select
              id="position"
              value={displayOptions?.position || 'bottom'}
              onChange={(e) => {
                const updatedOptions = { ...displayOptions, position: e.target.value };
                setDisplayOptions(updatedOptions);
              }}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="top">Top</option>
              <option value="bottom">Bottom</option>
              <option value="left">Left</option>
              <option value="right">Right</option>
            </select>
            <p className="mt-2 text-sm text-gray-500">
              Where the collapsible ad should appear on the page
            </p>
          </div>
        </div>
      )}

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="dismissible"
            type="checkbox"
            checked={dismissible}
            onChange={(e) => setDismissible(e.target.checked)}
            className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
          />
        </div>
        <div className="ml-3 text-sm">
          <label htmlFor="dismissible" className="font-medium text-gray-700">
            Dismissible
          </label>
          <p className="text-gray-500">
            Allow users to dismiss or close the ad.
          </p>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          disabled={saving}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {saving ? 'Saving...' : 'Save Display Options'}
        </button>
      </div>
    </form>
  );
}
