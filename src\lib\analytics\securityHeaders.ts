/**
 * Security Headers for Analytics Endpoints
 * 
 * Provides standardized security headers for all analytics API endpoints
 * to prevent common web security attacks and ensure data protection.
 */

import { NextResponse } from 'next/server';

/**
 * Standard security headers for analytics endpoints
 */
export const ANALYTICS_SECURITY_HEADERS = {
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Prevent clickjacking attacks
  'X-Frame-Options': 'DENY',
  
  // Enable XSS protection
  'X-XSS-Protection': '1; mode=block',
  
  // Control referrer information
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // Prevent caching of sensitive data
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0',
  
  // Content type for JSON responses
  'Content-Type': 'application/json',
  
  // CORS headers for analytics endpoints
  'Access-Control-Allow-Origin': '*', // Analytics can accept from any origin
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
  'Access-Control-Max-Age': '86400', // 24 hours
  
  // Additional security headers
  'X-Permitted-Cross-Domain-Policies': 'none',
  'X-Download-Options': 'noopen',
} as const;

/**
 * Admin-specific security headers (more restrictive)
 */
export const ADMIN_ANALYTICS_SECURITY_HEADERS = {
  ...ANALYTICS_SECURITY_HEADERS,
  
  // More restrictive CORS for admin endpoints
  'Access-Control-Allow-Origin': 'same-origin',
  
  // Additional admin security
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  'X-Robots-Tag': 'noindex, nofollow, noarchive, nosnippet',
} as const;

/**
 * Create a NextResponse with security headers
 */
export function createSecureAnalyticsResponse(
  data: any,
  options: {
    status?: number;
    isAdmin?: boolean;
    additionalHeaders?: Record<string, string>;
  } = {}
): NextResponse {
  const { status = 200, isAdmin = false, additionalHeaders = {} } = options;
  
  // Choose appropriate headers based on endpoint type
  const securityHeaders = isAdmin ? ADMIN_ANALYTICS_SECURITY_HEADERS : ANALYTICS_SECURITY_HEADERS;
  
  // Combine all headers
  const allHeaders = {
    ...securityHeaders,
    ...additionalHeaders,
  };
  
  return NextResponse.json(data, {
    status,
    headers: allHeaders,
  });
}

/**
 * Create an error response with security headers
 */
export function createSecureErrorResponse(
  error: string,
  options: {
    status?: number;
    isAdmin?: boolean;
    additionalHeaders?: Record<string, string>;
  } = {}
): NextResponse {
  const { status = 400, isAdmin = false, additionalHeaders = {} } = options;
  
  return createSecureAnalyticsResponse(
    {
      success: false,
      error,
      timestamp: new Date().toISOString(),
    },
    { status, isAdmin, additionalHeaders }
  );
}

/**
 * Create a rate limit exceeded response with security headers
 */
export function createRateLimitResponse(
  retryAfter?: number,
  additionalHeaders: Record<string, string> = {}
): NextResponse {
  const errorData = {
    success: false,
    error: 'Rate limit exceeded. Please try again later.',
    retryAfter,
    timestamp: new Date().toISOString(),
  };
  
  const headers = {
    ...additionalHeaders,
    ...(retryAfter && { 'Retry-After': retryAfter.toString() }),
  };
  
  return createSecureAnalyticsResponse(errorData, {
    status: 429,
    additionalHeaders: headers,
  });
}

/**
 * Handle OPTIONS requests for CORS preflight
 */
export function createCORSPreflightResponse(isAdmin: boolean = false): NextResponse {
  const headers = isAdmin ? ADMIN_ANALYTICS_SECURITY_HEADERS : ANALYTICS_SECURITY_HEADERS;
  
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': headers['Access-Control-Allow-Origin'],
      'Access-Control-Allow-Methods': headers['Access-Control-Allow-Methods'],
      'Access-Control-Allow-Headers': headers['Access-Control-Allow-Headers'],
      'Access-Control-Max-Age': headers['Access-Control-Max-Age'],
      'Content-Length': '0',
    },
  });
}

/**
 * Add security headers to an existing NextResponse
 */
export function addSecurityHeaders(
  response: NextResponse,
  isAdmin: boolean = false,
  additionalHeaders: Record<string, string> = {}
): NextResponse {
  const securityHeaders = isAdmin ? ADMIN_ANALYTICS_SECURITY_HEADERS : ANALYTICS_SECURITY_HEADERS;
  
  // Add all security headers
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  // Add any additional headers
  Object.entries(additionalHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
}

/**
 * Validate and sanitize response data
 */
export function sanitizeResponseData(data: any): any {
  // Remove any potentially sensitive information from responses
  if (typeof data === 'object' && data !== null) {
    const sanitized = { ...data };
    
    // Remove internal fields that shouldn't be exposed
    delete sanitized.internal_id;
    delete sanitized.raw_ip;
    delete sanitized.internal_metadata;
    
    // Sanitize nested objects
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = sanitizeResponseData(sanitized[key]);
      }
    });
    
    return sanitized;
  }
  
  return data;
}

/**
 * Create a health check response for analytics endpoints
 */
export function createHealthCheckResponse(): NextResponse {
  return createSecureAnalyticsResponse({
    status: 'healthy',
    service: 'analytics-api',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
}

/**
 * Log security events for monitoring
 */
export function logSecurityEvent(
  event: 'rate_limit_exceeded' | 'invalid_request' | 'validation_failed' | 'suspicious_activity',
  details: {
    ip?: string;
    userAgent?: string;
    endpoint?: string;
    error?: string;
    metadata?: Record<string, any>;
  }
): void {
  // In development, log to console
  if (process.env.NODE_ENV === 'development') {
    console.warn(`[SECURITY] ${event}:`, details);
  }
  
  // In production, this could send to a security monitoring service
  // Example: send to Sentry, DataDog, or custom security logging service
}
