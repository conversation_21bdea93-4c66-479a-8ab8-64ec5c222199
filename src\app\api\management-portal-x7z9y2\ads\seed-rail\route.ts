/**
 * API route for seeding side rail ad configurations
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/management-portal-x7z9y2/ads/seed-rail
 * 
 * Seeds the database with side rail ad configurations
 */
export async function GET(request: NextRequest) {
  try {
    // Define side rail ad configurations
    const sideRailAds = [
      {
        placement_id: 'left-rail',
        domain: 'localhost',
        ad_unit_id: '1234567890',
        ad_client_id: 'ca-pub-8397529755029714',
        is_enabled: true,
        device_types: ['desktop'],
        format: 'vertical',
        display_options: {
          position: 'fixed',
          margin: '10px 0',
          padding: '10px',
          backgroundColor: '#f9f9f9',
          borderRadius: '4px',
          maxWidth: '100%',
          overflow: 'hidden',
          showBorder: true,
          borderColor: '#e0e0e0',
          labelText: 'Advertisement',
          showLabel: true,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        placement_id: 'right-rail',
        domain: 'localhost',
        ad_unit_id: '0987654321',
        ad_client_id: 'ca-pub-8397529755029714',
        is_enabled: true,
        device_types: ['desktop'],
        format: 'vertical',
        display_options: {
          position: 'fixed',
          margin: '10px 0',
          padding: '10px',
          backgroundColor: '#f9f9f9',
          borderRadius: '4px',
          maxWidth: '100%',
          overflow: 'hidden',
          showBorder: true,
          borderColor: '#e0e0e0',
          labelText: 'Advertisement',
          showLabel: true,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        placement_id: 'left-rail',
        domain: '*',
        ad_unit_id: '1234567890',
        ad_client_id: 'ca-pub-8397529755029714',
        is_enabled: true,
        device_types: ['desktop'],
        format: 'vertical',
        display_options: {
          position: 'fixed',
          margin: '10px 0',
          padding: '10px',
          backgroundColor: '#f9f9f9',
          borderRadius: '4px',
          maxWidth: '100%',
          overflow: 'hidden',
          showBorder: true,
          borderColor: '#e0e0e0',
          labelText: 'Advertisement',
          showLabel: true,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        placement_id: 'right-rail',
        domain: '*',
        ad_unit_id: '0987654321',
        ad_client_id: 'ca-pub-8397529755029714',
        is_enabled: true,
        device_types: ['desktop'],
        format: 'vertical',
        display_options: {
          position: 'fixed',
          margin: '10px 0',
          padding: '10px',
          backgroundColor: '#f9f9f9',
          borderRadius: '4px',
          maxWidth: '100%',
          overflow: 'hidden',
          showBorder: true,
          borderColor: '#e0e0e0',
          labelText: 'Advertisement',
          showLabel: true,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // Get Supabase client
    const supabase = createServerSupabaseClient();

    // Check if entries already exist
    const { data: existingData, error: checkError } = await supabase
      .from('ad_config')
      .select('placement_id, domain')
      .in('placement_id', ['left-rail', 'right-rail']);

    if (checkError) {
      throw new Error(`Failed to check existing side rail ads: ${checkError.message}`);
    }

    // Log existing entries
    logInfo('ads', `Found ${existingData?.length || 0} existing side rail ad entries`);

    // Insert side rail ads if they don't exist
    const results = [];
    for (const ad of sideRailAds) {
      // Check if this specific combination already exists
      const exists = existingData?.some(
        (item) => item.placement_id === ad.placement_id && item.domain === ad.domain
      );

      if (!exists) {
        const { data, error } = await supabase
          .from('ad_config')
          .insert([ad])
          .select();

        if (error) {
          logError('ads', `Failed to insert side rail ad: ${error.message}`, { ad });
          results.push({ success: false, ad: ad.placement_id, domain: ad.domain, error: error.message });
        } else {
          logInfo('ads', `Inserted side rail ad: ${ad.placement_id} for domain: ${ad.domain}`);
          results.push({ success: true, ad: ad.placement_id, domain: ad.domain });
        }
      } else {
        logInfo('ads', `Side rail ad already exists: ${ad.placement_id} for domain: ${ad.domain}`);
        results.push({ success: true, ad: ad.placement_id, domain: ad.domain, skipped: true });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Side rail ads seeded successfully',
      results
    });
  } catch (error) {
    logError('ads', 'Error seeding side rail ads', { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to seed side rail ads' },
      { status: 500 }
    );
  }
}
