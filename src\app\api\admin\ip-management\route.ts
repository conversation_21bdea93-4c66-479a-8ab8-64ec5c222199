/**
 * Admin IP Management API
 * 
 * Provides endpoints for managing IP blocks, whitelist, and rate limit violations
 */

import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth/adminAuth';
import {
  blockIP,
  unblockIP,
  whitelistIP,
  removeFromWhitelist,
  isIPBlocked,
  isIPWhitelisted
} from '@/lib/ipBlocking';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logger } from '@/lib/logging/Logger';

/**
 * GET /api/admin/ip-management
 * Get IP management data (blocked IPs, whitelist, violations)
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '25');
    const offset = (page - 1) * limit;
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const endpoint = searchParams.get('endpoint') || 'all';
    const sortField = searchParams.get('sortField') || 'created_at';
    const sortDirection = searchParams.get('sortDirection') || 'desc';
    const isExport = searchParams.get('export') === 'true';
    const countsOnly = searchParams.get('countsOnly') === 'true';

    const supabase = createServerSupabaseClient();
    let data: any = {};
    let totalCounts: any = {};

    // If only counts are requested, return counts for all types
    if (countsOnly) {
      const [blockedCount, whitelistCount, violationsCount] = await Promise.all([
        supabase.from('blocked_ips').select('*', { count: 'exact', head: true }),
        supabase.from('ip_whitelist').select('*', { count: 'exact', head: true }),
        supabase.from('rate_limit_violations').select('*', { count: 'exact', head: true }).eq('is_resolved', false)
      ]);

      return NextResponse.json({
        success: true,
        counts: {
          blocked: blockedCount.count || 0,
          whitelist: whitelistCount.count || 0,
          violations: violationsCount.count || 0
        }
      });
    }

    if (type === 'all' || type === 'blocked') {
      // Build blocked IPs query
      let blockedQuery = supabase
        .from('blocked_ips')
        .select('*', { count: 'exact' });

      // Apply search filter
      if (search) {
        blockedQuery = blockedQuery.ilike('ip_address', `%${search}%`);
      }

      // Apply status filter
      if (status !== 'all') {
        if (status === 'active') {
          blockedQuery = blockedQuery.eq('is_active', true);
        } else if (status === 'inactive') {
          blockedQuery = blockedQuery.eq('is_active', false);
        }
      }

      // Apply date filters
      if (dateFrom) {
        blockedQuery = blockedQuery.gte('blocked_at', dateFrom);
      }
      if (dateTo) {
        blockedQuery = blockedQuery.lte('blocked_at', dateTo + 'T23:59:59.999Z');
      }

      // Apply sorting
      const sortColumn = sortField === 'created_at' ? 'blocked_at' : sortField;
      blockedQuery = blockedQuery.order(sortColumn, { ascending: sortDirection === 'asc' });

      // Apply pagination (unless exporting)
      if (!isExport) {
        blockedQuery = blockedQuery.range(offset, offset + limit - 1);
      }

      const { data: blockedIPs, error: blockedError, count: blockedCount } = await blockedQuery;

      if (blockedError) throw blockedError;
      data.blockedIPs = blockedIPs;
      totalCounts.blocked = blockedCount;
    }

    if (type === 'all' || type === 'whitelist') {
      // Build whitelist IPs query
      let whitelistQuery = supabase
        .from('ip_whitelist')
        .select('*', { count: 'exact' });

      // Apply search filter
      if (search) {
        whitelistQuery = whitelistQuery.ilike('ip_address', `%${search}%`);
      }

      // Apply status filter
      if (status !== 'all') {
        if (status === 'active') {
          whitelistQuery = whitelistQuery.eq('is_active', true);
        } else if (status === 'inactive') {
          whitelistQuery = whitelistQuery.eq('is_active', false);
        }
      }
      // Note: Removed default filter to show all whitelist entries (both active and inactive)

      // Apply date filters
      if (dateFrom) {
        whitelistQuery = whitelistQuery.gte('added_at', dateFrom);
      }
      if (dateTo) {
        whitelistQuery = whitelistQuery.lte('added_at', dateTo + 'T23:59:59.999Z');
      }

      // Apply sorting
      const sortColumn = sortField === 'created_at' ? 'added_at' : sortField;
      whitelistQuery = whitelistQuery.order(sortColumn, { ascending: sortDirection === 'asc' });

      // Apply pagination (unless exporting)
      if (!isExport) {
        whitelistQuery = whitelistQuery.range(offset, offset + limit - 1);
      }

      const { data: whitelistIPs, error: whitelistError, count: whitelistCount } = await whitelistQuery;

      if (whitelistError) throw whitelistError;
      data.whitelistIPs = whitelistIPs;
      totalCounts.whitelist = whitelistCount;
    }

    if (type === 'all' || type === 'violations') {
      // Build violations query
      let violationsQuery = supabase
        .from('rate_limit_violations')
        .select('*', { count: 'exact' });

      // Apply search filter
      if (search) {
        violationsQuery = violationsQuery.ilike('ip_address', `%${search}%`);
      }

      // Apply status filter
      if (status !== 'all') {
        if (status === 'resolved') {
          violationsQuery = violationsQuery.eq('is_resolved', true);
        } else if (status === 'unresolved') {
          violationsQuery = violationsQuery.eq('is_resolved', false);
        }
      }

      // Apply endpoint filter
      if (endpoint !== 'all') {
        violationsQuery = violationsQuery.eq('endpoint', endpoint);
      }

      // Apply date filters
      if (dateFrom) {
        violationsQuery = violationsQuery.gte('last_violation', dateFrom);
      }
      if (dateTo) {
        violationsQuery = violationsQuery.lte('last_violation', dateTo + 'T23:59:59.999Z');
      }

      // Apply sorting
      violationsQuery = violationsQuery.order(sortField, { ascending: sortDirection === 'asc' });

      // Apply pagination (unless exporting)
      if (!isExport) {
        violationsQuery = violationsQuery.range(offset, offset + limit - 1);
      }

      const { data: violations, error: violationsError, count: violationsCount } = await violationsQuery;

      if (violationsError) throw violationsError;

      // Enhance violations with blocked status information
      if (violations && violations.length > 0) {
        const ipAddresses = violations.map(v => v.ip_address);

        // Get blocked status for all IP addresses in the violations
        const { data: blockedIPs, error: blockedError } = await supabase
          .from('blocked_ips')
          .select('ip_address, blocked_at, blocked_by, reason, expires_at')
          .in('ip_address', ipAddresses)
          .eq('is_active', true);

        if (blockedError) {
          console.error('Error fetching blocked status:', blockedError);
        }

        // Create a map for quick lookup
        const blockedMap = new Map();
        if (blockedIPs) {
          blockedIPs.forEach(blocked => {
            blockedMap.set(blocked.ip_address, {
              isBlocked: true,
              blockedAt: blocked.blocked_at,
              blockedBy: blocked.blocked_by,
              blockReason: blocked.reason,
              expiresAt: blocked.expires_at
            });
          });
        }

        // Enhance violations with blocked status
        let enhancedViolations = violations.map(violation => ({
          ...violation,
          blockedStatus: blockedMap.get(violation.ip_address) || { isBlocked: false }
        }));

        // Apply blocked/unblocked filter if specified
        if (status === 'blocked') {
          enhancedViolations = enhancedViolations.filter(v => v.blockedStatus.isBlocked);
        } else if (status === 'unblocked') {
          enhancedViolations = enhancedViolations.filter(v => !v.blockedStatus.isBlocked);
        }

        data.violations = enhancedViolations;
      } else {
        data.violations = violations;
      }

      totalCounts.violations = violationsCount;
    }

    // Calculate if there are more pages
    const hasMore = !isExport && Object.values(data).some((arr: any) => Array.isArray(arr) && arr.length === limit);

    // Get total count for current type
    let total = 0;
    if (type === 'blocked') total = totalCounts.blocked || 0;
    else if (type === 'whitelist') total = totalCounts.whitelist || 0;
    else if (type === 'violations') total = totalCounts.violations || 0;
    else total = Object.values(totalCounts).reduce((sum: number, count: any) => sum + (count || 0), 0);

    return NextResponse.json({
      success: true,
      data,
      pagination: {
        page,
        limit,
        hasMore,
        total,
        totalPages: Math.ceil(total / limit)
      },
      filters: {
        search,
        status,
        dateFrom,
        dateTo,
        endpoint,
        sortField,
        sortDirection
      }
    });

  } catch (error) {
    await logger.error('ADMIN_IP_MANAGEMENT', 'Error fetching IP management data', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to fetch IP management data' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/ip-management
 * Perform IP management actions (block, unblock, whitelist, etc.)
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, ipAddress, reason, expiresAt, adminUser } = body;

    if (!action || !ipAddress) {
      return NextResponse.json(
        { success: false, error: 'Action and IP address are required' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'block':
        if (!reason) {
          return NextResponse.json(
            { success: false, error: 'Reason is required for blocking' },
            { status: 400 }
          );
        }
        result = await blockIP(
          ipAddress,
          reason,
          adminUser || authResult.user?.username || 'admin',
          expiresAt ? new Date(expiresAt) : undefined
        );
        break;

      case 'unblock':
        result = await unblockIP(ipAddress, adminUser || authResult.user?.username || 'admin');
        break;

      case 'whitelist':
        if (!reason) {
          return NextResponse.json(
            { success: false, error: 'Reason is required for whitelisting' },
            { status: 400 }
          );
        }
        result = await whitelistIP(ipAddress, reason, adminUser || authResult.user?.username || 'admin');
        break;

      case 'remove_whitelist':
        result = await removeFromWhitelist(ipAddress, adminUser || authResult.user?.username || 'admin');
        break;

      case 'check_status':
        const isBlocked = await isIPBlocked(ipAddress);
        const isWhitelisted = await isIPWhitelisted(ipAddress);
        result = { 
          success: true, 
          status: { 
            blocked: isBlocked, 
            whitelisted: isWhitelisted 
          } 
        };
        break;

      case 'resolve_violation':
        const supabase2 = createServerSupabaseClient();
        const { error: resolveError } = await supabase2
          .from('rate_limit_violations')
          .update({
            is_resolved: true,
            resolved_at: new Date().toISOString(),
            resolved_by: adminUser || authResult.user?.username || 'admin'
          })
          .eq('ip_address', ipAddress)
          .eq('is_resolved', false);

        if (resolveError) {
          result = { success: false, error: resolveError.message };
        } else {
          result = { success: true };
          await logger.info('ADMIN_IP_MANAGEMENT', 'Rate limit violation resolved', {
            ipAddress,
            resolvedBy: adminUser || authResult.user?.username
          });
        }
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);

  } catch (error) {
    await logger.error('ADMIN_IP_MANAGEMENT', 'Error performing IP management action', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to perform IP management action' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/ip-management
 * Delete operations for IP management records
 */
export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const recordId = searchParams.get('id');
    const ipAddress = searchParams.get('ipAddress');
    const recordType = searchParams.get('type'); // 'blocked', 'whitelist', 'violation'

    const supabase = createServerSupabaseClient();
    const adminUser = authResult.user?.username || 'admin';
    let result: any;

    if (action === 'delete_record') {
      if (!recordId || !recordType) {
        return NextResponse.json(
          { success: false, error: 'Record ID and type are required' },
          { status: 400 }
        );
      }

      let recordDetails: any = null;
      let deleteError: any = null;

      // Handle each record type separately to satisfy TypeScript
      switch (recordType) {
        case 'blocked':
          // Get record details for logging
          const { data: blockedRecord } = await supabase
            .from('blocked_ips')
            .select('*')
            .eq('id', recordId)
            .single();
          recordDetails = blockedRecord;

          // Perform deletion
          const { error: blockedDeleteError } = await supabase
            .from('blocked_ips')
            .delete()
            .eq('id', recordId);
          deleteError = blockedDeleteError;
          break;

        case 'whitelist':
          // Get record details for logging
          const { data: whitelistRecord } = await supabase
            .from('ip_whitelist')
            .select('*')
            .eq('id', recordId)
            .single();
          recordDetails = whitelistRecord;

          // Perform deletion
          const { error: whitelistDeleteError } = await supabase
            .from('ip_whitelist')
            .delete()
            .eq('id', recordId);
          deleteError = whitelistDeleteError;
          break;

        case 'violation':
          // Get record details for logging
          const { data: violationRecord } = await supabase
            .from('rate_limit_violations')
            .select('*')
            .eq('id', recordId)
            .single();
          recordDetails = violationRecord;

          // Perform deletion
          const { error: violationDeleteError } = await supabase
            .from('rate_limit_violations')
            .delete()
            .eq('id', recordId);
          deleteError = violationDeleteError;
          break;

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid record type' },
            { status: 400 }
          );
      }

      if (deleteError) {
        throw deleteError;
      }

      // Log the deletion in admin activity log
      await supabase
        .from('admin_activity_log')
        .insert({
          user_id: authResult.user?.userId || 0,
          action_type: 'DELETE_RECORD',
          resource_type: `IP_MANAGEMENT_${recordType.toUpperCase()}`,
          resource_id: recordId,
          details: {
            deleted_record: recordDetails,
            ip_address: recordDetails?.ip_address || ipAddress,
            reason: `Permanent deletion of ${recordType} record`,
            admin_user: adminUser
          },
          ip_address: request.headers.get('x-forwarded-for') || 'unknown',
          user_agent: request.headers.get('user-agent') || 'unknown'
        });

      await logger.info('ADMIN_IP_MANAGEMENT', `${recordType} record permanently deleted`, {
        recordId,
        ipAddress: recordDetails?.ip_address || ipAddress,
        deletedBy: adminUser,
        recordDetails
      });

      result = { success: true, message: `${recordType} record deleted successfully` };

    } else if (action === 'cleanup_expired') {
      // Clean up expired blocks (existing functionality)
      const { data, error } = await supabase.rpc('cleanup_expired_blocks');

      if (error) {
        throw error;
      }

      await logger.info('ADMIN_IP_MANAGEMENT', 'Expired blocks cleaned up', {
        affectedRows: data,
        cleanedBy: adminUser
      });

      result = {
        success: true,
        message: `Cleaned up ${data} expired blocks`
      };

    } else if (action === 'bulk_delete') {
      // Bulk delete functionality
      const body = await request.json();
      const { recordIds, recordType: bulkType } = body;

      if (!recordIds || !Array.isArray(recordIds) || recordIds.length === 0) {
        return NextResponse.json(
          { success: false, error: 'Record IDs array is required' },
          { status: 400 }
        );
      }

      let recordsToDelete: any[] = [];
      let bulkDeleteError: any = null;

      // Handle each record type separately for TypeScript compatibility
      switch (bulkType) {
        case 'blocked':
          // Get records before deletion for logging
          const { data: blockedRecords } = await supabase
            .from('blocked_ips')
            .select('*')
            .in('id', recordIds);
          recordsToDelete = blockedRecords || [];

          // Perform bulk deletion
          const { error: blockedBulkError } = await supabase
            .from('blocked_ips')
            .delete()
            .in('id', recordIds);
          bulkDeleteError = blockedBulkError;
          break;

        case 'whitelist':
          // Get records before deletion for logging
          const { data: whitelistRecords } = await supabase
            .from('ip_whitelist')
            .select('*')
            .in('id', recordIds);
          recordsToDelete = whitelistRecords || [];

          // Perform bulk deletion
          const { error: whitelistBulkError } = await supabase
            .from('ip_whitelist')
            .delete()
            .in('id', recordIds);
          bulkDeleteError = whitelistBulkError;
          break;

        case 'violation':
          // Get records before deletion for logging
          const { data: violationRecords } = await supabase
            .from('rate_limit_violations')
            .select('*')
            .in('id', recordIds);
          recordsToDelete = violationRecords || [];

          // Perform bulk deletion
          const { error: violationBulkError } = await supabase
            .from('rate_limit_violations')
            .delete()
            .in('id', recordIds);
          bulkDeleteError = violationBulkError;
          break;

        default:
          return NextResponse.json(
            { success: false, error: 'Invalid record type for bulk delete' },
            { status: 400 }
          );
      }

      if (bulkDeleteError) {
        throw bulkDeleteError;
      }

      // Log bulk deletion
      await supabase
        .from('admin_activity_log')
        .insert({
          user_id: authResult.user?.userId || 0,
          action_type: 'BULK_DELETE',
          resource_type: `IP_MANAGEMENT_${bulkType.toUpperCase()}`,
          resource_id: recordIds.join(','),
          details: {
            deleted_records: recordsToDelete,
            record_count: recordIds.length,
            reason: `Bulk deletion of ${recordIds.length} ${bulkType} records`,
            admin_user: adminUser
          },
          ip_address: request.headers.get('x-forwarded-for') || 'unknown',
          user_agent: request.headers.get('user-agent') || 'unknown'
        });

      await logger.info('ADMIN_IP_MANAGEMENT', `Bulk deletion of ${bulkType} records`, {
        recordCount: recordIds.length,
        recordIds,
        deletedBy: adminUser
      });

      result = {
        success: true,
        message: `Successfully deleted ${recordIds.length} ${bulkType} records`
      };

    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action' },
        { status: 400 }
      );
    }

    return NextResponse.json(result);

  } catch (error) {
    await logger.error('ADMIN_IP_MANAGEMENT', 'Error performing delete operation', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to perform delete operation' },
      { status: 500 }
    );
  }
}
