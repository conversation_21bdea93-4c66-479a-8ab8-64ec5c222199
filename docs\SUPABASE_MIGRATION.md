# Supabase Migration Guide

This document outlines the migration of the Fademail application from a local MySQL database to Supabase PostgreSQL.

## Overview

The Fademail application has been migrated from using a local MySQL database to Supabase PostgreSQL for improved scalability, reliability, and reduced infrastructure maintenance. This migration affects the following components:

1. **Temporary Email Storage**: All temporary email addresses are now stored in Supabase.
2. **Analytics Data**: All analytics events are now stored in Supabase.
3. **Email Content**: Email content is still retrieved from the Guerrilla Mail database.

## Prerequisites

To use the application with Supabase, you need:

1. A Supabase account and project
2. The following environment variables in your `.env.local` file:
   - `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
   - `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key (for admin operations)

## Database Schema

### Temporary Emails Table

The `temp_emails` table stores temporary email addresses and their metadata:

```sql
CREATE TABLE temp_emails (
  id SERIAL PRIMARY KEY,
  email_address VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  last_checked_at TIMESTAMPTZ
);

-- Create an index on email_address for faster lookups
CREATE INDEX idx_temp_emails_email_address ON temp_emails(email_address);

-- Create an index on expires_at for cleanup operations
CREATE INDEX idx_temp_emails_expires_at ON temp_emails(expires_at);
```

### Analytics Events Table

The `analytics_events` table stores analytics events:

```sql
CREATE TABLE analytics_events (
  id SERIAL PRIMARY KEY,
  event_type VARCHAR(255) NOT NULL,
  page_path VARCHAR(255),
  referrer TEXT,
  country VARCHAR(255),
  browser VARCHAR(255),
  device_type VARCHAR(255),
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  additional_data JSONB
);

-- Create an index on event_type for faster filtering
CREATE INDEX idx_analytics_events_event_type ON analytics_events(event_type);

-- Create an index on timestamp for time-based queries
CREATE INDEX idx_analytics_events_timestamp ON analytics_events(timestamp);
```

## Row-Level Security (RLS)

Supabase uses PostgreSQL's Row-Level Security to control access to data. The following RLS policies have been set up:

### Temporary Emails Table

- **Read**: Anyone can read from the `temp_emails` table (needed for public API endpoints)
- **Insert/Update/Delete**: Only authenticated users can insert, update, or delete from the `temp_emails` table

### Analytics Events Table

- **Read**: Only authenticated users can read from the `analytics_events` table
- **Insert**: Anyone can insert into the `analytics_events` table (needed for tracking events from public pages)
- **Update/Delete**: Only authenticated users can update or delete from the `analytics_events` table

## Migration Process

The migration process involved:

1. Creating the necessary tables in Supabase
2. Migrating data from MySQL to Supabase
3. Updating the application code to use Supabase
4. Setting up RLS policies for security

The migration script can be found in `scripts/migrate-to-supabase.js`.

## API Changes

The API endpoints have been updated to use Supabase instead of MySQL. The following endpoints are affected:

- `/api/emails/[address]`: Gets emails for a specific address
- `/api/generate`: Generates a new temporary email address
- `/api/analytics`: Records analytics events
- `/api/admin/analytics/*`: Admin endpoints for analytics data

## Performance Optimizations

The following performance optimizations have been implemented:

1. **Caching**: Frequently accessed data is cached in memory to reduce database queries
2. **Indexes**: Appropriate indexes have been created on the tables for faster queries
3. **Real-time Updates**: Supabase's real-time functionality is used to update the UI when new emails arrive

## Rollback Plan

In case of issues with the Supabase migration, the application can be rolled back to use MySQL by:

1. Updating the environment variables to use MySQL instead of Supabase
2. Reverting the code changes that use Supabase
3. Ensuring the MySQL database is up to date with the latest data

## Monitoring and Maintenance

To monitor the Supabase database:

1. Use the Supabase dashboard to monitor database usage and performance
2. Set up alerts for high database usage or errors
3. Regularly check the application logs for any database-related errors

## Scripts

The following scripts are available for managing the Supabase database:

- `npm run setup-supabase`: Sets up the Supabase database schema
- `npm run test-supabase`: Tests the connection to Supabase
- `npm run migrate-to-supabase`: Migrates data from MySQL to Supabase
- `npm run setup-supabase-rls`: Sets up Row-Level Security policies for Supabase tables

## Troubleshooting

### Common Issues

1. **Connection Issues**: Ensure the Supabase URL and keys are correct in your `.env.local` file
2. **Permission Issues**: Check the RLS policies if you're getting permission errors
3. **Data Synchronization**: If data appears out of sync, try clearing the cache using the "Clear Cache" button in the admin dashboard

### Debugging

1. Check the application logs for any database-related errors
2. Use the Supabase dashboard to inspect the database tables and queries
3. Test the API endpoints directly to isolate issues

## Future Improvements

1. **Backup Strategy**: Implement regular backups of the Supabase database
2. **Data Retention Policy**: Implement a data retention policy for analytics data
3. **Advanced Analytics**: Leverage Supabase's PostgreSQL capabilities for more advanced analytics queries
4. **Edge Functions**: Use Supabase Edge Functions for serverless functionality
5. **Storage**: Use Supabase Storage for handling email attachments
