/**
 * API routes for specific domain operations
 */
import { NextRequest, NextResponse } from 'next/server';
import { getDomain, updateDomain, deleteDomain, setDefaultDomain } from '@/lib/config/domainService';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/admin/domains/[domain]
 *
 * Get a specific domain
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ domain: string }> }
) {
  try {
    // Await params to ensure we have the domain
    const resolvedParams = await params;
    const domain = resolvedParams.domain;

    // Get domain
    const domainConfig = await getDomain(domain);

    if (!domainConfig) {
      return NextResponse.json(
        { success: false, error: 'Domain not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: domainConfig
    });
  } catch (error) {
    logError('api', 'Error getting domain', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to get domain' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/domains/[domain]
 *
 * Update a domain
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ domain: string }> }
) {
  try {
    // Await params to ensure we have the domain
    const resolvedParams = await params;
    const domain = resolvedParams.domain;

    // Get update data from request body
    const { isActive, settings } = await request.json();

    // Update domain
    const updatedDomain = await updateDomain(domain, isActive, settings);

    if (!updatedDomain) {
      return NextResponse.json(
        { success: false, error: 'Failed to update domain' },
        { status: 500 }
      );
    }

    logInfo('api', 'Updated domain', { domain: updatedDomain });

    return NextResponse.json({
      success: true,
      data: updatedDomain,
      message: 'Domain updated successfully'
    });
  } catch (error) {
    logError('api', 'Error updating domain', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to update domain' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/domains/[domain]
 *
 * Delete a domain
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ domain: string }> }
) {
  try {
    // Await params to ensure we have the domain
    const resolvedParams = await params;
    const domain = resolvedParams.domain;

    // Delete domain
    const success = await deleteDomain(domain);

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to delete domain' },
        { status: 500 }
      );
    }

    logInfo('api', 'Deleted domain', { domain });

    return NextResponse.json({
      success: true,
      message: 'Domain deleted successfully'
    });
  } catch (error) {
    logError('api', 'Error deleting domain', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to delete domain' },
      { status: 500 }
    );
  }
}

// The POST handler for setting a domain as default has been moved to
// src/app/api/admin/domains/[domain]/default/route.ts
