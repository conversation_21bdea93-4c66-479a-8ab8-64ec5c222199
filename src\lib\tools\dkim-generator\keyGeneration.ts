// DKIM Key Generation Utilities - Isolated implementation
import forge from 'node-forge';
import { Dkim<PERSON>eyPair } from '@/types/dkim';

/**
 * Generate RSA key pair for DKIM authentication
 * @param keyStrength - Key strength in bits (1024 or 2048)
 * @returns Promise<DkimKeyPair> - Generated key pair with metadata
 */
export async function generateDkimKeyPair(keyStrength: 1024 | 2048): Promise<DkimKeyPair> {
  try {
    // Generate RSA key pair using node-forge
    const keyPair = forge.pki.rsa.generateKeyPair({
      bits: keyStrength,
      e: 0x10001, // Standard public exponent (65537)
    });

    // Convert keys to PEM format
    const privateKeyPem = forge.pki.privateKeyToPem(keyPair.privateKey);
    const publicKeyPem = forge.pki.publicKeyToPem(keyPair.publicKey);

    return {
      privateKey: privateKeyPem,
      publicKey: publicKeyPem,
      keyStrength,
      generatedAt: new Date().toISOString(),
    };
  } catch (error) {
    console.error('DKIM key generation error:', error);
    throw new Error(`Failed to generate DKIM key pair: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Extract public key components for DKIM DNS record
 * @param publicKeyPem - Public key in PEM format
 * @returns Object with modulus and exponent for DNS record
 */
export function extractPublicKeyComponents(publicKeyPem: string): { modulus: string; exponent: string } {
  try {
    const publicKey = forge.pki.publicKeyFromPem(publicKeyPem);

    // Extract modulus and exponent
    const modulus = publicKey.n.toString(16);
    const exponent = publicKey.e.toString(16);

    return {
      modulus,
      exponent,
    };
  } catch (error) {
    console.error('Public key component extraction error:', error);
    throw new Error(`Failed to extract public key components: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Convert public key PEM to base64 format for DKIM DNS record
 * @param publicKeyPem - Public key in PEM format
 * @returns Base64 encoded public key without headers
 */
export function publicKeyToBase64(publicKeyPem: string): string {
  try {
    // Remove PEM headers and whitespace
    const base64Key = publicKeyPem
      .replace(/-----BEGIN PUBLIC KEY-----/g, '')
      .replace(/-----END PUBLIC KEY-----/g, '')
      .replace(/\s/g, '');

    return base64Key;
  } catch (error) {
    console.error('Public key base64 conversion error:', error);
    throw new Error(`Failed to convert public key to base64: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validate key strength parameter
 * @param keyStrength - Key strength to validate
 * @returns boolean - True if valid
 */
export function validateKeyStrength(keyStrength: number): keyStrength is 1024 | 2048 {
  return keyStrength === 1024 || keyStrength === 2048;
}

/**
 * Estimate key generation time based on key strength
 * @param keyStrength - Key strength in bits
 * @returns Estimated generation time in milliseconds
 */
export function estimateGenerationTime(keyStrength: 1024 | 2048): number {
  // Rough estimates based on typical performance
  switch (keyStrength) {
    case 1024:
      return 500; // ~0.5 seconds
    case 2048:
      return 2000; // ~2 seconds
    default:
      return 1000; // Default estimate
  }
}

/**
 * Validate generated key pair
 * @param keyPair - Key pair to validate
 * @returns boolean - True if valid
 */
export function validateKeyPair(keyPair: DkimKeyPair): boolean {
  try {
    // Basic validation checks
    if (!keyPair.privateKey || !keyPair.publicKey) {
      return false;
    }

    // Validate PEM format - node-forge generates RSA PRIVATE KEY format
    if ((!keyPair.privateKey.includes('-----BEGIN RSA PRIVATE KEY-----') &&
         !keyPair.privateKey.includes('-----BEGIN PRIVATE KEY-----')) ||
        !keyPair.publicKey.includes('-----BEGIN PUBLIC KEY-----')) {
      return false;
    }

    // Validate key strength
    if (!validateKeyStrength(keyPair.keyStrength)) {
      return false;
    }

    // Try to parse keys to ensure they're valid
    forge.pki.privateKeyFromPem(keyPair.privateKey);
    forge.pki.publicKeyFromPem(keyPair.publicKey);

    return true;
  } catch (error) {
    console.error('Key pair validation error:', error);
    return false;
  }
}
