'use client';

import React from 'react';

interface IframeControlsProps {
  /**
   * Current scale factor (0.5 to 2.0)
   */
  scaleFactor: number;

  /**
   * Function to handle zoom in
   */
  onZoomIn: () => void;

  /**
   * Function to handle zoom out
   */
  onZoomOut: () => void;

  /**
   * Function to reset zoom to 100%
   */
  onResetZoom: () => void;

  /**
   * Optional className for the controls container
   */
  className?: string;
}

/**
 * IframeControls Component
 *
 * Renders zoom and print controls for iframe content
 */
export function IframeControls({
  scaleFactor,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  className = ''
}: IframeControlsProps) {
  // Check if zoom is not at 100%
  const isZoomed = Math.abs(scaleFactor - 1.0) > 0.01;

  return (
    <div className={`flex items-center justify-end space-x-2 mb-2 ${className}`}>
      <div className="flex items-center bg-white shadow-sm rounded-md border border-gray-200 overflow-hidden">
        <button
          onClick={onZoomOut}
          className="p-2 text-[#605f5f] hover:bg-gray-50 active:bg-gray-100 active:scale-95 transition-all duration-150 focus:outline-none"
          title="Zoom out"
          aria-label="Zoom out"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
          </svg>
        </button>

        <div className="px-2 py-1 text-xs font-medium text-[#605f5f] border-l border-r border-gray-200 min-w-[48px] text-center">
          {Math.round(scaleFactor * 100)}%
        </div>

        <button
          onClick={onZoomIn}
          className="p-2 text-[#605f5f] hover:bg-gray-50 active:bg-gray-100 active:scale-95 transition-all duration-150 focus:outline-none"
          title="Zoom in"
          aria-label="Zoom in"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7" />
          </svg>
        </button>

        {isZoomed && (
          <button
            onClick={onResetZoom}
            className="p-2 text-[#605f5f] hover:bg-gray-50 active:bg-gray-100 active:scale-95 transition-all duration-150 border-l border-gray-200 focus:outline-none"
            title="Reset zoom to 100%"
            aria-label="Reset zoom"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
}
