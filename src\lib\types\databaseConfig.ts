/**
 * Database configuration types for Fademail
 * These types define the structure of database connection configurations
 */

export interface GuerrillaDbConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  connectionLimit: number;
  ssl?: boolean;
}

export interface SupabaseConfig {
  url: string;
  apiKey: string;
  serviceRoleKey?: string;
}

export interface DatabaseConfig {
  id?: number;
  name: string;
  description?: string;
  isActive: boolean;
  guerrillaConfig: GuerrillaDbConfig;
  supabaseConfig: SupabaseConfig;
  createdAt?: string;
  updatedAt?: string;
}

export interface DatabaseConnectionTestResult {
  success: boolean;
  message: string;
  error?: string;
}

export interface DatabaseTestResult {
  success: boolean;
  guerrilla: DatabaseConnectionTestResult;
  supabase: DatabaseConnectionTestResult;
}
