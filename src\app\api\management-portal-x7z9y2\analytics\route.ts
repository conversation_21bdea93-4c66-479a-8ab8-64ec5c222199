/**
 * Admin Analytics API Endpoint
 * 
 * This endpoint provides analytics data for the admin dashboard.
 * Requires authentication and provides various analytics queries.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logError, logInfo } from '@/lib/logging';
import { analyticsCache, CacheKeys, CACHE_TTL } from '@/lib/analytics/cache';

/**
 * Validate date range parameters
 */
function validateDateRange(startDate?: string, endDate?: string): { isValid: boolean; error?: string } {
  if (startDate && isNaN(Date.parse(startDate))) {
    return { isValid: false, error: 'Invalid startDate format' };
  }
  
  if (endDate && isNaN(Date.parse(endDate))) {
    return { isValid: false, error: 'Invalid endDate format' };
  }
  
  if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
    return { isValid: false, error: 'startDate cannot be after endDate' };
  }
  
  return { isValid: true };
}

/**
 * Get time range filter based on timeRange parameter
 */
function getTimeRangeFilter(timeRange: string): { startDate: Date; endDate: Date } {
  const now = new Date();
  const endDate = new Date(now);
  let startDate: Date;

  switch (timeRange) {
    case '1h':
      startDate = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour ago
      break;
    case '24h':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago
      break;
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      break;
    default:
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // Default to 24 hours
  }

  return { startDate, endDate };
}

/**
 * GET /api/management-portal-x7z9y2/analytics
 * Retrieve analytics data for dashboard
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '24h';
    const eventType = searchParams.get('eventType');
    const aggregation = searchParams.get('aggregation') || 'count';
    const customStartDate = searchParams.get('startDate');
    const customEndDate = searchParams.get('endDate');

    // Validate custom date range if provided
    if (customStartDate || customEndDate) {
      const validation = validateDateRange(customStartDate || undefined, customEndDate || undefined);
      if (!validation.isValid) {
        return NextResponse.json(
          { success: false, error: validation.error },
          { status: 400 }
        );
      }
    }

    // Get time range
    let startDate: Date, endDate: Date;
    if (customStartDate && customEndDate) {
      startDate = new Date(customStartDate);
      endDate = new Date(customEndDate);
    } else {
      ({ startDate, endDate } = getTimeRangeFilter(timeRange));
    }

    // Generate cache key
    const cacheKey = customStartDate && customEndDate
      ? CacheKeys.customRange(customStartDate, customEndDate, `analytics-${aggregation}`)
      : aggregation === 'timeline'
        ? CacheKeys.timelineData(timeRange, aggregation)
        : CacheKeys.aggregatedMetrics(timeRange, aggregation);

    // Try to get from cache first
    const cachedResult = analyticsCache.get<{
      data: any;
      meta: any;
      cacheTime: string;
    }>(cacheKey);
    if (cachedResult) {
      logInfo('AdminAnalyticsAPI', 'Cache hit for analytics data', {
        cacheKey,
        timeRange,
        aggregation
      });
      return NextResponse.json({
        success: true,
        data: cachedResult.data,
        meta: {
          ...cachedResult.meta,
          cached: true,
          cacheTime: cachedResult.cacheTime
        }
      });
    }

    const supabase = createServerSupabaseClient();

    // Get total count first (without limit) for accurate metrics
    let countQuery = supabase
      .from('analytics_events')
      .select('*', { count: 'exact', head: true })
      .gte('timestamp', startDate.toISOString())
      .lte('timestamp', endDate.toISOString());

    // Add event type filter to count query if specified
    if (eventType) {
      countQuery = countQuery.eq('event_type', eventType);
    }

    const { count: totalEventsCount, error: countError } = await countQuery;

    if (countError) {
      logError('AdminAnalyticsAPI', 'Failed to fetch analytics events count', { error: countError });
      return NextResponse.json(
        { success: false, error: 'Failed to fetch analytics count' },
        { status: 500 }
      );
    }

    // Build base query for detailed data (with reasonable limit for processing)
    let query = supabase
      .from('analytics_events')
      .select('*')
      .gte('timestamp', startDate.toISOString())
      .lte('timestamp', endDate.toISOString())
      .order('timestamp', { ascending: false })
      .limit(5000); // Reasonable limit for processing event breakdowns

    // Add event type filter if specified
    if (eventType) {
      query = query.eq('event_type', eventType);
    }

    const { data: events, error } = await query;

    if (error) {
      logError('AdminAnalyticsAPI', 'Failed to fetch analytics events', { error });
      return NextResponse.json(
        { success: false, error: 'Failed to fetch analytics data' },
        { status: 500 }
      );
    }

    // Process data based on aggregation type
    let result: any = {};

    if (aggregation === 'count') {
      // Count events by type from the sample data
      const eventCounts = events.reduce((acc: Record<string, number>, event) => {
        acc[event.event_type] = (acc[event.event_type] || 0) + 1;
        return acc;
      }, {});

      result = {
        totalEvents: totalEventsCount || 0, // Use actual total count from database
        eventCounts,
        timeRange: { startDate: startDate.toISOString(), endDate: endDate.toISOString() },
        sampleSize: events.length // Include sample size for transparency
      };
    } else if (aggregation === 'timeline') {
      // Group events by hour for timeline view
      const timeline = events.reduce((acc: Record<string, Record<string, number>>, event) => {
        const hour = new Date(event.timestamp).toISOString().slice(0, 13) + ':00:00.000Z';
        if (!acc[hour]) {
          acc[hour] = {};
        }
        acc[hour][event.event_type] = (acc[hour][event.event_type] || 0) + 1;
        return acc;
      }, {});

      result = {
        timeline,
        totalEvents: totalEventsCount || 0, // Include actual total count
        timeRange: { startDate: startDate.toISOString(), endDate: endDate.toISOString() },
        sampleSize: events.length // Include sample size for transparency
      };
    } else if (aggregation === 'sessions') {
      // Group events by session_id to provide session-based analytics
      const sessionMap = new Map<string, any>();

      events.forEach(event => {
        const sessionId = event.session_id;
        if (!sessionId) return; // Skip events without session_id

        if (!sessionMap.has(sessionId)) {
          sessionMap.set(sessionId, {
            sessionId,
            events: [],
            startTime: event.timestamp,
            endTime: event.timestamp,
            eventCounts: {},
            totalEvents: 0
          });
        }

        const session = sessionMap.get(sessionId);
        session.events.push(event);
        session.totalEvents++;
        session.eventCounts[event.event_type] = (session.eventCounts[event.event_type] || 0) + 1;

        // Update session time range
        if (new Date(event.timestamp) < new Date(session.startTime)) {
          session.startTime = event.timestamp;
        }
        if (new Date(event.timestamp) > new Date(session.endTime)) {
          session.endTime = event.timestamp;
        }
      });

      // Convert map to array and calculate session durations
      const sessions = Array.from(sessionMap.values()).map(session => ({
        ...session,
        duration: Math.floor((new Date(session.endTime).getTime() - new Date(session.startTime).getTime()) / 1000),
        emailsGenerated: session.eventCounts['email_address_generated'] || 0,
        emailsReceived: session.eventCounts['email_received'] || 0,
        emailsViewed: session.eventCounts['email_opened'] || 0,
        emailsCopied: session.eventCounts['email_address_copied'] || 0,
        manualRefreshes: session.eventCounts['manual_refresh_triggered'] || 0
      }));

      result = {
        totalSessions: sessions.length,
        sessions: sessions.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime()),
        timeRange: { startDate: startDate.toISOString(), endDate: endDate.toISOString() },
        summary: {
          avgSessionDuration: sessions.length > 0 ? sessions.reduce((sum, s) => sum + s.duration, 0) / sessions.length : 0,
          avgEventsPerSession: sessions.length > 0 ? sessions.reduce((sum, s) => sum + s.totalEvents, 0) / sessions.length : 0,
          avgEmailsGeneratedPerSession: sessions.length > 0 ? sessions.reduce((sum, s) => sum + s.emailsGenerated, 0) / sessions.length : 0,
          avgEmailsReceivedPerSession: sessions.length > 0 ? sessions.reduce((sum, s) => sum + s.emailsReceived, 0) / sessions.length : 0
        }
      };
    }

    logInfo('AdminAnalyticsAPI', `Retrieved analytics data`, {
      timeRange,
      eventType,
      aggregation,
      eventCount: events.length
    });

    // Prepare response data
    const responseData = {
      data: result,
      meta: {
        timeRange,
        eventType,
        aggregation,
        totalEvents: events.length,
        queryTime: new Date().toISOString(),
        cached: false
      }
    };

    // Cache the result with appropriate TTL
    const ttl = aggregation === 'timeline'
      ? CACHE_TTL.TIMELINE_DATA
      : customStartDate && customEndDate
        ? CACHE_TTL.AGGREGATED_METRICS
        : CACHE_TTL.DASHBOARD_OVERVIEW;

    analyticsCache.set(cacheKey, {
      data: result,
      meta: responseData.meta,
      cacheTime: new Date().toISOString()
    }, ttl);

    return NextResponse.json({
      success: true,
      ...responseData
    });

  } catch (error) {
    logError('AdminAnalyticsAPI', 'Unexpected error in analytics endpoint', { error });
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/management-portal-x7z9y2/analytics
 * Create custom analytics queries
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, parameters } = body;

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Query is required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabaseClient();

    // For security, only allow specific predefined queries
    const allowedQueries = [
      'session_summary',
      'event_summary',
      'device_breakdown',
      'browser_breakdown'
    ];

    if (!allowedQueries.includes(query)) {
      return NextResponse.json(
        { success: false, error: 'Query not allowed' },
        { status: 400 }
      );
    }

    let result: any = {};

    switch (query) {
      case 'session_summary':
        // TODO: Fix session_analytics table type definition
        // Session summary temporarily disabled due to missing table
        result = {
          totalSessions: 0,
          avgSessionDuration: 0,
          totalEmailsGenerated: 0,
          totalEmailsViewed: 0,
          sessions: [],
          note: 'Session summary temporarily disabled - session_analytics table not available'
        };
        break;

      case 'device_breakdown':
        const { data: deviceData, error: deviceError } = await supabase
          .from('analytics_events')
          .select('device_type')
          .not('device_type', 'is', null);

        if (deviceError) {
          throw deviceError;
        }

        const deviceCounts = deviceData.reduce((acc: Record<string, number>, event) => {
          const deviceType = event.device_type || 'unknown';
          acc[deviceType] = (acc[deviceType] || 0) + 1;
          return acc;
        }, {});

        result = { deviceBreakdown: deviceCounts };
        break;

      case 'browser_breakdown':
        const { data: browserData, error: browserError } = await supabase
          .from('analytics_events')
          .select('browser')
          .not('browser', 'is', null);

        if (browserError) {
          throw browserError;
        }

        const browserCounts = browserData.reduce((acc: Record<string, number>, event) => {
          const browser = event.browser || 'unknown';
          acc[browser] = (acc[browser] || 0) + 1;
          return acc;
        }, {});

        result = { browserBreakdown: browserCounts };
        break;
    }

    return NextResponse.json({
      success: true,
      data: result,
      meta: {
        query,
        parameters,
        queryTime: new Date().toISOString()
      }
    });

  } catch (error) {
    logError('AdminAnalyticsAPI', 'Error in custom analytics query', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to execute query' },
      { status: 500 }
    );
  }
}
