'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';

interface SessionData {
  summary: {
    totalSessions: number;
    avgSessionDuration: number;
    totalEmailsGenerated: number;
    totalEmailsReceived: number;
    totalEmailsViewed: number;
    totalEmailsDeleted: number;
    totalManualRefreshes: number;
    totalCopyActions: number;
  };
  breakdowns: {
    device: Record<string, number>;
    browser: Record<string, number>;
    country: Record<string, number>;
    sessionDuration: Record<string, number>;
  };
  engagement: {
    avgEmailsGeneratedPerSession: number;
    avgEmailsViewedPerSession: number;
    avgManualRefreshesPerSession: number;
    avgCopyActionsPerSession: number;
    emailViewRate: number;
    emailDeleteRate: number;
  };
}

interface SessionAnalyticsChartProps {
  sessionData: SessionData;
}

/**
 * Session Analytics Chart Component
 * 
 * Displays session analytics data using various chart types including
 * session duration distribution and engagement metrics.
 */
export default function SessionAnalyticsChart({ sessionData }: SessionAnalyticsChartProps) {
  
  // Transform session duration data for the bar chart
  const durationData = Object.entries(sessionData?.breakdowns?.sessionDuration || {}).map(([range, count]) => ({
    range,
    count,
    percentage: ((count / (sessionData?.summary?.totalSessions || 1)) * 100).toFixed(1)
  }));

  // Transform engagement data for comparison chart
  const engagementData = [
    {
      metric: 'Emails Generated',
      value: sessionData?.engagement?.avgEmailsGeneratedPerSession || 0,
      color: '#10b981'
    },
    {
      metric: 'Emails Viewed',
      value: sessionData?.engagement?.avgEmailsViewedPerSession || 0,
      color: '#8b5cf6'
    },
    {
      metric: 'Manual Refreshes',
      value: sessionData?.engagement?.avgManualRefreshesPerSession || 0,
      color: '#06b6d4'
    },
    {
      metric: 'Copy Actions',
      value: sessionData?.engagement?.avgCopyActionsPerSession || 0,
      color: '#f59e0b'
    }
  ];

  // Transform device data for pie chart
  const deviceData = Object.entries(sessionData?.breakdowns?.device || {}).map(([device, count]) => ({
    name: device.charAt(0).toUpperCase() + device.slice(1),
    value: count,
    percentage: ((count / (sessionData?.summary?.totalSessions || 1)) * 100).toFixed(1)
  }));

  // Colors for pie chart
  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

  /**
   * Custom tooltip for bar chart
   */
  const CustomBarTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-1">{label}</p>
          <p className="text-sm text-blue-600">
            Sessions: {payload[0].value}
          </p>
          <p className="text-sm text-gray-600">
            {payload[0].payload.percentage}% of total
          </p>
        </div>
      );
    }
    return null;
  };

  /**
   * Custom tooltip for pie chart
   */
  const CustomPieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-1">{data.name}</p>
          <p className="text-sm text-blue-600">
            Sessions: {data.value}
          </p>
          <p className="text-sm text-gray-600">
            {data.percentage}% of total
          </p>
        </div>
      );
    }
    return null;
  };

  /**
   * Custom label for pie chart
   */
  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // Don't show labels for slices smaller than 5%
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div className="space-y-6">
      {/* Session Duration Distribution */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-3">Session Duration Distribution</h4>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={durationData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="range" 
                stroke="#666"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis stroke="#666" fontSize={12} />
              <Tooltip content={<CustomBarTooltip />} />
              <Bar 
                dataKey="count" 
                fill="#3b82f6" 
                radius={[4, 4, 0, 0]}
                name="Sessions"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Engagement Metrics and Device Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Average Actions per Session */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Average Actions per Session</h4>
          <div className="h-48">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={engagementData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="metric" 
                  stroke="#666"
                  fontSize={11}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis stroke="#666" fontSize={12} />
                <Tooltip 
                  formatter={(value: number) => [value.toFixed(2), 'Average']}
                  labelStyle={{ color: '#374151' }}
                />
                <Bar 
                  dataKey="value" 
                  radius={[4, 4, 0, 0]}
                  name="Average"
                >
                  {engagementData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Device Breakdown */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Device Distribution</h4>
          <div className="h-48">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={deviceData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {deviceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip content={<CustomPieTooltip />} />
                <Legend 
                  verticalAlign="bottom" 
                  height={36}
                  formatter={(value, entry: any) => (
                    <span style={{ color: entry.color }}>{value}</span>
                  )}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {(sessionData?.engagement?.emailViewRate || 0).toFixed(1)}%
          </div>
          <div className="text-sm text-gray-600">Email View Rate</div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {(sessionData?.engagement?.emailDeleteRate || 0).toFixed(1)}%
          </div>
          <div className="text-sm text-gray-600">Email Delete Rate</div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {Math.round(sessionData?.summary?.avgSessionDuration || 0)}s
          </div>
          <div className="text-sm text-gray-600">Avg Duration</div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {sessionData?.summary?.totalSessions || 0}
          </div>
          <div className="text-sm text-gray-600">Total Sessions</div>
        </div>
      </div>
    </div>
  );
}
