/**
 * Clear Analytics Data API Endpoint
 * 
 * This endpoint allows clearing all analytics data for testing purposes.
 * Should only be used in development/testing environments.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logError, logInfo } from '@/lib/logging';

/**
 * POST /api/management-portal-x7z9y2/analytics/clear
 * Clear all analytics data (events and sessions)
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    logInfo('ClearAnalytics', 'Starting analytics data clearing process');

    // Clear analytics events in batches to handle large datasets
    let totalEventsCleared = 0;
    let batchSize = 1000;
    let hasMoreEvents = true;

    while (hasMoreEvents) {
      const { data: eventsBatch, error: fetchError } = await supabase
        .from('analytics_events')
        .select('id')
        .limit(batchSize);

      if (fetchError) {
        logError('ClearAnalytics', 'Failed to fetch analytics events batch', { error: fetchError });
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to fetch analytics events for deletion',
            details: fetchError.message
          },
          { status: 500 }
        );
      }

      if (!eventsBatch || eventsBatch.length === 0) {
        hasMoreEvents = false;
        break;
      }

      const eventIds = eventsBatch.map(event => event.id);
      const { error: deleteError, count: deletedCount } = await supabase
        .from('analytics_events')
        .delete()
        .in('id', eventIds);

      if (deleteError) {
        logError('ClearAnalytics', 'Failed to delete analytics events batch', { error: deleteError });
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to clear analytics events',
            details: deleteError.message
          },
          { status: 500 }
        );
      }

      totalEventsCleared += deletedCount || eventsBatch.length;

      // If we got fewer records than the batch size, we're done
      if (eventsBatch.length < batchSize) {
        hasMoreEvents = false;
      }
    }

    // Clear session analytics in batches
    let totalSessionsCleared = 0;
    let hasMoreSessions = true;

    while (hasMoreSessions) {
      const { data: sessionsBatch, error: fetchError } = await supabase
        .from('session_analytics')
        .select('id')
        .limit(batchSize);

      if (fetchError) {
        logError('ClearAnalytics', 'Failed to fetch session analytics batch', { error: fetchError });
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to fetch session analytics for deletion',
            details: fetchError.message
          },
          { status: 500 }
        );
      }

      if (!sessionsBatch || sessionsBatch.length === 0) {
        hasMoreSessions = false;
        break;
      }

      const sessionIds = sessionsBatch.map(session => session.id);
      const { error: deleteError, count: deletedCount } = await supabase
        .from('session_analytics')
        .delete()
        .in('id', sessionIds);

      if (deleteError) {
        logError('ClearAnalytics', 'Failed to delete session analytics batch', { error: deleteError });
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to clear session analytics',
            details: deleteError.message
          },
          { status: 500 }
        );
      }

      totalSessionsCleared += deletedCount || sessionsBatch.length;

      // If we got fewer records than the batch size, we're done
      if (sessionsBatch.length < batchSize) {
        hasMoreSessions = false;
      }
    }

    const result = {
      success: true,
      message: 'Analytics data cleared successfully',
      cleared: {
        analyticsEvents: totalEventsCleared,
        sessionAnalytics: totalSessionsCleared
      },
      timestamp: new Date().toISOString()
    };

    logInfo('ClearAnalytics', 'Analytics data cleared successfully', result);

    return NextResponse.json(result);

  } catch (error) {
    logError('ClearAnalytics', 'Unexpected error clearing analytics data', { error });
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Unexpected error occurred while clearing analytics data',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/management-portal-x7z9y2/analytics/clear
 * Get information about what would be cleared (for confirmation)
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Get count of analytics events
    const { count: eventsCount, error: eventsError } = await supabase
      .from('analytics_events')
      .select('*', { count: 'exact', head: true });

    if (eventsError) {
      logError('ClearAnalytics', 'Failed to count analytics events', { error: eventsError });
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to count analytics events',
          details: eventsError.message 
        },
        { status: 500 }
      );
    }

    // Get count of session analytics
    const { count: sessionsCount, error: sessionsError } = await supabase
      .from('session_analytics')
      .select('*', { count: 'exact', head: true });

    if (sessionsError) {
      logError('ClearAnalytics', 'Failed to count session analytics', { error: sessionsError });
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to count session analytics',
          details: sessionsError.message
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      counts: {
        analyticsEvents: eventsCount || 0,
        sessionAnalytics: sessionsCount || 0,
        total: (eventsCount || 0) + (sessionsCount || 0)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logError('ClearAnalytics', 'Unexpected error getting analytics counts', { error });
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Unexpected error occurred while getting analytics counts',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
