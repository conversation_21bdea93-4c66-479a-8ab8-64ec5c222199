# Sitemap Configuration Guide for VanishPost

This guide explains the sitemap configuration for the VanishPost application, ensuring proper indexing by search engines.

## Sitemap Files

VanishPost uses two sitemap files:

1. **sitemap.xml** - The main sitemap listing all pages
2. **image-sitemap.xml** - A specialized sitemap for images

Both sitemaps are referenced in the robots.txt file to ensure search engines can discover them.

## Main Sitemap (sitemap.xml)

The main sitemap includes all pages of the website with metadata about update frequency and priority:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://vanishpost.com/</loc>
    <lastmod>2024-07-01</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <!-- Additional URLs... -->
</urlset>
```

### Key Elements in Main Sitemap

- **loc**: The URL of the page
- **lastmod**: The date the page was last modified (YYYY-MM-DD format)
- **changefreq**: How frequently the page is likely to change (weekly, monthly, etc.)
- **priority**: The priority of this URL relative to other URLs (0.0 to 1.0)

## Image Sitemap (image-sitemap.xml)

The image sitemap provides information about images on the website:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
  <url>
    <loc>https://vanishpost.com/</loc>
    <lastmod>2024-07-01</lastmod>
    <image:image>
      <image:loc>https://vanishpost.com/vanishpost-temporary-email-logo.svg</image:loc>
      <image:title>VanishPost Secure Temporary Email Service</image:title>
      <image:caption>Secure temporary email addresses that expire in 15 minutes</image:caption>
    </image:image>
    <!-- Additional images... -->
  </url>
  <!-- Additional URLs... -->
</urlset>
```

### Key Elements in Image Sitemap

- **loc**: The URL of the page containing the image
- **lastmod**: The date the page was last modified
- **image:loc**: The URL of the image
- **image:title**: The title of the image
- **image:caption**: A caption for the image

### Optional Image Sitemap Elements

These elements can be added for more detailed image information:

- **image:geo_location**: The geographic location of the image (e.g., "London, UK")
- **image:license**: The URL of the license for the image
- **image:alt_text**: Alternative text for the image (for accessibility)

## Robots.txt Configuration

The robots.txt file references both sitemaps:

```
User-agent: *
Allow: /

# Disallow admin paths
Disallow: /management-portal-x7z9y2/
Disallow: /api/management-portal-x7z9y2/
Disallow: /admin-portal/
Disallow: /api/admin-portal/

# Sitemaps
Sitemap: https://vanishpost.com/sitemap.xml
Sitemap: https://vanishpost.com/image-sitemap.xml
```

## Best Practices for Sitemaps

1. **Keep Sitemaps Updated**: Update sitemaps when adding new pages or making significant changes to existing pages.

2. **Use Absolute URLs**: Always use absolute URLs (including domain) for all URLs in sitemaps.

3. **Include All Important Pages**: Ensure all important pages are included in the main sitemap.

4. **Limit Sitemap Size**: Keep sitemaps under 50MB and 50,000 URLs. If needed, split into multiple sitemaps.

5. **Include Accurate lastmod Dates**: Use accurate last modified dates to help search engines prioritize crawling.

6. **Set Appropriate Priorities**: Use the priority attribute to indicate the relative importance of pages.

7. **Include Image Metadata**: Provide detailed metadata for images to improve image search visibility.

8. **Reference Sitemaps in robots.txt**: Ensure both sitemaps are referenced in the robots.txt file.

9. **Submit Sitemaps to Search Engines**: Submit sitemaps to Google Search Console and Bing Webmaster Tools.

10. **Validate Sitemaps**: Use tools like Google Search Console to validate sitemaps and check for errors.

## Updating Sitemaps

When updating sitemaps:

1. **Add New Pages**: Add new pages to both the main sitemap and image sitemap (if they contain images).

2. **Update lastmod Dates**: Update the lastmod dates for pages that have been modified.

3. **Maintain Consistency**: Ensure consistency between the main sitemap and image sitemap.

4. **Validate After Changes**: Validate sitemaps after making changes to ensure they remain valid.

5. **Resubmit to Search Engines**: Resubmit updated sitemaps to search engines.

## Sitemap Validation

You can validate your sitemaps using these tools:

- [Google Search Console](https://search.google.com/search-console)
- [XML Sitemap Validator](https://www.xml-sitemaps.com/validate-xml-sitemap.html)
- [Bing Webmaster Tools](https://www.bing.com/webmasters/about)

## Monitoring Sitemap Performance

Monitor how search engines are using your sitemaps:

1. **Check Indexing Status**: Use Google Search Console to check how many URLs from your sitemaps are indexed.

2. **Monitor Crawl Stats**: Review crawl statistics to see how often search engines are crawling your site.

3. **Check for Errors**: Look for any sitemap errors reported by search engines.

4. **Track Image Search Performance**: Monitor performance in image search results.

## Automating Sitemap Updates

Consider implementing automated sitemap generation:

1. **Dynamic Generation**: Generate sitemaps dynamically based on your content.

2. **Scheduled Updates**: Set up scheduled tasks to update sitemaps regularly.

3. **Integration with CMS**: If using a CMS, integrate sitemap generation with content publishing workflows.

4. **Notification System**: Implement notifications for when sitemaps need to be updated.
