'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

/**
 * Component to display unread message count on the admin dashboard
 */
export default function MessageCountCard() {
  const [unreadMessageCount, setUnreadMessageCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUnreadMessageCount = async () => {
      try {
        setLoading(true);
        
        const response = await fetch('/api/management-portal-x7z9y2/messages/count?status=unread');
        const data = await response.json();
        
        if (data.success) {
          setUnreadMessageCount(data.count);
        } else {
          setError('Failed to fetch message count');
        }
      } catch (err) {
        setError('An error occurred while fetching message count');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUnreadMessageCount();
    
    // Set up polling to refresh the count every minute
    const intervalId = setInterval(fetchUnreadMessageCount, 60000);
    
    return () => clearInterval(intervalId);
  }, []);

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0 bg-indigo-500 rounded-md p-3">
            <svg className="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dt className="text-sm font-medium text-gray-500 truncate">
              Unread Messages
            </dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-gray-900">
                {loading ? '...' : unreadMessageCount}
              </div>
              {error && (
                <div className="ml-2 text-xs text-red-500">
                  Error loading count
                </div>
              )}
            </dd>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 px-5 py-3">
        <div className="text-sm">
          <Link href="/management-portal-x7z9y2/messages" className="font-medium text-indigo-600 hover:text-indigo-500">
            View all messages
          </Link>
        </div>
      </div>
    </div>
  );
}
