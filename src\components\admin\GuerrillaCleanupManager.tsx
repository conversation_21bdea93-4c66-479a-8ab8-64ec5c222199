/**
 * Guerrilla Email Cleanup Manager Component
 * 
 * Dedicated interface for managing Guerrilla email cleanup operations
 * Operates independently from the general cleanup system
 */

import { useState, useEffect, useCallback } from 'react';
import { ArrowPathIcon, PlayIcon, StopIcon, TrashIcon, ClockIcon, CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/solid';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

interface GuerrillaCleanupResult {
  success: boolean;
  guerrillaEmailsDeleted: number;
  ageThresholdHours: number;
  cutoffTime: string;
  duration: number;
  timestamp: string;
  error?: string;
  message?: string;
}

interface GuerrillaCleanupSchedulerStatus {
  running: boolean;
  intervalMinutes: number;
  nextRunAt?: string;
  autoStart?: boolean;
  ageThresholdHours: number;
  cleanupMode: 'manual' | 'auto';
  lastRunTime?: string;
}

interface GuerrillaCleanupHistoryItem {
  id: string;
  timestamp: string;
  success: boolean;
  guerrillaEmailsDeleted: number;
  duration: number;
  ageThresholdHours: number;
  error?: string;
}

interface GuerrillaCleanupManagerProps {
  apiBasePath?: string;
}

export default function GuerrillaCleanupManager({
  apiBasePath = '/api/management-portal-x7z9y2'
}: GuerrillaCleanupManagerProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastResult, setLastResult] = useState<GuerrillaCleanupResult | null>(null);
  const [schedulerStatus, setSchedulerStatus] = useState<GuerrillaCleanupSchedulerStatus | null>(null);
  const [cleanupHistory, setCleanupHistory] = useState<GuerrillaCleanupHistoryItem[]>([]);
  const [countdown, setCountdown] = useState<number | null>(null);

  // Configuration states
  const [ageThresholdHours, setAgeThresholdHours] = useState(24);
  const [cleanupMode, setCleanupMode] = useState<'manual' | 'auto'>('manual');
  const [cleanupInterval, setCleanupInterval] = useState(60); // Default: 60 minutes
  const [autoStart, setAutoStart] = useState(true);

  // Countdown timer effect
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (schedulerStatus?.running && schedulerStatus.nextRunAt) {
      const updateCountdown = () => {
        const nextRun = new Date(schedulerStatus.nextRunAt!).getTime();
        const now = Date.now();
        const timeLeft = Math.max(0, Math.floor((nextRun - now) / 1000));
        setCountdown(timeLeft);

        // If countdown reaches 0, refresh scheduler status
        if (timeLeft === 0) {
          fetchSchedulerStatus();
        }
      };

      updateCountdown();
      intervalId = setInterval(updateCountdown, 1000);
    } else {
      setCountdown(null);
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [schedulerStatus?.running, schedulerStatus?.nextRunAt]);

  // Format countdown display
  const formatCountdown = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Fetch scheduler status
  const fetchSchedulerStatus = useCallback(async () => {
    try {
      const response = await fetch(`${apiBasePath}/guerrilla-cleanup/status`);
      const data = await response.json();

      if (data.success) {
        setSchedulerStatus(data.status);
        setAgeThresholdHours(data.status.ageThresholdHours);
        setCleanupMode(data.status.cleanupMode);
        setCleanupInterval(data.status.intervalMinutes);
        setAutoStart(data.status.autoStart !== false);
      } else {
        setError(data.error || 'Failed to fetch scheduler status');
      }
    } catch (error) {
      console.error('Error fetching scheduler status:', error);
      setError('Failed to fetch scheduler status');
    }
  }, [apiBasePath]);

  // Run manual cleanup
  const runCleanup = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${apiBasePath}/guerrilla-cleanup`);
      const data = await response.json();
      setLastResult(data);

      // Add to history
      const historyItem: GuerrillaCleanupHistoryItem = {
        id: Date.now().toString(),
        timestamp: data.timestamp,
        success: data.success,
        guerrillaEmailsDeleted: data.guerrillaEmailsDeleted,
        duration: data.duration,
        ageThresholdHours: data.ageThresholdHours,
        error: data.error
      };

      setCleanupHistory(prev => [historyItem, ...prev.slice(0, 2)]); // Keep last 3 operations

      // Refresh scheduler status
      fetchSchedulerStatus();
    } catch (error) {
      console.error('Error running cleanup:', error);
      setError('Failed to run cleanup');
    } finally {
      setLoading(false);
    }
  };

  // Start scheduler
  const startScheduler = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${apiBasePath}/guerrilla-cleanup/scheduler`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'start',
          intervalMinutes: cleanupInterval,
          autoStart: autoStart
        })
      });

      const data = await response.json();

      if (data.success) {
        fetchSchedulerStatus();
      } else {
        setError(data.error || 'Failed to start scheduler');
      }
    } catch (error) {
      console.error('Error starting scheduler:', error);
      setError('Failed to start scheduler');
    } finally {
      setLoading(false);
    }
  };

  // Stop scheduler
  const stopScheduler = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${apiBasePath}/guerrilla-cleanup/scheduler`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'stop' })
      });

      const data = await response.json();

      if (data.success) {
        fetchSchedulerStatus();
      } else {
        setError(data.error || 'Failed to stop scheduler');
      }
    } catch (error) {
      console.error('Error stopping scheduler:', error);
      setError('Failed to stop scheduler');
    } finally {
      setLoading(false);
    }
  };

  // Save configuration
  const saveConfiguration = async () => {
    setLoading(true);
    setError(null);

    try {
      // Update age threshold and mode
      const configResponse = await fetch(`${apiBasePath}/config`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          guerrillaCleanupAgeThresholdHours: ageThresholdHours,
          guerrillaCleanupMode: cleanupMode,
          guerrillaCleanupIntervalMinutes: cleanupInterval,
          guerrillaCleanupAutoStart: autoStart
        })
      });

      if (!configResponse.ok) {
        throw new Error('Failed to save configuration');
      }

      // If switching to auto mode and not already running, start scheduler
      if (cleanupMode === 'auto' && !schedulerStatus?.running) {
        await startScheduler();
      }
      // If switching to manual mode and running, stop scheduler
      else if (cleanupMode === 'manual' && schedulerStatus?.running) {
        await stopScheduler();
      }

      fetchSchedulerStatus();
    } catch (error) {
      console.error('Error saving configuration:', error);
      setError('Failed to save configuration');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSchedulerStatus();
  }, [fetchSchedulerStatus]);

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800 flex items-center">
            <TrashIcon className="h-6 w-6 mr-2 text-red-600" />
            Guerrilla Email Cleanup Manager
          </h2>
          <p className="mt-1 text-sm text-gray-600">
            Dedicated cleanup system for old Guerrilla emails stored in MySQL database
          </p>
        </div>

        <div className="px-4 py-5 sm:px-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">{error}</div>
                </div>
              </div>
            </div>
          )}

          {/* Configuration Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
              Cleanup Configuration
            </h3>

            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <div>
                <label htmlFor="age-threshold" className="block text-sm font-medium text-gray-700">
                  Age Threshold (hours)
                </label>
                <div className="mt-1">
                  <Input
                    type="number"
                    name="age-threshold"
                    id="age-threshold"
                    min="1"
                    max="8760"
                    value={ageThresholdHours}
                    onChange={(e) => setAgeThresholdHours(Number(e.target.value))}
                    disabled={loading}
                    leftIcon={<ClockIcon className="h-4 w-4" />}
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  Delete Guerrilla emails older than this many hours
                </p>
              </div>

              <div>
                <label htmlFor="cleanup-mode" className="block text-sm font-medium text-gray-700">
                  Cleanup Mode
                </label>
                <div className="mt-1">
                  <select
                    id="cleanup-mode"
                    value={cleanupMode}
                    onChange={(e) => setCleanupMode(e.target.value as 'manual' | 'auto')}
                    disabled={loading}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="manual">Manual</option>
                    <option value="auto">Auto</option>
                  </select>
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  {cleanupMode === 'manual' ? 'Run cleanup manually on-demand' : 'Run cleanup automatically at intervals'}
                </p>
              </div>
            </div>

            <div className="mt-6">
              <Button
                onClick={saveConfiguration}
                disabled={loading}
                className="flex items-center"
              >
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                Save Configuration
              </Button>
            </div>
          </div>

          {/* Manual Cleanup Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <TrashIcon className="h-5 w-5 mr-2 text-red-600" />
              Manual Cleanup
            </h3>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Run Guerrilla Email Cleanup
                  </p>
                  <p className="text-sm text-gray-500">
                    Delete Guerrilla emails older than {ageThresholdHours} hours from MySQL database
                  </p>
                </div>
                <Button
                  onClick={runCleanup}
                  disabled={loading}
                  className="flex items-center"
                >
                  <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Run Cleanup
                </Button>
              </div>

              {lastResult && (
                <div className="mt-4 p-4 bg-white rounded-lg border">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {lastResult.success ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
                      )}
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {lastResult.success ? 'Cleanup Successful' : 'Cleanup Failed'}
                        </p>
                        <p className="text-sm text-gray-500">
                          {new Date(lastResult.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {lastResult.guerrillaEmailsDeleted} emails deleted
                      </p>
                      <p className="text-sm text-gray-500">
                        {(lastResult.duration / 1000).toFixed(2)}s duration
                      </p>
                    </div>
                  </div>
                  {lastResult.error && (
                    <div className="mt-2 text-sm text-red-600">
                      Error: {lastResult.error}
                    </div>
                  )}
                  {lastResult.message && (
                    <div className="mt-2 text-sm text-gray-600">
                      {lastResult.message}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Auto Scheduler Section */}
          {cleanupMode === 'auto' && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <ClockIcon className="h-5 w-5 mr-2 text-indigo-600" />
                Auto Scheduler Configuration
              </h3>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-3">
                  <div>
                    <label htmlFor="cleanup-interval" className="block text-sm font-medium text-gray-700">
                      Cleanup Interval (minutes)
                    </label>
                    <div className="mt-1">
                      <Input
                        type="number"
                        name="cleanup-interval"
                        id="cleanup-interval"
                        min="1"
                        max="1440"
                        value={cleanupInterval}
                        onChange={(e) => setCleanupInterval(Number(e.target.value))}
                        disabled={schedulerStatus?.running || loading}
                        leftIcon={<ClockIcon className="h-4 w-4" />}
                      />
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      {schedulerStatus?.running
                        ? `Current interval: ${schedulerStatus.intervalMinutes} minutes`
                        : 'Set the interval between cleanup runs'}
                    </p>
                  </div>

                  <div>
                    <label htmlFor="auto-start" className="block text-sm font-medium text-gray-700">
                      Auto-Start on App Load
                    </label>
                    <div className="mt-3">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={autoStart}
                          onChange={(e) => setAutoStart(e.target.checked)}
                          disabled={loading}
                          className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          Start scheduler automatically when application loads
                        </span>
                      </label>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      When enabled, the scheduler will start automatically on app startup
                    </p>
                  </div>

                  {schedulerStatus?.running && schedulerStatus.nextRunAt && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                        </svg>
                        Next Run
                      </h4>
                      <div className="mt-2 space-y-1">
                        <p className="text-sm text-gray-900">
                          {new Date(schedulerStatus.nextRunAt).toLocaleString()}
                        </p>
                        {countdown !== null && (
                          <div className="flex items-center">
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                            <span className="text-lg font-mono font-bold text-indigo-600">
                              {formatCountdown(countdown)}
                            </span>
                          </div>
                        )}
                        <p className="text-xs text-gray-500">
                          {formatDistanceToNow(new Date(schedulerStatus.nextRunAt), { addSuffix: true })}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="mt-6 flex items-center space-x-4">
                  {schedulerStatus?.running ? (
                    <Button
                      onClick={stopScheduler}
                      disabled={loading}
                      variant="secondary"
                      className="flex items-center"
                    >
                      <StopIcon className="h-4 w-4 mr-2" />
                      Stop Scheduler
                    </Button>
                  ) : (
                    <Button
                      onClick={startScheduler}
                      disabled={loading}
                      className="flex items-center"
                    >
                      <PlayIcon className="h-4 w-4 mr-2" />
                      Start Scheduler
                    </Button>
                  )}

                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-2 ${schedulerStatus?.running ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                    <span className="text-sm text-gray-600">
                      {schedulerStatus?.running ? 'Running' : 'Stopped'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Operation History */}
          {cleanupHistory.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                Recent Operations
              </h3>
              <div className="bg-white shadow overflow-hidden rounded-lg">
                <div className="px-4 py-5 sm:px-6">
                  <h4 className="text-lg font-medium text-gray-900">
                    Last {cleanupHistory.length} Guerrilla Cleanup Operations
                  </h4>
                  <p className="mt-1 text-sm text-gray-500">
                    History of recent Guerrilla email cleanup operations with their results
                  </p>
                </div>
                <div className="border-t border-gray-200">
                  <div className="space-y-0">
                    {cleanupHistory.map((item, index) => (
                      <div key={item.id} className={`px-4 py-4 ${index !== cleanupHistory.length - 1 ? 'border-b border-gray-200' : ''}`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {item.success ? (
                              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
                            ) : (
                              <XCircleIcon className="h-5 w-5 text-red-500 mr-3" />
                            )}
                            <div>
                              <p className="text-sm font-medium text-gray-900">
                                {item.success ? 'Guerrilla Cleanup Successful' : 'Guerrilla Cleanup Failed'}
                              </p>
                              <p className="text-sm text-gray-500">
                                {new Date(item.timestamp).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span>Deleted: {item.guerrillaEmailsDeleted}</span>
                              <span>Threshold: {item.ageThresholdHours}h</span>
                              <span>{(item.duration / 1000).toFixed(2)}s</span>
                            </div>
                            {item.error && (
                              <div className="mt-1 flex items-center text-sm text-red-600">
                                <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                                <span className="truncate max-w-xs">{item.error}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
