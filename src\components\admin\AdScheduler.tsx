'use client';

import { useState, useEffect } from 'react';
import { AdConfig, AdSchedule, DaySchedule } from '@/lib/config/types';
import { format, parse, isValid } from 'date-fns';

interface AdSchedulerProps {
  adPlacement: AdConfig;
  onUpdate: (placementId: string, domain: string, updates: any) => Promise<void>;
}

export default function AdScheduler({ adPlacement, onUpdate }: AdSchedulerProps) {
  // Initialize schedule with either existing schedule or default values
  const [schedule, setSchedule] = useState<AdSchedule>(() => {
    // If the ad placement has a schedule with useSchedule property, use it
    if (adPlacement.schedule?.useSchedule !== undefined) {
      return adPlacement.schedule;
    }

    // If the ad placement has a legacy schedule, convert it to the new format
    if (adPlacement.schedule) {
      const legacySchedule = adPlacement.schedule;

      // Create a new schedule with the weekly format
      return {
        useSchedule: true,
        // Keep legacy properties for backward compatibility
        ...legacySchedule,
        // Add default day schedules
        monday: { enabled: true, startTime: legacySchedule.startTime || '09:00', endTime: legacySchedule.endTime || '17:00' },
        tuesday: { enabled: true, startTime: legacySchedule.startTime || '09:00', endTime: legacySchedule.endTime || '17:00' },
        wednesday: { enabled: true, startTime: legacySchedule.startTime || '09:00', endTime: legacySchedule.endTime || '17:00' },
        thursday: { enabled: true, startTime: legacySchedule.startTime || '09:00', endTime: legacySchedule.endTime || '17:00' },
        friday: { enabled: true, startTime: legacySchedule.startTime || '09:00', endTime: legacySchedule.endTime || '17:00' },
        saturday: { enabled: false, startTime: '00:00', endTime: '23:59' },
        sunday: { enabled: false, startTime: '00:00', endTime: '23:59' },
      };
    }

    // If no schedule exists, create a default one
    return {
      useSchedule: false,
      // Legacy format properties
      isRecurring: false,
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
      // New weekly format
      monday: { enabled: true, startTime: '09:00', endTime: '17:00' },
      tuesday: { enabled: true, startTime: '09:00', endTime: '17:00' },
      wednesday: { enabled: true, startTime: '09:00', endTime: '17:00' },
      thursday: { enabled: true, startTime: '09:00', endTime: '17:00' },
      friday: { enabled: true, startTime: '09:00', endTime: '17:00' },
      saturday: { enabled: false, startTime: '00:00', endTime: '23:59' },
      sunday: { enabled: false, startTime: '00:00', endTime: '23:59' },
    };
  });

  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'basic' | 'advanced' | 'weekly'>('weekly');

  // Format date for input
  const formatDateForInput = (dateString?: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return isValid(date) ? format(date, 'yyyy-MM-dd') : '';
  };

  // Format time for input
  const formatTimeForInput = (timeString?: string): string => {
    if (!timeString) return '';
    try {
      const time = parse(timeString, 'HH:mm', new Date());
      return isValid(time) ? format(time, 'HH:mm') : '';
    } catch (error) {
      return '';
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);

      // Update ad placement with schedule
      await onUpdate(adPlacement.placementId, adPlacement.domain, {
        schedule: schedule.useSchedule ? schedule : null
      });

      // Show success message or handle success
    } catch (error) {
      console.error('Error updating schedule:', error);
      // Show error message
    } finally {
      setSaving(false);
    }
  };

  // Handle day schedule update
  const updateDaySchedule = (day: string, field: 'enabled' | 'startTime' | 'endTime', value: boolean | string) => {
    const dayKey = day as keyof AdSchedule;
    const currentDaySchedule = schedule[dayKey] as DaySchedule | undefined;

    setSchedule({
      ...schedule,
      [dayKey]: {
        ...(currentDaySchedule || { enabled: false, startTime: '00:00', endTime: '23:59' }),
        [field]: value
      }
    });
  };

  // Handle day of week toggle
  const toggleDayOfWeek = (day: number) => {
    const daysOfWeek = schedule.daysOfWeek || [];

    if (daysOfWeek.includes(day)) {
      setSchedule({
        ...schedule,
        daysOfWeek: daysOfWeek.filter(d => d !== day)
      });
    } else {
      setSchedule({
        ...schedule,
        daysOfWeek: [...daysOfWeek, day].sort()
      });
    }
  };

  // Add special date
  const addSpecialDate = () => {
    const specialDates = schedule.specialDates || [];
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    setSchedule({
      ...schedule,
      specialDates: [
        ...specialDates,
        {
          date: format(tomorrow, 'yyyy-MM-dd'),
          isEnabled: true
        }
      ]
    });
  };

  // Remove special date
  const removeSpecialDate = (index: number) => {
    const specialDates = schedule.specialDates || [];

    setSchedule({
      ...schedule,
      specialDates: specialDates.filter((_, i) => i !== index)
    });
  };

  // Update special date
  const updateSpecialDate = (index: number, field: 'date' | 'isEnabled', value: string | boolean) => {
    const specialDates = [...(schedule.specialDates || [])];
    specialDates[index] = {
      ...specialDates[index],
      [field]: value
    };

    setSchedule({
      ...schedule,
      specialDates
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex items-center">
        <input
          id="has-schedule"
          type="checkbox"
          checked={schedule.useSchedule}
          onChange={(e) => setSchedule({ ...schedule, useSchedule: e.target.checked })}
          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
        />
        <label htmlFor="has-schedule" className="ml-2 block text-sm text-gray-900">
          Enable scheduling for this ad placement
        </label>
      </div>

      {schedule.useSchedule && (
        <>
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                type="button"
                onClick={() => setActiveTab('weekly')}
                className={`${
                  activeTab === 'weekly'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Weekly Schedule
              </button>
              <button
                type="button"
                onClick={() => setActiveTab('basic')}
                className={`${
                  activeTab === 'basic'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Basic Schedule
              </button>
              <button
                type="button"
                onClick={() => setActiveTab('advanced')}
                className={`${
                  activeTab === 'advanced'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Advanced Options
              </button>
            </nav>
          </div>

          {activeTab === 'weekly' && (
            <div className="space-y-6 mt-4">
              <h3 className="text-lg font-medium text-gray-900">Weekly Schedule</h3>
              <p className="text-sm text-gray-500">Set specific times for each day of the week when this ad should be displayed.</p>

              <div className="space-y-4">
                {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => {
                  const daySchedule = schedule[day as keyof AdSchedule] as { enabled: boolean; startTime: string; endTime: string } | undefined;
                  const isEnabled = daySchedule?.enabled ?? false;

                  return (
                    <div key={day} className="border border-gray-200 rounded-md p-4">
                      <div className="flex items-center justify-between">
                        <h4 className="text-base font-medium text-gray-900 capitalize">{day}</h4>
                        <button
                          type="button"
                          role="switch"
                          aria-checked={isEnabled}
                          onClick={() => updateDaySchedule(day, 'enabled', !isEnabled)}
                          className={`${
                            isEnabled ? 'bg-indigo-600' : 'bg-gray-200'
                          } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2`}
                        >
                          <span
                            aria-hidden="true"
                            className={`${
                              isEnabled ? 'translate-x-5' : 'translate-x-0'
                            } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                          />
                        </button>
                      </div>

                      {isEnabled && (
                        <div className="mt-4 grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                          <div>
                            <label htmlFor={`${day}-start-time`} className="block text-sm font-medium text-gray-700">
                              Start Time
                            </label>
                            <div className="mt-1">
                              <input
                                type="time"
                                id={`${day}-start-time`}
                                value={daySchedule?.startTime || ''}
                                onChange={(e) => updateDaySchedule(day, 'startTime', e.target.value)}
                                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                              />
                            </div>
                          </div>

                          <div>
                            <label htmlFor={`${day}-end-time`} className="block text-sm font-medium text-gray-700">
                              End Time
                            </label>
                            <div className="mt-1">
                              <input
                                type="time"
                                id={`${day}-end-time`}
                                value={daySchedule?.endTime || ''}
                                onChange={(e) => updateDaySchedule(day, 'endTime', e.target.value)}
                                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <div className="sm:col-span-3">
                  <label htmlFor="start-date" className="block text-sm font-medium text-gray-700">
                    Start Date
                  </label>
                  <div className="mt-1">
                    <input
                      type="date"
                      id="start-date"
                      value={formatDateForInput(schedule.startDate)}
                      onChange={(e) => setSchedule({ ...schedule, startDate: e.target.value })}
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Leave blank for no start date restriction
                  </p>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="end-date" className="block text-sm font-medium text-gray-700">
                    End Date
                  </label>
                  <div className="mt-1">
                    <input
                      type="date"
                      id="end-date"
                      value={formatDateForInput(schedule.endDate)}
                      onChange={(e) => setSchedule({ ...schedule, endDate: e.target.value })}
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Leave blank for no end date restriction
                  </p>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="start-time" className="block text-sm font-medium text-gray-700">
                    Start Time
                  </label>
                  <div className="mt-1">
                    <input
                      type="time"
                      id="start-time"
                      value={formatTimeForInput(schedule.startTime)}
                      onChange={(e) => setSchedule({ ...schedule, startTime: e.target.value })}
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Leave blank to show all day
                  </p>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="end-time" className="block text-sm font-medium text-gray-700">
                    End Time
                  </label>
                  <div className="mt-1">
                    <input
                      type="time"
                      id="end-time"
                      value={formatTimeForInput(schedule.endTime)}
                      onChange={(e) => setSchedule({ ...schedule, endTime: e.target.value })}
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Leave blank to show all day
                  </p>
                </div>

                <div className="sm:col-span-6">
                  <fieldset>
                    <legend className="text-sm font-medium text-gray-700">Days of Week</legend>
                    <div className="mt-2 flex flex-wrap gap-3">
                      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
                        <div key={day} className="flex items-center">
                          <input
                            id={`day-${index}`}
                            type="checkbox"
                            checked={(schedule.daysOfWeek || []).includes(index)}
                            onChange={() => toggleDayOfWeek(index)}
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`day-${index}`} className="ml-2 block text-sm text-gray-700">
                            {day}
                          </label>
                        </div>
                      ))}
                    </div>
                  </fieldset>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'advanced' && (
            <div className="space-y-6">
              <div className="flex items-center">
                <input
                  id="is-recurring"
                  type="checkbox"
                  checked={schedule.isRecurring || false}
                  onChange={(e) => setSchedule({ ...schedule, isRecurring: e.target.checked })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="is-recurring" className="ml-2 block text-sm text-gray-900">
                  Enable recurring schedule
                </label>
              </div>

              {schedule.isRecurring && (
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                  <div className="sm:col-span-3">
                    <label htmlFor="recurring-pattern" className="block text-sm font-medium text-gray-700">
                      Recurring Pattern
                    </label>
                    <div className="mt-1">
                      <select
                        id="recurring-pattern"
                        value={schedule.recurringPattern || 'daily'}
                        onChange={(e) => setSchedule({ ...schedule, recurringPattern: e.target.value as any })}
                        className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                      </select>
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label htmlFor="recurring-interval" className="block text-sm font-medium text-gray-700">
                      Interval
                    </label>
                    <div className="mt-1">
                      <input
                        type="number"
                        id="recurring-interval"
                        value={schedule.recurringInterval || 1}
                        onChange={(e) => setSchedule({ ...schedule, recurringInterval: parseInt(e.target.value) })}
                        min="1"
                        max="30"
                        className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      {schedule.recurringPattern === 'daily' && 'Every X days'}
                      {schedule.recurringPattern === 'weekly' && 'Every X weeks'}
                      {schedule.recurringPattern === 'monthly' && 'Every X months'}
                    </p>
                  </div>
                </div>
              )}

              <div>
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium text-gray-700">Special Dates</h3>
                  <button
                    type="button"
                    onClick={addSpecialDate}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Add Date
                  </button>
                </div>
                <div className="mt-2 space-y-3">
                  {(schedule.specialDates || []).length === 0 ? (
                    <p className="text-sm text-gray-500">No special dates configured.</p>
                  ) : (
                    (schedule.specialDates || []).map((specialDate, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <input
                          type="date"
                          value={formatDateForInput(specialDate.date)}
                          onChange={(e) => updateSpecialDate(index, 'date', e.target.value)}
                          className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                        <select
                          value={specialDate.isEnabled ? 'enabled' : 'disabled'}
                          onChange={(e) => updateSpecialDate(index, 'isEnabled', e.target.value === 'enabled')}
                          className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        >
                          <option value="enabled">Enabled</option>
                          <option value="disabled">Disabled</option>
                        </select>
                        <button
                          type="button"
                          onClick={() => removeSpecialDate(index)}
                          className="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    ))
                  )}
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  Special dates override the regular schedule for specific dates.
                </p>
              </div>
            </div>
          )}
        </>
      )}

      <div className="flex justify-end">
        <button
          type="submit"
          disabled={saving}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {saving ? 'Saving...' : 'Save Schedule'}
        </button>
      </div>
    </form>
  );
}
