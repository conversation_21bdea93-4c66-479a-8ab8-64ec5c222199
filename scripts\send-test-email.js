/**
 * <PERSON><PERSON><PERSON> to send a test email to a specified address
 * 
 * Usage: node scripts/send-test-email.js <EMAIL>
 */
const nodemailer = require('nodemailer');

// Create a test account on Ethereal for testing
async function sendTestEmail() {
  // Get the recipient email from command line arguments
  const recipient = process.argv[2];
  
  if (!recipient) {
    console.error('Please provide a recipient email address');
    console.error('Usage: node scripts/send-test-email.js <EMAIL>');
    process.exit(1);
  }
  
  console.log(`Sending test email to ${recipient}...`);
  
  try {
    // Create a test account on Ethereal
    const testAccount = await nodemailer.createTestAccount();
    
    // Create a transporter using the test account
    const transporter = nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass
      }
    });
    
    // Create HTML content with inline image
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4f46e5;">Test Email from Fademail</h1>
        <p>This is a test email sent to verify that your temporary email address is working correctly.</p>
        <p>Here's some <strong>formatted text</strong> to test HTML rendering:</p>
        <ul>
          <li>Item 1 with <em>emphasis</em></li>
          <li>Item 2 with <span style="color: #4f46e5;">colored text</span></li>
          <li>Item 3 with <a href="https://example.com" style="color: #4f46e5;">a link</a></li>
        </ul>
        <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="margin: 0;">This is a styled box to test CSS rendering.</p>
        </div>
        <p>Below is an inline image:</p>
        <img src="cid:test-image" alt="Test Image" style="max-width: 100%; height: auto; border-radius: 5px;" />
        <p style="margin-top: 20px; font-size: 12px; color: #6b7280;">
          This email was sent from the Fademail testing script.
        </p>
      </div>
    `;
    
    // Create a simple colored image as a base64 string
    const imageBuffer = Buffer.from(
      '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',
      'base64'
    );
    
    // Send the email
    const info = await transporter.sendMail({
      from: '"Fademail Test" <<EMAIL>>',
      to: recipient,
      subject: 'Test Email with HTML and Inline Image',
      text: 'This is a test email from Fademail. If you can see this, your temporary email address is working correctly.',
      html: htmlContent,
      attachments: [
        {
          filename: 'test-image.png',
          content: imageBuffer,
          cid: 'test-image' // Same as the src in the HTML
        }
      ]
    });
    
    console.log('Email sent successfully!');
    console.log('Preview URL: %s', nodemailer.getTestMessageUrl(info));
  } catch (error) {
    console.error('Error sending email:', error);
    process.exit(1);
  }
}

// Run the function
sendTestEmail().catch(console.error);
