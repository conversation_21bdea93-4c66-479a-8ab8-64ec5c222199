-- Migration script to rename Email Deliverability Tool tables to Email Tester Tool
-- This script renames the database tables to match the new branding

-- Rename deliverability_test_addresses to email_tester_addresses
ALTER TABLE deliverability_test_addresses RENAME TO email_tester_addresses;

-- Rename deliverability_test_results to email_tester_results
ALTER TABLE deliverability_test_results RENAME TO email_tester_results;

-- Rename deliverability_recommendations to email_tester_recommendations
ALTER TABLE deliverability_recommendations RENAME TO email_tester_recommendations;

-- Update foreign key constraint names if needed
-- Note: Supabase typically handles constraint renaming automatically

-- Verify the tables were renamed successfully
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%email_tester%'
ORDER BY table_name;
