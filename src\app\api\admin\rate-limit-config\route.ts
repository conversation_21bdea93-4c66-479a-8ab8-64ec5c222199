/**
 * Rate Limit Configuration Management API
 * 
 * Provides endpoints for managing dynamic rate limit configurations
 */

import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth/adminAuth';
import { 
  getRateLimitConfigs,
  getRateLimitConfig,
  updateRateLimitConfig,
  createRateLimitConfig,
  initializeDefaultRateLimitConfigs
} from '@/lib/config/rateLimitConfig';
import { logger } from '@/lib/logging/Logger';

/**
 * GET /api/admin/rate-limit-config
 * Get all rate limit configurations or a specific one
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint');

    if (endpoint) {
      // Get specific configuration
      const config = await getRateLimitConfig(endpoint);
      return NextResponse.json({
        success: true,
        data: config
      });
    } else {
      // Get all configurations
      const configs = await getRateLimitConfigs();
      return NextResponse.json({
        success: true,
        data: configs
      });
    }

  } catch (error) {
    await logger.error('ADMIN_RATE_LIMIT_CONFIG', 'Error fetching rate limit configurations', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to fetch rate limit configurations' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/rate-limit-config
 * Create a new rate limit configuration
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      endpoint, 
      name, 
      description, 
      windowMs, 
      maxRequests, 
      skipSuccessfulRequests, 
      skipFailedRequests, 
      isActive 
    } = body;

    // Validate required fields
    if (!endpoint || !name || windowMs === undefined || maxRequests === undefined) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: endpoint, name, windowMs, maxRequests' },
        { status: 400 }
      );
    }

    // Validate values
    if (windowMs < 1000) { // Minimum 1 second
      return NextResponse.json(
        { success: false, error: 'Window time must be at least 1000ms (1 second)' },
        { status: 400 }
      );
    }

    if (maxRequests < 1) {
      return NextResponse.json(
        { success: false, error: 'Max requests must be at least 1' },
        { status: 400 }
      );
    }

    const result = await createRateLimitConfig({
      endpoint,
      name,
      description: description || '',
      windowMs,
      maxRequests,
      skipSuccessfulRequests: skipSuccessfulRequests || false,
      skipFailedRequests: skipFailedRequests || false,
      isActive: isActive !== undefined ? isActive : true,
      updatedBy: authResult.user?.username || 'admin'
    });

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Rate limit configuration created successfully'
    });

  } catch (error) {
    await logger.error('ADMIN_RATE_LIMIT_CONFIG', 'Error creating rate limit configuration', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to create rate limit configuration' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/rate-limit-config
 * Update an existing rate limit configuration
 */
export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      endpoint, 
      windowMs, 
      maxRequests, 
      skipSuccessfulRequests, 
      skipFailedRequests, 
      isActive 
    } = body;

    // Validate required fields
    if (!endpoint) {
      return NextResponse.json(
        { success: false, error: 'Endpoint is required' },
        { status: 400 }
      );
    }

    // Validate values if provided
    if (windowMs !== undefined && windowMs < 1000) {
      return NextResponse.json(
        { success: false, error: 'Window time must be at least 1000ms (1 second)' },
        { status: 400 }
      );
    }

    if (maxRequests !== undefined && maxRequests < 1) {
      return NextResponse.json(
        { success: false, error: 'Max requests must be at least 1' },
        { status: 400 }
      );
    }

    const result = await updateRateLimitConfig({
      endpoint,
      windowMs,
      maxRequests,
      skipSuccessfulRequests,
      skipFailedRequests,
      isActive,
      updatedBy: authResult.user?.username || 'admin'
    });

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Rate limit configuration updated successfully'
    });

  } catch (error) {
    await logger.error('ADMIN_RATE_LIMIT_CONFIG', 'Error updating rate limit configuration', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to update rate limit configuration' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/rate-limit-config/initialize
 * Initialize default rate limit configurations
 */
export async function PATCH(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'initialize_defaults') {
      await initializeDefaultRateLimitConfigs();
      
      await logger.info('ADMIN_RATE_LIMIT_CONFIG', 'Default rate limit configurations initialized', {
        initializedBy: authResult.user?.username || 'admin'
      });

      return NextResponse.json({
        success: true,
        message: 'Default rate limit configurations initialized successfully'
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    await logger.error('ADMIN_RATE_LIMIT_CONFIG', 'Error performing rate limit config action', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to perform action' },
      { status: 500 }
    );
  }
}
