# Analytics in VanishPost

VanishPost uses [PostHog](https://posthog.com/) as its analytics platform. This document explains how analytics is implemented and how to use it.

## Overview

PostHog is an open-source product analytics platform that helps track user behavior while respecting privacy. We've integrated PostHog into VanishPost to collect essential usage data while maintaining user privacy.

## Implementation

### Client-Side Analytics

The client-side implementation is in `src/lib/analytics/posthog.tsx`. This file provides:

- A PostHog provider component that initializes PostHog on the client
- Helper functions for tracking events
- Privacy-focused configuration

### Server-Side Analytics

The server-side implementation is in `src/lib/analytics/posthog-server.ts`. This file provides:

- A PostHog client for use in server components and API routes
- Configuration for immediate event flushing

## Configuration

PostHog requires the following environment variables:

```
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_api_key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

## Privacy Considerations

Our PostHog implementation includes the following privacy features:

- Respects Do Not Track browser settings
- Disables session recording by default
- Opts users out by default until they consent
- Skips tracking for admin and management portal pages

## Usage

### Tracking Events

To track events in client components:

```tsx
import { trackEvent } from '@/lib/analytics/posthog';

// Track a custom event
trackEvent('button_clicked', { buttonName: 'submit' });
```

### Tracking Page Views

Page views are automatically tracked by the `PostHogPageView` component in the root layout.

### Common Event Tracking Functions

We provide several helper functions for common events:

```tsx
import { 
  trackEmailGenerated,
  trackEmailAddressCopied,
  trackPageView
} from '@/lib/analytics/posthog';

// Track email generation
trackEmailGenerated('<EMAIL>');

// Track email address copy
trackEmailAddressCopied('<EMAIL>');

// Track a page view manually
trackPageView('/some-path');
```

### Server-Side Tracking

For server components or API routes:

```tsx
import PostHogClient from '@/lib/analytics/posthog-server';

export async function MyServerComponent() {
  const posthog = PostHogClient();
  
  // Track an event
  posthog.capture('server_event', { property: 'value' });
  
  // Always call shutdown when done
  await posthog.shutdown();
  
  return <div>Content</div>;
}
```

## Accessing Analytics Data

Analytics data can be viewed in the PostHog dashboard. You'll need access credentials for the PostHog account.

## Troubleshooting

If you encounter issues with PostHog:

1. Check that environment variables are correctly set
2. Verify that the PostHog provider is properly wrapped around the application
3. Check browser console for PostHog-related errors
4. Ensure the PostHog API key has the correct permissions

## Future Enhancements

Potential enhancements to our analytics implementation:

1. Add more detailed event tracking for specific user flows
2. Implement feature flags for A/B testing
3. Set up conversion funnels to analyze user journeys
4. Add session recording for specific pages (with user consent)
5. Implement heat maps for UI optimization
