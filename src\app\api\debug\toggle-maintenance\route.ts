/**
 * Debug API route for toggling maintenance mode
 * 
 * This API provides a way to toggle maintenance mode without authentication
 * for debugging purposes.
 */
import { NextRequest, NextResponse } from 'next/server';
import { getConfig, updateConfig, clearConfigCache } from '@/lib/config/configService';

/**
 * GET /api/debug/toggle-maintenance
 * 
 * Toggle maintenance mode
 */
export async function GET(request: NextRequest) {
  try {
    // Get current maintenance mode status
    const currentStatus = await getConfig('maintenanceMode');
    
    // Toggle the status
    const newStatus = !currentStatus;
    
    // Update the configuration
    await updateConfig('maintenanceMode', newStatus);
    
    // Clear the config cache to ensure the new value is used
    clearConfigCache();
    
    // Get the updated status to confirm
    const updatedStatus = await getConfig('maintenanceMode');
    
    return NextResponse.json({
      success: true,
      previousStatus: currentStatus,
      newStatus: newStatus,
      confirmedStatus: updatedStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error toggling maintenance mode:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
