// Audit Logging Types - Isolated from existing types

export interface AuditLogEntry {
  id: string;
  session_id?: string;
  user_id?: string;
  tool_name: 'dkim-generator' | 'dmarc-generator' | 'dns-validator';
  action: AuditAction;
  resource_type?: 'dkim_record' | 'dmarc_record' | 'dns_validation';
  resource_id?: string;
  metadata: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export type AuditAction = 
  | 'generate_dkim_keys'
  | 'validate_dkim_record'
  | 'generate_dmarc_record'
  | 'validate_dmarc_record'
  | 'validate_dns_record'
  | 'view_generated_record'
  | 'delete_generated_record'
  | 'export_record'
  | 'copy_record_value';

export interface AuditLogRequest {
  tool_name: 'dkim-generator' | 'dmarc-generator' | 'dns-validator';
  action: AuditAction;
  resource_type?: 'dkim_record' | 'dmarc_record' | 'dns_validation';
  resource_id?: string;
  metadata?: Record<string, any>;
  session_id?: string;
}

export interface AuditLogResponse {
  success: boolean;
  data?: {
    id: string;
    logged_at: string;
  };
  message?: string;
  error?: string;
}

export interface AuditLogQuery {
  tool_name?: 'dkim-generator' | 'dmarc-generator' | 'dns-validator';
  action?: AuditAction;
  user_id?: string;
  session_id?: string;
  start_date?: string;
  end_date?: string;
  limit?: number;
  offset?: number;
}

export interface AuditLogQueryResponse {
  success: boolean;
  data?: {
    entries: AuditLogEntry[];
    total: number;
    limit: number;
    offset: number;
  };
  message?: string;
  error?: string;
}
