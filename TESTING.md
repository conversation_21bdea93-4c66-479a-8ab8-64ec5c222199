# Testing Guide for Fademail

This document provides guidance on testing the Fademail application, with a focus on the System Monitoring and User Management features.

## Test Structure

The tests are organized into the following categories:

1. **API Tests**: Tests for the API endpoints
2. **Component Tests**: Tests for React components
3. **Service Tests**: Tests for service functions
4. **Integration Tests**: Tests that combine multiple parts of the application

## Running Tests

To run all tests:

```bash
npm test
```

To run a specific test file:

```bash
npm test -- path/to/test-file.test.ts
```

To run tests matching a specific pattern:

```bash
npm test -- -t "UserManagement"
```

To run tests with coverage:

```bash
npm test -- --coverage
```

## Test Setup

Before running the tests, make sure you have installed all the required dependencies:

```bash
npm install
npm install bcrypt
npm install @types/bcrypt --save-dev
```

## Mocking

The tests use several mocks to isolate the code being tested:

1. **Next.js Server Components**: The `NextRequest` and `NextResponse` objects are mocked in `src/__mocks__/next-server-mock.ts`.
2. **Supabase Client**: The Supabase client is mocked to return predefined data.
3. **bcrypt**: The bcrypt library is mocked to avoid actual password hashing.
4. **EventSource**: The EventSource API is mocked for Server-Sent Events (SSE) testing.

To use these mocks in your tests, import the setup file at the beginning of your test file:

```typescript
import '../__mocks__/setup-test-mocks';
```

## Writing Tests

### API Tests

API tests should verify that:

1. The API endpoints return the expected responses
2. Authentication and authorization are properly enforced
3. Error handling is implemented correctly

Example:

```typescript
describe('GET /api/admin/users', () => {
  it('should return all users', async () => {
    // Mock the getAdminUsers function
    mockedUserService.getAdminUsers.mockResolvedValue(mockUsers);

    // Call the API
    const { request } = createMockRequest();
    const response = await GET(request);
    const data = await response.json();

    // Verify the response
    expect(response.status).toBe(200);
    expect(data).toEqual({
      success: true,
      data: mockUsers,
    });
    expect(mockedUserService.getAdminUsers).toHaveBeenCalledTimes(1);
  });
});
```

### Component Tests

Component tests should verify that:

1. The component renders correctly
2. User interactions work as expected
3. The component handles different states (loading, error, etc.)

Example:

```typescript
describe('UserList Component', () => {
  it('renders the user list correctly', () => {
    render(
      <UserList
        users={mockUsers}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        isLoading={false}
      />
    );

    // Check if users are displayed
    expect(screen.getByText('admin')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
});
```

### Service Tests

Service tests should verify that:

1. The service functions return the expected results
2. Error handling is implemented correctly
3. Side effects (like database calls) are properly mocked

Example:

```typescript
describe('getAdminUsers', () => {
  it('should return all admin users', async () => {
    const users = await userService.getAdminUsers();
    
    expect(users).toHaveLength(2);
    expect(users[0].username).toBe('admin');
    expect(users[1].username).toBe('editor');
  });
});
```

## Troubleshooting

If you encounter issues with the tests, check the following:

1. **Missing Dependencies**: Make sure all required dependencies are installed.
2. **Incorrect Mocks**: Verify that the mocks match the actual implementation.
3. **Environment Variables**: Ensure that any required environment variables are set.
4. **Test Isolation**: Make sure tests are properly isolated and don't depend on each other.

For more detailed information on test results and issues, see the [TEST_RESULTS.md](./TEST_RESULTS.md) file.
