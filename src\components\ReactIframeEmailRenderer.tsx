'use client';

import React, { useEffect, useState, useRef } from 'react';
import DOMPurify from 'dompurify';

interface ReactIframeEmailRendererProps {
  /**
   * The HTML content to render in the iframe
   */
  html: string;

  /**
   * Optional className for the iframe container
   */
  className?: string;

  /**
   * Optional style object for the iframe
   */
  style?: React.CSSProperties;

  /**
   * Optional array of web fonts to load in the iframe
   * Each font should be a Google Fonts URL or similar
   */
  webFonts?: string[];

  /**
   * Optional scale factor for the email content (0.5 to 2.0)
   * Default is 1.0 (100%)
   */
  scaleFactor?: number;

  /**
   * Optional flag to enable lazy loading of images
   * Default is true
   */
  lazyLoadImages?: boolean;

  /**
   * Optional flag to enable link transformation (fixing broken links, etc.)
   * Default is true
   */
  transformLinks?: boolean;

  /**
   * Optional flag to enable print support
   * Default is true
   */
  enablePrint?: boolean;
}

/**
 * ReactIframeEmailRenderer Component
 *
 * A component for rendering email HTML content in an iframe.
 * Features:
 * - Complete style isolation through iframe
 * - Automatic height adjustment
 * - Server-side rendering compatibility
 */
export default function ReactIframeEmailRenderer({
  html,
  className = '',
  style = {},
  webFonts = [],
  scaleFactor = 1.0, // Default scale is 100%
  lazyLoadImages = true,
  transformLinks = true,
  enablePrint = true
}: ReactIframeEmailRendererProps) {
  // Create a ref for the iframe
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // State to track if the component is mounted (for SSR compatibility)
  const [isMounted, setIsMounted] = useState(false);

  // State to track loading status
  const [isLoading, setIsLoading] = useState(true);

  // State for current scale factor (can be changed by user)
  const [currentScaleFactor, setCurrentScaleFactor] = useState(scaleFactor);

  // State to track if images are loaded
  const [imagesLoaded, setImagesLoaded] = useState(0);

  // State to track total images
  const [totalImages, setTotalImages] = useState(0);

  // Effect to handle client-side rendering
  useEffect(() => {
    // Use setTimeout to ensure we're in the browser environment
    const timeoutId = setTimeout(() => {
      setIsMounted(true);
    }, 0);

    return () => clearTimeout(timeoutId);
  }, []);

  // Effect to sync props with state
  useEffect(() => {
    setCurrentScaleFactor(scaleFactor);
  }, [scaleFactor]);

  // Methods to control the iframe from the parent component
  const setScale = (scale: number) => {
    if (!iframeRef.current || !iframeRef.current.contentWindow) return;

    // Use a clean number with 2 decimal places for consistent scaling
    const cleanScale = parseFloat(scale.toFixed(2));

    // Use requestAnimationFrame for smoother animation
    requestAnimationFrame(() => {
      // Send message to iframe to set scale with transition
      iframeRef.current?.contentWindow?.postMessage({
        type: 'set-scale',
        scale: cleanScale
      }, '*');

      // Update state
      setCurrentScaleFactor(cleanScale);

      console.log('Parent sending scale change:', cleanScale);
    });
  };

  const printEmail = () => {
    if (!iframeRef.current || !iframeRef.current.contentWindow) return;

    iframeRef.current.contentWindow.postMessage({
      type: 'print'
    }, '*');
  };

  // Effect to handle iframe initialization and content
  useEffect(() => {
    if (!isMounted) return;

    const iframe = iframeRef.current;
    if (!iframe) return;

    try {
      // Get the iframe document
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) return;

      // Sanitize HTML with DOMPurify
      let sanitizedHtml = html;
      try {
        if (typeof DOMPurify !== 'undefined' && typeof DOMPurify.sanitize === 'function') {
          sanitizedHtml = DOMPurify.sanitize(html, {
            ADD_TAGS: ['style', 'link'],
            ADD_ATTR: ['target', 'rel', 'href', 'style'],
            WHOLE_DOCUMENT: true,
          });
          console.log('HTML sanitized successfully');
        } else {
          console.warn('DOMPurify not available, using raw HTML');
        }
      } catch (error) {
        console.error('Error sanitizing HTML:', error);
      }

      // Create a complete HTML document with necessary styles
      // We're using a minimal approach similar to SimpleIframeRenderer
      const htmlContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Email Content</title>
            ${webFonts.map(font => `<link href="${font}" rel="stylesheet">`).join('')}
            <style>
              /* Reset styles - keeping this minimal like SimpleIframeRenderer */
              html, body {
                margin: 0;
                padding: 0;
                overflow-y: visible; /* Allow content to expand without vertical scrollbars */
                overflow-x: auto; /* Allow horizontal scrolling when needed */
                height: auto; /* Allow height to adjust to content */
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
                line-height: 1.5;
                color: #333;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
              }

              /* Email wrapper with scaling */
              .email-wrapper {
                width: 100%;
                max-width: 100%;
                margin: 0 auto;
                padding: 0;
                transform: scale(${currentScaleFactor});
                transform-origin: top left;
                will-change: transform;
                transition: transform 0.3s ease-out;
              }

              /* Make sure images are responsive */
              img {
                max-width: 100%;
                height: auto;
              }

              /* Ensure tables are responsive */
              table {
                max-width: 100%;
              }

              /* Print styles */
              @media print {
                body {
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  print-color-adjust: exact !important;
                }

                .email-wrapper {
                  transform: scale(1) !important;
                }

                #email-controls {
                  display: none !important;
                }
              }

              /* Controls container */
              #email-controls {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                padding: 8px;
                display: flex;
                gap: 8px;
                z-index: 9999;
                font-size: 14px;
              }

              #email-controls button {
                background: #f0f0f0;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px 8px;
                cursor: pointer;
                font-size: 12px;
              }

              #email-controls button:hover {
                background: #e0e0e0;
              }
            </style>
            <script>
              // Function to adjust iframe height - direct approach
              function adjustHeight() {
                // Get the current scale
                const scale = parseFloat(getComputedStyle(document.querySelector('.email-wrapper')).transform.split(',')[0].slice(7)) || ${currentScaleFactor};

                // Get the content height and apply the scale
                const contentHeight = document.body.scrollHeight * scale;

                // Send the height message
                window.parent.postMessage({
                  type: 'resize-iframe',
                  height: contentHeight
                }, '*');
              }

              // Function to count images and track loading
              function trackImageLoading() {
                const images = document.querySelectorAll('img');
                const totalImages = images.length;
                let loadedImages = 0;

                window.parent.postMessage({
                  type: 'total-images',
                  count: totalImages
                }, '*');

                if (totalImages === 0) {
                  window.parent.postMessage({
                    type: 'images-loaded',
                    count: 0,
                    total: 0
                  }, '*');
                  return;
                }

                images.forEach(function(img) {
                  if (img.complete) {
                    loadedImages++;
                  }

                  img.addEventListener('load', function() {
                    loadedImages++;
                    window.parent.postMessage({
                      type: 'images-loaded',
                      count: loadedImages,
                      total: totalImages
                    }, '*');

                    // Also adjust height when image loads
                    adjustHeight();
                  });

                  img.addEventListener('error', function() {
                    loadedImages++;
                    window.parent.postMessage({
                      type: 'images-loaded',
                      count: loadedImages,
                      total: totalImages
                    }, '*');
                  });
                });

                // Report initially loaded images
                if (loadedImages > 0) {
                  window.parent.postMessage({
                    type: 'images-loaded',
                    count: loadedImages,
                    total: totalImages
                  }, '*');
                }
              }

              // Shared variable for scale change timeout
              var scaleChangeTimeout = null;

              // Function to change scale with smooth transition
              function setScale(scale) {
                // Get the wrapper element
                const wrapper = document.querySelector('.email-wrapper');
                if (!wrapper) return;

                // Ensure we have a clean scale value
                scale = parseFloat(scale.toFixed(2));

                // Clear any pending timeout
                if (scaleChangeTimeout) {
                  clearTimeout(scaleChangeTimeout);
                }

                // Simply set the transform with the new scale
                // The transition is handled by CSS
                wrapper.style.transform = 'scale(' + scale + ')';

                console.log('Setting scale to:', scale);

                // Use a timeout to adjust height after transition completes
                scaleChangeTimeout = setTimeout(function() {
                  // Get the natural height (before scaling)
                  const naturalHeight = document.body.scrollHeight;

                  // Apply the scale factor to get the final height
                  const scaledHeight = naturalHeight * scale;

                  // Send the height message
                  window.parent.postMessage({
                    type: 'resize-iframe',
                    height: scaledHeight,
                    isScaleChange: true
                  }, '*');

                  scaleChangeTimeout = null;
                }, 300); // Match the transition duration (300ms)
              }

              // Function to print email
              function printEmail() {
                window.print();
              }

              // Listen for load event
              window.addEventListener('load', function() {
                // Adjust height after images and other resources are loaded
                adjustHeight();

                // Track image loading
                trackImageLoading();

                // Add event listeners to all images
                document.querySelectorAll('img').forEach(function(img) {
                  img.addEventListener('load', adjustHeight);
                  // Force load event on images that might be cached
                  if (img.complete) {
                    img.dispatchEvent(new Event('load'));
                  }
                });

                // Handle links to make them work properly
                document.querySelectorAll('a[href]').forEach(function(link) {
                  link.addEventListener('click', function(e) {
                    e.preventDefault();

                    var href = this.getAttribute('href');

                    // Open links in new tab/window
                    if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
                      window.open(href, '_blank');
                    }
                  });
                });

                // Add controls if not in print mode
                if (!window.matchMedia('print').matches) {
                  const controlsDiv = document.createElement('div');
                  controlsDiv.id = 'email-controls';

                  // Zoom controls
                  const zoomOutBtn = document.createElement('button');
                  zoomOutBtn.textContent = '🔍-';
                  zoomOutBtn.title = 'Zoom out';
                  zoomOutBtn.onclick = function() {
                    // Get current scale
                    const currentScale = parseFloat(getComputedStyle(document.querySelector('.email-wrapper')).transform.split(',')[0].slice(7)) || 1;
                    // Calculate new scale with a limit
                    const newScale = Math.max(0.5, currentScale - 0.1);
                    // Apply scale with transition
                    setScale(newScale);
                    // Notify parent
                    window.parent.postMessage({ type: 'scale-changed', scale: newScale }, '*');
                  };

                  const zoomInBtn = document.createElement('button');
                  zoomInBtn.textContent = '🔍+';
                  zoomInBtn.title = 'Zoom in';
                  zoomInBtn.onclick = function() {
                    // Get current scale
                    const currentScale = parseFloat(getComputedStyle(document.querySelector('.email-wrapper')).transform.split(',')[0].slice(7)) || 1;
                    // Calculate new scale with a limit
                    const newScale = Math.min(2.0, currentScale + 0.1);
                    // Apply scale with transition
                    setScale(newScale);
                    // Notify parent
                    window.parent.postMessage({ type: 'scale-changed', scale: newScale }, '*');
                  };

                  // Reset zoom button
                  const resetZoomBtn = document.createElement('button');
                  resetZoomBtn.textContent = '↺';
                  resetZoomBtn.title = 'Reset zoom to 100%';
                  resetZoomBtn.onclick = function() {
                    // Apply scale with transition
                    setScale(1.0);
                    // Notify parent
                    window.parent.postMessage({ type: 'scale-changed', scale: 1.0 }, '*');
                  };

                  // Print button
                  const printBtn = document.createElement('button');
                  printBtn.textContent = '🖨️';
                  printBtn.title = 'Print email';
                  printBtn.onclick = function() {
                    printEmail();
                  };

                  // Style the controls
                  controlsDiv.style.position = 'fixed';
                  controlsDiv.style.bottom = '10px';
                  controlsDiv.style.left = '50%';
                  controlsDiv.style.transform = 'translateX(-50%)';
                  controlsDiv.style.display = 'flex';
                  controlsDiv.style.gap = '5px';
                  controlsDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
                  controlsDiv.style.padding = '5px';
                  controlsDiv.style.borderRadius = '4px';
                  controlsDiv.style.zIndex = '1000';
                  controlsDiv.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.1)';

                  // Style the buttons
                  const buttonStyle = {
                    padding: '4px 8px',
                    fontSize: '12px',
                    backgroundColor: '#f3f4f6',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    margin: '0 2px'
                  };

                  // Apply styles to buttons
                  [zoomOutBtn, zoomInBtn, printBtn].forEach(btn => {
                    if (!btn) return;
                    Object.assign(btn.style, buttonStyle);
                    btn.addEventListener('mouseover', function() {
                      this.style.backgroundColor = '#e5e7eb';
                    });
                    btn.addEventListener('mouseout', function() {
                      this.style.backgroundColor = '#f3f4f6';
                    });
                  });

                  // Apply special styles to reset button
                  Object.assign(resetZoomBtn.style, buttonStyle);
                  resetZoomBtn.addEventListener('mouseover', function() {
                    this.style.backgroundColor = '#b85518';
                  });
                  resetZoomBtn.addEventListener('mouseout', function() {
                    this.style.backgroundColor = '#ce601c';
                  });

                  // Function to update reset button visibility based on scale
                  const updateResetButtonVisibility = () => {
                    // Get current scale
                    const currentScale = parseFloat(getComputedStyle(document.querySelector('.email-wrapper')).transform.split(',')[0].slice(7)) || 1;
                    // Only show reset button when not at 100%
                    if (Math.abs(currentScale - 1.0) > 0.01) {
                      resetZoomBtn.style.display = 'inline-block';
                      resetZoomBtn.style.backgroundColor = '#ce601c';
                      resetZoomBtn.style.color = 'white';
                    } else {
                      resetZoomBtn.style.display = 'none';
                    }
                  };

                  // Initial update
                  updateResetButtonVisibility();

                  // Update on zoom changes
                  zoomInBtn.addEventListener('click', updateResetButtonVisibility);
                  zoomOutBtn.addEventListener('click', updateResetButtonVisibility);
                  resetZoomBtn.addEventListener('click', updateResetButtonVisibility);

                  // Add buttons to controls
                  controlsDiv.appendChild(zoomOutBtn);
                  controlsDiv.appendChild(zoomInBtn);
                  controlsDiv.appendChild(resetZoomBtn);

                  // Only add print button if enabled
                  if (${enablePrint}) {
                    controlsDiv.appendChild(printBtn);
                  }

                  // Add controls to body
                  document.body.appendChild(controlsDiv);
                }
              });

              // Trigger resize on DOMContentLoaded as well
              document.addEventListener('DOMContentLoaded', function() {
                adjustHeight();
              });

              // Listen for messages from parent
              window.addEventListener('message', function(event) {
                if (event.data) {
                  if (event.data.type === 'set-scale') {
                    // Use double requestAnimationFrame for smoother animation
                    // First frame to prepare, second frame to execute
                    requestAnimationFrame(function() {
                      requestAnimationFrame(function() {
                        setScale(event.data.scale);
                        console.log('Iframe received scale change:', event.data.scale);
                      });
                    });
                  } else if (event.data.type === 'print') {
                    printEmail();
                  } else if (event.data.type === 'recalculate-height') {
                    // Get current scale - more robust parsing
                    let currentScale = 1.0;
                    try {
                      const transformValue = getComputedStyle(document.querySelector('.email-wrapper')).transform;
                      if (transformValue !== 'none') {
                        const matrix = transformValue.match(/matrix\((.+)\)/);
                        if (matrix && matrix[1]) {
                          const values = matrix[1].split(', ');
                          // For a 2D matrix, the scale factor is in position 0
                          currentScale = parseFloat(values[0]) || 1.0;
                        }
                      }
                    } catch (e) {
                      console.log('Error getting scale:', e);
                    }

                    // Use double requestAnimationFrame for smoother animation
                    requestAnimationFrame(function() {
                      requestAnimationFrame(function() {
                        setScale(currentScale);
                      });
                    });
                  }
                }
              });
            </script>
          </head>
          <body>
            <div class="email-wrapper">
              ${sanitizedHtml}
            </div>
          </body>
        </html>
      `;

      // Since we're removing allow-same-origin, we need to use srcdoc instead of document.write
      // This provides the same functionality but works with the more restrictive sandbox
      console.log('Setting iframe srcdoc content');
      iframe.srcdoc = htmlContent;
      console.log('Iframe srcdoc content set');

      // Function to handle iframe messages
      const handleMessage = (event: MessageEvent) => {
        if (!event.data || !event.data.type || !iframe) return;

        switch (event.data.type) {
          case 'resize-iframe':
            console.log('Received resize message with height:', event.data.height,
                       event.data.isScaleChange ? '(from scale change)' : '');

            // Ensure we're setting a valid height and not 0 or NaN
            const newHeight = event.data.height;
            if (newHeight && newHeight > 0 && !isNaN(newHeight)) {
              // Set a minimum height to prevent too small iframes
              const finalHeight = Math.max(200, newHeight);

              // Set the iframe height directly
              iframe.style.height = `${finalHeight}px`;

              // If this is from a scale change, update the scale factor but don't trigger another height calculation
              if (event.data.isScaleChange) {
                // We already handled the height adjustment, so we don't need to do anything else
              }
            } else {
              console.warn('Invalid height received:', newHeight);
              iframe.style.height = '400px';
            }
            setIsLoading(false);
            break;

          case 'total-images':
            console.log('Total images in email:', event.data.count);
            setTotalImages(event.data.count);
            break;

          case 'images-loaded':
            console.log(`Images loaded: ${event.data.count}/${event.data.total}`);
            setImagesLoaded(event.data.count);
            break;

          case 'scale-changed':
            console.log('Scale changed to:', event.data.scale);
            // Just update the scale factor state, the height adjustment is handled separately
            setCurrentScaleFactor(event.data.scale);
            break;
        }
      };

      // Add message event listener
      window.addEventListener('message', handleMessage);

      // Set a timeout to ensure loading state is cleared even if no resize message is received
      const timeoutId = setTimeout(() => {
        console.log('Timeout reached, setting loading to false');
        setIsLoading(false);

        // Try to adjust height manually if no message was received - direct approach
        if (iframeDoc.body) {
          // Simply use the body scrollHeight
          const height = iframeDoc.body.scrollHeight;

          console.log('Manual height adjustment:', height);
          iframe.style.height = `${height}px`;
        }
      }, 1000);

      // Cleanup
      return () => {
        window.removeEventListener('message', handleMessage);
        clearTimeout(timeoutId);
      };
    } catch (error) {
      console.error('Error rendering email in iframe:', error);
      setIsLoading(false);
    }
  }, [isMounted, html, webFonts, currentScaleFactor, lazyLoadImages, transformLinks, enablePrint]);

  // Loading state component
  const loadingState = (
    <div className="animate-pulse flex flex-col space-y-4 p-4" style={{ minHeight: '200px' }}>
      <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
      <div className="h-4 bg-neutral-200 rounded w-full"></div>
      <div className="h-4 bg-neutral-200 rounded w-5/6"></div>
      <div className="h-4 bg-neutral-200 rounded w-4/6"></div>
      <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
    </div>
  );

  // Calculate image loading progress
  const imageLoadingProgress = totalImages > 0 ? Math.round((imagesLoaded / totalImages) * 100) : 100;

  return (
    <div
      className={`react-iframe-container ${className}`}
      style={{
        width: '100%',
        overflowY: 'visible', // Allow content to expand vertically
        overflowX: 'auto', // Allow horizontal scrolling when needed
        position: 'relative',
        transition: 'opacity 0.3s ease-in-out'
      }}
    >
      {/* External controls for parent component */}
      <div className="flex items-center justify-end space-x-2 mb-2">
        <div className="text-xs text-gray-500">
          {totalImages > 0 && (
            <span>
              Images: {imagesLoaded}/{totalImages} ({imageLoadingProgress}%)
            </span>
          )}
        </div>

        <div className="flex items-center space-x-1">
          <button
            onClick={() => setScale(Math.max(0.5, currentScaleFactor - 0.1))}
            className="p-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
            title="Zoom out"
            aria-label="Zoom out"
          >
            🔍-
          </button>

          <span className="text-xs">{Math.round(currentScaleFactor * 100)}%</span>

          <button
            onClick={() => setScale(Math.min(2.0, currentScaleFactor + 0.1))}
            className="p-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
            title="Zoom in"
            aria-label="Zoom in"
          >
            🔍+
          </button>

          {/* Reset zoom button - only visible when not at 100% */}
          {Math.abs(currentScaleFactor - 1.0) > 0.01 && (
            <button
              onClick={() => setScale(1.0)}
              className="p-1 text-xs bg-[#ce601c] text-white hover:bg-[#b85518] rounded"
              title="Reset zoom to 100%"
              aria-label="Reset zoom"
            >
              ↺
            </button>
          )}

          {enablePrint && (
            <button
              onClick={printEmail}
              className="p-1 text-xs bg-gray-100 hover:bg-gray-200 rounded ml-2"
              title="Print email"
              aria-label="Print email"
            >
              🖨️
            </button>
          )}
        </div>
      </div>

      {/* Loading state */}
      {!isMounted || isLoading ? (
        loadingState
      ) : null}

      {/* Image loading progress bar */}
      {isMounted && totalImages > 0 && imagesLoaded < totalImages && (
        <div className="w-full bg-gray-200 rounded-full h-1 mb-2">
          <div
            className="bg-blue-600 h-1 rounded-full transition-all duration-300 ease-in-out"
            style={{ width: `${imageLoadingProgress}%` }}
          />
        </div>
      )}

      {/* Always render the iframe, but hide it until loaded */}
      <iframe
        ref={iframeRef}
        title="Email content"
        style={{
          width: '100%',
          border: 'none',
          overflowY: 'hidden', // Hide vertical scrollbar
          overflowX: 'auto', // Allow horizontal scrolling
          minHeight: '200px', // Lower minimum height to allow for shorter emails
          display: 'block',
          transition: 'opacity 0.3s ease-in-out, height 0.3s ease-in-out',
          transform: 'translateZ(0)',
          opacity: isMounted && !isLoading ? 1 : 0,
          ...style
        }}
        sandbox="allow-scripts allow-popups allow-popups-to-escape-sandbox"
      />
    </div>
  );
}
