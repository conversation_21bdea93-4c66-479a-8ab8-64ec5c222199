'use client';

import React from 'react';

interface LoadingSkeletonProps {
  /**
   * Minimum height of the skeleton
   */
  minHeight?: string | number;
  
  /**
   * Optional className for the container
   */
  className?: string;
}

/**
 * LoadingSkeleton Component
 * 
 * Displays a loading skeleton animation for content
 */
export function LoadingSkeleton({
  minHeight = '200px',
  className = ''
}: LoadingSkeletonProps) {
  return (
    <div 
      className={`animate-pulse flex flex-col space-y-4 p-4 ${className}`} 
      style={{ minHeight }}
    >
      <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
      <div className="h-4 bg-neutral-200 rounded w-full"></div>
      <div className="h-4 bg-neutral-200 rounded w-5/6"></div>
      <div className="h-4 bg-neutral-200 rounded w-4/6"></div>
      <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
    </div>
  );
}
