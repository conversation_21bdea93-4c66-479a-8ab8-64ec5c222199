# Side Rail Ads Implementation

This document explains the implementation of side rail ads in Fademail, which stick to the sides of the screen as the user scrolls.

## Overview

Side rail ads are a type of advertisement that appears on the left and right sides of the main content area and remains visible as the user scrolls down the page. These ads are only visible on desktop devices with larger screens (xl breakpoint and above, which is 1280px width or more).

## Implementation Details

### Components

1. **StickyAdContainer Component**
   - Located at: `src/components/StickyAdContainer.tsx`
   - A specialized version of the AdContainer component designed specifically for side rail ads
   - Uses fixed positioning to stick to the sides of the screen
   - Supports both left and right positioning

2. **Integration in EmailApp**
   - Side rail ads are added to the main EmailApp component
   - They're positioned outside the main content area
   - Only visible on desktop with large screens (xl breakpoint and above)
   - Automatically adjust position based on available space
   - Automatically hide when there's not enough space to prevent overlap with content

### Ad Placement IDs

Two new ad placement IDs have been added:

1. **left-rail**: For ads that appear on the left side of the screen
2. **right-rail**: For ads that appear on the right side of the screen

### Admin Management

Side rail ads can be managed through the existing admin interface at `/management-portal-x7z9y2/ads`, just like the top and middle ads. Administrators can:

- Enable/disable the side rail ads
- Change ad unit IDs
- Configure device targeting (though they're only visible on desktop)
- Set up scheduling
- Customize display options

### Technical Implementation

The side rail ads use:

- Fixed positioning with `position: fixed`
- Vertical centering with `top: 50%` and `transform: translateY(-50%)`
- Z-index management to ensure proper layering
- Responsive hiding on smaller screens with Tailwind's `hidden xl:block`
- Dynamic positioning based on viewport width and available space
- Automatic hiding when viewport is too narrow to prevent content overlap
- Smooth transitions with CSS transitions
- Google AdSense vertical ad format

## Ad Format

Side rail ads are configured with:

- Width: 160px
- Height: 600px (minimum)
- Format: vertical
- Full-width responsive: false

## Usage

To add side rail ads to your page:

```tsx
// Left side - only visible on xl screens and above
<div className="hidden xl:block">
  <StickyAdContainer
    placementId="left-rail"
    position="left"
    className="mx-2"
    style={{ maxHeight: '90vh' }}
  />
</div>

// Right side - only visible on xl screens and above
<div className="hidden xl:block">
  <StickyAdContainer
    placementId="right-rail"
    position="right"
    className="mx-2"
    style={{ maxHeight: '90vh' }}
  />
</div>
```

## Configuration in Supabase

To enable these ads, add entries to the `ad_config` table in Supabase with:

- `placement_id`: "left-rail" or "right-rail"
- `domain`: Your domain or "*" for all domains
- `ad_unit_id`: Your Google AdSense ad unit ID
- `ad_client_id`: Your Google AdSense publisher ID
- `is_enabled`: true
- `device_types`: ["desktop"]
- `format`: "vertical"

### Display Options

The side rail ads support the following display options that can be configured through the admin interface:

```json
{
  "backgroundColor": "#f9f9f9",
  "padding": "10px",
  "margin": "5px",
  "borderRadius": "8px",
  "showBorder": true,
  "borderColor": "#e5e7eb",
  "maxWidth": "160px",
  "overflow": "hidden",
  "showLabel": true,
  "labelText": "Advertisement"
}
```

These options can be set in the admin interface by editing the ad placement and adding the JSON configuration in the Display Options field.

## Best Practices

1. Only show side rail ads on desktop devices with large screens (xl breakpoint and above)
2. Ensure they don't overlap with main content (the StickyAdContainer handles this automatically)
3. Consider user experience when enabling multiple ad types
4. Test on different screen sizes to ensure proper positioning and responsive behavior
5. Consider setting up scheduling to control when ads appear
6. Be mindful of the minimum screen width required for side rail ads (approximately 1440px for comfortable viewing)
7. Consider disabling side rail ads for users with smaller screens or when the main content needs more focus
