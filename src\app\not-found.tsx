import Link from 'next/link';
import { Metadata } from 'next';
import Logo from '@/components/Logo';

export const metadata: Metadata = {
  title: 'Page Not Found | VanishPost',
  description: 'The page you are looking for does not exist. Return to VanishPost home page.',
  robots: {
    index: false,
    follow: true,
  },
};

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <div className="flex-grow flex flex-col items-center justify-center px-4 py-16">
        <div className="mb-8">
          <Logo showText={false} />
        </div>

        <h1 className="text-4xl font-bold text-neutral-800 mb-4 text-center">404 - Page Not Found</h1>

        <p className="text-lg text-neutral-600 mb-8 text-center max-w-md">
          The page you are looking for doesn't exist or has been moved.
        </p>

        <div className="flex flex-col sm:flex-row gap-4">
          <Link
            href="/"
            className="inline-block bg-[#ce601c] text-white px-6 py-3 rounded-md font-medium hover:bg-[#b85518] transition-colors"
          >
            Return Home
          </Link>

          <Link
            href="/contact"
            className="inline-block bg-white text-[#605f5f] border border-[#605f5f] px-6 py-3 rounded-md font-medium hover:bg-neutral-50 transition-colors"
          >
            Contact Support
          </Link>
        </div>
      </div>

      <footer className="py-6 text-center text-neutral-500 border-t border-neutral-200">
        <p className="text-sm">
          © {new Date().getFullYear()} VanishPost. All rights reserved.
        </p>
      </footer>
    </div>
  );
}
