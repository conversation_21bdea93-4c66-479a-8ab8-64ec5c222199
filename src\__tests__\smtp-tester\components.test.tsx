/**
 * SMTP Tester Component Tests
 * 
 * Tests for SMTP tester React components
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SmtpTesterForm from '@/components/tools/smtp-tester/SmtpTesterForm';
import SmtpTesterResults from '@/components/tools/smtp-tester/SmtpTesterResults';
import SmtpTesterInstructions from '@/components/tools/smtp-tester/SmtpTesterInstructions';

// Mock the hook
jest.mock('@/hooks/useSmtpTester');

describe('SMTP Tester Components', () => {
  describe('SmtpTesterForm', () => {
    const mockProps = {
      formData: {
        server: '',
        port: '587',
        encryption: 'tls' as const,
        username: '',
        password: '',
        sender: '',
        testMode: 'auto' as const,
        recipient: ''
      },
      validationErrors: {},
      isLoading: false,
      onFormDataChange: jest.fn(),
      onSubmit: jest.fn(),
      onValidate: jest.fn()
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should render form fields correctly', () => {
      render(<SmtpTesterForm {...mockProps} />);

      expect(screen.getByLabelText(/smtp server/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/port/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/encryption/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/sender email/i)).toBeInTheDocument();
    });

    it('should show test mode selection', () => {
      render(<SmtpTesterForm {...mockProps} />);

      expect(screen.getByText(/auto mode/i)).toBeInTheDocument();
      expect(screen.getByText(/custom mode/i)).toBeInTheDocument();
    });

    it('should show recipient field in custom mode', () => {
      const customModeProps = {
        ...mockProps,
        formData: { ...mockProps.formData, testMode: 'custom' as const }
      };

      render(<SmtpTesterForm {...customModeProps} />);

      expect(screen.getByLabelText(/recipient email/i)).toBeInTheDocument();
    });

    it('should hide recipient field in auto mode', () => {
      render(<SmtpTesterForm {...mockProps} />);

      expect(screen.queryByLabelText(/recipient email/i)).not.toBeInTheDocument();
    });

    it('should call onFormDataChange when inputs change', async () => {
      const user = userEvent.setup();
      render(<SmtpTesterForm {...mockProps} />);

      const serverInput = screen.getByLabelText(/smtp server/i);
      await user.type(serverInput, 'smtp.gmail.com');

      expect(mockProps.onFormDataChange).toHaveBeenCalledWith({
        server: 'smtp.gmail.com'
      });
    });

    it('should show validation errors', () => {
      const propsWithErrors = {
        ...mockProps,
        validationErrors: {
          server: 'SMTP server is required',
          username: 'Username is required'
        }
      };

      render(<SmtpTesterForm {...propsWithErrors} />);

      expect(screen.getByText('SMTP server is required')).toBeInTheDocument();
      expect(screen.getByText('Username is required')).toBeInTheDocument();
    });

    it('should disable form when loading', () => {
      const loadingProps = { ...mockProps, isLoading: true };
      render(<SmtpTesterForm {...loadingProps} />);

      const serverInput = screen.getByLabelText(/smtp server/i);
      const submitButton = screen.getByRole('button', { name: /test smtp/i });

      expect(serverInput).toBeDisabled();
      expect(submitButton).toBeDisabled();
    });

    it('should show loading state on submit button', () => {
      const loadingProps = { ...mockProps, isLoading: true };
      render(<SmtpTesterForm {...loadingProps} />);

      expect(screen.getByText(/testing/i)).toBeInTheDocument();
    });

    it('should call onSubmit when form is submitted', async () => {
      const user = userEvent.setup();
      render(<SmtpTesterForm {...mockProps} />);

      const submitButton = screen.getByRole('button', { name: /test smtp/i });
      await user.click(submitButton);

      expect(mockProps.onSubmit).toHaveBeenCalled();
    });

    it('should show provider presets', () => {
      render(<SmtpTesterForm {...mockProps} />);

      expect(screen.getByText(/gmail/i)).toBeInTheDocument();
      expect(screen.getByText(/outlook/i)).toBeInTheDocument();
      expect(screen.getByText(/yahoo/i)).toBeInTheDocument();
    });

    it('should apply provider preset when clicked', async () => {
      const user = userEvent.setup();
      render(<SmtpTesterForm {...mockProps} />);

      const gmailButton = screen.getByText(/gmail/i);
      await user.click(gmailButton);

      expect(mockProps.onFormDataChange).toHaveBeenCalledWith({
        server: 'smtp.gmail.com',
        port: '587',
        encryption: 'tls'
      });
    });
  });

  describe('SmtpTesterResults', () => {
    it('should render successful test results', () => {
      const successResult = {
        success: true,
        messageId: 'test-message-id',
        logs: 'Connection successful\nEmail sent successfully',
        testAddress: '<EMAIL>',
        duration: 1500
      };

      render(<SmtpTesterResults result={successResult} onNewTest={jest.fn()} />);

      expect(screen.getByText(/test completed successfully/i)).toBeInTheDocument();
      expect(screen.getByText('test-message-id')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText(/1500ms/)).toBeInTheDocument();
    });

    it('should render failed test results', () => {
      const failureResult = {
        success: false,
        error: 'Authentication failed',
        logs: 'Connection failed\nInvalid credentials',
        duration: 500
      };

      render(<SmtpTesterResults result={failureResult} onNewTest={jest.fn()} />);

      expect(screen.getByText(/test failed/i)).toBeInTheDocument();
      expect(screen.getByText('Authentication failed')).toBeInTheDocument();
      expect(screen.getByText(/500ms/)).toBeInTheDocument();
    });

    it('should show diagnostic logs', () => {
      const result = {
        success: true,
        messageId: 'test-id',
        logs: 'Step 1: Connection established\nStep 2: Authentication successful\nStep 3: Email sent',
        duration: 1000
      };

      render(<SmtpTesterResults result={result} onNewTest={jest.fn()} />);

      expect(screen.getByText(/diagnostic logs/i)).toBeInTheDocument();
      expect(screen.getByText(/step 1: connection established/i)).toBeInTheDocument();
      expect(screen.getByText(/step 2: authentication successful/i)).toBeInTheDocument();
      expect(screen.getByText(/step 3: email sent/i)).toBeInTheDocument();
    });

    it('should call onNewTest when new test button is clicked', async () => {
      const user = userEvent.setup();
      const onNewTest = jest.fn();
      const result = {
        success: true,
        messageId: 'test-id',
        logs: 'Test completed',
        duration: 1000
      };

      render(<SmtpTesterResults result={result} onNewTest={onNewTest} />);

      const newTestButton = screen.getByRole('button', { name: /new test/i });
      await user.click(newTestButton);

      expect(onNewTest).toHaveBeenCalled();
    });

    it('should show email authentication analysis for successful tests', () => {
      const result = {
        success: true,
        messageId: 'test-id',
        logs: 'Test completed',
        testAddress: '<EMAIL>',
        duration: 1000
      };

      render(<SmtpTesterResults result={result} onNewTest={jest.fn()} />);

      expect(screen.getByText(/email authentication analysis/i)).toBeInTheDocument();
    });

    it('should show troubleshooting tips for failed tests', () => {
      const result = {
        success: false,
        error: 'Connection timeout',
        logs: 'Connection failed',
        duration: 5000
      };

      render(<SmtpTesterResults result={result} onNewTest={jest.fn()} />);

      expect(screen.getByText(/troubleshooting tips/i)).toBeInTheDocument();
    });
  });

  describe('SmtpTesterInstructions', () => {
    it('should render setup instructions', () => {
      render(<SmtpTesterInstructions />);

      expect(screen.getByText(/setup instructions/i)).toBeInTheDocument();
      expect(screen.getByText(/gmail/i)).toBeInTheDocument();
      expect(screen.getByText(/outlook/i)).toBeInTheDocument();
      expect(screen.getByText(/yahoo/i)).toBeInTheDocument();
    });

    it('should show provider-specific instructions', () => {
      render(<SmtpTesterInstructions />);

      // Gmail instructions
      expect(screen.getByText(/app passwords/i)).toBeInTheDocument();
      expect(screen.getByText(/2-factor authentication/i)).toBeInTheDocument();

      // Outlook instructions
      expect(screen.getByText(/modern authentication/i)).toBeInTheDocument();

      // Yahoo instructions
      expect(screen.getByText(/app password/i)).toBeInTheDocument();
    });

    it('should show troubleshooting section', () => {
      render(<SmtpTesterInstructions />);

      expect(screen.getByText(/troubleshooting/i)).toBeInTheDocument();
      expect(screen.getByText(/common issues/i)).toBeInTheDocument();
    });

    it('should show security best practices', () => {
      render(<SmtpTesterInstructions />);

      expect(screen.getByText(/security/i)).toBeInTheDocument();
      expect(screen.getByText(/best practices/i)).toBeInTheDocument();
    });

    it('should be collapsible', async () => {
      const user = userEvent.setup();
      render(<SmtpTesterInstructions />);

      const toggleButton = screen.getByRole('button', { name: /toggle instructions/i });
      await user.click(toggleButton);

      // Instructions should be collapsed/expanded
      expect(toggleButton).toBeInTheDocument();
    });
  });
});
