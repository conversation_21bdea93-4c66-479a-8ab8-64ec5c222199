I want to add an Email Deliverability Testing Tool to my VanishPost temporary email service. This tool should:

1. Create a dedicated section accessible via a "Tools" dropdown in the main navigation bar
2. Generate special temporary test addresses with a distinct prefix (e.g., "<EMAIL>" that we can store in our supabase)
3. Analyze incoming email headers and authentication results the guerrilla  to evaluate:
- SPF (Sender Policy Framework) configuration
- DKIM (DomainKeys Identified Mail) signatures
- DMARC (Domain-based Message Authentication) policies
- IP reputation and reverse DNS
- Content spam scoring

4. Integrate with the Deepseek AI API to:
- Calculate a comprehensive deliverability score (1-10)
- Provide detailed explanations for each issue detected
- Generate corrected DNS record templates (TXT records for SPF, DKIM, and DMARC)
- Suggest specific implementation steps based on the sender's domain

Implementation workflow:
1. User navigates to "Tools > Email Deliverability Test" from the navbar
2. User receives a unique test address (e.g., "<EMAIL>")
3. User sends an email from their domain's mail server to this address
4. System captures and analyzes the email headers, authentication results, and metadata
5. Deepseek AI processes the analysis results via API
6. User views a detailed report showing:
- Overall deliverability score
- Pass/fail status for each authentication method
- Specific issues identified in their configuration
- Exact DNS record corrections with implementation instructions
- Additional recommendations for improving deliverability

Technical requirements:
1. Create a separate connection to the go-guerrilla email server specifically for this tool
2. we shall use and share the same database  as the current application  for storing deliverability test results
    
    GUERRILLA_DB_HOST=*************
    GUERRILLA_DB_USER=guerrilla_private
    GUERRILLA_DB_PASSWORD=YiWTp9FChTFW3nCSG7
    GUERRILLA_DB_NAME=guerrilla_db
    GUERRILLA_DB_PORT=3306
    GUERRILLA_DB_SSL=false
    
3. Develop a header parser to extract authentication results and relevant metadata
4. Build an API integration with Deepseek AI for analysis and recommendations
          this is the Deepseek API:*********************************** and its documentation on this link: https://api-docs.deepseek.com/
5. Design a user-friendly interface for displaying the test results and recommendations

The tool should function independently from the main temporary email service while maintaining consistent design and user experience with the rest of the VanishPost application.