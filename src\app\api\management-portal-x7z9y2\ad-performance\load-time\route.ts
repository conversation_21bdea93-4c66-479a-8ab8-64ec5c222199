/**
 * API route for recording ad load times
 */
import { NextRequest, NextResponse } from 'next/server';
import { addAdLoadTime } from '@/lib/ad-performance/adLoadTimeService';
import { logInfo, logError } from '@/lib/logging';

/**
 * POST /api/management-portal-x7z9y2/ad-performance/load-time
 * 
 * Record a new ad load time
 */
export async function POST(request: NextRequest) {
  try {
    // Get the load time from the request body
    const { loadTime } = await request.json();
    
    // Validate the load time
    if (typeof loadTime !== 'number' || loadTime < 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid load time' },
        { status: 400 }
      );
    }
    
    // Add the load time
    addAdLoadTime(loadTime);
    
    // Log the event
    logInfo('api', 'Ad load time recorded', { loadTime });
    
    // Return success
    return NextResponse.json({ success: true });
  } catch (error) {
    logError('api', 'Error recording ad load time', { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to record ad load time' },
      { status: 500 }
    );
  }
}
