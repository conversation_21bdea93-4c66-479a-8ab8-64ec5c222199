import { useMemo } from 'react';
import DOMPurify from 'dompurify';

interface UseIframeContentProps {
  html: string;
  webFonts?: string[];
  scaleFactor?: number;
  isGuideEmail?: boolean;
}

/**
 * Simplified hook for generating and sanitizing iframe HTML content
 *
 * This hook provides a simple, reliable way to:
 * - Sanitize HTML content with DOMPurify
 * - Generate complete HTML document for iframe
 * - Include necessary scripts and styles
 * - Memoize content for performance
 */
export function useIframeContent({
  html,
  webFonts = [],
  scaleFactor = 1.0,
  isGuideEmail = false
}: UseIframeContentProps) {
  // Memoize the sanitized HTML to avoid unnecessary re-sanitization
  const sanitizedHtml = useMemo(() => {
    try {
      // For guide emails, we can skip most of the processing since they're trusted content
      if (isGuideEmail) {
        return html;
      }

      // Sanitize the HTML for regular emails
      let processedHtml = html;
      if (typeof DOMPurify !== 'undefined' && typeof DOMPurify.sanitize === 'function') {
        processedHtml = DOMPurify.sanitize(html, {
          ADD_TAGS: ['style', 'link'],
          ADD_ATTR: ['target', 'rel', 'href', 'style'],
          WHOLE_DOCUMENT: true,
        });
      } else {
        console.warn('DOMPurify not available, using raw HTML');
      }

      // Process HTML to handle iframes with CSP restrictions
      // Simple regex to find iframes with Google domains
      const googleIframeRegex = /<iframe[^>]*src=["'](https?:\/\/(www\.)?google\.com\/[^"']*)["'][^>]*><\/iframe>/gi;

      // Replace Google iframes with a placeholder and link
      processedHtml = processedHtml.replace(googleIframeRegex, (_match, url) => {
        return `<div style="border: 1px solid #ddd; padding: 16px; margin: 8px 0; text-align: center; background-color: #f9f9f9;">
          <p style="margin-bottom: 8px;">This content from Google cannot be displayed due to security restrictions.</p>
          <a href="${url}" target="_blank" style="color: #1a73e8; text-decoration: underline;">Open content in a new tab</a>
        </div>`;
      });

      return processedHtml;
    } catch (error) {
      console.error('Error processing HTML:', error);
      return html;
    }
  }, [html, isGuideEmail]);

  // Memoize the complete HTML document to avoid unnecessary re-generation
  const htmlContent = useMemo(() => {
    // Create font links HTML
    const fontLinksHtml = webFonts.map(font =>
      `<link href="${font}" rel="stylesheet">`
    ).join('');

    // Common CSS for both email types
    const commonCss = `
      /* Reset styles */
      html, body {
        margin: 0;
        padding: 0;
        overflow-y: visible;
        overflow-x: auto;
        height: auto;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        line-height: 1.5;
        color: #333;
      }

      /* Email wrapper with scaling */
      .email-wrapper {
        width: 100%;
        max-width: 100%;
        margin: 0 auto;
        padding: 0;
        transform-origin: top left;
        transition: transform 0.3s ease-out;
        transform: scale(${scaleFactor});
        will-change: transform;
      }

      /* Make sure images are responsive */
      img {
        max-width: 100%;
        height: auto;
      }
    `;

    // Common JavaScript for both email types
    const commonJs = `
      // Simple function to adjust iframe height
      function adjustHeight() {
        const scale = ${scaleFactor};
        const contentHeight = document.body.scrollHeight * scale;

        if (contentHeight > 0) {
          window.parent.postMessage({
            type: 'resize-iframe',
            height: contentHeight
          }, '*');
        }
      }

      // Handle scale changes from parent
      function handleScaleChange(newScale) {
        const wrapper = document.querySelector('.email-wrapper');
        if (wrapper) {
          wrapper.style.transform = 'scale(' + newScale + ')';

          // Recalculate height after scale change
          setTimeout(function() {
            const contentHeight = document.body.scrollHeight * newScale;
            window.parent.postMessage({
              type: 'resize-iframe',
              height: contentHeight,
              isScaleChange: true
            }, '*');
          }, 300); // Wait for transition to complete
        }
      }

      // Listen for messages from parent
      window.addEventListener('message', function(event) {
        if (event.data && event.data.type === 'set-scale') {
          handleScaleChange(event.data.scale);
        }
      });

      // Handle links to open in new tab
      function setupLinks() {
        document.querySelectorAll('a[href]').forEach(function(link) {
          link.addEventListener('click', function(e) {
            e.preventDefault();
            var href = this.getAttribute('href');
            if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
              window.open(href, '_blank');
            }
          });
        });
      }

      // Initialize on load
      window.addEventListener('load', function() {
        adjustHeight();
        setupLinks();

        // Track image loading for regular emails
        if (!${isGuideEmail}) {
          trackImageLoading();
        }
      });

      // Also adjust on DOMContentLoaded
      document.addEventListener('DOMContentLoaded', adjustHeight);
    `;

    // Additional JavaScript for regular emails
    const regularEmailJs = `
      // Function to track image loading
      function trackImageLoading() {
        const images = document.querySelectorAll('img');
        const totalImages = images.length;
        let loadedImages = 0;

        // Report total images
        window.parent.postMessage({
          type: 'total-images',
          count: totalImages
        }, '*');

        if (totalImages === 0) {
          window.parent.postMessage({
            type: 'images-loaded',
            count: 0,
            total: 0
          }, '*');
          return;
        }

        // Track image loading
        images.forEach(function(img) {
          if (img.complete) {
            loadedImages++;
          }

          img.addEventListener('load', function() {
            loadedImages++;
            window.parent.postMessage({
              type: 'images-loaded',
              count: loadedImages,
              total: totalImages
            }, '*');

            // Adjust height when image loads
            adjustHeight();
          });

          img.addEventListener('error', function() {
            loadedImages++;
            window.parent.postMessage({
              type: 'images-loaded',
              count: loadedImages,
              total: totalImages
            }, '*');
          });
        });

        // Report initially loaded images
        if (loadedImages > 0) {
          window.parent.postMessage({
            type: 'images-loaded',
            count: loadedImages,
            total: totalImages
          }, '*');
        }
      }
    `;

    // Build the HTML document
    let document = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          ${!isGuideEmail ? '<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests; default-src \'self\' \'unsafe-inline\' https: data:; img-src * data: blob: https:; font-src * data: https:; style-src \'self\' \'unsafe-inline\' https:;">' : ''}
          <title>${isGuideEmail ? 'Guide Email Content' : 'Email Content'}</title>
          ${fontLinksHtml}
          <style>
            ${commonCss}
            ${!isGuideEmail ? `
              /* Additional styles for regular emails */
              body {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
              }

              table {
                max-width: 100%;
              }

              @media print {
                body {
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  print-color-adjust: exact !important;
                }

                .email-wrapper {
                  transform: scale(1) !important;
                }
              }
            ` : ''}
          </style>
          <script>
            ${commonJs}
            ${!isGuideEmail ? regularEmailJs : ''}
          </script>
        </head>
        <body>
          <div class="email-wrapper">
            ${sanitizedHtml}
          </div>
        </body>
      </html>
    `;

    return document;
  }, [sanitizedHtml, webFonts, scaleFactor, isGuideEmail]);

  return {
    htmlContent,
    sanitizedHtml
  };
}
