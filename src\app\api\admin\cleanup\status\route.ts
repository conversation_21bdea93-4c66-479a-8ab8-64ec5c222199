/**
 * API route for getting the cleanup scheduler status
 * 
 * This API provides information about the current status of the cleanup scheduler.
 */
import { NextRequest, NextResponse } from 'next/server';
import { logError } from '@/lib/logging';
import { isCleanupSchedulerRunning } from '@/lib/cleanup/cleanupScheduler';
import { getConfig } from '@/lib/config/configService';

/**
 * GET /api/admin/cleanup/status
 * 
 * Get the status of the cleanup scheduler
 */
export async function GET(request: NextRequest) {
  try {
    const running = isCleanupSchedulerRunning();
    const intervalMinutes = await getConfig('cleanupIntervalMinutes');
    const autoStart = await getConfig('cleanupAutoStart');
    const lastRunTime = await getConfig('cleanupLastRunTime');
    const schedulerStartTime = await getConfig('cleanupSchedulerStartTime');

    // Calculate next run time if running
    let nextRunAt = null;
    if (running && intervalMinutes && schedulerStartTime) {
      const startTime = new Date(schedulerStartTime).getTime();
      const intervalMs = intervalMinutes * 60 * 1000;
      const now = Date.now();

      // Calculate how many intervals have passed since start
      const elapsedTime = now - startTime;
      const intervalsPassed = Math.floor(elapsedTime / intervalMs);

      // Calculate the next run time based on the scheduler start time and intervals
      const nextRunTime = startTime + ((intervalsPassed + 1) * intervalMs);
      nextRunAt = new Date(nextRunTime).toISOString();
    }

    return NextResponse.json({
      success: true,
      status: {
        running,
        intervalMinutes,
        nextRunAt,
        autoStart: autoStart !== false // Default to true if not set
      }
    });
  } catch (error) {
    logError('cleanup', 'Error getting cleanup scheduler status', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
