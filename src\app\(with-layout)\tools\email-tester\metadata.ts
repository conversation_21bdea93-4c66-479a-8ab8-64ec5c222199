/**
 * SEO Metadata for Email Tester Tool
 * Comprehensive metadata configuration for all Email Tester Tool pages
 */

import type { Metadata } from 'next';

const baseUrl = 'https://vanishpost.com';

export const emailTesterMetadata: Metadata = {
  title: 'Email Tester - Test Email Deliverability & Authentication | VanishPost',
  description: 'Free email deliverability testing tool. Check SPF, DKIM, DMARC authentication, test email server configuration, and get AI-powered recommendations to improve email delivery rates.',
  keywords: [
    'email tester',
    'email deliverability test',
    'SPF checker',
    'DKIM validator',
    'DMARC tester',
    'email authentication',
    'mail tester',
    'email server test',
    'email delivery test',
    'email reputation checker',
    'email blacklist check',
    'email authentication checker',
    'email deliverability analyzer',
    'free email tester',
    'email testing tool'
  ].join(', '),
  authors: [{ name: 'VanishPost Team' }],
  creator: 'VanishPost',
  publisher: 'VanishPost',
  metadataBase: new URL(baseUrl),
  alternates: {
    canonical: '/tools/email-tester',
  },
  openGraph: {
    title: 'Email Tester - Test Email Deliverability & Authentication',
    description: 'Free email deliverability testing tool. Check SPF, DKIM, DMARC authentication and get AI-powered recommendations to improve email delivery.',
    url: `${baseUrl}/tools/email-tester`,
    siteName: 'VanishPost',
    locale: 'en_US',
    type: 'website',
    images: [
      {
        url: `${baseUrl}/images/email-tester-tool-og.png`,
        width: 1200,
        height: 630,
        alt: 'Email Tester Tool - Test Email Deliverability',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Email Tester - Test Email Deliverability & Authentication',
    description: 'Free email deliverability testing tool. Check SPF, DKIM, DMARC authentication and improve email delivery rates.',
    creator: '@vanishpost',
    images: [`${baseUrl}/images/email-tester-tool-og.png`],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export const emailTesterHistoryMetadata: Metadata = {
  title: 'Email Test History - View Past Email Deliverability Tests | VanishPost',
  description: 'View your email deliverability test history. Track improvements in SPF, DKIM, DMARC authentication over time and monitor email server performance.',
  keywords: [
    'email test history',
    'email deliverability tracking',
    'email authentication history',
    'SPF test results',
    'DKIM test history',
    'DMARC validation history',
    'email performance tracking'
  ].join(', '),
  authors: [{ name: 'VanishPost Team' }],
  creator: 'VanishPost',
  publisher: 'VanishPost',
  metadataBase: new URL(baseUrl),
  alternates: {
    canonical: '/tools/email-tester/history',
  },
  openGraph: {
    title: 'Email Test History - Track Email Deliverability Performance',
    description: 'View your email deliverability test history and track improvements in email authentication over time.',
    url: `${baseUrl}/tools/email-tester/history`,
    siteName: 'VanishPost',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary',
    title: 'Email Test History - Track Email Deliverability Performance',
    description: 'View your email deliverability test history and track improvements over time.',
    creator: '@vanishpost',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
};

export const emailTesterResultsMetadata: Metadata = {
  title: 'Email Test Results - Detailed Email Deliverability Analysis | VanishPost',
  description: 'Detailed email deliverability test results with SPF, DKIM, DMARC analysis, authentication scores, and AI-powered recommendations for improvement.',
  keywords: [
    'email test results',
    'email deliverability analysis',
    'SPF results',
    'DKIM analysis',
    'DMARC validation results',
    'email authentication score',
    'email delivery recommendations'
  ].join(', '),
  authors: [{ name: 'VanishPost Team' }],
  creator: 'VanishPost',
  publisher: 'VanishPost',
  metadataBase: new URL(baseUrl),
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
};
