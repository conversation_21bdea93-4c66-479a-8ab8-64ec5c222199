To integrate an **SMTP Tester Tool** into your Next.js web application (VanishPost) with the specified requirements, you’ll add a feature that allows users to test SMTP server connectivity using their credentials and optionally analyze email authentication via your existing Email Deliverability Testing Tool. The tool will provide two options in the UI:

1. **Send to Auto-Generated Email**: Sends a test email to an auto-generated address (e.g., `<EMAIL>`) for analysis by your Email Deliverability Testing Tool, testing both SMTP connectivity and authentication (SPF, DKIM, DMARC).
2. **Send to Custom Recipient**: Sends a test email to a user-specified recipient email, testing only SMTP connectivity.

No data will be stored, ensuring security and simplicity. The tool will integrate with your “Tools” dropdown, using `nodemailer` for sending emails and leveraging your existing Mailauth-based deliverability tool for analysis.

---

### Implementation Guide for SMTP Tester Tool

This guide is concise, human-readable, and tailored for your AI code assistant to implement the SMTP Tester Tool with the specified UI options, integrating with your Email Deliverability Testing Tool.

#### Requirements Recap
- **UI Options**:
  - **Option 1**: Button to send a test email to an auto-generated address (e.g., `<EMAIL>`), followed by analysis using the Email Deliverability Testing Tool.
  - **Option 2**: Input field for a custom recipient email to test SMTP connectivity only.
- **Form Fields**:
  - SMTP Server (e.g., `smtp.gmail.com`).
  - Port (e.g., 587).
  - Encryption (None, TLS, SSL).
  - Username (e.g., email or API key).
  - Password (e.g., App Password for Gmail).
  - Sender Email (e.g., user’s email).
  - Recipient Email (for Option 2; auto-generated for Option 1).
- **Behavior**:
  - Test SMTP connection using `nodemailer`.
  - For Option 1, trigger Mailauth analysis and display authentication results.
  - For Option 2, confirm email delivery success or failure.
  - Display diagnostic logs (e.g., SMTP response, errors).
- **Security**: No storage of credentials or test results.
- **Integration**: Add to “Tools” dropdown, consistent with DKIM/DMARC generators and Email Deliverability Testing Tool.

#### Implementation Steps

##### 1. Update Navigation
- **Task**: Add SMTP Tester to the “Tools” dropdown.
- **Details**:
  - Modify `components/Navbar.tsx` to include a link to `/tools/smtp-tester` labeled “SMTP Tester”.
  - Ensure UI consistency with existing tools.

##### 2. Create SMTP Tester Frontend
- **Task**: Build a page with a form for SMTP credentials and two testing options.
- **Details**:
  - Create `pages/tools/smtp-tester.tsx`.
  - Implement a form with:
    - Inputs: SMTP Server, Port, Encryption (dropdown), Username, Password, Sender Email.
    - Option 1: Button labeled “Test with Email Analysis” to send to an auto-generated address (use `uuid` for `test-<uuid>@fademail.site`).
    - Option 2: Input field for custom recipient email and a “Test Connection” button.
  - On submission:
    - Call `/api/test-smtp` with form data and option type (`auto` or `custom`).
    - For Option 1, also call the existing `/api/analyze` (from Email Deliverability Testing Tool) to get authentication results.
  - Display:
    - Status (e.g., “Success: Email sent” or “Error: Authentication failed”).
    - Diagnostic logs (SMTP response or error).
    - For Option 1, show authentication results (SPF, DKIM, DMARC pass/fail).
    - Instructions for SMTP setup (e.g., “For Gmail, use an App Password from Google Account > Security > App Passwords”).

**Frontend Artifact**:
```typescript
import { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

const SmtpTester = () => {
  const [formData, setFormData] = useState({
    server: '',
    port: '587',
    encryption: 'TLS',
    username: '',
    password: '',
    sender: '',
    recipient: ''
  });
  const [testOption, setTestOption] = useState('auto');
  const [autoRecipient, setAutoRecipient] = useState(`test-${uuidv4()}@fademail.site`);
  const [status, setStatus] = useState('');
  const [logs, setLogs] = useState('');
  const [analysis, setAnalysis] = useState(null);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setStatus('');
    setLogs('');
    setAnalysis(null);

    const payload = {
      ...formData,
      recipient: testOption === 'auto' ? autoRecipient : formData.recipient,
      option: testOption
    };

    try {
      const res = await fetch('/api/test-smtp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      const result = await res.json();

      if (!res.ok) {
        throw new Error(result.error || 'Failed to send test email');
      }

      setStatus(result.status);
      setLogs(result.logs);

      if (testOption === 'auto') {
        const analysisRes = await fetch('/api/analyze', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ testEmail: autoRecipient })
        });
        if (analysisRes.ok) {
          setAnalysis(await analysisRes.json());
        }
      }
    } catch (err: any) {
      setError(err.message);
      setLogs(err.message);
    }
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold">SMTP Tester</h1>
      <form onSubmit={handleSubmit} className="mt-4">
        <div className="mb-4">
          <label className="block">SMTP Server</label>
          <input
            type="text"
            value={formData.server}
            onChange={(e) => setFormData({ ...formData, server: e.target.value })}
            className="border p-2 w-full"
            placeholder="e.g., smtp.gmail.com"
            required
          />
        </div>
        <div className="mb-4">
          <label className="block">Port</label>
          <input
            type="number"
            value={formData.port}
            onChange={(e) => setFormData({ ...formData, port: e.target.value })}
            className="border p-2 w-full"
            placeholder="e.g., 587"
            required
          />
        </div>
        <div className="mb-4">
          <label className="block">Encryption</label>
          <select
            value={formData.encryption}
            onChange={(e) => setFormData({ ...formData, encryption: e.target.value })}
            className="border p-2 w-full"
          >
            <option value="None">None</option>
            <option value="TLS">TLS</option>
            <option value="SSL">SSL</option>
          </select>
        </div>
        <div className="mb-4">
          <label className="block">Username</label>
          <input
            type="text"
            value={formData.username}
            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
            className="border p-2 w-full"
            placeholder="e.g., <EMAIL>"
            required
          />
        </div>
        <div className="mb-4">
          <label className="block">Password</label>
          <input
            type="password"
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            className="border p-2 w-full"
            placeholder="e.g., App Password for Gmail"
            required
          />
        </div>
        <div className="mb-4">
          <label className="block">Sender Email</label>
          <input
            type="email"
            value={formData.sender}
            onChange={(e) => setFormData({ ...formData, sender: e.target.value })}
            className="border p-2 w-full"
            placeholder="e.g., <EMAIL>"
            required
          />
        </div>
        <div className="mb-4">
          <label className="block">Test Option</label>
          <select
            value={testOption}
            onChange={(e) => setTestOption(e.target.value)}
            className="border p-2 w-full"
          >
            <option value="auto">Send to Auto-Generated Email (with Analysis)</option>
            <option value="custom">Send to Custom Recipient</option>
          </select>
        </div>
        {testOption === 'auto' ? (
          <div className="mb-4">
            <label className="block">Recipient (Auto-Generated)</label>
            <input
              type="text"
              value={autoRecipient}
              readOnly
              className="border p-2 w-full bg-gray-100"
            />
          </div>
        ) : (
          <div className="mb-4">
            <label className="block">Recipient Email</label>
            <input
              type="email"
              value={formData.recipient}
              onChange={(e) => setFormData({ ...formData, recipient: e.target.value })}
              className="border p-2 w-full"
              placeholder="e.g., <EMAIL>"
              required
            />
          </div>
        )}
        <button type="submit" className="bg-blue-500 text-white p-2 rounded">
          {testOption === 'auto' ? 'Test with Analysis' : 'Test Connection'}
        </button>
      </form>
      {error && <p className="text-red-500 mt-4">{error}</p>}
      {status && (
        <div className="mt-4">
          <h2 className="text-xl font-semibold">Test Results</h2>
          <p><strong>Status:</strong> {status}</p>
          <p><strong>Logs:</strong></p>
          <pre className="bg-gray-100 p-2">{logs}</pre>
          {analysis && (
            <div>
              <h3 className="text-lg font-semibold">Authentication Analysis</h3>
              <p>SPF: {analysis.spf.pass ? 'Pass' : 'Fail'}</p>
              <p>DKIM: {analysis.dkim.pass ? 'Pass' : `Fail (${analysis.dkim.reason || 'Unknown'})`}</p>
              <p>DMARC: {analysis.dmarc.pass ? 'Pass' : 'Fail'}</p>
            </div>
          )}
        </div>
      )}
      <div className="mt-4">
        <h2 className="text-xl font-semibold">Instructions</h2>
        <p>For Gmail, use smtp.gmail.com, port 587 (TLS), and an App Password (generate at Google Account > Security > App Passwords).</p>
        <p>For Outlook, use smtp-mail.outlook.com, port 587 (TLS).</p>
      </div>
    </div>
  );
};

export default SmtpTester;
```

##### 3. Create SMTP Tester Backend
- **Task**: Develop an API route to send test emails using `nodemailer`.
- **Details**:
  - Create `pages/api/test-smtp.ts`.
  - Use `nodemailer` to send the test email based on user-provided credentials.
  - Install dependency: `npm install nodemailer`.
  - Return success status and diagnostic logs (e.g., SMTP response) or error message.

**Backend Artifact**:
```typescript
import { NextApiRequest, NextApiResponse } from 'next';
import nodemailer from 'nodemailer';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { server, port, encryption, username, password, sender, recipient, option } = req.body;

  if (!server || !port || !username || !password || !sender || !recipient) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    const secure = encryption === 'SSL';
    const transport = nodemailer.createTransport({
      host: server,
      port: parseInt(port),
      secure,
      auth: { user: username, pass: password },
      tls: encryption === 'TLS' ? { requireTLS: true } : undefined
    });

    const info = await transport.sendMail({
      from: sender,
      to: recipient,
      subject: 'VanishPost SMTP Test',
      text: 'This is a test email from VanishPost SMTP Tester.'
    });

    res.status(200).json({
      status: 'Success: Email sent',
      logs: `Message sent: ${info.messageId}\nAccepted: ${info.accepted.join(', ')}`
    });
  } catch (error: any) {
    const errorMessage = error.response || error.message || 'Failed to send email';
    res.status(500).json({
      error: errorMessage,
      status: 'Error: Failed to send email',
      logs: errorMessage
    });
  }
}
```

##### 4. Integrate with Email Deliverability Testing Tool
- **Task**: Reuse the existing `/api/analyze` endpoint for Option 1.
- **Details**:
  - The frontend calls `/api/analyze` with the auto-generated email address (`<EMAIL>`) after a successful SMTP test.
  - Assume `/api/analyze` is already implemented (as per your Email Deliverability Testing Tool) and returns authentication results (SPF, DKIM, DMARC).
  - Display analysis results (e.g., pass/fail for each) in the SMTP Tester UI for Option 1.

##### 5. Testing and Validation
- **Task**: Verify functionality for both options.
- **Details**:
  - **Option 1 (Auto-Generated Email)**:
    - Test with Gmail: `smtp.gmail.com`, port 587, TLS, App Password.
    - Send to `<EMAIL>`.
    - Verify SMTP success and authentication results (SPF, DKIM, DMARC) via `/api/analyze`.
  - **Option 2 (Custom Recipient)**:
    - Test with Gmail, sending to a personal email (e.g., `<EMAIL>`).
    - Verify SMTP success or failure (e.g., check recipient inbox or error logs).
  - Test failure cases (e.g., wrong password, invalid server).
  - Ensure diagnostic logs are clear (e.g., “535 Authentication failed”).

##### 6. Security Considerations
- **No Storage**: Do not store credentials or test results.
- **HTTPS**: Ensure API calls use HTTPS.
- **Input Validation**: Sanitize inputs (server, port, emails) to prevent injection.
- **Rate Limiting**: Limit test frequency to prevent abuse (e.g., via middleware).

##### 7. User Experience
- **Instructions**: Include setup guides for Gmail (App Password) and other providers (e.g., Outlook: `smtp-mail.outlook.com`, port 587, TLS).
- **Clear Feedback**: Show success/error messages and logs prominently.
- **Reset Option**: Allow users to generate a new auto-generated address or retest.

---

### Technical Notes
- **Dependencies**:
  - `nodemailer`: Install with `npm install nodemailer`. Docs: [https://nodemailer.com](https://nodemailer.com).
  - `uuid`: For auto-generated addresses. Install with `npm install uuid`.
- **Gmail Setup**:
  - Use `smtp.gmail.com`, port 587 (TLS) or 465 (SSL).
  - App Password for 2FA: [Google App Passwords](https://myaccount.google.com/security).
- **Existing Endpoint**: Reuse `/api/analyze` from your Email Deliverability Testing Tool (assumed to query `guerrilla_mail` table and use Mailauth).
- **Nodemailer Configuration**:
  - `secure: true` for SSL (port 465).
  - `requireTLS: true` for TLS (port 587).
- **Validation**: Optionally use Node.js `dns` module to verify SMTP server domain (not required for basic functionality).

---

### Prompt for AI Code Assistant
Pass the following to your AI code assistant:

> Implement an SMTP Tester Tool for my Next.js app (VanishPost) with a UI at `/tools/smtp-tester` and an API at `/api/test-smtp`. The tool lets users test SMTP connectivity with their credentials and offers two options: (1) Send a test email to an auto-generated address (`test-<uuid>@fademail.site`) and analyze authentication (SPF, DKIM, DMARC) using the existing `/api/analyze` endpoint (Mailauth-based). (2) Send to a user-specified recipient email to test connectivity only. The UI should have a form for SMTP Server, Port, Encryption (None/TLS/SSL), Username, Password, Sender Email, and a dropdown to choose the test option (auto/custom). For Option 1, show the auto-generated address (read-only); for Option 2, include a recipient email input. Use `nodemailer` to send emails, display success/error status with diagnostic logs, and show authentication results for Option 1. Do not store any data. Include instructions for Gmail (App Password) and Outlook. Add the tool to the “Tools” dropdown in `components/Navbar.tsx`. Ensure security (no credential storage, HTTPS) and clear UX. Use the provided artifacts as templates.

Provide the assistant with the artifacts above and your existing `/api/analyze` code (or confirm it’s unchanged). Test with Gmail (`smtp.gmail.com`, port 587, TLS, App Password) and another provider (e.g., Outlook). Let me know if you need specific tweaks or additional artifacts!