'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

interface NavLinkProps {
  href: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  exact?: boolean;
}

export default function NavLink({ href, icon, children, exact = false }: NavLinkProps) {
  const pathname = usePathname() || '';
  const isActive = exact
    ? pathname === href
    : pathname.startsWith(href);

  return (
    <Link
      href={href}
      className={cn(
        "flex items-center px-4 py-3 text-sm font-medium transition-all duration-200",
        isActive
          ? "bg-[#505050] text-white shadow-sm"
          : "text-white hover:bg-[#605f5f] hover:text-white"
      )}
    >
      <span className="mr-3">{icon}</span>
      {children}
    </Link>
  );
}
