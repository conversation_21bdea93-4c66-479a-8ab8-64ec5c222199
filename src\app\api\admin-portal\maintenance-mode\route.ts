/**
 * API route for toggling maintenance mode
 * 
 * This API provides functionality to enable or disable maintenance mode.
 */
import { NextRequest, NextResponse } from 'next/server';
import { logInfo, logError } from '@/lib/logging';
import { getConfig, updateConfig } from '@/lib/config/configService';

/**
 * GET /api/admin-portal/maintenance-mode
 * 
 * Get the current maintenance mode status
 */
export async function GET(request: NextRequest) {
  try {
    const maintenanceMode = await getConfig('maintenanceMode');
    
    return NextResponse.json({
      success: true,
      maintenanceMode
    });
  } catch (error) {
    logError('maintenance', 'Error getting maintenance mode status', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

/**
 * POST /api/admin-portal/maintenance-mode
 * 
 * Toggle maintenance mode
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { enabled } = body;
    
    if (typeof enabled !== 'boolean') {
      return NextResponse.json({
        success: false,
        error: 'Invalid request: "enabled" must be a boolean'
      }, { status: 400 });
    }
    
    // Update the configuration
    await updateConfig('maintenanceMode', enabled);
    
    logInfo('maintenance', `Maintenance mode ${enabled ? 'enabled' : 'disabled'}`);
    
    return NextResponse.json({
      success: true,
      maintenanceMode: enabled
    });
  } catch (error) {
    logError('maintenance', 'Error updating maintenance mode', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
