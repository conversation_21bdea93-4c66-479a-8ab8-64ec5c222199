'use client';

import React from 'react';
import * as SwitchPrimitive from '@radix-ui/react-switch';
import { cn } from '@/lib/utils';

/**
 * shadcn/ui Switch Component adapted for VanishPost
 *
 * Enhanced switch component with better accessibility and modern styling.
 * Maintains backward compatibility with existing VanishPost Switch usage.
 */

// Legacy interface for backward compatibility
interface LegacySwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  label?: string;
  id?: string;
  className?: string;
}

// Modern shadcn/ui Switch component
function ModernSwitch({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>) {
  return (
    <SwitchPrimitive.Root
      data-slot="switch"
      className={cn(
        // Base styles adapted for VanishPost theme
        "peer inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-sm transition-all outline-none",
        "focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",
        "disabled:cursor-not-allowed disabled:opacity-50",
        // VanishPost color scheme
        "data-[state=checked]:bg-blue-600 data-[state=unchecked]:bg-gray-300",
        "dark:data-[state=checked]:bg-blue-500 dark:data-[state=unchecked]:bg-gray-600",
        // Hover states
        "hover:data-[state=checked]:bg-blue-700 hover:data-[state=unchecked]:bg-gray-400",
        className
      )}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot="switch-thumb"
        className={cn(
          "pointer-events-none block size-4 rounded-full bg-white ring-0 transition-transform",
          "data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0",
          "shadow-sm"
        )}
      />
    </SwitchPrimitive.Root>
  );
}

// Legacy Switch component for backward compatibility
export function Switch({
  checked,
  onChange,
  disabled = false,
  label,
  id,
  className = '',
}: LegacySwitchProps) {
  // Use modern switch with legacy interface
  return (
    <div className={`flex items-center ${className}`}>
      {label && (
        <label
          htmlFor={id}
          className={`mr-3 text-sm font-medium ${
            disabled ? 'text-gray-400' : 'text-gray-700'
          }`}
        >
          {label}
        </label>
      )}
      <ModernSwitch
        id={id}
        checked={checked}
        onCheckedChange={onChange}
        disabled={disabled}
      />
    </div>
  );
}

// Enhanced Switch variants for VanishPost admin use cases

// Switch with label for better UX
interface SwitchWithLabelProps extends React.ComponentProps<typeof SwitchPrimitive.Root> {
  label: string;
  description?: string;
  labelPosition?: 'left' | 'right';
}

export function SwitchWithLabel({
  label,
  description,
  labelPosition = 'right',
  className,
  id,
  ...props
}: SwitchWithLabelProps) {
  const switchId = id || `switch-${Math.random().toString(36).substr(2, 9)}`;

  const switchElement = <ModernSwitch id={switchId} className={className} {...props} />;
  const labelElement = (
    <div className="flex flex-col">
      <label
        htmlFor={switchId}
        className="text-sm font-medium text-gray-900 cursor-pointer"
      >
        {label}
      </label>
      {description && (
        <span className="text-xs text-gray-500 mt-0.5">
          {description}
        </span>
      )}
    </div>
  );

  return (
    <div className="flex items-center space-x-3">
      {labelPosition === 'left' && labelElement}
      {switchElement}
      {labelPosition === 'right' && labelElement}
    </div>
  );
}

// Switch for admin settings with status indicator
interface AdminSwitchProps extends React.ComponentProps<typeof SwitchPrimitive.Root> {
  label: string;
  description?: string;
  status?: 'enabled' | 'disabled' | 'warning';
  showStatus?: boolean;
}

export function AdminSwitch({
  label,
  description,
  status,
  showStatus = true,
  className,
  checked,
  ...props
}: AdminSwitchProps) {
  const getStatusColor = () => {
    if (!showStatus) return '';
    switch (status || (checked ? 'enabled' : 'disabled')) {
      case 'enabled':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'disabled':
      default:
        return 'text-gray-500';
    }
  };

  const getStatusText = () => {
    if (!showStatus) return '';
    switch (status || (checked ? 'enabled' : 'disabled')) {
      case 'enabled':
        return 'Enabled';
      case 'warning':
        return 'Warning';
      case 'disabled':
      default:
        return 'Disabled';
    }
  };

  return (
    <div className="flex items-center justify-between p-4 border-gray-200 rounded-xl bg-gray-50/50 hover:bg-gray-100/50 transition-all duration-200 shadow-sm">
      <div className="flex-1">
        <div className="flex items-center space-x-4">
          <ModernSwitch checked={checked} className={className} {...props} />
          <div>
            <h4 className="text-sm font-medium text-gray-900">{label}</h4>
            {description && (
              <p className="text-xs text-gray-600 mt-0.5">{description}</p>
            )}
          </div>
        </div>
      </div>
      {showStatus && (
        <div className="flex items-center space-x-2 ml-4">
          <div className={`w-2.5 h-2.5 rounded-full shadow-sm ${
            status === 'enabled' || checked ? 'bg-green-500' :
            status === 'warning' ? 'bg-yellow-500' : 'bg-gray-400'
          }`} />
          <span className={`text-xs font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
      )}
    </div>
  );
}

// Export the modern switch as well
export { ModernSwitch };
