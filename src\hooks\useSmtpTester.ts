/**
 * useSmtpTester Hook
 *
 * Custom hook for SMTP testing functionality
 * Manages state, form validation, and API interactions
 */

'use client';

import { useState, useCallback } from 'react';
import { SmtpTestState, SmtpFormData, SmtpTestRequest, SmtpTestResult, SmtpValidationErrors } from '@/types/smtp';

const initialFormData: SmtpFormData = {
  server: '',
  port: '587',
  encryption: 'tls',
  username: '',
  password: '',
  sender: '',
  testMode: 'auto',
  recipient: ''
};

const initialState: SmtpTestState = {
  isLoading: false,
  result: null,
  error: null,
  formData: initialFormData,
  validationErrors: {},
  validationSuccess: false
};

export function useSmtpTester() {
  const [state, setState] = useState<SmtpTestState>(initialState);

  // Update form data
  const updateFormData = useCallback((updates: Partial<SmtpFormData>) => {
    setState(prev => {
      const newFormData = { ...prev.formData, ...updates };
      const testModeChanged = updates.testMode && updates.testMode !== prev.formData.testMode;

      return {
        ...prev,
        formData: newFormData,
        validationErrors: {}, // Clear validation errors when form changes
        validationSuccess: false, // Clear validation success when form changes
        result: testModeChanged ? null : prev.result // Clear result when test mode changes
      };
    });
  }, []);

  // Validate form data
  const validateForm = useCallback((formData: SmtpFormData): SmtpValidationErrors => {
    const errors: SmtpValidationErrors = {};

    if (!formData.server.trim()) {
      errors.server = 'SMTP server is required';
    }

    const port = parseInt(formData.port);
    if (!formData.port || isNaN(port) || port < 1 || port > 65535) {
      errors.port = 'Valid port number (1-65535) is required';
    }

    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    }

    if (!formData.password.trim()) {
      errors.password = 'Password is required';
    }

    if (!formData.sender.trim()) {
      errors.sender = 'Sender email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.sender)) {
      errors.sender = 'Valid sender email is required';
    }

    if (formData.testMode === 'custom') {
      if (!formData.recipient.trim()) {
        errors.recipient = 'Recipient email is required for custom mode';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.recipient)) {
        errors.recipient = 'Valid recipient email is required';
      }
    }

    return errors;
  }, []);

  // Validate SMTP configuration
  const validateSmtpConfig = useCallback(async (formData: SmtpFormData): Promise<boolean> => {
    // Validate form first
    const validationErrors = validateForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setState(prev => ({ ...prev, validationErrors }));
      return false;
    }

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      validationErrors: {},
      validationSuccess: false
    }));

    try {
      const config = {
        server: formData.server.trim(),
        port: parseInt(formData.port),
        encryption: formData.encryption,
        username: formData.username.trim(),
        password: formData.password,
        sender: formData.sender.trim()
      };

      const response = await fetch('/api/tools/smtp-tester/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ config })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'SMTP validation failed');
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        validationSuccess: true,
        error: null
      }));
      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'SMTP validation failed',
        validationSuccess: false
      }));
      return false;
    }
  }, [validateForm]);

  // Test SMTP connection
  const testSmtpConnection = useCallback(async (formData: SmtpFormData): Promise<void> => {
    // Validate form
    const validationErrors = validateForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setState(prev => ({ ...prev, validationErrors }));
      return;
    }

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      result: null,
      validationErrors: {},
      validationSuccess: false
    }));

    try {
      const request: SmtpTestRequest = {
        config: {
          server: formData.server.trim(),
          port: parseInt(formData.port),
          encryption: formData.encryption,
          username: formData.username.trim(),
          password: formData.password,
          sender: formData.sender.trim()
        },
        testMode: formData.testMode,
        recipient: formData.testMode === 'custom' ? formData.recipient.trim() : undefined
      };

      const response = await fetch('/api/tools/smtp-tester', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      });

      const result: SmtpTestResult = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'SMTP test failed');
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        result
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      }));
    }
  }, [validateForm]);

  // Reset state
  const reset = useCallback(() => {
    setState(initialState);
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null, validationSuccess: false }));
  }, []);

  return {
    ...state,
    updateFormData,
    testSmtpConnection,
    validateSmtpConfig,
    validateForm,
    reset,
    clearError
  };
}
