/**
 * SMTP Utils Unit Tests
 *
 * Tests for utility functions used in SMTP testing
 */

import {
  generateTestEmailAddress,
  extractTestAddressId,
  formatSmtpLogs,
  getEncryptionDisplayName,
  getDefaultPortForEncryption,
  maskPassword,
  formatTestDuration,
  createTestEmailContent
} from '@/lib/tools/smtp-tester/utils';

describe('SMTP Utility Functions', () => {
  describe('generateTestEmailAddress', () => {
    beforeEach(() => {
      // Reset environment variable for each test
      delete process.env.EMAIL_TESTER_DOMAIN;
    });

    it('should generate valid test email addresses with default domain', () => {
      const email1 = generateTestEmailAddress();
      const email2 = generateTestEmailAddress();

      // Should be different each time
      expect(email1).not.toBe(email2);

      // Should match the expected pattern with default domain
      expect(email1).toMatch(/^test-.+@fademail\.site$/);
      expect(email2).toMatch(/^test-.+@fademail\.site$/);

      // Should contain UUID-like string
      const uuid1 = email1.split('@')[0].replace('test-', '');
      const uuid2 = email2.split('@')[0].replace('test-', '');
      expect(uuid1).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
      expect(uuid2).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
    });

    it('should use custom domain when EMAIL_TESTER_DOMAIN is set', () => {
      process.env.EMAIL_TESTER_DOMAIN = 'nodfern.org';

      const email1 = generateTestEmailAddress();
      const email2 = generateTestEmailAddress();

      // Should be different each time
      expect(email1).not.toBe(email2);

      // Should match the expected pattern with custom domain
      expect(email1).toMatch(/^test-.+@nodfern\.org$/);
      expect(email2).toMatch(/^test-.+@nodfern\.org$/);

      // Should contain UUID-like string
      const uuid1 = email1.split('@')[0].replace('test-', '');
      const uuid2 = email2.split('@')[0].replace('test-', '');
      expect(uuid1).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
      expect(uuid2).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
    });
  });

  describe('extractTestAddressId', () => {
    it('should extract UUID from valid test email addresses', () => {
      const testAddress = '<EMAIL>';
      const extractedId = extractTestAddressId(testAddress);

      expect(extractedId).toBe('247790ed-a8da-4fe2-9741-f9028849cf6f');
    });

    it('should extract UUID from test addresses with different domains', () => {
      const testAddress = '<EMAIL>';
      const extractedId = extractTestAddressId(testAddress);

      expect(extractedId).toBe('12345678-1234-1234-1234-123456789012');
    });

    it('should handle uppercase UUIDs', () => {
      const testAddress = '<EMAIL>';
      const extractedId = extractTestAddressId(testAddress);

      expect(extractedId).toBe('247790ED-A8DA-4FE2-9741-F9028849CF6F');
    });

    it('should return null for invalid test address formats', () => {
      const invalidAddresses = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '',
        '<EMAIL>', // Missing last UUID segment
        '<EMAIL>' // Extra characters
      ];

      invalidAddresses.forEach(address => {
        expect(extractTestAddressId(address)).toBeNull();
      });
    });

    it('should handle malformed input gracefully', () => {
      expect(extractTestAddressId(null as any)).toBeNull();
      expect(extractTestAddressId(undefined as any)).toBeNull();
      expect(extractTestAddressId(123 as any)).toBeNull();
    });

    it('should work with generated test addresses', () => {
      const generatedAddress = generateTestEmailAddress();
      const extractedId = extractTestAddressId(generatedAddress);

      expect(extractedId).not.toBeNull();
      expect(extractedId).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
    });
  });

  describe('formatSmtpLogs', () => {
    it('should format log arrays correctly', () => {
      const logs = [
        '[2025-01-01T12:00:00.000Z] Starting SMTP test',
        '[2025-01-01T12:00:01.000Z] Connection established',
        '[2025-01-01T12:00:02.000Z] Email sent successfully'
      ];

      const formatted = formatSmtpLogs(logs);
      expect(formatted).toBe(logs.join('\n'));
    });

    it('should handle empty log arrays', () => {
      const formatted = formatSmtpLogs([]);
      expect(formatted).toBe('');
    });

    it('should handle single log entry', () => {
      const logs = ['[2025-01-01T12:00:00.000Z] Single log entry'];
      const formatted = formatSmtpLogs(logs);
      expect(formatted).toBe(logs[0]);
    });
  });

  describe('getEncryptionDisplayName', () => {
    it('should return correct display names for encryption types', () => {
      expect(getEncryptionDisplayName('none')).toBe('None (Plain)');
      expect(getEncryptionDisplayName('tls')).toBe('TLS (STARTTLS)');
      expect(getEncryptionDisplayName('ssl')).toBe('SSL/TLS');
    });

    it('should return the input for unknown encryption types', () => {
      expect(getEncryptionDisplayName('unknown')).toBe('unknown');
      expect(getEncryptionDisplayName('')).toBe('');
    });
  });

  describe('getDefaultPortForEncryption', () => {
    it('should return correct default ports for encryption types', () => {
      expect(getDefaultPortForEncryption('none')).toBe(25);
      expect(getDefaultPortForEncryption('tls')).toBe(587);
      expect(getDefaultPortForEncryption('ssl')).toBe(465);
    });

    it('should return default TLS port for unknown encryption types', () => {
      expect(getDefaultPortForEncryption('unknown')).toBe(587);
      expect(getDefaultPortForEncryption('')).toBe(587);
    });
  });

  describe('maskPassword', () => {
    it('should mask passwords correctly', () => {
      expect(maskPassword('password123')).toBe('pa*******23');
      expect(maskPassword('secret')).toBe('se**et');
      expect(maskPassword('test')).toBe('****');
      expect(maskPassword('ab')).toBe('**');
      expect(maskPassword('a')).toBe('*');
      expect(maskPassword('')).toBe('');
    });

    it('should handle short passwords', () => {
      expect(maskPassword('abc')).toBe('***');
      expect(maskPassword('abcd')).toBe('****');
    });

    it('should handle long passwords', () => {
      const longPassword = 'verylongpasswordwithmanycharacters';
      const masked = maskPassword(longPassword);
      expect(masked.startsWith('ve')).toBe(true);
      expect(masked.endsWith('rs')).toBe(true);
      expect(masked.includes('*')).toBe(true);
      expect(masked.length).toBe(longPassword.length);
    });
  });

  describe('formatTestDuration', () => {
    it('should format test duration correctly', () => {
      const startTime = new Date('2025-01-01T12:00:00.000Z');
      const endTime = new Date('2025-01-01T12:00:01.500Z');

      const formatted = formatTestDuration(startTime, endTime);
      expect(formatted).toBe('1500ms');
    });

    it('should handle zero duration', () => {
      const time = new Date('2025-01-01T12:00:00.000Z');
      const formatted = formatTestDuration(time, time);
      expect(formatted).toBe('0ms');
    });

    it('should handle millisecond precision', () => {
      const startTime = new Date('2025-01-01T12:00:00.123Z');
      const endTime = new Date('2025-01-01T12:00:00.456Z');

      const formatted = formatTestDuration(startTime, endTime);
      expect(formatted).toBe('333ms');
    });
  });

  describe('createTestEmailContent', () => {
    const testAddress = '<EMAIL>';

    it('should create proper email content structure', () => {
      const content = createTestEmailContent(testAddress);

      expect(content).toHaveProperty('subject');
      expect(content).toHaveProperty('text');
      expect(content).toHaveProperty('html');
    });

    it('should include test address in all content types', () => {
      const content = createTestEmailContent(testAddress);

      expect(content.subject).toContain('SMTP Test from VanishPost');
      expect(content.text).toContain(testAddress);
      expect(content.html).toContain(testAddress);
    });

    it('should include timestamp in subject', () => {
      const content = createTestEmailContent(testAddress);

      // Subject should contain ISO timestamp
      expect(content.subject).toMatch(/SMTP Test from VanishPost - \d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);
    });

    it('should include VanishPost branding', () => {
      const content = createTestEmailContent(testAddress);

      expect(content.text).toContain('VanishPost Email Tools');
      expect(content.text).toContain('https://vanishpost.com/tools/smtp-tester');
      expect(content.html).toContain('VanishPost Email Tools');
      expect(content.html).toContain('https://vanishpost.com/tools/smtp-tester');
    });

    it('should create valid HTML content', () => {
      const content = createTestEmailContent(testAddress);

      expect(content.html).toContain('<!DOCTYPE html>');
      expect(content.html).toContain('<html>');
      expect(content.html).toContain('</html>');
      expect(content.html).toContain('<body');
      expect(content.html).toContain('</body>');
    });

    it('should include test details in content', () => {
      const content = createTestEmailContent(testAddress);

      expect(content.text).toContain('Test Details:');
      expect(content.text).toContain('Test Address:');
      expect(content.text).toContain('Timestamp:');
      expect(content.text).toContain('Purpose:');

      expect(content.html).toContain('Test Details:');
      expect(content.html).toContain('Test Address:');
      expect(content.html).toContain('Timestamp:');
      expect(content.html).toContain('Purpose:');
    });

    it('should explain the purpose of the test', () => {
      const content = createTestEmailContent(testAddress);

      expect(content.text).toContain('SMTP connectivity and email authentication testing');
      expect(content.html).toContain('SMTP connectivity and email authentication testing');
    });

    it('should have consistent content between text and HTML', () => {
      const content = createTestEmailContent(testAddress);

      // Both should contain the test address
      expect(content.text).toContain(testAddress);
      expect(content.html).toContain(testAddress);

      // Both should contain VanishPost URL
      expect(content.text).toContain('vanishpost.com/tools/smtp-tester');
      expect(content.html).toContain('vanishpost.com/tools/smtp-tester');
    });
  });
});
