/**
 * API Route for Email Analysis
 *
 * This route analyzes an email for deliverability issues and provides recommendations.
 *
 * POST /api/tools/email-tester/analyze
 */

import { NextRequest, NextResponse } from 'next/server';
import { getEmailTesterEmail, storeEmailTesterResults } from '@/lib/tools/email-tester/database';
import { analyzeEmailAuthentication, parseAuthenticationHeaders, extractSenderDomain, extractSenderIp } from '@/lib/tools/email-tester/headerParser';
import { analyzeEmailDeliverability } from '@/lib/tools/email-tester/deepseekService';
import { getOrCreateSession } from '@/lib/tools/email-tester/sessionManager';
import { logError } from '@/lib/logging';

export async function POST(request: NextRequest) {
  try {
    const { testAddress, testAddressId } = await request.json();

    console.log('Received request for test address:', testAddress, 'with ID:', testAddressId);

    // Get or create session for access control
    const sessionId = await getOrCreateSession();
    console.log('Session ID:', sessionId);

    if (!testAddress) {
      return NextResponse.json(
        { success: false, message: 'Test address is required' },
        { status: 400 }
      );
    }

    // Get the email from Guerrilla database
    console.log('Fetching email from Guerrilla database...');
    const email = await getEmailTesterEmail(testAddress);

    if (!email) {
      console.log('No email found for test address:', testAddress);
      return NextResponse.json(
        {
          success: false,
          message: 'No email found for this test address',
          status: 'waiting', // Indicates this is expected polling state
          polling: true // Flag to indicate this is a polling response
        },
        { status: 200 } // Changed from 404 to 200 - this is expected during polling
      );
    }

    console.log('Email found:', email.mail_id);

    // Use enhanced Mailauth-based analysis
    const rawEmail = email.mail;
    console.log('Analyzing email authentication with Mailauth...');

    let authResults;
    try {
      // Try enhanced Mailauth analysis first
      authResults = await analyzeEmailAuthentication(
        rawEmail,
        email.clientIp, // Use converted IP from database
        email.return_path
      );
      console.log('Enhanced auth results:', JSON.stringify(authResults, null, 2));
    } catch (mailauthError) {
      console.warn('Mailauth analysis failed, falling back to legacy parsing:', mailauthError);
      // Fallback to legacy parsing
      authResults = parseAuthenticationHeaders(rawEmail);
      console.log('Fallback auth results:', JSON.stringify(authResults));
    }

    // Extract sender domain (use enhanced result or fallback)
    const senderDomain = authResults.fromDomain || extractSenderDomain(rawEmail);
    console.log('Sender domain:', senderDomain);

    // Use enhanced IP or fallback
    const senderIp = authResults.ipAddress || email.clientIp || extractSenderIp(rawEmail);
    console.log('Sender IP:', senderIp);

    console.log('Analyzing email with Deepseek AI...');
    // Analyze the email with Deepseek AI using enhanced data
    const analysisResult = await analyzeEmailDeliverability({
      headers: rawEmail,
      authResults,
      senderDomain,
      senderIp,
      spamScore: email.spam_score || 0
    });

    console.log('Analysis complete, storing results...');

    // Extract reverse DNS hostname for database storage (legacy field)
    const reverseDnsHostname = authResults.reverseDns?.hostname ||
                              authResults.reverseDns?.ipAddress ||
                              '';

    // Create enhanced analysis data that includes all authentication results
    const enhancedAnalysisData = {
      ...analysisResult,
      // Include individual auth results at top level for easier UI access
      spf: authResults.spf,
      dkim: authResults.dkim,
      dmarc: authResults.dmarc,
      arc: authResults.arc,
      bimi: authResults.bimi,
      mx: authResults.mx, // Include MX at top level
      reverseDns: authResults.reverseDns, // Include full reverse DNS object in JSON
      enhancedAuthResults: authResults, // Include enhanced auth results
      mailauthData: authResults.mailauth // Include raw Mailauth data for future analysis
    };

    // Store the results in Supabase with enhanced data
    const resultId = await storeEmailTesterResults(
      testAddressId,
      email.mail_id,
      senderDomain,
      senderIp,
      authResults.spf.result,
      authResults.dkim.result,
      authResults.dmarc.result,
      reverseDnsHostname, // Pass the hostname string for legacy database field
      email.spam_score || 0,
      analysisResult.overallScore,
      rawEmail, // Store the full raw email
      enhancedAnalysisData,
      sessionId // Add session ID for access control
    );

    console.log('Results stored with ID:', resultId);
    return NextResponse.json({
      success: true,
      resultId,
      analysis: enhancedAnalysisData // Return enhanced data instead of just Deepseek result
    });
  } catch (error) {
    console.error('Error analyzing email deliverability:', error);
    logError('API', 'Failed to analyze email deliverability', error);
    return NextResponse.json(
      { success: false, message: `Failed to analyze email deliverability: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
