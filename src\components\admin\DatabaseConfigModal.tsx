'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, <PERSON>alogTitle, DialogFooter, DialogDescription } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Textarea } from '@/components/ui/Textarea';
import { Checkbox } from '@/components/ui/Checkbox';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { Spinner } from '@/components/ui/Spinner';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import { DatabaseConfig, DatabaseTestResult } from '@/lib/types/databaseConfig';
import { cn } from '@/lib/utils';


interface ConfigurationModalProps {
  config: DatabaseConfig | null;
  onClose: (refreshNeeded: boolean) => void;
}

export default function ConfigurationModal({ config, onClose }: ConfigurationModalProps) {
  const [formData, setFormData] = useState<DatabaseConfig>({
    name: '',
    description: '',
    isActive: false,
    guerrillaConfig: {
      host: '',
      port: 3306,
      database: '',
      user: '',
      password: '',
      connectionLimit: 10,
      ssl: false
    },
    supabaseConfig: {
      url: '',
      apiKey: '',
      serviceRoleKey: ''
    }
  });

  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testResult, setTestResult] = useState<DatabaseTestResult | null>(null);
  const [activeTab, setActiveTab] = useState('general');

  // Initialize form data when config changes
  useEffect(() => {
    if (config) {
      setFormData(config);
    }
  }, [config]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('guerrilla.')) {
      const field = name.split('.')[1];
      setFormData({
        ...formData,
        guerrillaConfig: {
          ...formData.guerrillaConfig,
          [field]: field === 'port' || field === 'connectionLimit' ? parseInt(value) : value
        }
      });
    } else if (name.startsWith('supabase.')) {
      const field = name.split('.')[1];
      setFormData({
        ...formData,
        supabaseConfig: {
          ...formData.supabaseConfig,
          [field]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    if (name.startsWith('guerrilla.')) {
      const field = name.split('.')[1];
      setFormData({
        ...formData,
        guerrillaConfig: {
          ...formData.guerrillaConfig,
          [field]: checked
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: checked
      });
    }
  };

  // Test the configuration
  const handleTestConnection = async () => {
    try {
      setTestLoading(true);
      setTestResult(null);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/database-config/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error(`Failed to test connection: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      setTestResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while testing connection');
      console.error('Error testing connection:', err);
    } finally {
      setTestLoading(false);
    }
  };

  // Save the configuration
  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate form data
      if (!formData.name) {
        setError('Name is required');
        return;
      }

      if (!formData.guerrillaConfig.host || !formData.guerrillaConfig.database || !formData.guerrillaConfig.user) {
        setError('Guerrilla database configuration is incomplete');
        setActiveTab('guerrilla');
        return;
      }

      if (!formData.supabaseConfig.url || !formData.supabaseConfig.apiKey) {
        setError('Supabase configuration is incomplete');
        setActiveTab('supabase');
        return;
      }

      // Create or update the configuration
      const url = '/api/management-portal-x7z9y2/database-config';
      const method = config ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (result.success) {
        onClose(true);
      } else {
        setError(result.error || 'Failed to save configuration');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while saving configuration');
      console.error('Error saving configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={() => onClose(false)}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[650px] p-0 overflow-hidden">
        <DialogHeader className="px-6 pt-6 pb-2">
          <DialogTitle className="text-xl font-bold flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
            </svg>
            {config ? 'Edit Configuration' : 'Create Configuration'}
          </DialogTitle>
          <DialogDescription className="text-gray-500 mt-1">
            Configure database connections for both Guerrilla Mail and Supabase.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-2">
          <TabsList className="px-6 border-b border-gray-200">
            <TabsTrigger
              value="general"
              className={cn(
                "flex items-center gap-2 px-4 py-3 border-b-2 font-medium text-sm transition-colors",
                activeTab === "general"
                  ? "border-indigo-600 text-indigo-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
              General
            </TabsTrigger>
            <TabsTrigger
              value="guerrilla"
              className={cn(
                "flex items-center gap-2 px-4 py-3 border-b-2 font-medium text-sm transition-colors",
                activeTab === "guerrilla"
                  ? "border-indigo-600 text-indigo-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path d="M3 12v3c0 1.657 3.134 3 7 3s7-1.343 7-3v-3c0 1.657-3.134 3-7 3s-7-1.343-7-3z" />
                <path d="M3 7v3c0 1.657 3.134 3 7 3s7-1.343 7-3V7c0 1.657-3.134 3-7 3S3 8.657 3 7z" />
                <path d="M17 5c0 1.657-3.134 3-7 3S3 6.657 3 5s3.134-3 7-3 7 1.343 7 3z" />
              </svg>
              Guerrilla DB
            </TabsTrigger>
            <TabsTrigger
              value="supabase"
              className={cn(
                "flex items-center gap-2 px-4 py-3 border-b-2 font-medium text-sm transition-colors",
                activeTab === "supabase"
                  ? "border-indigo-600 text-indigo-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
              </svg>
              Supabase
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="px-6 py-4">
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Configuration Information
              </h3>
              <p className="text-xs text-gray-600">
                Give your configuration a descriptive name and optional details. You can create multiple configurations and switch between them.
              </p>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-5">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-medium text-gray-700">Configuration Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="e.g., Production Database"
                    required
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description" className="text-sm font-medium text-gray-700">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description || ''}
                    onChange={handleChange}
                    placeholder="Add details about this configuration"
                    rows={3}
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>

                <div className="flex items-center space-x-2 pt-2">
                  <Checkbox
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleCheckboxChange('isActive', checked === true)}
                    className="text-indigo-600 focus:ring-indigo-500"
                  />
                  <Label htmlFor="isActive" className="text-sm font-medium text-gray-700">
                    Set as active configuration
                  </Label>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="guerrilla" className="px-6 py-4">
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Guerrilla Mail Database Connection
              </h3>
              <p className="text-xs text-gray-600">
                Configure the connection to your Guerrilla Mail MySQL database. This database stores temporary email addresses and messages.
              </p>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-5">
                <div className="space-y-2">
                  <Label htmlFor="guerrilla.host" className="text-sm font-medium text-gray-700 flex items-center">
                    Host
                    <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="guerrilla.host"
                    name="guerrilla.host"
                    value={formData.guerrillaConfig.host}
                    onChange={handleChange}
                    placeholder="e.g., *************"
                    required
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="guerrilla.port" className="text-sm font-medium text-gray-700 flex items-center">
                    Port
                    <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="guerrilla.port"
                    name="guerrilla.port"
                    type="number"
                    value={formData.guerrillaConfig.port}
                    onChange={handleChange}
                    placeholder="e.g., 3306"
                    required
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-5">
                <div className="space-y-2">
                  <Label htmlFor="guerrilla.database" className="text-sm font-medium text-gray-700 flex items-center">
                    Database Name
                    <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="guerrilla.database"
                    name="guerrilla.database"
                    value={formData.guerrillaConfig.database}
                    onChange={handleChange}
                    placeholder="e.g., guerrilla_db"
                    required
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="guerrilla.connectionLimit" className="text-sm font-medium text-gray-700">
                    Connection Limit
                  </Label>
                  <Input
                    id="guerrilla.connectionLimit"
                    name="guerrilla.connectionLimit"
                    type="number"
                    value={formData.guerrillaConfig.connectionLimit}
                    onChange={handleChange}
                    placeholder="e.g., 10"
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Maximum number of connections to create at once
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-5">
                <div className="space-y-2">
                  <Label htmlFor="guerrilla.user" className="text-sm font-medium text-gray-700 flex items-center">
                    Username
                    <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="guerrilla.user"
                    name="guerrilla.user"
                    value={formData.guerrillaConfig.user}
                    onChange={handleChange}
                    placeholder="e.g., guerrilla_private"
                    required
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="guerrilla.password" className="text-sm font-medium text-gray-700 flex items-center">
                    Password
                    <span className="text-red-500 ml-1">*</span>
                  </Label>
                  <Input
                    id="guerrilla.password"
                    name="guerrilla.password"
                    type="password"
                    value={formData.guerrillaConfig.password}
                    onChange={handleChange}
                    placeholder="Database password"
                    required
                    className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2 pt-2">
                <Checkbox
                  id="guerrilla.ssl"
                  checked={formData.guerrillaConfig.ssl || false}
                  onCheckedChange={(checked) => handleCheckboxChange('guerrilla.ssl', checked === true)}
                  className="text-indigo-600 focus:ring-indigo-500"
                />
                <Label htmlFor="guerrilla.ssl" className="text-sm font-medium text-gray-700">
                  Use SSL connection
                </Label>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="supabase" className="px-6 py-4">
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Supabase Configuration
              </h3>
              <p className="text-xs text-gray-600">
                Configure your Supabase project connection. Supabase is used for storing application data, configurations, and analytics.
              </p>
            </div>

            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="supabase.url" className="text-sm font-medium text-gray-700 flex items-center">
                  Supabase URL
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="supabase.url"
                  name="supabase.url"
                  value={formData.supabaseConfig.url}
                  onChange={handleChange}
                  placeholder="https://your-project.supabase.co"
                  required
                  className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  The URL of your Supabase project (found in the Supabase dashboard)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="supabase.apiKey" className="text-sm font-medium text-gray-700 flex items-center">
                  API Key (Anon)
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="supabase.apiKey"
                  name="supabase.apiKey"
                  value={formData.supabaseConfig.apiKey}
                  onChange={handleChange}
                  placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  required
                  className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 font-mono text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">
                  The public API key (anon key) used for client-side operations
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="supabase.serviceRoleKey" className="text-sm font-medium text-gray-700">
                  Service Role Key (Optional)
                </Label>
                <Input
                  id="supabase.serviceRoleKey"
                  name="supabase.serviceRoleKey"
                  type="password"
                  value={formData.supabaseConfig.serviceRoleKey || ''}
                  onChange={handleChange}
                  placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  className="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 font-mono text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">
                  The secret key used for server-side operations with elevated privileges. Only provide this if needed for specific operations.
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {error && (
          <Alert variant="error" className="mx-6 mt-4">
            <AlertDescription className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {testResult && (
          <div className="mx-6 mt-4 border rounded-lg overflow-hidden shadow-sm">
            <div className={`p-4 ${testResult.success ? 'bg-green-50 border-b border-green-100' : 'bg-red-50 border-b border-red-100'}`}>
              <h3 className={`text-sm font-medium flex items-center ${testResult.success ? 'text-green-800' : 'text-red-800'}`}>
                {testResult.success ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
                Connection Test Result: {testResult.success ? 'Success' : 'Failed'}
              </h3>
            </div>
            <div className="p-4 space-y-3 text-sm bg-white">
              <div className="flex items-start">
                <div className={`flex-shrink-0 w-5 h-5 mt-0.5 mr-3 rounded-full flex items-center justify-center ${
                  testResult.guerrilla.success ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                }`}>
                  {testResult.guerrilla.success ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <div className="flex-1">
                  <div className="font-medium">Guerrilla DB</div>
                  <div className={testResult.guerrilla.success ? 'text-green-600' : 'text-red-600'}>
                    {testResult.guerrilla.message}
                  </div>
                  {testResult.guerrilla.error && (
                    <div className="mt-1 p-2 bg-red-50 border border-red-100 rounded text-xs text-red-700">
                      {testResult.guerrilla.error}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-start">
                <div className={`flex-shrink-0 w-5 h-5 mt-0.5 mr-3 rounded-full flex items-center justify-center ${
                  testResult.supabase.success ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                }`}>
                  {testResult.supabase.success ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <div className="flex-1">
                  <div className="font-medium">Supabase</div>
                  <div className={testResult.supabase.success ? 'text-green-600' : 'text-red-600'}>
                    {testResult.supabase.message}
                  </div>
                  {testResult.supabase.error && (
                    <div className="mt-1 p-2 bg-red-50 border border-red-100 rounded text-xs text-red-700">
                      {testResult.supabase.error}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        <DialogFooter className="px-6 py-4 bg-gray-50 border-t border-gray-200 mt-6">
          <div className="flex justify-between items-center w-full">
            <Button
              type="button"
              variant="outline"
              onClick={handleTestConnection}
              disabled={loading || testLoading}
              className="border-indigo-300 text-indigo-700 hover:bg-indigo-50 hover:text-indigo-800"
            >
              {testLoading ? (
                <>
                  <Spinner className="mr-2 h-4 w-4 text-indigo-600" />
                  Testing Connection...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  Test Connection
                </>
              )}
            </Button>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => onClose(false)}
                disabled={loading}
                className="border-gray-300"
              >
                Cancel
              </Button>

              <Button
                type="button"
                onClick={handleSave}
                disabled={loading}
                className="bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500"
              >
                {loading ? (
                  <>
                    <Spinner className="mr-2 h-4 w-4" />
                    Saving...
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Save Configuration
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
