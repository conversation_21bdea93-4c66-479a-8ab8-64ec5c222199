/**
 * <PERSON><PERSON><PERSON> to insert an email address into the local database
 * 
 * Usage: node scripts/insert-email-address.js <EMAIL>
 */
const mysql = require('mysql2/promise');

// Local database configuration
const localDbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'tempmail_web_user',
  password: process.env.DB_PASSWORD || 'yiWTpFChtFW3neWCS',
  database: process.env.DB_NAME || 'tempmail_web',
};

async function insertEmailAddress() {
  // Get the email address from command line arguments
  const emailAddress = process.argv[2];
  
  if (!emailAddress) {
    console.error('Please provide an email address');
    console.error('Usage: node scripts/insert-email-address.js <EMAIL>');
    process.exit(1);
  }
  
  console.log(`Inserting email address: ${emailAddress}`);
  
  let connection;
  try {
    // Create connection
    connection = await mysql.createConnection(localDbConfig);
    
    // Check if the email address already exists
    const [existingRows] = await connection.execute(
      'SELECT * FROM TempEmail WHERE emailAddress = ?',
      [emailAddress]
    );
    
    if (existingRows.length > 0) {
      console.log('✅ Email address already exists in the database');
      console.log('Details:');
      console.log(`  - ID: ${existingRows[0].id}`);
      console.log(`  - Email Address: ${existingRows[0].emailAddress}`);
      console.log(`  - Creation Time: ${existingRows[0].creationTime}`);
      console.log(`  - Expiration Date: ${existingRows[0].expirationDate}`);
      return;
    }
    
    // Set expiration date (24 hours from now)
    const expirationDate = new Date();
    expirationDate.setHours(expirationDate.getHours() + 24);
    
    // Insert the email address
    const [result] = await connection.execute(
      'INSERT INTO TempEmail (emailAddress, expirationDate) VALUES (?, ?)',
      [emailAddress, expirationDate]
    );
    
    console.log('✅ Email address inserted successfully');
    console.log(`  - ID: ${result.insertId}`);
    
    // Get the inserted row
    const [rows] = await connection.execute(
      'SELECT * FROM TempEmail WHERE id = ?',
      [result.insertId]
    );
    
    console.log('Details:');
    console.log(`  - ID: ${rows[0].id}`);
    console.log(`  - Email Address: ${rows[0].emailAddress}`);
    console.log(`  - Creation Time: ${rows[0].creationTime}`);
    console.log(`  - Expiration Date: ${rows[0].expirationDate}`);
    
  } catch (error) {
    console.error('Error inserting email address:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the function
insertEmailAddress().catch(console.error);
