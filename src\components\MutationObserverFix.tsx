'use client';

import { useEffect } from 'react';

/**
 * MutationObserverFix Component
 * 
 * This component injects a script to fix the MutationObserver error in web-client-content-script.js
 * It specifically targets the fo function pattern described in console_error.md
 */
export default function MutationObserverFix() {
  useEffect(() => {
    // Only run in the browser
    if (typeof window === 'undefined') return;

    // Create a script to fix the MutationObserver error
    const fixScript = document.createElement('script');
    fixScript.id = 'mutation-observer-fix';
    fixScript.textContent = `
      (function() {
        // Fix for the fo function pattern in web-client-content-script.js
        // This directly implements the fix suggested in console_error.md
        
        // Store original functions we need to patch
        const originalAddEventListener = HTMLIFrameElement.prototype.addEventListener;
        
        // Create a patched version of addEventListener for iframes
        HTMLIFrameElement.prototype.addEventListener = function(type, listener, options) {
          // Only intercept 'load' events
          if (type === 'load') {
            // Create a wrapped listener that implements the fix
            const patchedListener = function(event) {
              // Call the original listener
              const result = listener.call(this, event);
              
              // After the original listener runs, check if we need to apply our fix
              setTimeout(() => {
                try {
                  // Check if this is the iframe from web-client-content-script.js
                  // by looking for patterns in the listener function
                  const listenerStr = listener.toString();
                  
                  // If this looks like the fo function from web-client-content-script.js
                  if (listenerStr.includes('MutationObserver') && 
                      listenerStr.includes('contentDocument') && 
                      listenerStr.includes('observe')) {
                    
                    // Apply our fix by patching MutationObserver in the iframe's window
                    if (this.contentWindow && this.contentWindow.MutationObserver) {
                      const originalObserve = this.contentWindow.MutationObserver.prototype.observe;
                      
                      // Replace the observe method with a safer version
                      this.contentWindow.MutationObserver.prototype.observe = function(target, options) {
                        // Check if target is a valid Node before calling the original method
                        if (!target || !(target instanceof Node)) {
                          console.warn('Prevented MutationObserver error: target is not a Node');
                          return;
                        }
                        
                        // Call the original observe method with the valid target
                        return originalObserve.call(this, target, options);
                      };
                    }
                    
                    // Also patch the specific fo function pattern if we can find it
                    // This directly implements the fix from console_error.md
                    if (this.contentWindow && typeof this.contentWindow.fo === 'function') {
                      const originalFo = this.contentWindow.fo;
                      
                      this.contentWindow.fo = function(e) {
                        return new Promise(function(t) {
                          if (e) {
                            e.addEventListener('load', function() {
                              var n = e.contentDocument;
                              // Add the safety check from console_error.md
                              if (n) {
                                var r = new MutationObserver(function() {
                                  if (n.readyState === 'complete') {
                                    r.disconnect();
                                    t(n);
                                  }
                                });
                                r.observe(n, { childList: true, subtree: true });
                              } else {
                                // Handle the case where contentDocument is not available
                                console.warn('iframe contentDocument not available');
                                t(null);
                              }
                            });
                          } else {
                            t(null);
                          }
                        });
                      };
                    }
                  }
                } catch (error) {
                  // Silently handle any errors in our patch
                }
              }, 0);
              
              return result;
            };
            
            // Call the original addEventListener with our patched listener
            return originalAddEventListener.call(this, type, patchedListener, options);
          }
          
          // For all other event types, call the original method
          return originalAddEventListener.call(this, type, listener, options);
        };
        
        console.log('MutationObserver fix applied');
      })();
    `;
    
    // Inject the script into the page
    document.head.appendChild(fixScript);
    
    // Clean up when the component unmounts
    return () => {
      const script = document.getElementById('mutation-observer-fix');
      if (script) {
        document.head.removeChild(script);
      }
    };
  }, []);
  
  // This component doesn't render anything
  return null;
}
