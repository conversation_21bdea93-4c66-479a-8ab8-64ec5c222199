'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>min<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>min<PERSON>ard<PERSON>ontent, AdminCardTitle } from '@/components/admin/ui/AdminCard';
import {
  Shield,
  Activity,
  AlertTriangle,
  Lock,
  Zap,
  Settings,
  ShieldCheck,
  CheckCircle,
  BarChart3,
  Eye,
  RefreshCw
} from 'lucide-react';

interface SecurityStat {
  value: number;
  change: string;
  changeType: 'increase' | 'decrease' | 'neutral';
}

interface SecurityStats {
  blockedIPs: SecurityStat;
  blockedIPRanges: SecurityStat;
  blockedSessions: SecurityStat;
  securityScore: SecurityStat;
}

/**
 * Security Overview Component
 * Displays security features and navigation for the admin portal
 */
export default function SecurityOverview() {
  const [stats, setStats] = useState<SecurityStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch security statistics
  const fetchSecurityStats = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/security/stats', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch security stats: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch security stats');
      }

      console.log('Security stats loaded:', result.data);
      setStats(result.data);
    } catch (err) {
      console.error('Error fetching security stats:', err);
      setError(err instanceof Error ? err.message : 'Failed to load security statistics');
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount and set up auto-refresh
  useEffect(() => {
    fetchSecurityStats();

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSecurityStats, 30000);

    return () => clearInterval(interval);
  }, []);

  // Format numbers for display
  const formatValue = (value: number, isPercentage: boolean = false) => {
    if (isPercentage) {
      return `${value}%`;
    }
    return value.toLocaleString();
  };

  const securityFeatures = [
    {
      icon: Shield,
      title: 'Security Dashboard',
      description: 'Monitor security metrics, threat levels, and system protection status',
      href: '/management-portal-x7z9y2/security',
      status: 'Active',
      statusColor: 'text-green-600 bg-green-100'
    },
    {
      icon: Activity,
      title: 'Live Session Monitor',
      description: 'Real-time monitoring of active sessions, IP rotation detection, and threat assessment',
      href: '/management-portal-x7z9y2/security/live-sessions',
      status: 'Active',
      statusColor: 'text-green-600 bg-green-100'
    },
    {
      icon: AlertTriangle,
      title: 'Emergency Controls',
      description: 'Immediate threat response, manual blocking controls, and emergency security actions',
      href: '/management-portal-x7z9y2/security/emergency-controls',
      status: 'Active',
      statusColor: 'text-green-600 bg-green-100'
    },
    {
      icon: Lock,
      title: 'IP Management',
      description: 'Manage IP blocking, whitelisting, and rate limit violations',
      href: '/management-portal-x7z9y2/ip-management',
      status: 'Active',
      statusColor: 'text-green-600 bg-green-100'
    },
    {
      icon: Zap,
      title: 'Rate Limit Config',
      description: 'Configure rate limiting rules, thresholds, and automated responses',
      href: '/management-portal-x7z9y2/rate-limit-config',
      status: 'Active',
      statusColor: 'text-green-600 bg-green-100'
    },
    {
      icon: BarChart3,
      title: 'Rate Limit Monitor',
      description: 'Real-time monitoring of rate limits and security violations',
      href: '/management-portal-x7z9y2/rate-limit-monitoring',
      status: 'Active',
      statusColor: 'text-green-600 bg-green-100'
    }
  ];

  // Security statistics for display
  const securityStats = stats ? [
    {
      label: 'Blocked IPs',
      value: formatValue(stats.blockedIPs.value),
      change: stats.blockedIPs.change,
      changeType: stats.blockedIPs.changeType,
      icon: Shield
    },
    {
      label: 'Blocked IP Ranges',
      value: formatValue(stats.blockedIPRanges.value),
      change: stats.blockedIPRanges.change,
      changeType: stats.blockedIPRanges.changeType,
      icon: Lock
    },
    {
      label: 'Blocked Sessions',
      value: formatValue(stats.blockedSessions.value),
      change: stats.blockedSessions.change,
      changeType: stats.blockedSessions.changeType,
      icon: AlertTriangle
    },
    {
      label: 'Security Score',
      value: formatValue(stats.securityScore.value, true),
      change: stats.securityScore.change,
      changeType: stats.securityScore.changeType,
      icon: ShieldCheck
    }
  ] : [];

  return (
    <div className="space-y-6">
      {/* Security Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {loading ? (
          // Loading state
          Array.from({ length: 4 }).map((_, index) => (
            <AdminCard key={index}>
              <AdminCardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded animate-pulse w-20"></div>
                  </div>
                  <div className="p-3 bg-gray-100 rounded-lg">
                    <RefreshCw className="h-6 w-6 text-gray-400 animate-spin" />
                  </div>
                </div>
              </AdminCardContent>
            </AdminCard>
          ))
        ) : error ? (
          // Error state
          <div className="col-span-full">
            <AdminCard>
              <AdminCardContent className="p-6">
                <div className="text-center">
                  <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Security Stats</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <button
                    onClick={fetchSecurityStats}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry
                  </button>
                </div>
              </AdminCardContent>
            </AdminCard>
          </div>
        ) : (
          // Data loaded successfully
          securityStats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <AdminCard key={index}>
                <AdminCardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      <p className={`text-sm ${
                        stat.changeType === 'increase' ? 'text-green-600' :
                        stat.changeType === 'decrease' ? 'text-red-600' :
                        'text-gray-500'
                      }`}>
                        {stat.changeType === 'neutral' ? stat.change : `${stat.change} from last week`}
                      </p>
                    </div>
                    <div className="p-3 bg-blue-100 rounded-lg">
                      <IconComponent className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                </AdminCardContent>
              </AdminCard>
            );
          })
        )}
      </div>

      {/* Security Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {securityFeatures.map((feature, index) => {
          const IconComponent = feature.icon;
          return (
            <AdminCard key={index} className="hover:shadow-lg transition-shadow duration-200">
              <AdminCardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <IconComponent className="h-5 w-5 text-blue-600" />
                    </div>
                    <AdminCardTitle className="text-lg">{feature.title}</AdminCardTitle>
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${feature.statusColor}`}>
                    {feature.status}
                  </span>
                </div>
              </AdminCardHeader>
              <AdminCardContent>
                <p className="text-gray-600 mb-4">{feature.description}</p>
                <Link
                  href={feature.href}
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
                >
                  Access {feature.title}
                  <Eye className="ml-1 h-4 w-4" />
                </Link>
              </AdminCardContent>
            </AdminCard>
          );
        })}
      </div>

      {/* Quick Actions */}
      <AdminCard>
        <AdminCardHeader>
          <AdminCardTitle className="flex items-center">
            <Settings className="mr-2 h-5 w-5" />
            Quick Security Actions
          </AdminCardTitle>
        </AdminCardHeader>
        <AdminCardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/management-portal-x7z9y2/security/emergency-controls"
              className="p-4 border border-red-200 rounded-lg hover:bg-red-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <div>
                  <h3 className="font-medium text-gray-900">Emergency Block</h3>
                  <p className="text-sm text-gray-600">Immediately block suspicious activity</p>
                </div>
              </div>
            </Link>
            
            <Link
              href="/management-portal-x7z9y2/ip-management"
              className="p-4 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <Shield className="h-5 w-5 text-blue-600" />
                <div>
                  <h3 className="font-medium text-gray-900">Manage IPs</h3>
                  <p className="text-sm text-gray-600">Add or remove IP restrictions</p>
                </div>
              </div>
            </Link>
            
            <Link
              href="/management-portal-x7z9y2/security/live-sessions"
              className="p-4 border border-green-200 rounded-lg hover:bg-green-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <Activity className="h-5 w-5 text-green-600" />
                <div>
                  <h3 className="font-medium text-gray-900">Monitor Sessions</h3>
                  <p className="text-sm text-gray-600">View real-time session activity</p>
                </div>
              </div>
            </Link>
          </div>
        </AdminCardContent>
      </AdminCard>
    </div>
  );
}
