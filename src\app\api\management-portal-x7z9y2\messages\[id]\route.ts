import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { logInfo, logError } from '@/lib/logging';
import { MessageStatus } from '@/lib/types/contact';

/**
 * API route for updating a message's status
 */
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Await the params object before accessing its properties
    const params = await context.params;
    const id = parseInt(params.id, 10);

    if (isNaN(id)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid message ID'
      }, { status: 400 });
    }

    const { status } = await request.json();
    if (!['unread', 'read', 'replied', 'archived'].includes(status)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid status'
      }, { status: 400 });
    }

    // Get Supabase client
    const supabase = await createServerSupabaseClient();

    // Update message status
    const { data, error } = await supabase
      .from('contact_messages')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select();

    if (error) {
      logError('messages', `Failed to update message ${id}`, { error });
      return NextResponse.json({
        success: false,
        message: 'Failed to update message'
      }, { status: 500 });
    }

    if (!data || data.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'Message not found'
      }, { status: 404 });
    }

    logInfo('messages', `Updated message ${id} status to ${status}`);

    return NextResponse.json({
      success: true,
      message: 'Message updated successfully'
    });
  } catch (error) {
    logError('messages', 'Error updating message', { error });
    return NextResponse.json({
      success: false,
      message: 'An error occurred while updating the message'
    }, { status: 500 });
  }
}
