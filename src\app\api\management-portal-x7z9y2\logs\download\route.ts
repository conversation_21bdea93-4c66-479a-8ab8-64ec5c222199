/**
 * API route for downloading logs
 */
import { NextRequest, NextResponse } from 'next/server';
import { getLogs } from '@/lib/logging/dbLogger';
import { verifyAdminAuth } from '@/lib/auth/adminAuth';
import { logger } from '@/lib/logging/Logger';

/**
 * GET /api/management-portal-x7z9y2/logs/download
 *
 * Download logs as CSV or JSON
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const level = searchParams.get('level');
    const category = searchParams.get('category');
    const format = searchParams.get('format') || 'json'; // Default to JSON
    const limit = parseInt(searchParams.get('limit') || '1000', 10); // Default to 1000 logs

    // Parse date filters if provided
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (searchParams.has('startDate')) {
      startDate = new Date(searchParams.get('startDate')!);
    }

    if (searchParams.has('endDate')) {
      endDate = new Date(searchParams.get('endDate')!);
    }

    // Get logs with filters
    const logs = await getLogs({
      level: level || undefined,
      category: category || undefined,
      startDate,
      endDate,
      limit,
      offset: 0
    });

    await logger.info('LOGS_DOWNLOAD', `Admin downloaded ${logs.length} logs in ${format} format`);

    // Format logs based on requested format
    if (format === 'csv') {
      // Convert logs to CSV
      const headers = ['id', 'timestamp', 'level', 'category', 'message', 'metadata'];
      const csvRows = [
        // Headers row
        headers.join(','),
        // Data rows
        ...logs.map(log => {
          return [
            log.id,
            log.timestamp,
            log.level,
            log.category,
            // Escape quotes in message
            `"${(log.message || '').replace(/"/g, '""')}"`,
            // Convert metadata to string and escape quotes
            log.metadata ? `"${JSON.stringify(log.metadata).replace(/"/g, '""')}"` : ''
          ].join(',');
        })
      ];

      const csvContent = csvRows.join('\n');

      // Return CSV response
      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="fademail_logs_${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    } else {
      // Return JSON response
      return new NextResponse(JSON.stringify(logs, null, 2), {
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="fademail_logs_${new Date().toISOString().split('T')[0]}.json"`
        }
      });
    }
  } catch (error) {
    await logger.error('LOGS_DOWNLOAD', `Error downloading logs: ${error instanceof Error ? error.message : String(error)}`, { error });

    return NextResponse.json(
      { success: false, error: 'Failed to download logs' },
      { status: 500 }
    );
  }
}
