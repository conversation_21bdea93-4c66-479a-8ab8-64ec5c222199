/**
 * Session Management for Email Tester Tool
 * Handles user session tracking for access control
 */

import { cookies } from 'next/headers';
import { generateUUID } from '@/lib/utils/uuid';

const SESSION_COOKIE_NAME = 'email_tester_session';
const SESSION_DURATION = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

export interface EmailTesterSession {
  sessionId: string;
  createdAt: Date;
  expiresAt: Date;
}

/**
 * Get or create a session for the current user
 */
export async function getOrCreateSession(): Promise<string> {
  const cookieStore = await cookies();
  const existingSessionId = cookieStore.get(SESSION_COOKIE_NAME)?.value;

  if (existingSessionId && isValidSessionId(existingSessionId)) {
    // Extend session expiry
    cookieStore.set(SESSION_COOKIE_NAME, existingSessionId, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: SESSION_DURATION / 1000, // Convert to seconds
      path: '/'
    });
    return existingSessionId;
  }

  // Create new session
  const newSessionId = generateUUID();
  cookieStore.set(SESSION_COOKIE_NAME, newSessionId, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: SESSION_DURATION / 1000, // Convert to seconds
    path: '/'
  });

  return newSessionId;
}

/**
 * Get the current session ID without creating a new one
 */
export async function getCurrentSessionId(): Promise<string | null> {
  const cookieStore = await cookies();
  const sessionId = cookieStore.get(SESSION_COOKIE_NAME)?.value;

  if (sessionId && isValidSessionId(sessionId)) {
    return sessionId;
  }

  return null;
}

/**
 * Validate session ID format
 */
function isValidSessionId(sessionId: string): boolean {
  // UUID v4 format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(sessionId);
}

/**
 * Clear the current session
 */
export async function clearSession(): Promise<void> {
  const cookieStore = await cookies();
  cookieStore.delete(SESSION_COOKIE_NAME);
}

/**
 * Client-side session management utilities
 */
export const clientSessionManager = {
  /**
   * Get session ID from client-side (for API calls)
   */
  getSessionId(): string | null {
    if (typeof window === 'undefined') return null;

    // Try to get from localStorage first (fallback)
    const localStorageSessionId = localStorage.getItem('email_tester_session_id');
    if (localStorageSessionId && isValidSessionId(localStorageSessionId)) {
      return localStorageSessionId;
    }

    // Generate new session ID if none exists
    const newSessionId = generateUUID();
    localStorage.setItem('email_tester_session_id', newSessionId);
    return newSessionId;
  },

  /**
   * Set session ID on client-side
   */
  setSessionId(sessionId: string): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem('email_tester_session_id', sessionId);
  },

  /**
   * Clear session ID on client-side
   */
  clearSessionId(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem('email_tester_session_id');
  }
};

/**
 * Generate a shareable token for a specific test result
 */
export function generateShareToken(testId: string, sessionId: string): string {
  // Simple token generation - in production, consider using JWT or more secure method
  const timestamp = Date.now();
  const tokenData = `${testId}:${sessionId}:${timestamp}`;
  return Buffer.from(tokenData).toString('base64url');
}

/**
 * Validate and parse a share token
 */
export function parseShareToken(token: string): { testId: string; sessionId: string; timestamp: number } | null {
  try {
    const decoded = Buffer.from(token, 'base64url').toString();
    const [testId, sessionId, timestampStr] = decoded.split(':');
    const timestamp = parseInt(timestampStr, 10);

    if (!testId || !sessionId || !timestamp || !isValidSessionId(sessionId)) {
      return null;
    }

    // Check if token is not older than 7 days
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
    if (Date.now() - timestamp > maxAge) {
      return null;
    }

    return { testId, sessionId, timestamp };
  } catch {
    return null;
  }
}
