/**
 * Email Generator Analytics Service for VanishPost
 * 
 * This service handles tracking of all email generator related events
 * and integrates with both PostHog and our internal analytics system.
 */

import { createServerSupabaseClient } from '@/lib/supabase';
import { updateSessionMetrics, getSessionDuration } from './clientSessionManager';
import { trackEvent as trackPostHogEvent } from './posthog';
import { logError, logInfo } from '@/lib/logging';

export interface AnalyticsEventData {
  sessionId: string;
  userId?: string;
  eventType: string;
  pagePath?: string;
  referrer?: string;
  country?: string;
  browser?: string;
  deviceType?: string;
  additionalData?: Record<string, any>;
}

/**
 * Store an analytics event in the database
 */
async function storeAnalyticsEvent(eventData: AnalyticsEventData): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();

    // Get session start time and duration for the event
    const sessionDuration = await getSessionDuration(eventData.sessionId);

    const analyticsRecord = {
      event_type: eventData.eventType,
      session_id: eventData.sessionId,
      user_id: eventData.userId || null,
      session_start_time: new Date().toISOString(), // This should be the actual session start time
      session_duration: sessionDuration,
      page_path: eventData.pagePath || '/',
      referrer: eventData.referrer || null,
      country: eventData.country || 'unknown',
      browser: eventData.browser || 'unknown',
      device_type: eventData.deviceType || 'unknown',
      timestamp: new Date().toISOString(),
      additional_data: eventData.additionalData || {},
    };

    const { error } = await supabase
      .from('analytics_events')
      .insert(analyticsRecord);

    if (error) {
      logError('EmailGeneratorAnalytics', `Failed to store analytics event ${eventData.eventType}`, { error, eventData });
      return false;
    }

    return true;

  } catch (error) {
    logError('EmailGeneratorAnalytics', `Error storing analytics event ${eventData.eventType}`, { error, eventData });
    return false;
  }
}

/**
 * Track email address generation event
 */
export async function trackEmailAddressGenerated(
  sessionId: string,
  emailAddress: string,
  metadata?: {
    userId?: string;
    deviceType?: string;
    browser?: string;
    country?: string;
    referrer?: string;
  }
): Promise<void> {
  try {
    // Store in our analytics database
    await storeAnalyticsEvent({
      sessionId,
      userId: metadata?.userId,
      eventType: 'email_address_generated',
      pagePath: '/',
      referrer: metadata?.referrer,
      country: metadata?.country,
      browser: metadata?.browser,
      deviceType: metadata?.deviceType,
      additionalData: {
        emailAddress: emailAddress,
        domain: emailAddress.split('@')[1] || 'unknown',
      },
    });

    // Update session metrics
    await updateSessionMetrics(sessionId, {
      emailsGeneratedCount: 1,
    });

    // Track in PostHog (client-side will handle this)
    if (typeof window !== 'undefined') {
      await trackPostHogEvent('email_address_generated', {
        sessionId,
        emailAddress,
        domain: emailAddress.split('@')[1] || 'unknown',
      });
    }

    logInfo('EmailGeneratorAnalytics', `Tracked email generation for session ${sessionId}`, { emailAddress });

  } catch (error) {
    logError('EmailGeneratorAnalytics', `Error tracking email generation for session ${sessionId}`, { error, emailAddress });
  }
}

/**
 * Track email address copied to clipboard event
 */
export async function trackEmailAddressCopied(
  sessionId: string,
  emailAddress: string,
  metadata?: {
    userId?: string;
    deviceType?: string;
    browser?: string;
    country?: string;
  }
): Promise<void> {
  try {
    // Store in our analytics database
    await storeAnalyticsEvent({
      sessionId,
      userId: metadata?.userId,
      eventType: 'email_address_copied',
      pagePath: '/',
      country: metadata?.country,
      browser: metadata?.browser,
      deviceType: metadata?.deviceType,
      additionalData: {
        emailAddress: emailAddress,
        domain: emailAddress.split('@')[1] || 'unknown',
      },
    });

    // Update session metrics
    await updateSessionMetrics(sessionId, {
      copyActionsCount: 1,
    });

    // Track in PostHog (client-side will handle this)
    if (typeof window !== 'undefined') {
      await trackPostHogEvent('email_address_copied', {
        sessionId,
        emailAddress,
        domain: emailAddress.split('@')[1] || 'unknown',
      });
    }

    logInfo('EmailGeneratorAnalytics', `Tracked email copy for session ${sessionId}`, { emailAddress });

  } catch (error) {
    logError('EmailGeneratorAnalytics', `Error tracking email copy for session ${sessionId}`, { error, emailAddress });
  }
}

/**
 * Track email opened/viewed event
 */
export async function trackEmailOpened(
  sessionId: string,
  emailId: string,
  metadata?: {
    userId?: string;
    deviceType?: string;
    browser?: string;
    country?: string;
    emailSubject?: string;
    emailSender?: string;
  }
): Promise<void> {
  try {
    // Store in our analytics database
    await storeAnalyticsEvent({
      sessionId,
      userId: metadata?.userId,
      eventType: 'email_opened',
      pagePath: '/',
      country: metadata?.country,
      browser: metadata?.browser,
      deviceType: metadata?.deviceType,
      additionalData: {
        emailId: emailId,
        emailSubject: metadata?.emailSubject,
        emailSender: metadata?.emailSender,
      },
    });

    // Update session metrics
    await updateSessionMetrics(sessionId, {
      emailsViewedCount: 1,
    });

    // Track in PostHog (client-side will handle this)
    if (typeof window !== 'undefined') {
      await trackPostHogEvent('email_opened', {
        sessionId,
        emailId,
        emailSubject: metadata?.emailSubject,
        emailSender: metadata?.emailSender,
      });
    }

    logInfo('EmailGeneratorAnalytics', `Tracked email view for session ${sessionId}`, { emailId });

  } catch (error) {
    logError('EmailGeneratorAnalytics', `Error tracking email view for session ${sessionId}`, { error, emailId });
  }
}

/**
 * Track email deleted event
 */
export async function trackEmailDeleted(
  sessionId: string,
  emailId: string,
  metadata?: {
    userId?: string;
    deviceType?: string;
    browser?: string;
    country?: string;
    emailSubject?: string;
  }
): Promise<void> {
  try {
    // Store in our analytics database
    await storeAnalyticsEvent({
      sessionId,
      userId: metadata?.userId,
      eventType: 'email_deleted',
      pagePath: '/',
      country: metadata?.country,
      browser: metadata?.browser,
      deviceType: metadata?.deviceType,
      additionalData: {
        emailId: emailId,
        emailSubject: metadata?.emailSubject,
      },
    });

    // Update session metrics
    await updateSessionMetrics(sessionId, {
      emailsDeletedCount: 1,
    });

    // Track in PostHog (client-side will handle this)
    if (typeof window !== 'undefined') {
      await trackPostHogEvent('email_deleted', {
        sessionId,
        emailId,
        emailSubject: metadata?.emailSubject,
      });
    }

    logInfo('EmailGeneratorAnalytics', `Tracked email deletion for session ${sessionId}`, { emailId });

  } catch (error) {
    logError('EmailGeneratorAnalytics', `Error tracking email deletion for session ${sessionId}`, { error, emailId });
  }
}

/**
 * Track manual refresh event
 */
export async function trackManualRefresh(
  sessionId: string,
  metadata?: {
    userId?: string;
    deviceType?: string;
    browser?: string;
    country?: string;
    emailAddress?: string;
  }
): Promise<void> {
  try {
    // Store in our analytics database
    await storeAnalyticsEvent({
      sessionId,
      userId: metadata?.userId,
      eventType: 'manual_refresh_triggered',
      pagePath: '/',
      country: metadata?.country,
      browser: metadata?.browser,
      deviceType: metadata?.deviceType,
      additionalData: {
        emailAddress: metadata?.emailAddress,
      },
    });

    // Update session metrics
    await updateSessionMetrics(sessionId, {
      manualRefreshCount: 1,
    });

    // Track in PostHog (client-side will handle this)
    if (typeof window !== 'undefined') {
      await trackPostHogEvent('manual_refresh_triggered', {
        sessionId,
        emailAddress: metadata?.emailAddress,
      });
    }

    logInfo('EmailGeneratorAnalytics', `Tracked manual refresh for session ${sessionId}`);

  } catch (error) {
    logError('EmailGeneratorAnalytics', `Error tracking manual refresh for session ${sessionId}`, { error });
  }
}

/**
 * Track heartbeat to indicate user is still active (for live visitor count)
 */
export async function trackHeartbeat(
  sessionId: string,
  metadata?: {
    userId?: string;
    deviceType?: string;
    browser?: string;
    country?: string;
  }
): Promise<void> {
  try {
    // Update last_seen_at in session_analytics table
    const response = await fetch('/api/analytics/heartbeat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionId,
        timestamp: new Date().toISOString(),
        ...metadata
      }),
    });

    if (!response.ok) {
      throw new Error(`Heartbeat API error: ${response.status}`);
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Heartbeat failed');
    }

  } catch (error) {
    // Don't log heartbeat errors as they're not critical and happen frequently
    // Just silently fail to avoid console spam
    console.debug('Heartbeat failed:', error);
  }
}

/**
 * Track session start event
 */
export async function trackSessionStart(
  sessionId: string,
  metadata: {
    userId?: string;
    deviceType?: string;
    browser?: string;
    country?: string;
    referrer?: string;
  }
): Promise<void> {
  try {
    // Store in our analytics database
    await storeAnalyticsEvent({
      sessionId,
      userId: metadata.userId,
      eventType: 'session_start',
      pagePath: '/',
      referrer: metadata.referrer,
      country: metadata.country,
      browser: metadata.browser,
      deviceType: metadata.deviceType,
      additionalData: {
        sessionStartTime: new Date().toISOString(),
      },
    });

    // Track in PostHog (client-side will handle this)
    if (typeof window !== 'undefined') {
      await trackPostHogEvent('session_start', {
        sessionId,
        deviceType: metadata.deviceType,
        browser: metadata.browser,
        country: metadata.country,
      });
    }

    logInfo('EmailGeneratorAnalytics', `Tracked session start for ${sessionId}`, metadata);

  } catch (error) {
    logError('EmailGeneratorAnalytics', `Error tracking session start for ${sessionId}`, { error, metadata });
  }
}

/**
 * Track session end event
 */
export async function trackSessionEnd(
  sessionId: string,
  metadata?: {
    userId?: string;
    sessionDuration?: number;
    totalEmailsGenerated?: number;
    totalEmailsViewed?: number;
  }
): Promise<void> {
  try {
    const sessionDuration = metadata?.sessionDuration || await getSessionDuration(sessionId);

    // Store in our analytics database
    await storeAnalyticsEvent({
      sessionId,
      userId: metadata?.userId,
      eventType: 'session_end',
      pagePath: '/',
      additionalData: {
        sessionDuration,
        totalEmailsGenerated: metadata?.totalEmailsGenerated,
        totalEmailsViewed: metadata?.totalEmailsViewed,
        sessionEndTime: new Date().toISOString(),
      },
    });

    // Track in PostHog (client-side will handle this)
    if (typeof window !== 'undefined') {
      await trackPostHogEvent('session_end', {
        sessionId,
        sessionDuration,
        totalEmailsGenerated: metadata?.totalEmailsGenerated,
        totalEmailsViewed: metadata?.totalEmailsViewed,
      });
    }

    logInfo('EmailGeneratorAnalytics', `Tracked session end for ${sessionId}`, { sessionDuration });

  } catch (error) {
    logError('EmailGeneratorAnalytics', `Error tracking session end for ${sessionId}`, { error });
  }
}

/**
 * Track emails received count update
 * This is called when new emails are detected in the inbox
 * NOTE: This only creates an analytics event, session metrics are updated by individual trackEmailReceived calls
 */
export async function trackEmailsReceived(
  sessionId: string,
  emailCount: number,
  metadata?: {
    userId?: string;
    emailAddress?: string;
  }
): Promise<void> {
  try {
    // Store in our analytics database (for aggregate tracking)
    await storeAnalyticsEvent({
      sessionId,
      userId: metadata?.userId,
      eventType: 'emails_received_count',
      pagePath: '/',
      additionalData: {
        emailCount,
        emailAddress: metadata?.emailAddress,
      },
    });

    // NOTE: Session metrics are updated by individual trackEmailReceived calls to avoid double-counting

    logInfo('EmailGeneratorAnalytics', `Tracked emails received count for session ${sessionId}`, { emailCount });

  } catch (error) {
    logError('EmailGeneratorAnalytics', `Error tracking emails received count for session ${sessionId}`, { error, emailCount });
  }
}

/**
 * Track individual email received event
 * This is called when a single email is received by a temporary email address
 */
export async function trackEmailReceived(
  sessionId: string,
  emailData: {
    emailId: string;
    emailAddress: string;
    subject?: string;
    sender?: string;
    domain?: string;
  },
  metadata?: {
    userId?: string;
    deviceType?: string;
    browser?: string;
    country?: string;
  }
): Promise<void> {
  try {
    // Store in our analytics database
    await storeAnalyticsEvent({
      sessionId,
      userId: metadata?.userId,
      eventType: 'email_received',
      pagePath: '/',
      country: metadata?.country,
      browser: metadata?.browser,
      deviceType: metadata?.deviceType,
      additionalData: {
        emailId: emailData.emailId,
        emailAddress: emailData.emailAddress,
        emailSubject: emailData.subject,
        emailSender: emailData.sender,
        domain: emailData.domain || emailData.emailAddress.split('@')[1] || 'unknown',
      },
    });

    // Update session metrics (increment by 1)
    await updateSessionMetrics(sessionId, {
      emailsReceivedCount: 1,
    });

    // Track in PostHog (client-side will handle this)
    if (typeof window !== 'undefined') {
      await trackPostHogEvent('email_received', {
        sessionId,
        emailId: emailData.emailId,
        emailAddress: emailData.emailAddress,
        emailSubject: emailData.subject,
        emailSender: emailData.sender,
        domain: emailData.domain,
      });
    }

    console.log(`[ANALYTICS] Email received tracked:`, {
      sessionId,
      emailId: emailData.emailId,
      emailAddress: emailData.emailAddress,
      timestamp: new Date().toISOString()
    });

    logInfo('EmailGeneratorAnalytics', `Tracked email received for session ${sessionId}`, {
      emailId: emailData.emailId,
      emailAddress: emailData.emailAddress
    });

  } catch (error) {
    logError('EmailGeneratorAnalytics', `Error tracking email received for session ${sessionId}`, {
      error,
      emailData
    });
  }
}
