<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Fademail Color Palette</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            padding: 40px;
            background-color: #f0efed;
            color: #1c1b1a;
        }

        h1 {
            margin-bottom: 30px;
            color: #1c1b1a;
        }

        h2 {
            margin: 30px 0 15px;
            color: #1c1b1a;
            border-bottom: 1px solid #d5d3d2;
            padding-bottom: 10px;
        }

        .color-section {
            margin-bottom: 40px;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }

        .color-card {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            background-color: white;
        }

        .color-sample {
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .color-info {
            padding: 15px;
            background-color: white;
        }

        .color-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .color-hex {
            font-family: monospace;
            color: #7d7b79;
        }

        .color-usage {
            margin-top: 8px;
            font-size: 14px;
            color: #4a4846;
        }

        .ui-example {
            background-color: white;
            border-radius: 0.5rem;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .ui-example h3 {
            margin-bottom: 15px;
            color: #1c1b1a;
        }

        .button-example {
            display: inline-block;
            padding: 12px 20px;
            border-radius: 0.5rem;
            font-weight: 500;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
        }

        .email-card {
            border: 1px solid #e8e7e6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            background-color: white;
        }

        .email-card.selected {
            background-color: #e8e7e6;
            border-left: 4px solid #ce601c;
        }

        .email-sender {
            font-weight: 600;
            color: #1c1b1a;
            margin-bottom: 5px;
        }

        .email-subject {
            font-weight: 500;
            color: #4a4846;
            margin-bottom: 5px;
        }

        .email-preview {
            color: #7d7b79;
            font-size: 14px;
        }

        .unread-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #ce601c;
            border-radius: 50%;
            margin-right: 8px;
        }

        .logo-example {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background-color: #1c1b1a;
            border-radius: 8px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .logo-text {
            color: #1c1b1a;
            font-weight: 600;
            font-size: 20px;
        }

        .inbox-viewer {
            display: flex;
            border: 1px solid #e8e7e6;
            border-radius: 10px;
            overflow: hidden;
            height: 200px;
        }

        .inbox-section {
            width: 40%;
            background-color: white;
            border-right: 1px solid #e8e7e6;
            padding: 15px;
        }

        .viewer-section {
            width: 60%;
            background-color: white;
            padding: 15px;
        }

        .inbox-header {
            font-weight: 600;
            color: #1c1b1a;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .inbox-icon {
            color: #1c1b1a;
            margin-right: 5px;
        }

        .svg-background {
            background-color: #f5dfd2;
            padding: 20px;
            border-radius: 10px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>New Fademail UI Color Palette</h1>

    <div class="color-section">
        <h2>Background Colors</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #ffffff; color: #1c1b1a;">Main UI Components</div>
                <div class="color-info">
                    <div class="color-name">Main UI Components</div>
                    <div class="color-hex">#ffffff</div>
                    <div class="color-usage">Used for email controls, inbox, email viewer, and footer backgrounds across all pages</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #ffffff; color: #1c1b1a; opacity: 0.95;">Semi-Transparent White</div>
                <div class="color-info">
                    <div class="color-name">Semi-Transparent White</div>
                    <div class="color-hex">#ffffff (95% opacity)</div>
                    <div class="color-usage">Used for navbar with backdrop blur for a frosted glass effect</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #fafafa; color: #1c1b1a;">Background</div>
                <div class="color-info">
                    <div class="color-name">Background</div>
                    <div class="color-hex">#fafafa</div>
                    <div class="color-usage">Used for the empty space around the main UI with subtle pattern</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #ffffff; color: #1c1b1a;">Surface</div>
                <div class="color-info">
                    <div class="color-name">Surface</div>
                    <div class="color-hex">#ffffff</div>
                    <div class="color-usage">Used for cards, modals, and other surface elements</div>
                </div>
            </div>
        </div>

        <div class="ui-example">
            <h3>Background Layering Example</h3>
            <div style="background-color: #fafafa; background-image: url('data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23f0f0f0\' fill-opacity=\'0.4\' fill-rule=\'evenodd\'/%3E%3C/svg%3E'); padding: 20px; border-radius: 10px; position: relative;">
                <div style="background-color: #ffffff; border-radius: 8px; padding: 15px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03);">
                    <div style="background-color: #ffffff; border-radius: 6px; padding: 10px; border: 1px solid #e8e7e6;">
                        <p style="color: #1c1b1a; margin: 0;">Email content, navbar, footer</p>
                    </div>
                    <p style="color: #3c3a38; margin-top: 10px;">Main UI components (white background)</p>
                </div>
                <p style="color: #605f5d; margin-top: 10px; font-size: 12px; text-align: center;">Page background (light gray with pattern)</p>
            </div>
        </div>
    </div>

    <div class="color-section">
        <h2>Text Colors</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #1c1b1a;">Primary Text</div>
                <div class="color-info">
                    <div class="color-name">Primary Text</div>
                    <div class="color-hex">#1c1b1a</div>
                    <div class="color-usage">Used for headings and important text</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #3c3a38;">Secondary Text</div>
                <div class="color-info">
                    <div class="color-name">Secondary Text</div>
                    <div class="color-hex">#3c3a38</div>
                    <div class="color-usage">Used for body text and descriptions</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #605f5d;">Tertiary Text</div>
                <div class="color-info">
                    <div class="color-name">Tertiary Text</div>
                    <div class="color-hex">#605f5d</div>
                    <div class="color-usage">Used for less important text elements</div>
                </div>
            </div>
        </div>
    </div>

    <div class="color-section">
        <h2>Button Colors</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #605f5f;">Primary Button</div>
                <div class="color-info">
                    <div class="color-name">Primary Button</div>
                    <div class="color-hex">#605f5f</div>
                    <div class="color-usage">Used for primary button backgrounds (New Address)</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #505050;">Button Hover</div>
                <div class="color-info">
                    <div class="color-name">Button Hover</div>
                    <div class="color-hex">#505050</div>
                    <div class="color-usage">Used for primary button hover state</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #ce601c;">Accent Color</div>
                <div class="color-info">
                    <div class="color-name">Accent Color</div>
                    <div class="color-hex">#ce601c</div>
                    <div class="color-usage">Used for selected email border, unread indicator, and copy button</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #e67e22;">Accent Gradient End</div>
                <div class="color-info">
                    <div class="color-name">Accent Gradient End</div>
                    <div class="color-hex">#e67e22</div>
                    <div class="color-usage">Used for gradient end in mobile navigation buttons</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #ffffff; color: #ce601c; border: 1px solid #ce601c;">Copy Button</div>
                <div class="color-info">
                    <div class="color-name">Copy Button</div>
                    <div class="color-hex">#ffffff (bg), #ce601c (text/border)</div>
                    <div class="color-usage">Used for the copy button</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #ffffff; color: #605f5f; border: 1px solid #605f5f;">Secondary Button</div>
                <div class="color-info">
                    <div class="color-name">Secondary Button</div>
                    <div class="color-hex">#ffffff (bg), #605f5f (text/border)</div>
                    <div class="color-usage">Used for secondary buttons</div>
                </div>
            </div>
        </div>

        <div class="ui-example">
            <h3>Button Examples</h3>
            <div class="button-example" style="background-color: white; color: #ce601c; border: 1px solid #ce601c;">
                Copy
            </div>
            <div class="button-example" style="background-color: #605f5f; color: white; border: 1px solid #605f5f;">
                New Address
            </div>
            <div class="button-example" style="background-color: white; color: #605f5f; border: 1px solid #605f5f;">
                Secondary
            </div>

            <h3 style="margin-top: 20px;">Mobile Navigation Buttons</h3>
            <div style="background-color: #f0efed; padding: 15px; border-radius: 20px; display: flex; gap: 10px; justify-content: center; margin-top: 10px;">
                <div class="button-example" style="background: linear-gradient(to right, #ce601c, #e67e22); color: white; border: none; border-radius: 20px; transform: scale(1.05); box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                    <svg style="display: inline-block; width: 16px; height: 16px; margin-right: 5px; vertical-align: middle;" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M22 6l-10 7L2 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    Inbox
                </div>
                <div class="button-example" style="background-color: white; color: #605f5d; border: none; border-radius: 20px;">
                    <svg style="display: inline-block; width: 16px; height: 16px; margin-right: 5px; vertical-align: middle;" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    View Email
                </div>
            </div>
        </div>
    </div>

    <div class="color-section">
        <h2>Email Card Background</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #ffffff; color: #1c1b1a;">Card Background</div>
                <div class="color-info">
                    <div class="color-name">Card Background</div>
                    <div class="color-hex">#ffffff</div>
                    <div class="color-usage">Used for email card backgrounds</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #e8e7e6; color: #1c1b1a;">Selected Email</div>
                <div class="color-info">
                    <div class="color-name">Selected Email Background</div>
                    <div class="color-hex">#e8e7e6</div>
                    <div class="color-usage">Used for selected email backgrounds</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: #ce601c;">Selected Email Border</div>
                <div class="color-info">
                    <div class="color-name">Selected Email Border</div>
                    <div class="color-hex">#ce601c</div>
                    <div class="color-usage">Used for selected email left border and unread indicator</div>
                </div>
            </div>
        </div>

        <div class="ui-example">
            <h3>Email Card Examples</h3>
            <div class="email-card">
                <div class="email-sender"><span class="unread-indicator"></span>Fademail Team</div>
                <div class="email-subject">Welcome to Fademail!</div>
                <div class="email-preview">Welcome to Fademail, your temporary email service...</div>
            </div>
            <div class="email-card selected">
                <div class="email-sender">Fademail Tips</div>
                <div class="email-subject">Tips for using Fademail</div>
                <div class="email-preview">Here are some tips to get the most out of your Fademail...</div>
            </div>
        </div>
    </div>

    <div class="color-section">
        <h2>Logo Colors</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #1c1b1a;">Logo Color</div>
                <div class="color-info">
                    <div class="color-name">Logo Color</div>
                    <div class="color-hex">#1c1b1a</div>
                    <div class="color-usage">Used for the logo icon and text</div>
                </div>
            </div>
        </div>

        <div class="ui-example">
            <h3>Logo Example</h3>
            <div class="logo-example">
                <div class="logo-icon">F</div>
                <div class="logo-text">Fademail</div>
            </div>
        </div>
    </div>

    <div class="color-section">
        <h2>Shadows</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: white; color: #1c1b1a; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03);">Medium Shadow</div>
                <div class="color-info">
                    <div class="color-name">Medium Shadow</div>
                    <div class="color-hex">0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03)</div>
                    <div class="color-usage">Used for the main content container</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: white; color: #1c1b1a; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);">Card Shadow</div>
                <div class="color-info">
                    <div class="color-name">Card Shadow</div>
                    <div class="color-hex">0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)</div>
                    <div class="color-usage">Used for cards and other elevated elements</div>
                </div>
            </div>

            <div class="color-card">
                <div class="color-sample" style="background-color: white; color: #1c1b1a; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.03);">Button Hover Shadow</div>
                <div class="color-info">
                    <div class="color-name">Button Hover Shadow</div>
                    <div class="color-hex">0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.03)</div>
                    <div class="color-usage">Used for button hover states</div>
                </div>
            </div>
        </div>

        <div class="ui-example">
            <h3>Shadow Examples</h3>
            <div style="display: flex; gap: 20px; flex-wrap: wrap; margin-top: 15px;">
                <div style="width: 150px; height: 100px; background-color: white; border-radius: 8px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03);">
                    Medium Shadow
                </div>
                <div style="width: 150px; height: 100px; background-color: white; border-radius: 8px; display: flex; align-items: center; justify-content: center; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);">
                    Card Shadow
                </div>
                <div style="width: 150px; height: 100px; background-color: white; border-radius: 8px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.03);">
                    Button Hover Shadow
                </div>
            </div>
        </div>
    </div>

    <div class="color-section">
        <h2>SVG Background</h2>
        <div class="color-grid">
            <div class="color-card">
                <div class="color-sample" style="background-color: #f5dfd2; color: #1c1b1a;">SVG Background</div>
                <div class="color-info">
                    <div class="color-name">SVG Background</div>
                    <div class="color-hex">#f5dfd2</div>
                    <div class="color-usage">Used for SVG backgrounds</div>
                </div>
            </div>
        </div>

        <div class="ui-example">
            <h3>SVG Background Example</h3>
            <div class="svg-background">
                <svg width="100" height="100" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6z" stroke="#1c1b1a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M22 6l-10 7L2 6" stroke="#1c1b1a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
        </div>
    </div>
</body>
</html>
