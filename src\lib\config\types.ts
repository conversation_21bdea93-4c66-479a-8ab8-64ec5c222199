/**
 * Types for the configuration system
 */

/**
 * Application configuration
 */
export interface AppConfig {
  emailExpirationMinutes: number;
  autoRefreshInterval: number;
  maxEmailsPerAddress: number;
  maintenanceMode: boolean;
  cleanupIntervalMinutes: number;
  cleanupBatchSize: number;
  cleanupAutoStart?: boolean; // Auto-start cleanup scheduler on app load
  cleanupLastRunTime?: string; // Last time cleanup was executed
  cleanupSchedulerStartTime?: string; // When the current scheduler session started

  // Guerrilla Email Cleanup Configuration
  guerrillaCleanupAgeThresholdHours?: number; // Age threshold for Guerrilla email cleanup (in hours)
  guerrillaCleanupMode?: 'manual' | 'auto'; // Cleanup mode: manual or auto
  guerrillaCleanupIntervalMinutes?: number; // Interval for auto cleanup (in minutes)
  guerrillaCleanupAutoStart?: boolean; // Auto-start Guerrilla cleanup scheduler on app load
  guerrillaCleanupLastRunTime?: string; // Last time Guerrilla cleanup was executed
  guerrillaCleanupSchedulerStartTime?: string; // When the current Guerrilla scheduler session started
  maintenanceIntervalHours: number;
  alertThresholds?: Record<string, number>;
  analyticsEnabled?: boolean; // Added for backward compatibility with existing code
}

/**
 * Domain configuration
 */
export interface DomainConfig {
  domain: string;
  isActive: boolean;
  settings: DomainSettings;
  createdAt: string;
  updatedAt: string;
}

/**
 * Domain settings
 */
export interface DomainSettings {
  isDefault: boolean;
  weight: number;
  features: {
    adsEnabled: boolean;
    analyticsEnabled: boolean;
    autoRefreshEnabled: boolean;
  };
}

/**
 * Ad configuration
 */
export interface AdConfig {
  placementId: string;
  domain: string;
  adUnitId: string;
  adClientId: string; // Required field with default value in database
  isEnabled: boolean;
  deviceTypes: string[];
  displayOptions?: AdDisplayOptions;
  schedule?: AdSchedule;
  createdAt: string;
  updatedAt: string;
}

/**
 * Ad schedule
 */
export interface AdSchedule {
  // Weekly schedule
  useSchedule?: boolean;
  monday?: DaySchedule;
  tuesday?: DaySchedule;
  wednesday?: DaySchedule;
  thursday?: DaySchedule;
  friday?: DaySchedule;
  saturday?: DaySchedule;
  sunday?: DaySchedule;

  // Legacy options - keeping for backward compatibility
  startDate?: string;
  endDate?: string;
  startTime?: string;
  endTime?: string;
  daysOfWeek?: number[];
  isRecurring?: boolean;
  recurringPattern?: 'daily' | 'weekly' | 'monthly';
  recurringInterval?: number;
  specialDates?: {
    date: string;
    isEnabled: boolean;
  }[];
}

/**
 * Day schedule
 */
export interface DaySchedule {
  enabled: boolean;
  startTime: string;
  endTime: string;
}

/**
 * Ad display options
 */
export interface AdDisplayOptions {
  // Style options
  position?: 'relative' | 'absolute' | 'fixed' | 'sticky';
  margin?: string;
  padding?: string;
  backgroundColor?: string;
  borderRadius?: string;
  maxWidth?: string;
  overflow?: string;

  // Border options
  showBorder?: boolean;
  borderColor?: string;

  // Label options
  showLabel?: boolean;
  labelText?: string;

  // Legacy options - keeping for backward compatibility
  delaySeconds?: number;
  displayDuration?: number;
  maxDisplaysPerSession?: number;
  idleThresholdSeconds?: number;
  displayMode?: 'overlay' | 'inline';
  dismissible?: boolean;
  triggerAction?: string;
  frequency?: number;
  initiallyExpanded?: boolean;
  collapseOption?: boolean;
  rememberState?: boolean;
  expandAfterMinutes?: number;
}
