'use client';

import { useState, useEffect } from 'react';
import { getCacheStats } from '@/lib/config/adCacheService';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/Select';
import { Checkbox } from '@/components/ui/Checkbox';
import { Badge } from '@/components/ui/Badge';
import { Spinner } from '@/components/ui/Spinner';
import { formatNumber, formatPercentage } from '@/lib/utils';

interface CacheStats {
  hits: number;
  misses: number;
  keys: number;
  ksize: number;
  vsize: number;
}

interface PerformanceMetrics {
  adCache: CacheStats;
  domainCache: CacheStats;
  hitRatio: number;
  totalRequests: number;
  averageLoadTime: number;
  loadTimes: number[];
}

export default function AdPerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    adCache: { hits: 0, misses: 0, keys: 0, ksize: 0, vsize: 0 },
    domainCache: { hits: 0, misses: 0, keys: 0, ksize: 0, vsize: 0 },
    hitRatio: 0,
    totalRequests: 0,
    averageLoadTime: 0,
    loadTimes: []
  });
  const [refreshInterval, setRefreshInterval] = useState(10); // seconds
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  // Fetch cache stats
  const fetchCacheStats = async () => {
    try {
      const response = await fetch('/api/management-portal-x7z9y2/ad-performance');
      if (!response.ok) {
        throw new Error('Failed to fetch performance metrics');
      }

      const data = await response.json();

      if (data.success) {
        const { adCache, domainCache, loadTimes } = data.data;
        const totalHits = adCache.hits + domainCache.hits;
        const totalMisses = adCache.misses + domainCache.misses;
        const totalRequests = totalHits + totalMisses;
        const hitRatio = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0;
        const averageLoadTime = loadTimes.length > 0
          ? loadTimes.reduce((sum: number, time: number) => sum + time, 0) / loadTimes.length
          : 0;

        setMetrics({
          adCache,
          domainCache,
          hitRatio,
          totalRequests,
          averageLoadTime,
          loadTimes
        });
      }
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
    }
  };

  // Set up auto-refresh
  useEffect(() => {
    fetchCacheStats();

    let intervalId: NodeJS.Timeout | null = null;

    if (isAutoRefresh) {
      intervalId = setInterval(fetchCacheStats, refreshInterval * 1000);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [refreshInterval, isAutoRefresh]);

  // Format time in milliseconds
  const formatTime = (ms: number) => {
    return ms.toFixed(2) + 'ms';
  };

  return (
    <Card variant="elevated" hover={false}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Ad Performance Monitor</CardTitle>
            <CardDescription>
              Real-time performance metrics for ad loading and caching
            </CardDescription>
          </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <label htmlFor="refresh-interval" className="mr-2 text-sm text-gray-700">
              Refresh:
            </label>
            <Select
              value={refreshInterval.toString()}
              onValueChange={(value) => setRefreshInterval(parseInt(value))}
              disabled={!isAutoRefresh}
            >
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5s</SelectItem>
                <SelectItem value="10">10s</SelectItem>
                <SelectItem value="30">30s</SelectItem>
                <SelectItem value="60">1m</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Checkbox
            id="auto-refresh"
            checked={isAutoRefresh}
            onCheckedChange={(checked) => setIsAutoRefresh(checked)}
            label="Auto-refresh"
          />
          <Button
            variant="primary"
            size="sm"
            onClick={fetchCacheStats}
            leftIcon={
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
              </svg>
            }
          >
            Refresh
          </Button>
        </div>
      </div>
    </CardHeader>
      <CardContent className="p-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
          <Card className="bg-gray-50 hover:shadow-md transition-all duration-300">
            <CardContent className="p-4">
              <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd" />
                </svg>
                Cache Performance
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Hit Ratio</p>
                  <div className="flex items-center">
                    <p className="text-2xl font-semibold text-gray-900">
                      {formatPercentage(metrics.hitRatio)}
                    </p>
                    <Badge variant="default" className="ml-2">
                      {metrics.hitRatio > 80 ? 'Excellent' : metrics.hitRatio > 50 ? 'Good' : 'Poor'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Total Requests</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatNumber(metrics.totalRequests)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Cache Hits</p>
                  <p className="text-2xl font-semibold text-green-600">
                    {formatNumber(metrics.adCache.hits + metrics.domainCache.hits)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Cache Misses</p>
                  <p className="text-2xl font-semibold text-red-600">
                    {formatNumber(metrics.adCache.misses + metrics.domainCache.misses)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-50 hover:shadow-md transition-all duration-300">
            <CardContent className="p-4">
              <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                </svg>
                Load Performance
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Avg Load Time</p>
                  <div className="flex items-center">
                    <p className="text-2xl font-semibold text-gray-900">
                      {formatTime(metrics.averageLoadTime)}
                    </p>
                    <Badge variant={metrics.averageLoadTime < 50 ? 'success' : metrics.averageLoadTime < 200 ? 'warning' : 'destructive'} className="ml-2">
                      {metrics.averageLoadTime < 50 ? 'Fast' : metrics.averageLoadTime < 200 ? 'Medium' : 'Slow'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Max Load Time</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatTime(Math.max(...metrics.loadTimes, 0))}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Min Load Time</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatTime(Math.min(...(metrics.loadTimes.length > 0 ? metrics.loadTimes : [0])))}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Samples</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatNumber(metrics.loadTimes.length)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-50 hover:shadow-md transition-all duration-300">
            <CardContent className="p-4">
              <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                Cache Storage
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Ad Cache Keys</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatNumber(metrics.adCache.keys)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Domain Cache Keys</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatNumber(metrics.domainCache.keys)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Key Size</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatNumber(metrics.adCache.ksize + metrics.domainCache.ksize)} bytes
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Value Size</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatNumber(metrics.adCache.vsize + metrics.domainCache.vsize)} bytes
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between items-center">
        <h4 className="text-sm font-medium text-gray-500 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5 2a2 2 0 00-2 2v14l3.5-2 3.5 2 3.5-2 3.5 2V4a2 2 0 00-2-2H5zm4.707 3.707a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L8.414 9H10a3 3 0 013 3v1a1 1 0 102 0v-1a5 5 0 00-5-5H8.414l1.293-1.293z" clipRule="evenodd" />
          </svg>
          Cache Management
        </h4>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={async () => {
              await fetch('/api/management-portal-x7z9y2/ad-performance/flush-cache', { method: 'POST' });
              fetchCacheStats();
            }}
            leftIcon={
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            }
          >
            Flush All Caches
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
