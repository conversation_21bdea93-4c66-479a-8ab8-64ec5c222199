# Database Connection Management Guide

## Understanding the "Too many connections" Error

When you encounter the error:

```
Guerrilla connection error: Error [DatabaseConnectionError]: Failed to connect to guerrilla database: Too many connections
```

This is a MySQL database error (code 'ER_CON_COUNT_ERROR', errno: 1040) that occurs when the database server has reached its maximum allowed connections limit.

## Why This Happens

### 1. Connection Limits

MySQL has a configuration parameter called `max_connections` that limits how many simultaneous connections can be made to the database. The default value varies by MySQL version and configuration, but it's typically between 100-151 connections.

### 2. Connection Leaks

The most common cause of this error in development environments is connection leaks. This happens when:

- Database connections are opened but not properly closed
- Error handling doesn't include closing connections
- Promises or async functions don't have proper `finally` blocks to ensure connections are closed

### 3. Development Server Behavior

Next.js development server with hot reloading can exacerbate this issue:

- Each file change can trigger a server restart without properly closing connections
- Multiple API routes being tested in rapid succession can open many connections
- The development server may not properly terminate connections when restarting

### 4. Multiple Server Instances

If you're running multiple instances of the application (e.g., multiple terminal windows with the development server), they all share the same connection limit to the database.

## How to Avoid This Issue

### Immediate Solutions

1. **Restart the development server**
   - This is the quickest fix as it closes all existing connections
   - Use `Ctrl+C` to stop the server, then restart it with `npm run dev` or `pnpm dev`

2. **Restart the database server**
   - If restarting the application server doesn't help, you may need to restart the database server
   - This will forcibly close all connections

### Long-term Solutions

1. **Implement proper connection pooling**
   - Use a connection pool manager instead of creating new connections for each request
   - Example using `mysql2`:
   ```javascript
   const pool = mysql.createPool({
     host: 'localhost',
     user: 'user',
     password: 'password',
     database: 'db',
     waitForConnections: true,
     connectionLimit: 10,
     queueLimit: 0
   });
   ```

2. **Always close connections**
   - Ensure every database connection is properly closed after use
   - Use try/catch/finally blocks to guarantee connection closure:
   ```javascript
   let connection;
   try {
     connection = await pool.getConnection();
     // Database operations
   } catch (error) {
     // Error handling
   } finally {
     if (connection) connection.release(); // Always release the connection
   }
   ```

3. **Use connection middleware**
   - Create middleware that manages connections for API routes
   - This centralizes connection management and ensures proper cleanup

4. **Implement connection timeout**
   - Set reasonable timeouts for database operations
   - This prevents connections from staying open indefinitely:
   ```javascript
   const pool = mysql.createPool({
     // other options
     connectTimeout: 10000, // 10 seconds
     acquireTimeout: 10000,
     timeout: 60000 // 1 minute
   });
   ```

5. **Monitor active connections**
   - Add logging to track connection opening and closing
   - This helps identify where leaks might be occurring:
   ```javascript
   // When opening a connection
   console.log(`[${new Date().toISOString()}] Opening DB connection`);
   
   // When closing a connection
   console.log(`[${new Date().toISOString()}] Closing DB connection`);
   ```

## Checking Current Connections

To check how many connections are currently active in MySQL:

```sql
SHOW STATUS WHERE Variable_name = 'Threads_connected';
```

To see the maximum allowed connections:

```sql
SHOW VARIABLES LIKE 'max_connections';
```

## Development Best Practices

1. **Use environment-specific connection limits**
   - Set lower connection limits in development
   - This helps identify connection issues earlier

2. **Implement graceful shutdown**
   - Ensure your application closes all connections when shutting down
   - Add process handlers for SIGTERM and SIGINT signals

3. **Consider using an ORM**
   - ORMs like Prisma, Sequelize, or TypeORM handle connection management automatically
   - They typically implement connection pooling by default

4. **Use transactions wisely**
   - Long-running transactions can hold connections open
   - Keep transactions as short as possible

By following these guidelines, you can avoid the "Too many connections" error and ensure your application manages database resources efficiently.
