import { NextRequest } from 'next/server';
import { verifyAdminAuth } from '../../../../../lib/auth';
import { logger } from '../../../../../lib/logging/Logger';
import { realTimeMonitor } from '../../../../../lib/logging/realTimeMonitor';

// Set a timeout for inactive connections (5 minutes)
const CONNECTION_TIMEOUT = 5 * 60 * 1000;

// Set a heartbeat interval (15 seconds)
const HEARTBEAT_INTERVAL = 15 * 1000;

// Store active connections
const connections = new Set<ReadableStreamController<Uint8Array>>();

// Helper function to safely send data to a client
function safelySendToClient(controller: ReadableStreamController<Uint8Array>, data: string): boolean {
  try {
    // Check if the controller is closed before sending data
    if ((controller as any).closed) {
      console.log('Controller is already closed, skipping send');
      return false;
    }

    // Only log non-heartbeat messages to reduce noise
    if (!data.includes('"type":"heartbeat"')) {
      console.log('Sending data to client:', data);
    }

    controller.enqueue(new TextEncoder().encode(data));
    return true;
  } catch (error) {
    console.error('Error sending data to client:', error);

    // Mark the controller as closed
    (controller as any).closed = true;

    // Clean up the connection
    cleanupConnection(controller);
    return false;
  }
}

// Helper function to clean up a connection
function cleanupConnection(controller: ReadableStreamController<Uint8Array>): void {
  // Clear heartbeat interval
  if ((controller as any).heartbeatInterval) {
    clearInterval((controller as any).heartbeatInterval);
    (controller as any).heartbeatInterval = null;
  }

  // Remove this connection from the set
  connections.delete(controller);

  // Remove log listener
  if ((controller as any).logListener) {
    logger.removeListener((controller as any).logListener);
    (controller as any).logListener = null;
  }

  console.log(`Connection cleaned up. Total connections: ${connections.size}`);
}

// Register alert callback
realTimeMonitor.registerAlertCallback((alert) => {
  // Send alert to all connected clients
  const alertData = `data: ${JSON.stringify({ type: 'alert', alert })}\n\n`;
  for (const controller of connections) {
    safelySendToClient(controller, alertData);
  }
});

/**
 * GET /api/management-portal-x7z9y2/logs/stream
 * Stream logs in real-time using Server-Sent Events (SSE)
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const level = searchParams.get('level');
    const category = searchParams.get('category');

    console.log(`Setting up log stream with filters: level=${level || 'all'}, category=${category || 'all'}`);

    // Create a stream
    const stream = new ReadableStream({
      start(controller) {
        // Add this connection to the set
        connections.add(controller);
        console.log(`New connection added. Total connections: ${connections.size}`);

        // Send initial message
        const initialData = `data: ${JSON.stringify({ type: 'connected', timestamp: new Date().toISOString() })}\n\n`;
        safelySendToClient(controller, initialData);

        // Store the last activity time
        (controller as any).lastActivity = Date.now();

        // Set up log listener
        const logListener = async (logEntry: any) => {
          try {
            console.log('Log listener called with entry:', logEntry);

            // Filter logs based on query parameters
            if (level && logEntry.level !== level) {
              console.log('Filtering out log due to level mismatch');
              return;
            }
            if (category && !logEntry.category.includes(category)) {
              console.log('Filtering out log due to category mismatch');
              return;
            }

            // Send log entry to client
            const logData = `data: ${JSON.stringify({ type: 'log', log: logEntry })}\n\n`;
            console.log('Sending log data to client:', logEntry);
            const success = safelySendToClient(controller, logData);

            if (success) {
              // Update last activity time on successful send
              (controller as any).lastActivity = Date.now();
              console.log('Successfully sent log to client');
            } else {
              console.log('Failed to send log to client');
            }
          } catch (error) {
            console.error('Error in log listener:', error);
            // Don't throw errors from the listener as it could crash the server
          }
        };

        // Register log listener
        logger.addListener(logListener);
        console.log('Log listener registered');

        // Store the listener so we can remove it later
        (controller as any).logListener = logListener;

        // Set up heartbeat interval
        const heartbeatInterval = setInterval(() => {
          try {
            // Check if the controller is already closed
            if ((controller as any).closed) {
              console.log('Controller is already closed, cleaning up heartbeat');
              clearInterval(heartbeatInterval);
              return;
            }

            // Check if the connection has timed out
            const lastActivity = (controller as any).lastActivity || 0;
            const now = Date.now();

            if (now - lastActivity > CONNECTION_TIMEOUT) {
              // Connection has timed out, clean up
              console.log('Connection timed out, cleaning up');
              (controller as any).closed = true;
              cleanupConnection(controller);
              return;
            }

            // Send heartbeat
            const heartbeatData = `data: ${JSON.stringify({ type: 'heartbeat', timestamp: new Date().toISOString() })}\n\n`;
            const success = safelySendToClient(controller, heartbeatData);

            if (!success) {
              // Failed to send heartbeat, clean up
              console.log('Failed to send heartbeat, cleaning up');
              (controller as any).closed = true;
              clearInterval(heartbeatInterval);
            } else {
              // Update last activity time
              (controller as any).lastActivity = now;
            }
          } catch (error) {
            console.error('Error in heartbeat:', error);
            // Clean up on error
            (controller as any).closed = true;
            clearInterval(heartbeatInterval);
            cleanupConnection(controller);
          }
        }, HEARTBEAT_INTERVAL);

        // Store the interval so we can clear it later
        (controller as any).heartbeatInterval = heartbeatInterval;
      },
      cancel(controller) {
        // Mark the controller as closed
        (controller as any).closed = true;

        // Use the shared cleanup function
        cleanupConnection(controller);

        console.log('Client disconnected from log stream');
      }
    });

    // Return the stream as Server-Sent Events
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    });
  } catch (error) {
    await logger.error('API_LOGS_STREAM', `Error setting up log stream: ${error}`);
    return new Response('Internal Server Error', { status: 500 });
  }
}
