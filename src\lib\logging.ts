/**
 * Logging utility functions for the application
 *
 * This module provides functions for logging events and errors
 * with different severity levels to both console and database.
 */
import { logToDatabase } from './logging/dbLogger';

// Define LogLevel enum directly to avoid circular dependency
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

// Current log level (can be set via environment variable)
const currentLogLevel = (process.env.LOG_LEVEL || 'INFO') as LogLevel;

// Log level priority
const logLevelPriority: Record<LogLevel, number> = {
  [LogLevel.DEBUG]: 0,
  [LogLevel.INFO]: 1,
  [LogLevel.WARN]: 2,
  [LogLevel.ERROR]: 3,
};

/**
 * Check if a log level should be displayed based on the current log level
 */
function shouldLog(level: LogLevel): boolean {
  return logLevelPriority[level] >= logLevelPriority[currentLogLevel as LogLevel];
}

/**
 * Format a log message with timestamp, level, and context
 */
function formatLogMessage(level: LogLevel, context: string, message: string): string {
  const timestamp = new Date().toISOString();
  return `[${timestamp}] [${level}] [${context}] ${message}`;
}

/**
 * Log a message at the DEBUG level
 */
export function logDebug(context: string, message: string, data?: any): void {
  if (!shouldLog(LogLevel.DEBUG)) return;

  // Log to console
  console.debug(formatLogMessage(LogLevel.DEBUG, context, message));
  if (data !== undefined) {
    console.debug(data);
  }

  // Log to database
  logToDatabase(LogLevel.DEBUG, context, message, data);
}

/**
 * Log a message at the INFO level
 */
export function logInfo(context: string, message: string, data?: any): void {
  if (!shouldLog(LogLevel.INFO)) return;

  // Log to console
  console.info(formatLogMessage(LogLevel.INFO, context, message));
  if (data !== undefined) {
    console.info(data);
  }

  // Log to database
  logToDatabase(LogLevel.INFO, context, message, data);
}

/**
 * Log a message at the WARN level
 */
export function logWarn(context: string, message: string, data?: any): void {
  if (!shouldLog(LogLevel.WARN)) return;

  // Log to console
  console.warn(formatLogMessage(LogLevel.WARN, context, message));
  if (data !== undefined) {
    console.warn(data);
  }

  // Log to database
  logToDatabase(LogLevel.WARN, context, message, data);
}

/**
 * Log a message at the ERROR level
 */
export function logError(context: string, message: string, error?: unknown): void {
  if (!shouldLog(LogLevel.ERROR)) return;

  // Log to console
  console.error(formatLogMessage(LogLevel.ERROR, context, message));

  let metadata: Record<string, any> | undefined;

  if (error !== undefined) {
    if (error instanceof Error) {
      console.error(`Error: ${error.message}`);
      console.error(`Stack: ${error.stack}`);

      // Prepare metadata for database logging
      metadata = {
        errorMessage: error.message,
        errorStack: error.stack
      };
    } else {
      console.error(error);

      // Prepare metadata for database logging
      metadata = { error: String(error) };
    }
  }

  // Log to database
  logToDatabase(LogLevel.ERROR, context, message, metadata);
}

/**
 * Log an API request
 */
export function logApiRequest(method: string, url: string, params?: any): void {
  logInfo('API', `${method} ${url}`, params);
}

/**
 * Log an API response
 */
export function logApiResponse(method: string, url: string, status: number, data?: any): void {
  logInfo('API', `${method} ${url} - ${status}`, data);
}

/**
 * Log a database query
 */
export function logDbQuery(query: string, params?: any): void {
  logDebug('DB', `Executing query: ${query}`, params);
}

/**
 * Log a database error
 */
export function logDbError(operation: string, error: unknown): void {
  logError('DB', `Error during ${operation}`, error);
}

/**
 * Log application startup
 */
export function logAppStartup(): void {
  logInfo('APP', `Application starting with log level: ${currentLogLevel}`);
}

/**
 * Log application shutdown
 */
export function logAppShutdown(): void {
  logInfo('APP', 'Application shutting down');
}
