/**
 * Analytics Heartbeat API Endpoint
 * 
 * This endpoint handles heartbeat requests to track live visitors.
 * Updates the last_seen_at timestamp for active sessions.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logError } from '@/lib/logging';

/**
 * POST /api/analytics/heartbeat
 * Update last_seen_at timestamp for a session to indicate user is still active
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, timestamp } = body;

    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabaseClient();

    // Update the last_seen_at timestamp and ensure is_active is true
    const { error } = await supabase
      .from('session_analytics')
      .update({
        last_seen_at: timestamp || new Date().toISOString(),
        is_active: true,
        updated_at: new Date().toISOString(),
      })
      .eq('session_id', sessionId);

    if (error) {
      logError('HeartbeatAPI', 'Failed to update session heartbeat', {
        error,
        sessionId
      });
      return NextResponse.json(
        { success: false, error: 'Failed to update heartbeat' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      timestamp: timestamp || new Date().toISOString()
    });

  } catch (error) {
    logError('HeartbeatAPI', 'Unexpected error in heartbeat endpoint', { error });
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/analytics/heartbeat
 * Get heartbeat status (for debugging)
 */
export async function GET() {
  return NextResponse.json({
    endpoint: 'Analytics Heartbeat API',
    description: 'Updates last_seen_at timestamp for live visitor tracking',
    methods: ['POST'],
    version: '1.0.0'
  });
}
