/**
 * TestAddressGenerator Component
 *
 * This component generates a test email address for deliverability testing.
 */

'use client';

import { useState } from 'react';

interface TestAddressGeneratorProps {
  testAddress: string | null;
  isGenerating: boolean;
  onGenerate: () => void;
  onCopyAddress?: () => void; // Callback when address is copied
}

export default function TestAddressGenerator({
  testAddress,
  isGenerating,
  onGenerate,
  onCopyAddress
}: TestAddressGeneratorProps) {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopyAddress = async () => {
    if (testAddress) {
      try {
        await navigator.clipboard.writeText(testAddress);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);

        // Trigger the callback to start auto-refresh
        if (onCopyAddress) {
          onCopyAddress();
        }
      } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        // Fallback: still trigger the callback even if clipboard fails
        if (onCopyAddress) {
          onCopyAddress();
        }
      }
    }
  };

  return (
    <div>
      {!testAddress ? (
        <div className="text-center px-4">
          <div className="mb-6 sm:mb-8">
            {/* Clean Outline Icon */}
            <div className="inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 rounded-3xl mb-4 sm:mb-6 cursor-pointer"
                 style={{
                   backgroundColor: '#fbfaf8',
                   border: '2px solid #f3ece8'
                 }}
                 onClick={onGenerate}>
              <svg className="w-12 h-12 sm:w-16 sm:h-16"
                   fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1.5"
                   style={{ color: '#ce601c' }}>
                <path strokeLinecap="round" strokeLinejoin="round"
                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>

            {/* Enhanced Typography with Better Hierarchy */}
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-xl sm:text-2xl font-bold" style={{ color: '#1b130e' }}>
                Email Tester
              </h3>


              {/* Feature Highlights */}
              <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mt-4 sm:mt-6 px-2">
                <div className="flex items-center space-x-1.5 sm:space-x-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-xl" style={{ backgroundColor: '#f3ece8' }}>
                  <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full" style={{ backgroundColor: '#66b077' }}></div>
                  <span className="text-xs sm:text-sm font-medium" style={{ color: '#4a3728' }}>SPF Analysis</span>
                </div>
                <div className="flex items-center space-x-1.5 sm:space-x-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-xl" style={{ backgroundColor: '#f3ece8' }}>
                  <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full" style={{ backgroundColor: '#956b50' }}></div>
                  <span className="text-xs sm:text-sm font-medium" style={{ color: '#4a3728' }}>DKIM Validation</span>
                </div>
                <div className="flex items-center space-x-1.5 sm:space-x-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-xl" style={{ backgroundColor: '#f3ece8' }}>
                  <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full" style={{ backgroundColor: '#f59e0b' }}></div>
                  <span className="text-xs sm:text-sm font-medium" style={{ color: '#4a3728' }}>DMARC Policy</span>
                </div>
                <div className="flex items-center space-x-1.5 sm:space-x-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-xl" style={{ backgroundColor: '#f3ece8' }}>
                  <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full" style={{ backgroundColor: '#1b130e' }}></div>
                  <span className="text-xs sm:text-sm font-medium" style={{ color: '#4a3728' }}>PTR Records</span>
                </div>
                <div className="flex items-center space-x-1.5 sm:space-x-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-xl" style={{ backgroundColor: '#f3ece8' }}>
                  <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full" style={{ backgroundColor: '#956b50' }}></div>
                  <span className="text-xs sm:text-sm font-medium" style={{ color: '#4a3728' }}>MX Records</span>
                </div>
              </div>
            </div>
          </div>
          <button
            onClick={onGenerate}
            disabled={isGenerating}
            className="border border-[#ce601c] text-[#ce601c] bg-transparent py-3 px-6 rounded-xl hover:bg-[#ce601c] hover:text-white hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center font-semibold text-base mx-auto"
          >
            <div className="flex items-center justify-center">
              {isGenerating ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Generating Address...</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2"
                       fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2.5">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span>Generate Test Address</span>
                </>
              )}
            </div>
          </button>

          {/* Call-to-Action Helper Text */}
          <div className="mt-4 sm:mt-6 flex items-center justify-center space-x-2 text-xs sm:text-sm px-4" style={{ color: '#4a3728' }}>
            <svg className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-center">Click the icon above or this button to get started</span>
          </div>
        </div>
      ) : (
        <div className="space-y-4 sm:space-y-6 px-4">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-14 h-14 sm:w-16 sm:h-16 rounded-2xl mb-3 sm:mb-4" style={{ backgroundColor: '#66b077' }}>
              <svg className="w-7 h-7 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="space-y-2">
              <p className="text-base sm:text-lg leading-relaxed font-medium px-2" style={{ color: '#4a3728' }}>
                Your test email address has been generated successfully!
              </p>
              <p className="text-sm sm:text-base leading-normal px-2" style={{ color: '#4a3728' }}>
                Use this address to send a test email from your server.
              </p>
            </div>
          </div>

          <div className="rounded-2xl p-4 sm:p-6" style={{ backgroundColor: '#f3ece8', border: '1px solid #f3ece8' }}>
            {/* Mobile Layout: Stacked */}
            <div className="flex flex-col gap-3 sm:hidden">
              <div className="flex-1 relative">
                <div className="p-3 rounded-xl font-mono break-all text-sm font-medium" style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8', color: '#1b130e' }}>
                  {testAddress}
                </div>
              </div>
              <button
                onClick={handleCopyAddress}
                className="group p-3 rounded-xl transition-all duration-200 font-medium w-full"
                style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8', color: '#1b130e' }}
              >
                <div className="flex items-center justify-center">
                  {isCopied ? (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#66b077' }}>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="text-sm">Copied!</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      <span className="text-sm">Copy</span>
                    </>
                  )}
                </div>
              </button>
            </div>

            {/* Desktop/Tablet Layout: Inline */}
            <div className="hidden sm:flex items-center gap-3">
              <div className="flex-1">
                <div className="p-3 rounded-xl font-mono break-all text-base font-medium" style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8', color: '#1b130e' }}>
                  {testAddress}
                </div>
              </div>
              <button
                onClick={handleCopyAddress}
                className="group p-3 rounded-xl transition-all duration-200 font-medium flex-shrink-0"
                style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8', color: '#1b130e' }}
              >
                <div className="flex items-center">
                  {isCopied ? (
                    <>
                      <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#66b077' }}>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="text-sm">Copied!</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-1.5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      <span className="text-sm">Copy</span>
                    </>
                  )}
                </div>
              </button>
            </div>
          </div>

          <div className="flex items-center justify-center space-x-2 text-xs sm:text-sm px-4" style={{ color: '#4a3728' }}>
            <svg className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-center">This address will be active for 24 hours</span>
          </div>
        </div>
      )}
    </div>
  );
}
