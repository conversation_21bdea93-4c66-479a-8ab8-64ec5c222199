/**
 * Type declarations for mailauth library
 * Since mailauth doesn't provide official TypeScript declarations,
 * we define the basic types we use in our application.
 */

declare module 'mailauth' {
  export interface AuthenticateOptions {
    ip?: string;
    helo?: string;
    mta?: string;
    sender?: string;
    headers?: string;
    body?: string;
  }

  export interface AuthenticationResult {
    spf?: {
      status?: string;
      domain?: string;
      explanation?: string;
      mechanism?: string;
      qualifier?: string;
      info?: string;
    };
    dkim?: {
      status?: {
        result?: string;
        header?: {
          d?: string;
          s?: string;
          a?: string;
        };
      };
      results?: Array<{
        status?: {
          result?: string;
          header?: {
            d?: string;
            s?: string;
            a?: string;
          };
        };
        signingDomain?: string;
        selector?: string;
        algo?: string;
        modulusLength?: number;
        bodyHash?: string;
        bodyHashExpecting?: string;
      }>;
      canonicalization?: string;
      info?: string;
      domain?: string;
      selector?: string;
      algorithm?: string;
      error?: string;
    };
    dmarc?: {
      status?: string;
      domain?: string;
      policy?: string;
      disposition?: string;
      alignment?: {
        spf?: string;
        dkim?: string;
      };
      info?: string;
    };
    arc?: {
      status?: {
        result?: string;
      };
      i?: number;
      authResults?: string;
      info?: string;
    };
    bimi?: {
      status?: {
        result?: string;
        header?: any;
      };
      location?: string;
      authority?: any;
      info?: string;
    };
    receivedChain?: Array<{
      ip?: string;
      hostname?: string;
      helo?: string;
    }>;
    helo?: string;
    dkimResults?: any[];
  }

  export function authenticate(
    message: string | Buffer,
    options?: AuthenticateOptions
  ): Promise<AuthenticationResult>;
}
