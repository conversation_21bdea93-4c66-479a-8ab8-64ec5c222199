/**
 * DetailedResults Component
 *
 * This component displays detailed results of the email deliverability test.
 */

'use client';

import { useState } from 'react';

interface DetailedResultsProps {
  result: any;
}

interface CollapsibleSectionProps {
  title: string;
  status: string;
  statusColor: string;
  icon: React.ReactNode;
  summary: React.ReactNode;
  children: React.ReactNode;
  defaultExpanded?: boolean;
}

function CollapsibleSection({
  title,
  status,
  statusColor,
  icon,
  summary,
  children,
  defaultExpanded = false
}: CollapsibleSectionProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <div className="bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl transition-all duration-300 hover:bg-white/80">
      {/* Collapsible Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-3 py-2 sm:px-4 sm:py-3 text-left transition-all duration-200 hover:bg-black/5 focus:outline-none focus:ring-0"
        style={{
          backgroundColor: 'transparent'
        }}
        aria-expanded={isExpanded}
        aria-label={`${isExpanded ? 'Collapse' : 'Expand'} ${title} details`}
      >
        {/* Single Line Layout for Collapsed State */}
        <div className="flex items-center justify-between w-full gap-2">
          {/* Left Side: Icon + Title + Preview + Arrow */}
          <div className="flex items-center space-x-1.5 sm:space-x-2 flex-1 min-w-0">
            {/* Protocol Icon */}
            <div className={`flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 rounded-lg shadow-sm flex-shrink-0`} style={{
              backgroundColor: statusColor
            }}>
              <div className="scale-50 sm:scale-75">
                {icon}
              </div>
            </div>

            {/* Protocol Title */}
            <h4 className="text-sm sm:text-base font-bold flex-shrink-0" style={{ color: '#1b130e' }}>
              {title}
            </h4>

            {/* Preview Text (only in collapsed state) */}
            {!isExpanded && (
              <>
                <span className="text-sm flex-shrink-0 hidden sm:inline" style={{ color: '#4a3728' }}>-</span>
                <div className="text-xs sm:text-sm truncate flex-1 min-w-0" style={{ color: '#4a3728' }}>
                  {summary}
                </div>
              </>
            )}

            {/* Expand/Collapse Arrow */}
            <div className="flex items-center flex-shrink-0 ml-1">
              <svg
                className={`w-3 h-3 sm:w-4 sm:h-4 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                style={{ color: '#4a3728' }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>

          {/* Right Side: Status Badge */}
          <div className="flex items-center flex-shrink-0">
            <span className={`inline-flex items-center px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-lg text-xs font-bold shadow-sm`} style={{
              backgroundColor: statusColor,
              color: '#ffffff',
              border: `1px solid ${statusColor}`
            }}>
              {status}
            </span>
          </div>
        </div>
      </button>

      {/* Expandable Content */}
      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isExpanded ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="px-4 pb-4 sm:px-6 sm:pb-6">
          <div className="text-base" style={{ color: '#4a3728' }}>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function DetailedResults({ result }: DetailedResultsProps) {
  return (
    <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-8 shadow-sm transition-all duration-300">
      <div className="space-y-10">
        {/* Authentication Details Section - Full Width Priority */}
        <div>
          <div className="flex items-center space-x-4 mb-8">
            <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-[#1b130e] to-[#4a3728] rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0">
              <svg className="w-6 h-6 sm:w-7 sm:h-7 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <div>
              <h3 className="text-2xl font-bold" style={{ color: '#1b130e' }}>Authentication Details</h3>
              <p style={{ color: '#4a3728' }}>Comprehensive analysis of email authentication protocols</p>
            </div>
          </div>

          <div className="space-y-3 sm:space-y-4">
            {/* SPF Section */}
            <CollapsibleSection
              title="SPF"
              status={result.spf_result === 'pass' ? 'Pass' :
                     result.spf_result === 'fail' ? 'Fail' :
                     result.spf_result === 'softfail' ? '⚠️ SoftFail' :
                     result.spf_result === 'neutral' ? '📋 Neutral' :
                     '🔍 ' + (result.spf_result || 'None')}
              statusColor={result.spf_result === 'pass' ? '#66b077' :
                          result.spf_result === 'fail' ? '#f59e0b' :
                          result.spf_result === 'softfail' ? '#f59e0b' :
                          '#4a3728'}
              icon={
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.586-3.414A2 2 0 0018 7.414V6a2 2 0 00-2-2H8a2 2 0 00-2 2v1.414a2 2 0 00.586 1.414L9 11.414V15a2 2 0 002 2h2a2 2 0 002-2v-3.586l2.414-2.414z" />
                </svg>
              }
              summary={
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <span className="truncate">
                    {result.analysis_json?.spf?.domain
                      ? `Domain: ${result.analysis_json.spf.domain}, Result: ${result.spf_result}`
                      : `Result: ${result.spf_result}`}
                  </span>
                  {result.analysis_json?.spf?.issues && result.analysis_json.spf.issues.length > 0 && (
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#d97706' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              }
            >

                {result.analysis_json?.spf && (
                  <div className="space-y-3">
                    {result.analysis_json.spf.record && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>SPF Record:</p>
                        <div className="text-sm font-mono p-3 rounded break-all" style={{ backgroundColor: '#f3ece8', color: '#1b130e' }}>
                          {result.analysis_json.spf.record}
                        </div>
                      </div>
                    )}

                    {result.analysis_json.spf.mechanisms && result.analysis_json.spf.mechanisms.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>
                          Mechanisms ({result.analysis_json.spf.mechanisms.length}):
                        </p>
                        <div className="space-y-2">
                          {result.analysis_json.spf.mechanisms.slice(0, 3).map((mechanism: any, index: number) => (
                            <div key={index} className="text-sm font-mono p-3 rounded break-all" style={{ backgroundColor: '#f3ece8' }}>
                              <span className={`inline-block w-7 text-center rounded text-xs font-medium ${
                                mechanism.qualifier === '+' ? 'bg-green-100 text-green-800' :
                                mechanism.qualifier === '-' ? 'bg-red-100 text-red-800' :
                                mechanism.qualifier === '~' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {mechanism.qualifier}
                              </span>
                              <span className="ml-2 break-words" style={{ color: '#1b130e' }}>{mechanism.type}</span>
                              {mechanism.value && <span className="break-words" style={{ color: '#4a3728' }}>:{mechanism.value}</span>}
                            </div>
                          ))}
                          {result.analysis_json.spf.mechanisms.length > 3 && (
                            <p className="text-sm" style={{ color: '#4a3728' }}>
                              +{result.analysis_json.spf.mechanisms.length - 3} more mechanisms
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    {result.analysis_json.spf.dnsLookups !== undefined && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm" style={{ color: '#4a3728' }}>DNS Lookups:</span>
                        <span className={`text-sm font-medium`} style={{
                          color: result.analysis_json.spf.dnsLookups > 10 ? '#f59e0b' :
                                 result.analysis_json.spf.dnsLookups > 8 ? '#f59e0b' :
                                 '#66b077'
                        }}>
                          {result.analysis_json.spf.dnsLookups}/10
                        </span>
                      </div>
                    )}

                    {/* SPF Authentication Comment */}
                    {(result.analysis_json.spf.result?.comment || result.analysis_json.spf.comment) && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>Authentication Details:</p>
                        <div className="text-sm p-3 rounded break-words" style={{ backgroundColor: '#f3ece8', color: '#4a3728' }}>
                          {(() => {
                            const comment = result.analysis_json.spf.result?.comment || result.analysis_json.spf.comment;
                            // Remove server prefix (e.g., "DESKTOP-KTQ2OST: ") and extract the meaningful part
                            const cleanComment = comment.replace(/^[^:]+:\s*/, '');
                            return cleanComment;
                          })()}
                        </div>
                      </div>
                    )}

                    {result.analysis_json.spf.issues && result.analysis_json.spf.issues.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#d97706' }}>Recommendations:</p>
                        <ul className="text-sm space-y-2" style={{ color: '#d97706' }}>
                          {result.analysis_json.spf.issues.slice(0, 2).map((issue: string, index: number) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="mt-0.5" style={{ color: '#d97706' }}>•</span>
                              <span>{issue}</span>
                            </li>
                          ))}
                          {result.analysis_json.spf.issues.length > 2 && (
                            <li style={{ color: '#4a3728' }}>
                              +{result.analysis_json.spf.issues.length - 2} more recommendations
                            </li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {!result.analysis_json?.spf && (
                  <p className="text-sm" style={{ color: '#4a3728' }}>
                    {result.analysis_json?.analysis?.spf?.details ||
                     `Result: ${result.spf_result}`}
                  </p>
                )}
            </CollapsibleSection>

            {/* DKIM Section */}
            <CollapsibleSection
              title="DKIM"
              status={result.dkim_result === 'pass' ? 'Pass' :
                     result.dkim_result === 'fail' ? 'Fail' :
                     '🔍 ' + (result.dkim_result || 'None')}
              statusColor={result.dkim_result === 'pass' ? '#66b077' : '#f59e0b'}
              icon={
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 1 21 9z" />
                </svg>
              }
              summary={
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <span className="truncate">
                    {result.analysis_json?.dkim?.domain
                      ? `Domain: ${result.analysis_json.dkim.domain}, Selector: ${result.analysis_json.dkim.selector || 'N/A'}`
                      : `Result: ${result.dkim_result}`}
                  </span>
                  {result.analysis_json?.dkim?.issues && result.analysis_json.dkim.issues.length > 0 && (
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#d97706' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              }
            >

                {result.analysis_json?.dkim && (
                  <div className="space-y-3">
                    {(result.analysis_json.dkim.domain || result.analysis_json.dkim.selector) && (
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                        {result.analysis_json.dkim.domain && (
                          <div className="break-words">
                            <span style={{ color: '#4a3728' }}>Domain:</span>
                            <span className="ml-1 font-mono break-all" style={{ color: '#1b130e' }}>{result.analysis_json.dkim.domain}</span>
                          </div>
                        )}
                        {result.analysis_json.dkim.selector && (
                          <div className="break-words">
                            <span style={{ color: '#4a3728' }}>Selector:</span>
                            <span className="ml-1 font-mono break-all" style={{ color: '#1b130e' }}>{result.analysis_json.dkim.selector}</span>
                          </div>
                        )}
                      </div>
                    )}

                    {result.analysis_json.dkim.dnsRecord && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>DKIM Public Key Record:</p>
                        <div className="text-sm font-mono p-3 rounded break-all max-h-24 overflow-y-auto" style={{ backgroundColor: '#f3ece8', color: '#1b130e' }}>
                          {result.analysis_json.dkim.dnsRecord}
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                      {result.analysis_json.dkim.keyStrength && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>Key Strength:</span>
                          <span className={`ml-1 font-medium`} style={{
                            color: result.analysis_json.dkim.keyStrength >= 2048 ? '#66b077' :
                                   result.analysis_json.dkim.keyStrength >= 1024 ? '#f59e0b' :
                                   '#f59e0b'
                          }}>
                            {result.analysis_json.dkim.keyStrength} bits
                          </span>
                        </div>
                      )}
                      {result.analysis_json.dkim.algorithm && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>Algorithm:</span>
                          <span className="ml-1 font-mono break-all" style={{ color: '#1b130e' }}>{result.analysis_json.dkim.algorithm}</span>
                        </div>
                      )}
                      {result.analysis_json.dkim.canonicalization && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>Canonicalization:</span>
                          <span className="ml-1 font-mono break-all" style={{ color: '#1b130e' }}>{result.analysis_json.dkim.canonicalization}</span>
                        </div>
                      )}
                      {result.analysis_json.dkim.keyType && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>Key Type:</span>
                          <span className="ml-1 font-mono break-all" style={{ color: '#1b130e' }}>{result.analysis_json.dkim.keyType}</span>
                        </div>
                      )}
                    </div>

                    {result.analysis_json.dkim.bodyHashMatch !== undefined && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm" style={{ color: '#4a3728' }}>Body Hash:</span>
                        <span className={`text-sm font-medium`} style={{
                          color: result.analysis_json.dkim.bodyHashMatch ? '#66b077' : '#f59e0b'
                        }}>
                          {result.analysis_json.dkim.bodyHashMatch ? 'Valid' : 'Invalid'}
                        </span>
                      </div>
                    )}

                    {result.analysis_json.dkim.issues && result.analysis_json.dkim.issues.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#d97706' }}>Recommendations:</p>
                        <ul className="text-sm space-y-2" style={{ color: '#d97706' }}>
                          {result.analysis_json.dkim.issues.slice(0, 2).map((issue: string, index: number) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="mt-0.5" style={{ color: '#d97706' }}>•</span>
                              <span>{issue}</span>
                            </li>
                          ))}
                          {result.analysis_json.dkim.issues.length > 2 && (
                            <li style={{ color: '#4a3728' }}>
                              +{result.analysis_json.dkim.issues.length - 2} more recommendations
                            </li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {!result.analysis_json?.dkim && (
                  <p className="text-sm" style={{ color: '#4a3728' }}>
                    {result.analysis_json?.analysis?.dkim?.details ||
                     `Result: ${result.dkim_result}`}
                  </p>
                )}
            </CollapsibleSection>

            {/* DMARC Section */}
            <CollapsibleSection
              title="DMARC"
              status={result.dmarc_result === 'pass' ? 'Pass' :
                     result.dmarc_result === 'fail' ? 'Fail' :
                     '🔍 ' + (result.dmarc_result || 'None')}
              statusColor={result.dmarc_result === 'pass' ? '#66b077' : '#f59e0b'}
              icon={
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              }
              summary={
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <span className="truncate">
                    {result.analysis_json?.dmarc?.policy
                      ? `Policy: ${result.analysis_json.dmarc.policy}, Coverage: ${result.analysis_json.dmarc.percentage || 100}%`
                      : `Result: ${result.dmarc_result}`}
                  </span>
                  {result.analysis_json?.dmarc?.issues && result.analysis_json.dmarc.issues.length > 0 && (
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#d97706' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              }
            >

                {result.analysis_json?.dmarc && (
                  <div className="space-y-3">
                    {result.analysis_json.dmarc.record && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>DMARC Record:</p>
                        <div className="text-sm font-mono p-3 rounded break-all" style={{ backgroundColor: '#f3ece8', color: '#1b130e' }}>
                          {result.analysis_json.dmarc.record}
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                      {result.analysis_json.dmarc.policy && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>Policy:</span>
                          <span className={`ml-1 font-medium`} style={{
                            color: result.analysis_json.dmarc.policy === 'reject' ? '#66b077' :
                                   result.analysis_json.dmarc.policy === 'quarantine' ? '#f59e0b' :
                                   '#f59e0b'
                          }}>
                            {result.analysis_json.dmarc.policy}
                          </span>
                        </div>
                      )}
                      {result.analysis_json.dmarc.percentage !== undefined && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>Coverage:</span>
                          <span className={`ml-1 font-medium`} style={{
                            color: result.analysis_json.dmarc.percentage === 100 ? '#66b077' : '#f59e0b'
                          }}>
                            {result.analysis_json.dmarc.percentage}%
                          </span>
                        </div>
                      )}
                      {result.analysis_json.dmarc.subdomainPolicy && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>Subdomain Policy:</span>
                          <span className="ml-1 font-mono break-all" style={{ color: '#1b130e' }}>{result.analysis_json.dmarc.subdomainPolicy}</span>
                        </div>
                      )}
                    </div>

                    {result.analysis_json.dmarc.alignmentMode && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>Alignment Mode:</p>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                          {result.analysis_json.dmarc.alignmentMode.spf && (
                            <div className="break-words">
                              <span style={{ color: '#4a3728' }}>SPF:</span>
                              <span className="ml-1 font-mono" style={{ color: '#1b130e' }}>
                                {result.analysis_json.dmarc.alignmentMode.spf === 'r' ? 'Relaxed' : 'Strict'}
                              </span>
                            </div>
                          )}
                          {result.analysis_json.dmarc.alignmentMode.dkim && (
                            <div className="break-words">
                              <span style={{ color: '#4a3728' }}>DKIM:</span>
                              <span className="ml-1 font-mono" style={{ color: '#1b130e' }}>
                                {result.analysis_json.dmarc.alignmentMode.dkim === 'r' ? 'Relaxed' : 'Strict'}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {result.analysis_json.dmarc.reportingEmails && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>Reporting:</p>
                        <div className="space-y-2">
                          {result.analysis_json.dmarc.reportingEmails.aggregate && (
                            <div className="text-sm">
                              <span style={{ color: '#4a3728' }}>Aggregate:</span>
                              <span className="ml-1 font-mono" style={{ color: '#956b50' }}>
                                {result.analysis_json.dmarc.reportingEmails.aggregate.slice(0, 2).join(', ')}
                                {result.analysis_json.dmarc.reportingEmails.aggregate.length > 2 &&
                                 ` +${result.analysis_json.dmarc.reportingEmails.aggregate.length - 2} more`}
                              </span>
                            </div>
                          )}
                          {result.analysis_json.dmarc.reportingEmails.forensic && (
                            <div className="text-sm">
                              <span style={{ color: '#4a3728' }}>Forensic:</span>
                              <span className="ml-1 font-mono" style={{ color: '#956b50' }}>
                                {result.analysis_json.dmarc.reportingEmails.forensic.slice(0, 2).join(', ')}
                                {result.analysis_json.dmarc.reportingEmails.forensic.length > 2 &&
                                 ` +${result.analysis_json.dmarc.reportingEmails.forensic.length - 2} more`}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {result.analysis_json.dmarc.issues && result.analysis_json.dmarc.issues.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#d97706' }}>Recommendations:</p>
                        <ul className="text-sm space-y-2" style={{ color: '#d97706' }}>
                          {result.analysis_json.dmarc.issues.slice(0, 2).map((issue: string, index: number) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="mt-0.5" style={{ color: '#d97706' }}>•</span>
                              <span>{issue}</span>
                            </li>
                          ))}
                          {result.analysis_json.dmarc.issues.length > 2 && (
                            <li style={{ color: '#4a3728' }}>
                              +{result.analysis_json.dmarc.issues.length - 2} more recommendations
                            </li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {!result.analysis_json?.dmarc && (
                  <p className="text-sm" style={{ color: '#4a3728' }}>
                    {result.analysis_json?.analysis?.dmarc?.details ||
                     `Result: ${result.dmarc_result}`}
                  </p>
                )}
            </CollapsibleSection>

            {/* MX Records Section */}
            <CollapsibleSection
              title="MX Records"
              status={result.mx_result === 'pass' ? 'Pass' :
                     result.mx_result === 'warning' ? '⚠️ Warning' :
                     result.mx_result === 'fail' ? 'Fail' :
                     result.mx_result === 'error' ? '🔍 Error' :
                     '📋 None'}
              statusColor={result.mx_result === 'pass' ? '#66b077' :
                          result.mx_result === 'warning' ? '#f59e0b' :
                          result.mx_result === 'fail' ? '#f59e0b' :
                          '#4a3728'}
              icon={
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                </svg>
              }
              summary={
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <span className="truncate">
                    {result.analysis_json?.mx?.totalRecords
                      ? `${result.analysis_json.mx.totalRecords} record${result.analysis_json.mx.totalRecords !== 1 ? 's' : ''} found, Status: ${result.mx_result}`
                      : `Result: ${result.mx_result || 'Not available'}`}
                  </span>
                  {result.analysis_json?.mx?.issues && result.analysis_json.mx.issues.length > 0 && (
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#d97706' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              }
            >

                {result.analysis_json?.mx && (
                  <div className="space-y-3">
                    <p className="text-sm" style={{ color: '#4a3728' }}>
                      {result.analysis_json.mx.info}
                    </p>

                    {result.analysis_json.mx.records && result.analysis_json.mx.records.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>
                          MX Records ({result.analysis_json.mx.totalRecords}):
                        </p>
                        <div className="space-y-2">
                          {result.analysis_json.mx.records.slice(0, 3).map((record: any, index: number) => (
                            <div key={index} className="text-sm font-mono p-3 rounded break-all" style={{ backgroundColor: '#f3ece8' }}>
                              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                                <span className="break-words font-medium" style={{ color: '#1b130e' }}>{record.exchange}</span>
                                <div className="flex items-center gap-2 flex-shrink-0">
                                  <span className="text-xs sm:text-sm" style={{ color: '#4a3728' }}>Priority: {record.priority}</span>
                                  <span className={`w-3 h-3 rounded-full flex-shrink-0`} style={{
                                    backgroundColor: record.resolved ? '#66b077' : '#f59e0b'
                                  }} title={record.resolved ? 'Resolved' : 'Failed to resolve'}></span>
                                </div>
                              </div>
                              {record.ipAddresses && record.ipAddresses.length > 0 && (
                                <div className="mt-2 break-words" style={{ color: '#4a3728' }}>
                                  → {record.ipAddresses.slice(0, 2).join(', ')}
                                  {record.ipAddresses.length > 2 && ` +${record.ipAddresses.length - 2} more`}
                                </div>
                              )}
                            </div>
                          ))}
                          {result.analysis_json.mx.records.length > 3 && (
                            <p className="text-sm" style={{ color: '#4a3728' }}>
                              +{result.analysis_json.mx.records.length - 3} more records
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    {result.analysis_json.mx.issues && result.analysis_json.mx.issues.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#d97706' }}>Recommendations:</p>
                        <ul className="text-sm space-y-2" style={{ color: '#d97706' }}>
                          {result.analysis_json.mx.issues.slice(0, 2).map((issue: string, index: number) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="mt-0.5" style={{ color: '#d97706' }}>•</span>
                              <span>{issue}</span>
                            </li>
                          ))}
                          {result.analysis_json.mx.issues.length > 2 && (
                            <li style={{ color: '#4a3728' }}>
                              +{result.analysis_json.mx.issues.length - 2} more recommendations
                            </li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {!result.analysis_json?.mx && (
                  <p className="text-sm" style={{ color: '#4a3728' }}>
                    Result: {result.mx_result || 'Not available'}
                  </p>
                )}
            </CollapsibleSection>

            {/* Reverse DNS Section */}
            <CollapsibleSection
              title="Reverse DNS"
              status={result.analysis_json?.reverseDns?.result === 'pass' ? 'Pass' :
                     result.analysis_json?.reverseDns?.result === 'warning' ? '⚠️ Warning' :
                     result.analysis_json?.reverseDns?.result === 'fail' ? 'Fail' :
                     result.analysis_json?.reverseDns?.result === 'error' ? '🔍 Error' :
                     '📋 None'}
              statusColor={result.analysis_json?.reverseDns?.result === 'pass' ? '#66b077' :
                          result.analysis_json?.reverseDns?.result === 'warning' ? '#f59e0b' :
                          result.analysis_json?.reverseDns?.result === 'fail' ? '#f59e0b' :
                          '#4a3728'}
              icon={
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                </svg>
              }
              summary={
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <span className="truncate">
                    {result.analysis_json?.reverseDns?.ipAddress
                      ? `IP: ${result.analysis_json.reverseDns.ipAddress}, Domain Match: ${result.analysis_json.reverseDns.domainMatch ? 'Yes' : 'No'}`
                      : 'Reverse DNS validation not available'}
                  </span>
                  {result.analysis_json?.reverseDns?.issues && result.analysis_json.reverseDns.issues.length > 0 && (
                    <svg className="w-4 h-4 flex-shrink-0" style={{ color: '#d97706' }} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              }
            >

                {result.analysis_json?.reverseDns && (
                  <div className="space-y-3">
                    <p className="text-sm" style={{ color: '#4a3728' }}>
                      {result.analysis_json.reverseDns.info}
                    </p>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                      {result.analysis_json.reverseDns.ipAddress && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>IP Address:</span>
                          <span className="ml-1 font-mono break-all" style={{ color: '#1b130e' }}>{result.analysis_json.reverseDns.ipAddress}</span>
                        </div>
                      )}
                      {result.analysis_json.reverseDns.hostname && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>Hostname:</span>
                          <span className="ml-1 font-mono break-all" style={{ color: '#1b130e' }}>{result.analysis_json.reverseDns.hostname}</span>
                        </div>
                      )}
                    </div>

                    {result.analysis_json.reverseDns.ptrRecord && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>PTR Record:</p>
                        <div className="text-sm font-mono p-3 rounded break-all" style={{ backgroundColor: '#f3ece8', color: '#1b130e' }}>
                          {result.analysis_json.reverseDns.ptrRecord}
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                      {result.analysis_json.reverseDns.domainMatch !== undefined && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>Domain Match:</span>
                          <span className={`ml-1 font-medium`} style={{
                            color: result.analysis_json.reverseDns.domainMatch ? '#66b077' : '#f59e0b'
                          }}>
                            {result.analysis_json.reverseDns.domainMatch ? 'Yes' : 'No'}
                          </span>
                        </div>
                      )}
                      {result.analysis_json.reverseDns.senderDomain && (
                        <div className="break-words">
                          <span style={{ color: '#4a3728' }}>Sender Domain:</span>
                          <span className="ml-1 font-mono break-all" style={{ color: '#1b130e' }}>{result.analysis_json.reverseDns.senderDomain}</span>
                        </div>
                      )}
                    </div>

                    {result.analysis_json.reverseDns.issues && result.analysis_json.reverseDns.issues.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#d97706' }}>Recommendations:</p>
                        <ul className="text-sm space-y-2" style={{ color: '#d97706' }}>
                          {result.analysis_json.reverseDns.issues.slice(0, 2).map((issue: string, index: number) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="mt-0.5" style={{ color: '#d97706' }}>•</span>
                              <span>{issue}</span>
                            </li>
                          ))}
                          {result.analysis_json.reverseDns.issues.length > 2 && (
                            <li style={{ color: '#4a3728' }}>
                              +{result.analysis_json.reverseDns.issues.length - 2} more recommendations
                            </li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {!result.analysis_json?.reverseDns && (
                  <p className="text-sm" style={{ color: '#4a3728' }}>
                    Reverse DNS validation not available
                  </p>
                )}
            </CollapsibleSection>

            {/* ARC Results Section */}
            {result.analysis_json?.arc && result.analysis_json.arc.result !== 'none' && (
              <CollapsibleSection
                title="ARC (Forwarding Chain)"
                status={result.analysis_json.arc.result === 'pass' ? 'Pass' :
                       result.analysis_json.arc.result === 'fail' ? 'Fail' :
                       '🔍 ' + (result.analysis_json.arc.result || 'None')}
                statusColor={result.analysis_json.arc.result === 'pass' ? '#66b077' :
                            result.analysis_json.arc.result === 'fail' ? '#f59e0b' :
                            '#4a3728'}
                icon={
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                }
                summary={
                  result.analysis_json.arc.instance > 0
                    ? `${result.analysis_json.arc.instance} forwarding hop${result.analysis_json.arc.instance !== 1 ? 's' : ''}, Result: ${result.analysis_json.arc.result}`
                    : `Result: ${result.analysis_json.arc.result}`
                }
              >

                  <div className="space-y-3">
                    {result.analysis_json.arc.instance > 0 && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm" style={{ color: '#4a3728' }}>Forwarding Hops:</span>
                        <span className="text-sm font-medium" style={{ color: '#956b50' }}>
                          {result.analysis_json.arc.instance}
                        </span>
                      </div>
                    )}

                    {result.analysis_json.arc.authResults && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>Authentication Results:</p>
                        <div className="text-sm font-mono p-3 rounded break-all max-h-20 overflow-y-auto" style={{ backgroundColor: '#f3ece8', color: '#1b130e' }}>
                          {result.analysis_json.arc.authResults}
                        </div>
                      </div>
                    )}

                    <p className="text-sm" style={{ color: '#4a3728' }}>
                      {result.analysis_json.arc.info || 'ARC validation preserves authentication through email forwarding'}
                    </p>
                  </div>
              </CollapsibleSection>
            )}

            {/* BIMI Results Section - Temporarily Hidden */}
            {/* BIMI protocol display is temporarily hidden while keeping backend processing intact */}
            {false && result.analysis_json?.bimi && result.analysis_json.bimi.result !== 'none' && (
              <CollapsibleSection
                title="BIMI (Brand Indicators)"
                status={result.analysis_json.bimi.result === 'pass' ? 'Pass' :
                       result.analysis_json.bimi.result === 'fail' ? 'Fail' :
                       '🔍 ' + (result.analysis_json.bimi.result || 'None')}
                statusColor={result.analysis_json.bimi.result === 'pass' ? '#66b077' :
                            result.analysis_json.bimi.result === 'fail' ? '#f59e0b' :
                            '#4a3728'}
                icon={
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                }
                summary={
                  result.analysis_json.bimi.location
                    ? `Logo available, Authority: ${result.analysis_json.bimi.authority ? 'Verified' : 'Not verified'}`
                    : `Result: ${result.analysis_json.bimi.result}`
                }
              >

                  <div className="space-y-3">
                    {result.analysis_json.bimi.location && (
                      <div>
                        <p className="text-sm font-medium mb-2" style={{ color: '#1b130e' }}>Logo Location:</p>
                        <div className="text-sm font-mono p-3 rounded break-all" style={{ backgroundColor: '#f3ece8', color: '#1b130e' }}>
                          {result.analysis_json.bimi.location}
                        </div>
                      </div>
                    )}

                    {result.analysis_json.bimi.authority && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm" style={{ color: '#4a3728' }}>VMC Authority:</span>
                        <span className="text-sm font-medium" style={{ color: '#66b077' }}>
                          Verified
                        </span>
                      </div>
                    )}

                    <p className="text-sm" style={{ color: '#4a3728' }}>
                      {result.analysis_json.bimi.info || 'BIMI enables brand logo display in supported email clients'}
                    </p>
                  </div>
              </CollapsibleSection>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
