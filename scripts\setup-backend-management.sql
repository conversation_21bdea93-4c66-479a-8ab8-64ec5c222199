-- Setup script for Fademail Backend Management System
-- This script creates the necessary tables for configuration, domain management, and logging

-- App configuration table
CREATE TABLE IF NOT EXISTS app_config (
  key VARCHAR(50) PRIMARY KEY,
  value JSONB NOT NULL,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Domain configuration table
CREATE TABLE IF NOT EXISTS domain_config (
  domain VARCHAR(100) PRIMARY KEY,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  settings JSONB NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Ad configuration table
CREATE TABLE IF NOT EXISTS ad_config (
  placement_id VARCHAR(50) NOT NULL,
  domain VARCHAR(100) NOT NULL,
  ad_unit_id VARCHAR(100) NOT NULL,
  is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  device_types JSONB NOT NULL,
  display_options JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (placement_id, domain)
);

-- System logs table
CREATE TABLE IF NOT EXISTS system_logs (
  id SERIAL PRIMARY KEY,
  level VARCHAR(20) NOT NULL,
  category VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  metadata JSONB,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_category ON system_logs(category);
CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp);

-- Insert default configuration values
INSERT INTO app_config (key, value)
VALUES 
  ('emailExpirationMinutes', '30'::jsonb),
  ('autoRefreshInterval', '14'::jsonb),
  ('maxEmailsPerAddress', '100'::jsonb),
  ('maintenanceMode', 'false'::jsonb),
  ('cleanupIntervalMinutes', '15'::jsonb)
ON CONFLICT (key) 
DO UPDATE SET 
  value = EXCLUDED.value,
  updated_at = NOW();

-- Insert default domain configuration
INSERT INTO domain_config (domain, is_active, settings)
VALUES 
  ('fademail.site', TRUE, '{
    "isDefault": true,
    "weight": 100,
    "features": {
      "adsEnabled": true,
      "analyticsEnabled": true,
      "autoRefreshEnabled": true
    }
  }'::jsonb)
ON CONFLICT (domain) 
DO UPDATE SET 
  is_active = EXCLUDED.is_active,
  settings = EXCLUDED.settings,
  updated_at = NOW();

-- Insert default ad placements
INSERT INTO ad_config (placement_id, domain, ad_unit_id, is_enabled, device_types)
VALUES 
  ('sidebar-top', 'fademail.site', 'ca-pub-XXXXXXXXXXXXXXXX', TRUE, '["desktop", "tablet"]'::jsonb),
  ('inbox-bottom', 'fademail.site', 'ca-pub-XXXXXXXXXXXXXXXX', TRUE, '["desktop", "tablet", "mobile"]'::jsonb),
  ('email-view-right', 'fademail.site', 'ca-pub-XXXXXXXXXXXXXXXX', TRUE, '["desktop"]'::jsonb)
ON CONFLICT (placement_id, domain) 
DO UPDATE SET 
  ad_unit_id = EXCLUDED.ad_unit_id,
  is_enabled = EXCLUDED.is_enabled,
  device_types = EXCLUDED.device_types,
  updated_at = NOW();
