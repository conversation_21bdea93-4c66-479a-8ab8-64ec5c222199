/**
 * API Route for Email Tester Address Generation
 *
 * This route generates a new test email address for email testing.
 *
 * POST /api/tools/email-tester
 */

import { NextRequest, NextResponse } from 'next/server';
import { generateEmailTesterAddress } from '@/lib/tools/email-tester/database';
import { logError } from '@/lib/logging';

export async function POST(request: NextRequest) {
  try {
    // Generate a new test email address
    const testAddress = await generateEmailTesterAddress();

    return NextResponse.json({
      success: true,
      id: testAddress.id,
      testAddress: testAddress.testAddress,
      expirationDate: testAddress.expirationDate
    });
  } catch (error) {
    logError('API', 'Failed to generate test email address', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate test email address' },
      { status: 500 }
    );
  }
}
