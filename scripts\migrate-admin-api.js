/**
 * <PERSON><PERSON>t to migrate admin API routes from /api/admin to /api/management-portal-x7z9y2
 */
const fs = require('fs');
const path = require('path');

// Configuration
const OLD_API_PATH = 'src/app/api/admin';
const NEW_API_PATH = 'src/app/api/management-portal-x7z9y2';
const OLD_PATH_PATTERN = /\/api\/admin\//g;
const NEW_PATH_REPLACEMENT = '/api/management-portal-x7z9y2/';
const OLD_COMMENT_PATTERN = /\/\*\*\n \* (GET|POST|PUT|DELETE) \/api\/admin\//g;
const NEW_COMMENT_REPLACEMENT = '/**\n * $1 /api/management-portal-x7z9y2/';

// Create the new API directory if it doesn't exist
if (!fs.existsSync(NEW_API_PATH)) {
  fs.mkdirSync(NEW_API_PATH, { recursive: true });
}

/**
 * Copy a directory recursively
 * @param {string} source Source directory
 * @param {string} destination Destination directory
 */
function copyDirectory(source, destination) {
  // Create the destination directory if it doesn't exist
  if (!fs.existsSync(destination)) {
    fs.mkdirSync(destination, { recursive: true });
  }

  // Get all files and directories in the source directory
  const entries = fs.readdirSync(source, { withFileTypes: true });

  // Process each entry
  for (const entry of entries) {
    const sourcePath = path.join(source, entry.name);
    const destinationPath = path.join(destination, entry.name);

    if (entry.isDirectory()) {
      // Recursively copy subdirectories
      copyDirectory(sourcePath, destinationPath);
    } else {
      // Read the file content
      let content = fs.readFileSync(sourcePath, 'utf8');

      // Replace references to the old API path
      content = content.replace(OLD_PATH_PATTERN, NEW_PATH_REPLACEMENT);
      content = content.replace(OLD_COMMENT_PATTERN, NEW_COMMENT_REPLACEMENT);

      // Write the updated content to the destination file
      fs.writeFileSync(destinationPath, content);
      console.log(`Copied and updated: ${destinationPath}`);
    }
  }
}

// Copy the admin API directory
console.log(`Copying API routes from ${OLD_API_PATH} to ${NEW_API_PATH}...`);
copyDirectory(OLD_API_PATH, NEW_API_PATH);

console.log('Migration completed successfully!');
