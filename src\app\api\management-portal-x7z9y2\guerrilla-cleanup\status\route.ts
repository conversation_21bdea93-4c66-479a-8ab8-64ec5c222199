/**
 * Guerrilla Email Cleanup Status API
 * 
 * Provides status information for the Guerrilla email cleanup system
 */

import { NextRequest, NextResponse } from 'next/server';
import { isGuerrillaCleanupSchedulerRunning } from '@/lib/cleanup/guerrillaCleanupScheduler';
import { getConfig } from '@/lib/config/configService';
import { logError } from '@/lib/logging';

export async function GET(request: NextRequest) {
  try {
    const running = isGuerrillaCleanupSchedulerRunning();
    const intervalMinutes = await getConfig('guerrillaCleanupIntervalMinutes');
    const autoStart = await getConfig('guerrillaCleanupAutoStart');
    const lastRunTime = await getConfig('guerrillaCleanupLastRunTime');
    const schedulerStartTime = await getConfig('guerrillaCleanupSchedulerStartTime');
    const ageThresholdHours = await getConfig('guerrillaCleanupAgeThresholdHours');
    const cleanupMode = await getConfig('guerrillaCleanupMode');
    
    // Calculate next run time if running
    let nextRunAt = null;
    if (running && intervalMinutes && schedulerStartTime) {
      const startTime = new Date(schedulerStartTime).getTime();
      const intervalMs = intervalMinutes * 60 * 1000;
      const now = Date.now();
      
      // Calculate how many intervals have passed since start
      const elapsedTime = now - startTime;
      const intervalsPassed = Math.floor(elapsedTime / intervalMs);
      
      // Calculate the next run time based on the scheduler start time and intervals
      const nextRunTime = startTime + ((intervalsPassed + 1) * intervalMs);
      nextRunAt = new Date(nextRunTime).toISOString();
    }
    
    return NextResponse.json({
      success: true,
      status: {
        running,
        intervalMinutes: intervalMinutes || 60, // Default: 60 minutes
        nextRunAt,
        autoStart: autoStart !== false, // Default to true if not set
        ageThresholdHours: ageThresholdHours || 24, // Default: 24 hours
        cleanupMode: cleanupMode || 'manual', // Default: manual mode
        lastRunTime
      }
    });
  } catch (error) {
    logError('guerrilla-cleanup', 'Error getting status', { error });
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}
