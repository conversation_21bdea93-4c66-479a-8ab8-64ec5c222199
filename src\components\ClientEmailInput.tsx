'use client';

import { useState, useEffect } from 'react';

interface ClientEmailInputProps {
  emailAddress: string;
}

/**
 * ClientEmailInput Component
 *
 * A client-side only component that displays the email address in an input field.
 * This component is loaded dynamically with no SSR to avoid hydration issues.
 *
 * @param {string} props.emailAddress - The email address to display
 * @returns {JSX.Element} The rendered component
 */
export default function ClientEmailInput({ emailAddress }: ClientEmailInputProps) {
  // Use state to handle client-side rendering only
  const [mounted, setMounted] = useState(false);

  // Only show the component after it's mounted on the client
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a placeholder during SSR and initial client render
    return (
      <div className="w-full px-4 border border-neutral-200 rounded-md bg-neutral-50 h-[56px] animate-pulse"></div>
    );
  }

  return (
    <div className="relative">
      <input
        type="text"
        value={emailAddress}
        readOnly
        placeholder="Your temporary email will appear here"
        className="w-full px-4 border border-neutral-200 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent shadow-sm font-medium text-neutral-700 bg-neutral-50 text-base h-[56px]"
        aria-label="Your temporary email address"
      />
      {emailAddress && (
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <div className="h-2 w-2 rounded-full bg-success-500 animate-pulse"></div>
        </div>
      )}
    </div>
  );
}
