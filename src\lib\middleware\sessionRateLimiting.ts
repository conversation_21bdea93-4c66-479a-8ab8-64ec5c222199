/**
 * Session-Based Rate Limiting System
 * Prevents IP rotation bypass by tracking limits per session ID
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { getRateLimitConfig } from '@/lib/config/sessionConfig';

// Get Supabase client instance
async function getSupabaseClient() {
  return await createServerSupabaseClient();
}

interface SessionRateLimit {
  sessionId: string;
  endpoint: string;
  requestCount: number;
  windowStart: Date;
  lastRequest: Date;
}

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  blockDuration?: number; // Optional block duration in ms
}

// Default session-based rate limits
const SESSION_RATE_LIMITS: Record<string, RateLimitConfig> = {
  emailGeneration: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 8, // Max 8 emails per session per hour
    blockDuration: 60 * 60 * 1000 // Block for 1 hour if exceeded
  },

  dailyLimit: {
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    maxRequests: 50, // Max 50 emails per session per day
    blockDuration: 24 * 60 * 60 * 1000 // Block for 24 hours if exceeded
  }
};

/**
 * Check if a session has exceeded rate limits
 */
export async function checkSessionRateLimit(
  sessionId: string,
  endpoint: string,
  config?: RateLimitConfig
): Promise<{
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  blocked?: boolean;
  blockExpires?: Date;
}> {
  try {
    // Use dynamic configuration if available, fallback to hardcoded values
    let rateLimitConfig = config;

    if (!rateLimitConfig) {
      try {
        // Get dynamic rate limit configuration
        const dynamicConfig = await getRateLimitConfig();

        // Convert dynamic config to session rate limit format
        if (endpoint === 'emailGeneration' && dynamicConfig.emailGeneration) {
          // Check if the feature is disabled in the database
          if (!dynamicConfig.emailGeneration.enabled) {
            return {
              allowed: true,
              remaining: 999,
              resetTime: new Date(Date.now() + 60 * 60 * 1000)
            };
          }

          rateLimitConfig = {
            windowMs: dynamicConfig.emailGeneration.sessionLimits.windowMs,
            maxRequests: dynamicConfig.emailGeneration.sessionLimits.maxRequests,
            blockDuration: dynamicConfig.emailGeneration.sessionLimits.blockDuration
          };
        }
      } catch (error) {
        console.warn('Failed to get dynamic session rate limit config, using fallback:', error);
      }
    }

    // Final fallback to hardcoded values
    if (!rateLimitConfig) {
      rateLimitConfig = SESSION_RATE_LIMITS[endpoint];
    }

    if (!rateLimitConfig) {
      return {
        allowed: true,
        remaining: 999,
        resetTime: new Date(Date.now() + 60 * 60 * 1000)
      };
    }

    // If maxRequests is 0, treat as disabled (allow unlimited requests)
    if (rateLimitConfig.maxRequests === 0) {
      return {
        allowed: true,
        remaining: 999,
        resetTime: new Date(Date.now() + rateLimitConfig.windowMs)
      };
    }

    const now = new Date();
    const windowStart = new Date(now.getTime() - rateLimitConfig.windowMs);

    // Calculate reset time based on the oldest request in the window (sliding window)
    // If no requests exist, reset time is now + windowMs

    // Check if session is currently blocked
    const blockCheck = await checkSessionBlock(sessionId, endpoint);
    if (blockCheck.blocked) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: blockCheck.blockExpires!,
        blocked: true,
        blockExpires: blockCheck.blockExpires
      };
    }

    // Count requests in current window
    const supabase = await getSupabaseClient();
    const { data: requests, error } = await supabase
      .from('session_rate_limits')
      .select('*')
      .eq('session_id', sessionId)
      .eq('endpoint', endpoint)
      .gte('created_at', windowStart.toISOString())
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error checking session rate limit:', error);
      return {
        allowed: true,
        remaining: rateLimitConfig.maxRequests,
        resetTime: new Date(now.getTime() + rateLimitConfig.windowMs)
      };
    }

    const requestCount = requests?.length || 0;
    // Include current request in the count (consistent with IP rate limiting)
    const totalCount = requestCount + 1;
    const remaining = Math.max(0, rateLimitConfig.maxRequests - totalCount);

    // Calculate reset time: when the oldest request in the window will expire
    // For sliding window, this is the oldest request time + windowMs
    const resetTime = requests && requests.length > 0 && requests[requests.length - 1].created_at
      ? new Date(new Date(requests[requests.length - 1].created_at!).getTime() + rateLimitConfig.windowMs)
      : new Date(now.getTime() + rateLimitConfig.windowMs);

    if (totalCount > rateLimitConfig.maxRequests) {
      // Block the session if configured
      if (rateLimitConfig.blockDuration) {
        await blockSession(sessionId, endpoint, rateLimitConfig.blockDuration);
      }

      return {
        allowed: false,
        remaining: 0,
        resetTime,
        blocked: !!rateLimitConfig.blockDuration,
        blockExpires: rateLimitConfig.blockDuration 
          ? new Date(now.getTime() + rateLimitConfig.blockDuration)
          : undefined
      };
    }

    return {
      allowed: true,
      remaining,
      resetTime
    };
  } catch (error) {
    console.error('Error in checkSessionRateLimit:', error);
    return {
      allowed: true,
      remaining: 999,
      resetTime: new Date(Date.now() + 60 * 60 * 1000)
    };
  }
}

/**
 * Record a request for session rate limiting
 */
export async function recordSessionRequest(
  sessionId: string,
  endpoint: string,
  metadata?: any
): Promise<boolean> {
  try {
    const supabase = await getSupabaseClient();
    const { error } = await supabase
      .from('session_rate_limits')
      .insert({
        session_id: sessionId,
        endpoint,
        metadata: metadata || {},
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error recording session request:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in recordSessionRequest:', error);
    return false;
  }
}

/**
 * Check if a session is currently blocked
 */
async function checkSessionBlock(sessionId: string, endpoint: string): Promise<{
  blocked: boolean;
  blockExpires?: Date;
}> {
  try {
    const supabase = await getSupabaseClient();
    const { data: blocks, error } = await supabase
      .from('blocked_sessions')
      .select('*')
      .eq('session_id', sessionId)
      .eq('is_active', true)
      .or(`expires_at.is.null,expires_at.gt.${new Date().toISOString()}`);

    if (error || !blocks?.length) {
      return { blocked: false };
    }

    // Check for endpoint-specific or global blocks
    const relevantBlock = blocks.find((block: any) =>
      block.reason.includes(endpoint) || block.reason.includes('global')
    );

    if (relevantBlock) {
      return {
        blocked: true,
        blockExpires: relevantBlock.expires_at ? new Date(relevantBlock.expires_at) : undefined
      };
    }

    return { blocked: false };
  } catch (error) {
    console.error('Error checking session block:', error);
    return { blocked: false };
  }
}

/**
 * Block a session for a specific duration
 */
async function blockSession(
  sessionId: string,
  endpoint: string,
  durationMs: number
): Promise<boolean> {
  try {
    const supabase = await getSupabaseClient();
    const expiresAt = new Date(Date.now() + durationMs);

    const { error } = await supabase
      .from('blocked_sessions')
      .insert({
        session_id: sessionId,
        reason: `Rate limit exceeded for ${endpoint}`,
        blocked_by: 'Session Rate Limiter',
        expires_at: expiresAt.toISOString(),
        is_active: true
      });

    if (error) {
      console.error('Error blocking session:', error);
      return false;
    }

    // Log security event
    await supabase
      .from('security_events')
      .insert({
        event_type: 'session_blocked',
        session_id: sessionId,
        severity: 'high',
        description: `Session blocked for rate limit violation: ${endpoint}`,
        metadata: {
          endpoint,
          duration_ms: durationMs,
          expires_at: expiresAt.toISOString()
        },
        action_taken: 'session_blocked'
      });

    return true;
  } catch (error) {
    console.error('Error in blockSession:', error);
    return false;
  }
}

/**
 * Middleware function for session-based rate limiting
 */
export async function sessionRateLimitMiddleware(
  req: NextRequest,
  endpoint: string,
  config?: RateLimitConfig
) {
  try {
    const sessionId = req.headers.get('x-session-id') ||
                     req.cookies.get('session-id')?.value ||
                     'anonymous';

    const result = await checkSessionRateLimit(sessionId, endpoint, config);

    if (result.allowed) {
      // Record the request
      await recordSessionRequest(sessionId, endpoint, {
        ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip'),
        userAgent: req.headers.get('user-agent'),
        path: req.nextUrl.pathname
      });
    }

    return result;
  } catch (error) {
    console.error('Error in session rate limit middleware:', error);
    return {
      allowed: true,
      remaining: 999,
      resetTime: new Date(Date.now() + 60 * 60 * 1000)
    };
  }
}

/**
 * Create database table for session rate limiting
 */
export async function createSessionRateLimitTable(): Promise<boolean> {
  try {
    // Table already exists in schema, no need to create
    console.log('Session rate limit table already exists');
    return true;
  } catch (error) {
    console.error('Error in createSessionRateLimitTable:', error);
    return false;
  }
}

export default {
  checkSessionRateLimit,
  recordSessionRequest,
  sessionRateLimitMiddleware,
  SESSION_RATE_LIMITS
};
