/**
 * Session Management API Endpoint
 * 
 * This endpoint handles session creation, updates, and cleanup for analytics.
 * Used by the frontend to manage user sessions without direct database access.
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  getOrCreateSession,
  endSession,
  updateSessionMetrics,
  getSession,
  SessionMetadata,
  SessionMetrics
} from '@/lib/analytics/sessionManager';
import { logError, logInfo } from '@/lib/logging';

/**
 * POST /api/analytics/session
 * Create or get a session
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, sessionId, metadata, metrics } = body;

    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'create':
        // Create or get existing session
        const sessionData = await getOrCreateSession(sessionId, metadata as SessionMetadata);
        
        if (!sessionData) {
          return NextResponse.json(
            { success: false, error: 'Failed to create session' },
            { status: 500 }
          );
        }

        logInfo('SessionAPI', `Session created/retrieved: ${sessionId}`);
        
        return NextResponse.json({
          success: true,
          data: sessionData
        });

      case 'end':
        // End the session
        const endResult = await endSession(sessionId);
        
        if (!endResult) {
          return NextResponse.json(
            { success: false, error: 'Failed to end session' },
            { status: 500 }
          );
        }

        logInfo('SessionAPI', `Session ended: ${sessionId}`);
        
        return NextResponse.json({
          success: true,
          message: 'Session ended successfully'
        });

      case 'update':
        // Update session metrics
        if (!metrics) {
          return NextResponse.json(
            { success: false, error: 'Metrics are required for update action' },
            { status: 400 }
          );
        }

        const updateResult = await updateSessionMetrics(sessionId, metrics as SessionMetrics);
        
        if (!updateResult) {
          return NextResponse.json(
            { success: false, error: 'Failed to update session metrics' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          message: 'Session metrics updated successfully'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action. Use: create, end, or update' },
          { status: 400 }
        );
    }

  } catch (error) {
    logError('SessionAPI', 'Unexpected error in session endpoint', { error });
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/analytics/session
 * Get session information
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      );
    }

    const sessionData = await getSession(sessionId);

    if (!sessionData) {
      return NextResponse.json(
        { success: false, error: 'Session not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: sessionData
    });

  } catch (error) {
    logError('SessionAPI', 'Error retrieving session', { error });
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/analytics/session
 * Delete/cleanup a session
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      );
    }

    const endResult = await endSession(sessionId);
    
    if (!endResult) {
      return NextResponse.json(
        { success: false, error: 'Failed to delete session' },
        { status: 500 }
      );
    }

    logInfo('SessionAPI', `Session deleted: ${sessionId}`);
    
    return NextResponse.json({
      success: true,
      message: 'Session deleted successfully'
    });

  } catch (error) {
    logError('SessionAPI', 'Error deleting session', { error });
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
