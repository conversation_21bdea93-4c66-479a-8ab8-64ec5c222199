'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, ArrowPathIcon } from '@heroicons/react/24/solid';
import { AdConfig, AdDisplayOptions } from '@/lib/config/types';
import AdPlacementForm from '@/components/admin/AdPlacementForm';
import AdDisplayOptionsComponent from '@/components/admin/AdDisplayOptions';
import AdScheduler from '@/components/admin/AdScheduler';
import { Button } from '@/components/ui/Button';
import { Card, CardHeader, CardContent, CardTitle, CardDescription } from '@/components/ui/Card';
import { PageHeader } from '@/components/ui/PageHeader';
import { Alert } from '@/components/ui/Alert';
import { SkeletonCard } from '@/components/ui/Skeleton';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/Dialog';
import Link from 'next/link';

interface AdPlacementDetailPageProps {
  params: Promise<{
    placementId: string;
    domain: string;
  }>;
}

export default function AdPlacementDetailPage({ params }: AdPlacementDetailPageProps) {
  const router = useRouter();
  const [adPlacement, setAdPlacement] = useState<AdConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showDisplayOptions, setShowDisplayOptions] = useState(false);
  const [showScheduler, setShowScheduler] = useState(false);

  // Unwrap the params Promise using React.use()
  const resolvedParams = use(params);
  const { placementId, domain } = resolvedParams;
  const decodedPlacementId = decodeURIComponent(placementId);
  const decodedDomain = decodeURIComponent(domain);

  // Fetch ad placement
  const fetchAdPlacement = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/management-portal-x7z9y2/ads/${encodeURIComponent(decodedPlacementId)}/${encodeURIComponent(decodedDomain)}`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error(`Ad placement not found: ${decodedPlacementId} for domain: ${decodedDomain}`);
        }
        throw new Error(`Failed to fetch ad placement: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setAdPlacement(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch ad placement');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      console.error('Error fetching ad placement:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAdPlacement();
  }, [decodedPlacementId, decodedDomain]);

  // Handle update ad placement
  const handleUpdateAdPlacement = async (
    placementId: string,
    domain: string,
    adUnitId: string,
    adClientId: string,
    isEnabled: boolean,
    deviceTypes: string[],
    displayOptions?: AdDisplayOptions
  ) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/management-portal-x7z9y2/ads/${encodeURIComponent(placementId)}/${encodeURIComponent(domain)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          adUnitId,
          adClientId,
          isEnabled,
          deviceTypes,
          displayOptions
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to update ad placement: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setAdPlacement(data.data);
        setIsEditing(false);
      } else {
        throw new Error(data.error || 'Failed to update ad placement');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      console.error('Error updating ad placement:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle delete ad placement
  const handleDeleteAdPlacement = async () => {
    if (!adPlacement) return;

    if (!window.confirm(`Are you sure you want to delete the ad placement "${adPlacement.placementId}" for domain "${adPlacement.domain}"?`)) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/management-portal-x7z9y2/ads/${encodeURIComponent(adPlacement.placementId)}/${encodeURIComponent(adPlacement.domain)}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`Failed to delete ad placement: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        router.push('/management-portal-x7z9y2/ads');
      } else {
        throw new Error(data.error || 'Failed to delete ad placement');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      console.error('Error deleting ad placement:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle update for display options or schedule
  const handlePartialUpdate = async (
    placementId: string,
    domain: string,
    updates: {
      adUnitId?: string;
      adClientId?: string;
      isEnabled?: boolean;
      deviceTypes?: string[];
      displayOptions?: AdDisplayOptions;
      schedule?: any;
    }
  ) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/management-portal-x7z9y2/ads/${encodeURIComponent(placementId)}/${encodeURIComponent(domain)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        throw new Error(`Failed to update ad placement: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setAdPlacement(data.data);
      } else {
        throw new Error(data.error || 'Failed to update ad placement');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      console.error('Error updating ad placement:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <PageHeader
        title={adPlacement ? `Ad Placement: ${adPlacement.placementId}` : "Ad Placement Details"}
        description={adPlacement ? `Domain: ${adPlacement.domain}` : "Loading ad placement details..."}
        icon={
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5z" />
          </svg>
        }
        actions={
          <Link href="/management-portal-x7z9y2/ads">
            <Button variant="secondary" className="flex items-center">
              <ArrowLeftIcon className="h-4 w-4 mr-1.5" />
              Back to Ad Placements
            </Button>
          </Link>
        }
      />

      {loading && !adPlacement ? (
        <div className="space-y-6">
          <SkeletonCard />
          <SkeletonCard />
        </div>
      ) : error ? (
        <Alert variant="error" title="Error" onDismiss={() => setError(null)}>
          <p>{error}</p>
        </Alert>
      ) : adPlacement ? (
        <div>
          <Card>
            <div className="px-6 py-4 flex justify-end space-x-3 border-b border-gray-200">
              <Button
                variant="secondary"
                onClick={() => setIsEditing(true)}
                disabled={loading}
              >
                Edit
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteAdPlacement}
                disabled={loading}
              >
                Delete
              </Button>
            </div>
            {isEditing ? (
              <CardContent className="pt-4">
                <AdPlacementForm
                  onSubmit={handleUpdateAdPlacement}
                  onCancel={() => setIsEditing(false)}
                  initialValues={adPlacement}
                  isEditMode={true}
                />
              </CardContent>
            ) : (
              <CardContent>
                <dl className="divide-y divide-gray-100">
                  <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium text-gray-500">Ad Client ID</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {adPlacement.adClientId}
                    </dd>
                  </div>
                  <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium text-gray-500">Ad Unit ID</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {adPlacement.adUnitId}
                    </dd>
                  </div>
                  <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium text-gray-500">Status</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          adPlacement.isEnabled
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {adPlacement.isEnabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </dd>
                  </div>
                  <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium text-gray-500">Device Types</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <div className="flex flex-wrap gap-2">
                        {adPlacement.deviceTypes.map((deviceType) => (
                          <span
                            key={deviceType}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {deviceType}
                          </span>
                        ))}
                      </div>
                    </dd>
                  </div>
                  <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium text-gray-500">Display Options</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <div className="flex justify-between items-start">
                        <div>
                          {adPlacement.displayOptions ? (
                            <pre className="bg-gray-50 p-3 rounded-md overflow-auto max-h-40 text-xs">
                              {JSON.stringify(adPlacement.displayOptions, null, 2)}
                            </pre>
                          ) : (
                            <span className="text-gray-500 italic">No display options set</span>
                          )}
                        </div>
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => setShowDisplayOptions(true)}
                        >
                          Configure
                        </Button>
                      </div>
                    </dd>
                  </div>
                  <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium text-gray-500">Schedule</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      <div className="flex justify-between items-start">
                        <div>
                          {adPlacement.schedule ? (
                            <pre className="bg-gray-50 p-3 rounded-md overflow-auto max-h-40 text-xs">
                              {JSON.stringify(adPlacement.schedule, null, 2)}
                            </pre>
                          ) : (
                            <span className="text-gray-500 italic">No schedule set</span>
                          )}
                        </div>
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => setShowScheduler(true)}
                        >
                          Configure
                        </Button>
                      </div>
                    </dd>
                  </div>
                  <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium text-gray-500">Created At</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {new Date(adPlacement.createdAt).toLocaleString()}
                    </dd>
                  </div>
                  <div className="px-4 py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {new Date(adPlacement.updatedAt).toLocaleString()}
                    </dd>
                  </div>
                </dl>
              </CardContent>
            )}
          </Card>

          {/* Display Options Modal */}
          {showDisplayOptions && adPlacement && (
            <Dialog open={showDisplayOptions} onOpenChange={() => setShowDisplayOptions(false)}>
              <DialogContent className="sm:max-w-[650px]">
                <DialogHeader>
                  <DialogTitle>Display Options for {adPlacement.placementId}</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                  <AdDisplayOptionsComponent
                    adPlacement={adPlacement}
                    onUpdate={handlePartialUpdate}
                  />
                </div>
                <DialogFooter>
                  <Button
                    variant="primary"
                    onClick={() => setShowDisplayOptions(false)}
                  >
                    Close
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}

          {/* Scheduler Modal */}
          {showScheduler && adPlacement && (
            <Dialog open={showScheduler} onOpenChange={() => setShowScheduler(false)}>
              <DialogContent className="sm:max-w-[650px]">
                <DialogHeader>
                  <DialogTitle>Schedule for {adPlacement.placementId}</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                  <AdScheduler
                    adPlacement={adPlacement}
                    onUpdate={handlePartialUpdate}
                  />
                </div>
                <DialogFooter>
                  <Button
                    variant="primary"
                    onClick={() => setShowScheduler(false)}
                  >
                    Close
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      ) : null}
    </div>
  );
}
