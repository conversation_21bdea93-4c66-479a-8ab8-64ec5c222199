# 🔧 **CURRENT SESSION SETTINGS & BACKEND CONTROLS**

## **📊 CURRENT SESSION CONFIGURATION**

### **🕒 Session Duration Settings**
- **Session Duration**: `24 hours` (86,400,000 ms)
- **Session Extension**: `24 hours` per extension
- **Max Extensions**: `3 extensions` (total 96 hours possible)
- **Activity Update Interval**: `1 minute` (60,000 ms)

### **💾 Storage Settings**
- **Session ID Key**: `vanishpost_session_id`
- **Session Metadata Key**: `vanishpost_session_metadata`
- **Storage Method**: `localStorage` (client-side persistence)
- **Cross-Browser Sessions**: `Disabled` (each browser gets unique session)

### **🔒 Security Settings**
- **Session Persistence**: `✅ Enabled` (survives page refreshes)
- **Session Extension**: `✅ Enabled` (can extend expiration)
- **Automatic Cleanup**: `✅ Enabled` (removes expired sessions)
- **Debug Logging**: `Development only`
- **Session Analytics**: `✅ Enabled`

---

## **⚡ CURRENT RATE LIMITING CONFIGURATION**

### **📧 Email Generation Limits**
| User Type | IP Limit | Session Limit | Window | Block Duration |
|-----------|----------|---------------|---------|----------------|
| **Persistent Session** | `5/hour` | `8/hour` | 1 hour | 1 hour |
| **Anonymous Session** | `5/hour` | `2/hour` | 1 hour | 1 hour |
| **Burst Protection** | `2/5min` | `3/5min` | 5 minutes | 15 minutes |

### **🛡️ Security Mode**
- **Strict Mode**: `✅ Enabled` (both IP AND session limits must be satisfied)
- **Progressive Blocking**: `✅ Integrated`
- **Emergency Mode**: `❌ Disabled` (can be activated instantly)

### **🚨 Emergency Mode Settings** (when activated)
- **IP Limit**: `1 email/hour`
- **Session Limit**: `1 email/hour`
- **Block Duration**: `4 hours`
- **Session Duration**: `4 hours` (reduced from 24h)

---

## **🎛️ BACKEND CONTROLS AVAILABLE**

### **1. Admin API Endpoint**
**URL**: `/api/admin/session-config`  
**Authentication**: Bearer token (ADMIN_API_KEY environment variable)

#### **Available Actions:**

**GET Requests:**
```bash
# Get configuration summary
GET /api/admin/session-config?action=summary

# Get session configuration
GET /api/admin/session-config?action=session

# Get rate limit configuration  
GET /api/admin/session-config?action=rateLimit

# Get available presets
GET /api/admin/session-config?action=presets
```

**POST Requests:**
```bash
# Apply session preset
POST /api/admin/session-config
{
  "action": "applySessionPreset",
  "preset": "PRODUCTION|DEVELOPMENT|HIGH_SECURITY|RELAXED"
}

# Apply rate limit preset
POST /api/admin/session-config
{
  "action": "applyRateLimitPreset", 
  "preset": "PRODUCTION|DEVELOPMENT|HIGH_SECURITY|EMERGENCY"
}

# Toggle emergency mode
POST /api/admin/session-config
{
  "action": "emergencyMode",
  "enabled": true|false
}

# Quick adjustments
POST /api/admin/session-config
{
  "action": "quickAdjust",
  "type": "session|rateLimit",
  "adjustment": {
    "duration": 12, // hours
    "emailLimit": 15 // emails per hour
  }
}
```

### **2. Configuration Presets**

#### **Session Presets:**
| Preset | Duration | Extensions | Use Case |
|--------|----------|------------|----------|
| **PRODUCTION** | 24 hours | 2 max | Standard production |
| **DEVELOPMENT** | 2 hours | Unlimited | Development/testing |
| **HIGH_SECURITY** | 4 hours | 0 max | High security events |
| **RELAXED** | 48 hours | 5 max | Special events/promotions |

#### **Rate Limit Presets:**
| Preset | IP Limit | Session Limit | Anonymous Limit |
|--------|----------|---------------|-----------------|
| **PRODUCTION** | 5/hour | 8/hour | 2/hour |
| **DEVELOPMENT** | 20/hour | 25/hour | 10/hour |
| **HIGH_SECURITY** | 2/hour | 3/hour | 1/hour |
| **EMERGENCY** | 1/hour | 1/hour | 1/hour |

### **3. Admin Dashboard Component**
**File**: `src/components/admin/SessionConfigDashboard.tsx`

**Features:**
- ✅ Real-time configuration viewing
- ✅ One-click preset application
- ✅ Emergency mode toggle
- ✅ Quick adjustment buttons
- ✅ Secure admin authentication
- ✅ Live configuration refresh

---

## **🚀 HOW TO CHANGE SETTINGS**

### **Method 1: Admin Dashboard (Recommended)**
1. Navigate to admin portal: `/management-portal-x7z9y2/session-config`
2. Enter admin API key
3. Use preset buttons or quick adjustments
4. Changes apply immediately

### **Method 2: API Calls**
```bash
# Example: Switch to high security mode
curl -X POST https://your-domain.com/api/admin/session-config \
  -H "Authorization: Bearer YOUR_ADMIN_KEY" \
  -H "Content-Type: application/json" \
  -d '{"action": "applySessionPreset", "preset": "HIGH_SECURITY"}'

# Example: Activate emergency mode
curl -X POST https://your-domain.com/api/admin/session-config \
  -H "Authorization: Bearer YOUR_ADMIN_KEY" \
  -H "Content-Type: application/json" \
  -d '{"action": "emergencyMode", "enabled": true}'
```

### **Method 3: Environment Variables** (requires restart)
```bash
# Set in .env.local
ADMIN_API_KEY=your-secure-admin-key
NODE_ENV=production|development
```

---

## **⚡ INSTANT EMERGENCY CONTROLS**

### **🚨 Emergency Scenarios & Responses**

#### **Scenario 1: Abuse Attack Detected**
**Action**: Activate Emergency Mode
```bash
POST /api/admin/session-config
{"action": "emergencyMode", "enabled": true}
```
**Result**: Limits drop to 1 email/hour immediately

#### **Scenario 2: Server Overload**
**Action**: Apply High Security Preset
```bash
POST /api/admin/session-config
{"action": "applyRateLimitPreset", "preset": "HIGH_SECURITY"}
```
**Result**: Limits drop to 2-3 emails/hour

#### **Scenario 3: Special Event (More Traffic Expected)**
**Action**: Apply Relaxed Preset
```bash
POST /api/admin/session-config
{"action": "applySessionPreset", "preset": "RELAXED"}
```
**Result**: Sessions last 48 hours, more extensions allowed

#### **Scenario 4: Quick Limit Adjustment**
**Action**: Quick Adjust
```bash
POST /api/admin/session-config
{
  "action": "quickAdjust",
  "type": "rateLimit", 
  "adjustment": {"emailLimit": 10}
}
```
**Result**: Email limit changes to 10/hour immediately

---

## **📊 MONITORING & ANALYTICS**

### **Configuration Changes Logged:**
- ✅ All preset applications
- ✅ Emergency mode activations
- ✅ Quick adjustments
- ✅ Admin authentication attempts
- ✅ Configuration API calls

### **Real-time Monitoring:**
- ✅ Current session counts
- ✅ Rate limiting effectiveness
- ✅ Emergency mode status
- ✅ Configuration change history

---

## **🔐 SECURITY FEATURES**

### **Admin Authentication:**
- ✅ Bearer token authentication
- ✅ Environment variable protection
- ✅ Failed authentication logging
- ✅ Admin action audit trail

### **Fail-Safe Mechanisms:**
- ✅ Invalid configurations rejected
- ✅ Automatic fallback to defaults on errors
- ✅ Configuration validation
- ✅ Secure error handling

---

## **🎯 SUMMARY**

**Current Status**: ✅ **FULLY CONFIGURABLE**

**Available Controls:**
- ✅ **Session Duration**: 2-48 hours (configurable)
- ✅ **Rate Limits**: 1-25 emails/hour (configurable)
- ✅ **Emergency Mode**: Instant activation
- ✅ **Presets**: 4 session + 4 rate limit presets
- ✅ **Quick Adjustments**: Instant limit changes
- ✅ **Admin Dashboard**: User-friendly interface
- ✅ **API Controls**: Programmatic management

**Response Time**: **Immediate** (no restart required)  
**Security**: **Enterprise-grade** with admin authentication  
**Flexibility**: **Maximum** with presets and custom adjustments

You now have **complete backend control** over all session and rate limiting settings with the ability to respond instantly to any situation! 🚀

---

*Configuration system implemented on July 19, 2025*  
*All settings can be changed in real-time without service interruption*
