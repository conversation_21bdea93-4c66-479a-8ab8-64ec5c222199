'use client'

import { usePathname, useSearchParams } from "next/navigation"
import { useEffect, Suspense, useState } from "react"
import { usePostHog } from 'posthog-js/react'
import { SuppressHydrationWarning } from "@/lib/utils/suppressHydrationWarning"

/**
 * PostHogPageView Component
 *
 * Tracks page views in PostHog when the URL path changes
 * Uses Suspense to avoid de-opting the whole app into client-side rendering
 *
 * This component is wrapped with SuppressHydrationWarning to prevent hydration errors
 * caused by third-party scripts like Google AdSense that modify the DOM.
 */
function PostHogPageView() {
  const pathname = usePathname() || ''
  const searchParams = useSearchParams() || null
  const posthog = usePostHog()
  const [isMounted, setIsMounted] = useState(false)

  // Set mounted state
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Track pageviews
  useEffect(() => {
    // Only run tracking after component is mounted on the client
    if (!isMounted || !pathname || !posthog) return

    // Skip tracking for admin and management portal pages
    if (pathname.startsWith('/admin') || pathname.startsWith('/management-portal-x7z9y2')) {
      return
    }

    let url = window.origin + pathname
    if (searchParams && searchParams.toString()) {
      url = url + `?${searchParams.toString()}`
    }
    posthog.capture('$pageview', { '$current_url': url })
  }, [pathname, searchParams, posthog, isMounted])

  // Return null to avoid rendering anything
  return null
}

/**
 * Wrap the PostHogPageView component in Suspense to avoid the `useSearchParams` usage
 * from de-opting the whole app into client-side rendering
 * See: https://nextjs.org/docs/messages/deopted-into-client-rendering
 *
 * Also wrap in SuppressHydrationWarning to prevent hydration errors from third-party scripts
 */
export default function SuspendedPostHogPageView() {
  return (
    <SuppressHydrationWarning>
      <Suspense fallback={null}>
        <PostHogPageView />
      </Suspense>
    </SuppressHydrationWarning>
  )
}
