

---

### Requirements:

1. **Overall Layout**:
   - Create a responsive, three-column layout inspired by the attached screenshot:
     - **Left Column (Inbox Sidebar)**: A narrow sidebar (20% width) listing email cards (inbox items).
     - **Right Column (Email Content)**: A wider area (80% width) displaying the full content of the selected email.
     - **Top Bar**: A header section above both columns containing the email field and buttons.
   - Use Tailwind CSS to style the layout with a white background, subtle borders, and a professional look similar to the screenshot.
   - Ensure the layout is responsive and works on both desktop and mobile devices (stack columns vertically on mobile).

2. **Top Bar**:
   - Include an email field (read-only input) displaying a randomly generated temporary email address (e.g., `<EMAIL>`). Use a placeholder for now since the actual generation logic will be added later.
   - Add three buttons:
     - **Refresh Button**: An icon button (use a circular arrow icon or similar) to refresh the inbox. Place it to the left of the email field.
     - **Copy Button**: A button labeled “Copy” or an icon (clipboard) to copy the email address to the clipboard. Place it immediately to the right of the email field.
     - **Generate New Address Button**: A button labeled “New Address” or an icon to generate a new temporary email address. Place it to the right of the Copy button.
   - Style the buttons with Tailwind CSS to have a clean, modern look (e.g., blue background for primary actions, hover effects, rounded corners).
   - Ensure the email field and buttons are horizontally aligned and centered in the top bar, with padding and spacing similar to the screenshot.

3. **Left Column (Inbox Sidebar)**:
   - Display a list of email cards in a scrollable container.
   - Each email card should include:
     - **Sender’s Name**: Bold text (e.g., “Andrew Cano”).
     - **Subject Line**: Normal text below the sender’s name (e.g., “Report - Chatto project”).
     - **Preview of Email Content**: A short snippet of the email body (e.g., “Hello, Orlando! Please see the project status...”).
     - **Timestamp**: A small text showing the time received (e.g., “10:32 AM”).
   - Style each card with a white background, subtle border, and hover effect (e.g., light gray background on hover) to match the screenshot’s aesthetic.
   - Add a TypeScript interface `Email` to define the structure of an email object:
     ```typescript
     interface Email {
       id: string;
       sender: string;
       subject: string;
       preview: string;
       timestamp: string;
       content: string;
     }
     ```
   - Use a dummy array of `Email` objects for now to populate the inbox (e.g., 3-5 sample emails).
   - When an email card is clicked, it should become highlighted (e.g., blue background or border), and its full content should display in the right column.

4. **Right Column (Email Content)**:
   - Display the full content of the selected email, including:
     - **Sender’s Name and Email**: At the top (e.g., “Andrew Cano <<EMAIL>>”).
     - **Subject Line**: Below the sender, in bold.
     - **Timestamp**: Next to the subject or below it (e.g., “10:32 AM (15 min ago)”).
     - **Email Body**: The full text content of the email (e.g., “Hello, Orlando! Please see the project status in the pdf...”).
     - **Attachments** (if any): A section showing attachment names and sizes (e.g., “Report - Chatto project.pdf, 2.4 MB”). For now, display dummy attachments as in the screenshot, with a “Download All” link.
   - Style the content area to match the screenshot: white background, clean typography, proper spacing, and a subtle header section.
   - If no email is selected, show a placeholder message like “Select an email to view its content.”

5. **Additional Details**:
   - Use Tailwind CSS utility classes for all styling (e.g., `bg-white`, `p-4`, `border`, `hover:bg-gray-100`).
   - Ensure the UI is accessible (e.g., add `aria-label` attributes to buttons, use semantic HTML).
   - Write the code in a single `EmailApp` component in a file named `EmailApp.tsx` under the `components` directory.
   - Use functional components with React hooks (e.g., `useState` to manage the selected email).
   - Include basic TypeScript types for props and state to ensure type safety.
   - Do not implement backend logic (e.g., email generation or fetching); use static data for now.
   - Add comments in the code to explain key sections (e.g., layout, email card rendering, state management).

6. **Expected File Structure**:
   - Place the component in `components/EmailApp.tsx`.
   - Export the `EmailApp` component as the default export.
   - If additional components are needed (e.g., `EmailCard`, `EmailContent`), define them in the same file for simplicity.

7. **Styling Notes**:
   - Use a color palette similar to the screenshot: white backgrounds, gray borders, blue accents for buttons and highlights.
   - Ensure fonts are clean and readable (e.g., default Tailwind fonts or system fonts like `sans-serif`).
   - Match the spacing, padding, and alignment of the screenshot as closely as possible.

### Example Output:
Generate a complete `EmailApp.tsx` file that includes:
- A responsive layout with Tailwind CSS.
- A top bar with an email field and three buttons (Refresh, Copy, Generate New Address).
- An inbox sidebar with clickable email cards (sender, subject, preview, timestamp).
- A content area showing the selected email’s details (sender, subject, timestamp, body, attachments).
- TypeScript interfaces and type-safe code.
- Dummy data for emails and attachments.
- Comments explaining the code structure.

Please write the code now, ensuring it’s clean, modular, and matches the described UI and functionality.

---
