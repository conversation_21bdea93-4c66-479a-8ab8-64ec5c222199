/**
 * <PERSON>ript to migrate data from MySQL to Supabase
 *
 * This script migrates data from the local MySQL database to Supabase PostgreSQL.
 */
require('dotenv').config();
const mysql = require('mysql2/promise');
const { createClient } = require('@supabase/supabase-js');

// MySQL configuration
const mysqlConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'tempmail_web_user',
  password: process.env.DB_PASSWORD || 'yiWTpFChtFW3neWCS',
  database: process.env.DB_NAME || 'tempmail_web',
};

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://pfxzwsjgnpycqopkfyuh.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBmeHp3c2pnbnB5Y3FvcGtmeXVoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyMzkzMzYsImV4cCI6MjA2MDgxNTMzNn0.t3pm-SvvMkpUKpN5hyG0OTOt2jSX-U6WHSikdcGcB5k';

// Create a Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Parse additional_data field safely
 *
 * This function handles different formats of additional_data that might be stored in MySQL
 */
function parseAdditionalData(data) {
  if (!data) return null;

  try {
    // If it's already a string representation of JSON, parse it
    if (typeof data === 'string') {
      return JSON.parse(data);
    }

    // If it's a Buffer (which MySQL might return for JSON/BLOB fields)
    if (Buffer.isBuffer(data)) {
      return JSON.parse(data.toString());
    }

    // If it's already an object, return it as is
    if (typeof data === 'object') {
      return data;
    }

    // Default fallback
    return null;
  } catch (error) {
    console.warn(`Warning: Could not parse additional_data: ${error.message}`);
    console.warn(`Data type: ${typeof data}`);
    console.warn(`Data preview: ${String(data).substring(0, 100)}...`);

    // Return null if parsing fails
    return null;
  }
}

async function migrateTempEmails() {
  console.log('Migrating temporary emails from MySQL to Supabase...');

  let mysqlConnection;

  try {
    // Connect to MySQL
    mysqlConnection = await mysql.createConnection(mysqlConfig);
    console.log('Connected to MySQL database');

    // Get all temporary emails from MySQL
    const [rows] = await mysqlConnection.execute('SELECT * FROM TempEmail');
    console.log(`Found ${rows.length} temporary emails to migrate`);

    if (rows.length === 0) {
      console.log('No data to migrate');
      return;
    }

    // Transform data for Supabase
    const transformedData = rows.map(row => ({
      email_address: row.emailAddress,
      creation_time: row.creationTime.toISOString(),
      expiration_date: row.expirationDate.toISOString()
    }));

    // Insert data into Supabase in batches of 100
    const batchSize = 100;
    for (let i = 0; i < transformedData.length; i += batchSize) {
      const batch = transformedData.slice(i, i + batchSize);

      console.log(`Migrating batch ${i / batchSize + 1} of ${Math.ceil(transformedData.length / batchSize)}`);

      const { data, error } = await supabase
        .from('temp_emails')
        .insert(batch)
        .select();

      if (error) {
        console.error('Error inserting batch:', error);
      } else {
        console.log(`Successfully migrated ${data.length} emails in this batch`);
      }
    }

    console.log('Temporary email migration completed successfully');
  } catch (error) {
    console.error('Error migrating temporary emails:', error);
  } finally {
    if (mysqlConnection) {
      await mysqlConnection.end();
    }
  }
}

async function migrateAnalyticsEvents() {
  console.log('\nMigrating analytics events from MySQL to Supabase...');

  let mysqlConnection;

  try {
    // Connect to MySQL
    mysqlConnection = await mysql.createConnection(mysqlConfig);

    // Check if analytics_events table exists
    const [tables] = await mysqlConnection.execute("SHOW TABLES LIKE 'analytics_events'");

    if (tables.length === 0) {
      console.log('analytics_events table does not exist in MySQL. Skipping migration.');
      return;
    }

    // Get all analytics events from MySQL
    const [rows] = await mysqlConnection.execute('SELECT * FROM analytics_events');
    console.log(`Found ${rows.length} analytics events to migrate`);

    // Log a sample row to help with debugging
    if (rows.length > 0) {
      console.log('Sample row structure:');
      const sampleRow = rows[0];
      console.log('- Keys:', Object.keys(sampleRow));
      console.log('- additional_data type:', typeof sampleRow.additional_data);
      if (sampleRow.additional_data) {
        console.log('- additional_data preview:', String(sampleRow.additional_data).substring(0, 100));
      }
    }

    if (rows.length === 0) {
      console.log('No analytics data to migrate');
      return;
    }

    // Transform data for Supabase
    const transformedData = rows.map(row => ({
      event_type: row.event_type,
      page_path: row.page_path,
      referrer: row.referrer,
      country: row.country,
      browser: row.browser,
      device_type: row.device_type,
      timestamp: row.timestamp.toISOString(),
      additional_data: parseAdditionalData(row.additional_data)
    }));

    // Insert data into Supabase in batches of 100
    const batchSize = 100;
    for (let i = 0; i < transformedData.length; i += batchSize) {
      const batch = transformedData.slice(i, i + batchSize);

      console.log(`Migrating batch ${i / batchSize + 1} of ${Math.ceil(transformedData.length / batchSize)}`);

      const { data, error } = await supabase
        .from('analytics_events')
        .insert(batch)
        .select();

      if (error) {
        console.error('Error inserting batch:', error);
      } else {
        console.log(`Successfully migrated ${data.length} events in this batch`);
      }
    }

    console.log('Analytics events migration completed successfully');
  } catch (error) {
    console.error('Error migrating analytics events:', error);
  } finally {
    if (mysqlConnection) {
      await mysqlConnection.end();
    }
  }
}

async function runMigration() {
  console.log('=== MySQL to Supabase Migration ===\n');

  try {
    await migrateTempEmails();
    await migrateAnalyticsEvents();

    console.log('\n=== Migration Summary ===');
    console.log('Migration completed. Please verify the data in Supabase.');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration().catch(console.error);
