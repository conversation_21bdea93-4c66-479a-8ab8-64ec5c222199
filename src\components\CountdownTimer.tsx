'use client';

import { useState, useEffect } from 'react';

interface CountdownTimerProps {
  /** Expiration date of the email address */
  expirationDate: Date | string | null | undefined;

  /** Whether to show the timer (used to conditionally render) */
  show: boolean;
}

/**
 * CountdownTimer Component
 *
 * Displays a countdown timer showing the remaining time before an email address expires
 */
export default function CountdownTimer({ expirationDate, show }: CountdownTimerProps) {
  const [timeLeft, setTimeLeft] = useState<{ hours: number; minutes: number; seconds: number }>({
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  const [isExpiringSoon, setIsExpiringSoon] = useState(false);

  useEffect(() => {
    if (!expirationDate || !show) return;

    // Convert string to Date if needed
    const expDate = typeof expirationDate === 'string' ? new Date(expirationDate) : expirationDate;

    const calculateTimeLeft = () => {
      const now = new Date();
      const difference = expDate.getTime() - now.getTime();

      if (difference <= 0) {
        // Email has expired
        return { hours: 0, minutes: 0, seconds: 0 };
      }

      // Calculate hours, minutes, seconds
      const hours = Math.floor(difference / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      // Check if expiring soon (less than 5 minutes)
      setIsExpiringSoon(difference < 5 * 60 * 1000);

      return { hours, minutes, seconds };
    };

    // Initial calculation
    setTimeLeft(calculateTimeLeft());

    // Update every second
    const timer = setInterval(() => {
      const newTimeLeft = calculateTimeLeft();
      setTimeLeft(newTimeLeft);

      // Clear interval if expired
      if (newTimeLeft.hours === 0 && newTimeLeft.minutes === 0 && newTimeLeft.seconds === 0) {
        clearInterval(timer);
      }
    }, 1000);

    // Cleanup
    return () => clearInterval(timer);
  }, [expirationDate, show]);

  if (!show || !expirationDate) return null;

  // Format the time left
  const formatTimeUnit = (value: number): string => {
    return value < 10 ? `0${value}` : `${value}`;
  };

  return (
    <div className={`flex items-center text-xs ${isExpiringSoon ? 'text-error-500' : 'text-[#24ad80]'}`}>
      <div className={`h-1.5 w-1.5 rounded-full mr-1.5 ${isExpiringSoon ? 'bg-error-500 animate-pulse' : 'bg-success-500 animate-pulse'}`}></div>
      <span className="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 opacity-70" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="10" />
          <polyline points="12 6 12 12 16 14" />
        </svg>
        Expires in: {formatTimeUnit(timeLeft.hours)}:{formatTimeUnit(timeLeft.minutes)}:{formatTimeUnit(timeLeft.seconds)}
      </span>
    </div>
  );
}
