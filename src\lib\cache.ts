/**
 * Simple in-memory cache implementation
 * 
 * This is used to cache expensive database queries to improve performance
 */

type CacheEntry<T> = {
  value: T;
  expiresAt: number;
};

class MemoryCache {
  private cache: Map<string, CacheEntry<any>> = new Map();
  
  /**
   * Get a value from the cache
   * 
   * @param key The cache key
   * @returns The cached value or undefined if not found or expired
   */
  get<T>(key: string): T | undefined {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return undefined;
    }
    
    // Check if the entry has expired
    if (entry.expiresAt < Date.now()) {
      this.cache.delete(key);
      return undefined;
    }
    
    return entry.value as T;
  }
  
  /**
   * Set a value in the cache
   * 
   * @param key The cache key
   * @param value The value to cache
   * @param ttlMs Time to live in milliseconds (default: 60 seconds)
   */
  set<T>(key: string, value: T, ttlMs: number = 60000): void {
    this.cache.set(key, {
      value,
      expiresAt: Date.now() + ttlMs
    });
  }
  
  /**
   * Delete a value from the cache
   * 
   * @param key The cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }
  
  /**
   * Clear all values from the cache
   */
  clear(): void {
    this.cache.clear();
  }
  
  /**
   * Get a value from the cache, or compute it if not found
   * 
   * @param key The cache key
   * @param fn Function to compute the value if not found in cache
   * @param ttlMs Time to live in milliseconds (default: 60 seconds)
   * @returns The cached or computed value
   */
  async getOrCompute<T>(
    key: string,
    fn: () => Promise<T>,
    ttlMs: number = 60000
  ): Promise<T> {
    const cachedValue = this.get<T>(key);
    
    if (cachedValue !== undefined) {
      return cachedValue;
    }
    
    const computedValue = await fn();
    this.set(key, computedValue, ttlMs);
    
    return computedValue;
  }
}

// Export a singleton instance
export const memoryCache = new MemoryCache();
