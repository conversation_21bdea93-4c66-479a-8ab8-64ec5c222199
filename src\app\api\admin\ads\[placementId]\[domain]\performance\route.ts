/**
 * API route for ad performance metrics
 */
import { NextRequest, NextResponse } from 'next/server';
import { getAdPerformanceMetrics } from '@/lib/config/adService';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/admin/ads/[placementId]/[domain]/performance
 *
 * Get performance metrics for a specific ad placement
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ placementId: string; domain: string }> }
) {
  try {
    const { placementId, domain } = await params;

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Validate required parameters
    if (!startDate || !endDate) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters: startDate and endDate' },
        { status: 400 }
      );
    }

    // Get performance metrics
    const metrics = await getAdPerformanceMetrics(placementId, domain, startDate, endDate);

    logInfo('api', 'Retrieved ad performance metrics', { placementId, domain, startDate, endDate });

    return NextResponse.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    logError('api', 'Error getting ad performance metrics', { error, params });

    return NextResponse.json(
      { success: false, error: 'Failed to get ad performance metrics' },
      { status: 500 }
    );
  }
}
