/**
 * Debug API to check database table structure
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient();

    // Check if admin_users table exists and get its structure
    const { data: adminUsersData, error: adminUsersError } = await supabase
      .from('admin_users')
      .select('*')
      .limit(1);

    // Check if admin_activity_log table exists and get its structure  
    const { data: activityLogData, error: activityLogError } = await supabase
      .from('admin_activity_log')
      .select('*')
      .limit(1);

    // Skip table info query - not needed for debug purposes
    const tableInfo = null;
    const tableInfoError = null;

    // Try a simple join to see if the relationship works
    const { data: joinTest, error: joinError } = await supabase
      .from('admin_activity_log')
      .select(`
        id,
        user_id,
        action_type,
        admin_users(id, username)
      `)
      .limit(1);

    return NextResponse.json({
      success: true,
      data: {
        admin_users: {
          exists: !adminUsersError,
          error: adminUsersError?.message,
          sample: adminUsersData?.[0] || null
        },
        admin_activity_log: {
          exists: !activityLogError,
          error: activityLogError?.message,
          sample: activityLogData?.[0] || null
        },
        table_info: {
          data: tableInfo,
          error: tableInfoError
        },
        join_test: {
          success: !joinError,
          error: joinError?.message,
          data: joinTest
        }
      }
    });
  } catch (error) {
    console.error('Error checking tables:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check tables' },
      { status: 500 }
    );
  }
}
