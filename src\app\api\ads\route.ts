/**
 * API route for client-side ad fetching
 */
import { NextRequest, NextResponse } from 'next/server';
import { getClientAdPlacements } from '@/lib/config/adService';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/ads
 * 
 * Get ads for client-side rendering
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const domain = searchParams.get('domain');
    const deviceType = searchParams.get('deviceType') as 'desktop' | 'tablet' | 'mobile';
    
    // Validate required parameters
    if (!domain) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameter: domain' },
        { status: 400 }
      );
    }
    
    if (!deviceType || !['desktop', 'tablet', 'mobile'].includes(deviceType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid or missing parameter: deviceType' },
        { status: 400 }
      );
    }
    
    // Get ads for the domain and device type
    const ads = await getClientAdPlacements(domain, deviceType);
    
    logInfo('api', 'Retrieved client-side ads', { domain, deviceType, count: ads.length });
    
    return NextResponse.json({
      success: true,
      data: ads
    });
  } catch (error) {
    logError('api', 'Error getting client-side ads', { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to get ads' },
      { status: 500 }
    );
  }
}
