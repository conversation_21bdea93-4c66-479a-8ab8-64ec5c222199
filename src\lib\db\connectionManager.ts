/**
 * Database Connection Manager
 *
 * This module provides centralized management of database connections
 * using configurations stored in the database.
 */

import mysql from 'mysql2/promise';
import { createClient } from '@supabase/supabase-js';
import { getDefaultConfiguration } from '@/lib/services/databaseConfigService';
import { logInfo, logError } from '@/lib/logging';
import { DatabaseConnectionError } from '@/lib/db';
import { Database } from '@/lib/database.types';

// Connection pools
let guerrillaPool: mysql.Pool | null = null;
let supabaseClient: ReturnType<typeof createClient<Database>> | null = null;

// Configuration cache
let cachedConfig: {
  timestamp: number;
  config: any;
} | null = null;

// Cache expiration time (5 minutes)
const CACHE_EXPIRATION_MS = 5 * 60 * 1000;

/**
 * Get the active database configuration with caching
 */
async function getConfig() {
  // Check if we have a cached config that's still valid
  const now = Date.now();
  if (cachedConfig && (now - cachedConfig.timestamp < CACHE_EXPIRATION_MS)) {
    return cachedConfig.config;
  }

  try {
    // Create a temporary Supabase client using environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

    const tempClient = createClient<Database>(
      supabaseUrl,
      supabaseAnonKey,
      {
        global: {
          headers: {
            apikey: supabaseAnonKey,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      }
    );

    // Get the active configuration directly from the database
    const { data, error } = await tempClient
      .from('database_configurations')
      .select('*')
      .eq('is_active', true)
      .single();

    if (error || !data) {
      logError('DB', 'Error fetching active database configuration:', error);
      return null;
    }

    // Map the database configuration
    const config = {
      id: data.id,
      name: data.name,
      description: data.description,
      isActive: data.is_active,
      guerrillaConfig: data.guerrilla_config,
      supabaseConfig: data.supabase_config,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };

    // Cache the configuration
    cachedConfig = {
      timestamp: now,
      config
    };

    return config;
  } catch (error) {
    logError('DB', 'Error in getConfig:', error);
    return null;
  }
}

/**
 * Get a connection pool for the Guerrilla MySQL database
 */
export async function getGuerrillaDbPool(): Promise<mysql.Pool> {
  try {
    // If we already have a pool, return it
    if (guerrillaPool) {
      return guerrillaPool;
    }

    // Get the active configuration
    const config = await getConfig();

    // If no configuration is found, use default
    const defaultConfig = await getDefaultConfiguration();
    const guerrillaConfig = config?.guerrillaConfig || defaultConfig.guerrillaConfig;

    logInfo('DB', 'Creating Guerrilla DB connection pool with config:', {
      host: guerrillaConfig.host,
      port: guerrillaConfig.port,
      database: guerrillaConfig.database,
      user: guerrillaConfig.user,
      // Don't log password
      connectionLimit: guerrillaConfig.connectionLimit,
      ssl: guerrillaConfig.ssl ? 'enabled' : 'disabled'
    });

    // Create a new connection pool
    guerrillaPool = mysql.createPool({
      host: guerrillaConfig.host,
      port: guerrillaConfig.port,
      database: guerrillaConfig.database,
      user: guerrillaConfig.user,
      password: guerrillaConfig.password,
      ssl: guerrillaConfig.ssl ? { rejectUnauthorized: false } : undefined,
      connectionLimit: guerrillaConfig.connectionLimit,
      queueLimit: 0,
      waitForConnections: true,
      enableKeepAlive: true,
      keepAliveInitialDelay: 10000,
      connectTimeout: 30000
    });

    return guerrillaPool;
  } catch (error) {
    logError('DB', 'Error creating Guerrilla DB connection pool:', error);
    throw new DatabaseConnectionError(
      'Failed to create Guerrilla DB connection pool',
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * Get a connection from the Guerrilla MySQL database pool
 */
export async function getGuerrillaDbConnection(): Promise<mysql.PoolConnection> {
  try {
    logInfo('DB', 'Getting connection from Guerrilla DB pool');

    // Get the pool
    const pool = await getGuerrillaDbPool();

    // Get a connection from the pool
    const connection = await pool.getConnection();

    logInfo('DB', 'Successfully got connection from Guerrilla DB pool');
    return connection;
  } catch (error) {
    logError('DB', 'Error getting connection from Guerrilla DB pool:', error);
    throw new DatabaseConnectionError(
      'Failed to get connection from Guerrilla DB pool',
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * Get a Supabase client
 */
export async function getSupabaseClient(): Promise<ReturnType<typeof createClient<Database>>> {
  try {
    // If we already have a client, return it
    if (supabaseClient) {
      return supabaseClient;
    }

    // Get the active configuration
    const config = await getConfig();

    // If no configuration is found, use default
    const defaultConfig = await getDefaultConfiguration();
    const supabaseConfig = config?.supabaseConfig || defaultConfig.supabaseConfig;

    logInfo('DB', 'Creating Supabase client with config:', {
      url: supabaseConfig.url,
      // Don't log keys
    });

    // Create a new client
    supabaseClient = createClient<Database>(
      supabaseConfig.url,
      supabaseConfig.apiKey,
      {
        global: {
          headers: {
            apikey: supabaseConfig.apiKey,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      }
    );

    return supabaseClient;
  } catch (error) {
    logError('DB', 'Error creating Supabase client:', error);
    throw new DatabaseConnectionError(
      'Failed to create Supabase client',
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * Reset the connection pools and clients
 * This is useful for testing and when configuration changes
 */
export function resetConnections() {
  // Reset the cache
  cachedConfig = null;

  // Close and reset the Guerrilla pool
  if (guerrillaPool) {
    guerrillaPool.end();
    guerrillaPool = null;
  }

  // Reset the Supabase client
  supabaseClient = null;

  logInfo('DB', 'Database connections reset');
}
