const { execSync } = require('child_process');

try {
  console.log('Building Next.js app with type checking and linting disabled...');
  execSync('npx next build', {
    env: {
      ...process.env,
      NEXT_SKIP_TYPECHECKING: 'true',
      NEXT_SKIP_LINTING: 'true',
      NODE_OPTIONS: '--max-old-space-size=4096'
    },
    stdio: 'inherit'
  });
  console.log('Build completed successfully!');
} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
}
