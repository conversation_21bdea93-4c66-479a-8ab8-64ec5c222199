'use client';

import { useState, useEffect } from 'react';
import { ChevronUpIcon, ChevronDownIcon, XMarkIcon } from '@heroicons/react/24/solid';
import { useLocalStorage } from '@/lib/hooks/useLocalStorage';

interface CollapsibleAdProps {
  adId: string;
  adContent: React.ReactNode;
  position: 'top' | 'bottom' | 'left' | 'right';
  initiallyExpanded?: boolean;
  rememberState?: boolean;
  autoExpandAfterMinutes?: number;
  dismissible?: boolean;
  onDismiss?: () => void;
  onExpand?: () => void;
  onCollapse?: () => void;
  className?: string;
}

export default function CollapsibleAd({
  adId,
  adContent,
  position = 'bottom',
  initiallyExpanded = true,
  rememberState = true,
  autoExpandAfterMinutes,
  dismissible = true,
  onDismiss,
  onExpand,
  onCollapse,
  className = ''
}: CollapsibleAdProps) {
  // Get the stored state from localStorage if rememberState is true
  const storageKey = `collapsible-ad-${adId}-state`;
  const [storedState, setStoredState] = useLocalStorage<{
    expanded: boolean;
    dismissed: boolean;
    lastCollapsedAt?: string;
  }>(storageKey, {
    expanded: initiallyExpanded,
    dismissed: false
  });

  // Local state
  const [isExpanded, setIsExpanded] = useState(
    rememberState ? storedState.expanded : initiallyExpanded
  );
  const [isDismissed, setIsDismissed] = useState(
    rememberState ? storedState.dismissed : false
  );

  // Auto-expand after specified time
  useEffect(() => {
    if (
      !isExpanded &&
      !isDismissed &&
      autoExpandAfterMinutes &&
      rememberState &&
      storedState.lastCollapsedAt
    ) {
      const lastCollapsed = new Date(storedState.lastCollapsedAt);
      const expandAfter = new Date(lastCollapsed.getTime() + autoExpandAfterMinutes * 60 * 1000);
      const now = new Date();

      if (now >= expandAfter) {
        setIsExpanded(true);
        if (onExpand) onExpand();
        
        // Update stored state
        if (rememberState) {
          setStoredState({
            ...storedState,
            expanded: true
          });
        }
      } else {
        // Set a timeout to auto-expand
        const timeoutId = setTimeout(() => {
          setIsExpanded(true);
          if (onExpand) onExpand();
          
          // Update stored state
          if (rememberState) {
            setStoredState({
              ...storedState,
              expanded: true
            });
          }
        }, expandAfter.getTime() - now.getTime());
        
        return () => clearTimeout(timeoutId);
      }
    }
  }, [isExpanded, isDismissed, autoExpandAfterMinutes, rememberState, storedState, onExpand, setStoredState]);

  // Handle toggle
  const handleToggle = () => {
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);
    
    // Call appropriate callback
    if (newExpandedState) {
      if (onExpand) onExpand();
    } else {
      if (onCollapse) onCollapse();
    }
    
    // Update stored state
    if (rememberState) {
      setStoredState({
        ...storedState,
        expanded: newExpandedState,
        lastCollapsedAt: newExpandedState ? undefined : new Date().toISOString()
      });
    }
  };

  // Handle dismiss
  const handleDismiss = () => {
    setIsDismissed(true);
    if (onDismiss) onDismiss();
    
    // Update stored state
    if (rememberState) {
      setStoredState({
        ...storedState,
        dismissed: true
      });
    }
  };

  // If dismissed, don't render anything
  if (isDismissed) {
    return null;
  }

  // Determine position-specific styles
  const getPositionStyles = () => {
    switch (position) {
      case 'top':
        return {
          container: 'top-0 left-0 right-0 border-b',
          collapseButton: 'bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2'
        };
      case 'bottom':
        return {
          container: 'bottom-0 left-0 right-0 border-t',
          collapseButton: 'top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
        };
      case 'left':
        return {
          container: 'top-0 left-0 bottom-0 border-r',
          collapseButton: 'right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2'
        };
      case 'right':
        return {
          container: 'top-0 right-0 bottom-0 border-l',
          collapseButton: 'left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2'
        };
      default:
        return {
          container: 'bottom-0 left-0 right-0 border-t',
          collapseButton: 'top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
        };
    }
  };

  const positionStyles = getPositionStyles();
  const isHorizontal = position === 'top' || position === 'bottom';
  const CollapseIcon = isExpanded ? 
    (isHorizontal ? ChevronDownIcon : (position === 'left' ? ChevronLeftIcon : ChevronRightIcon)) : 
    (isHorizontal ? ChevronUpIcon : (position === 'left' ? ChevronRightIcon : ChevronLeftIcon));

  return (
    <div
      className={`fixed z-40 bg-white shadow-md border-gray-200 transition-all duration-300 ${
        positionStyles.container
      } ${
        isExpanded
          ? isHorizontal
            ? 'h-auto max-h-60'
            : 'w-auto max-w-60'
          : isHorizontal
            ? 'h-0 overflow-hidden'
            : 'w-0 overflow-hidden'
      } ${className}`}
    >
      {/* Collapse/Expand Button */}
      <button
        onClick={handleToggle}
        className={`absolute z-50 bg-white rounded-full p-1 shadow-md border border-gray-200 ${
          positionStyles.collapseButton
        }`}
        aria-label={isExpanded ? 'Collapse' : 'Expand'}
        title={isExpanded ? 'Collapse' : 'Expand'}
      >
        <CollapseIcon className="h-4 w-4 text-gray-500" />
      </button>

      {/* Dismiss Button */}
      {dismissible && isExpanded && (
        <button
          onClick={handleDismiss}
          className="absolute z-50 top-2 right-2 bg-white rounded-full p-1 shadow-sm border border-gray-200"
          aria-label="Dismiss"
          title="Dismiss"
        >
          <XMarkIcon className="h-4 w-4 text-gray-500" />
        </button>
      )}

      {/* Ad Content */}
      <div className={`p-4 ${isExpanded ? 'block' : 'hidden'}`}>
        {adContent}
      </div>
    </div>
  );
}

// Additional icons for left/right collapse
function ChevronLeftIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      {...props}
    >
      <path
        fillRule="evenodd"
        d="M7.72 12.53a.75.75 0 010-1.06l7.5-7.5a.75.75 0 111.06 1.06L9.31 12l6.97 6.97a.75.75 0 11-1.06 1.06l-7.5-7.5z"
        clipRule="evenodd"
      />
    </svg>
  );
}

function ChevronRightIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      {...props}
    >
      <path
        fillRule="evenodd"
        d="M16.28 11.47a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 01-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 011.06-1.06l7.5 7.5z"
        clipRule="evenodd"
      />
    </svg>
  );
}
