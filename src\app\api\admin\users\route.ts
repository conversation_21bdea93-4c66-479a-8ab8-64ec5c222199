/**
 * API routes for admin user management
 */
import { NextRequest, NextResponse } from 'next/server';
import {
  getAdminUsers,
  getAdminUserById,
  createAdminUser,
  updateAdminUser,
  deleteAdminUser,
  logAdminActivity
} from '@/lib/admin/user-service';
import { getCurrentUser } from '@/lib/auth';
import { AdminUserCreate, AdminUserUpdate } from '@/lib/types/admin-users';

/**
 * GET /api/admin/users - Get all admin users
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin privileges
    const currentUser = await getCurrentUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const users = await getAdminUsers();

    return NextResponse.json({ success: true, data: users });
  } catch (error) {
    console.error('Error getting admin users:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get admin users' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/users - Create a new admin user
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin privileges
    const currentUser = await getCurrentUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.username || !body.email || !body.password || !body.role) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create user
    const userData: AdminUserCreate = {
      username: body.username,
      email: body.email,
      password: body.password,
      fullName: body.fullName,
      role: body.role,
      isActive: body.isActive !== undefined ? body.isActive : true
    };

    const user = await createAdminUser(userData);

    // Log activity
    await logAdminActivity(
      currentUser.userId,
      'create',
      'user',
      user.id.toString(),
      { username: user.username, role: user.role },
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown'
    );

    return NextResponse.json(
      { success: true, data: user },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating admin user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create admin user' },
      { status: 500 }
    );
  }
}
