# Enhanced Iframe Email Renderer

## Overview

The `EnhancedIframeEmailRenderer` is an improved version of the original `ReactIframeEmailRenderer` component. It provides a modular, maintainable, and performance-optimized approach to rendering email content in iframes.

## Key Improvements

1. **Modularization**
   - Extracted functionality into reusable hooks
   - Separated UI components for better maintainability
   - Improved code organization and readability

2. **Performance Optimization**
   - Memoized expensive calculations
   - Reduced unnecessary re-renders
   - Optimized state management

3. **Better Documentation**
   - Comprehensive JSDoc comments
   - Clear component and hook interfaces
   - Detailed README documentation

4. **Clearer Naming**
   - Renamed from `ReactIframeEmailRenderer` to `EnhancedIframeEmailRenderer` to avoid confusion with the react-iframe library

## Component Structure

```
EnhancedIframeEmailRenderer
├── Hooks
│   ├── useIframeHeight - Manages iframe height adjustments
│   ├── useIframeScaling - Handles content scaling/zooming
│   ├── useImageLoading - Tracks image loading progress
│   ├── useIframeContent - Generates and sanitizes HTML content
│   └── useIframeMessage - Handles iframe postMessage communication
└── UI Components
    ├── IframeControls - Renders zoom and print controls
    ├── ImageLoadingIndicator - Shows image loading progress
    └── LoadingSkeleton - Displays loading animation
```

## Usage

```tsx
import EnhancedIframeEmailRenderer from '@/components/EnhancedIframeEmailRenderer';

// Basic usage
<EnhancedIframeEmailRenderer html={emailHtml} />

// With all options
<EnhancedIframeEmailRenderer
  html={emailHtml}
  className="custom-class"
  style={{ minHeight: '600px' }}
  webFonts={[
    'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap'
  ]}
  scaleFactor={1.0}
  lazyLoadImages={false}
  transformLinks={true}
  enablePrint={true}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `html` | string | (required) | The HTML content to render in the iframe |
| `className` | string | `''` | Optional className for the iframe container |
| `style` | React.CSSProperties | `{}` | Optional style object for the iframe |
| `webFonts` | string[] | `[]` | Optional array of web fonts to load in the iframe |
| `scaleFactor` | number | `1.0` | Optional scale factor for the email content (0.5 to 2.0) |
| `lazyLoadImages` | boolean | `true` | Optional flag to enable lazy loading of images |
| `transformLinks` | boolean | `true` | Optional flag to enable link transformation |
| `enablePrint` | boolean | `true` | Optional flag to enable print support |

## Features

### Height Adjustment

The component automatically adjusts the iframe height based on the content, ensuring that the entire email is visible without scrollbars.

### Content Scaling

Users can zoom in and out to adjust the scale of the email content. The component handles smooth transitions and proper height adjustments during scaling.

### Image Loading Tracking

The component tracks the loading progress of images in the email and displays a progress indicator.

### Print Support

Users can print the email content directly from the component.

### Security

The iframe uses appropriate sandbox attributes to ensure security while allowing necessary functionality.

## Implementation Details

### HTML Content Generation

The component generates a complete HTML document for the iframe, including:
- Proper DOCTYPE and meta tags
- Web fonts loading
- CSS styles for email content
- JavaScript for height adjustment, image loading tracking, and scaling

### Message Communication

The component uses the postMessage API to communicate between the parent window and the iframe, handling:
- Height adjustments
- Image loading progress
- Scale changes
- Print commands

## Maintenance

When updating this component, consider:
1. Keeping the hooks modular and focused on single responsibilities
2. Maintaining backward compatibility with the original API
3. Adding comprehensive tests for new functionality
4. Updating this documentation with any significant changes
