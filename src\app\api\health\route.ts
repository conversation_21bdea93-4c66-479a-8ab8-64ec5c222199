import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { performanceMonitor } from '@/lib/monitoring/performance';
import { CONFIG } from '@/lib/config/optimization';

interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: ServiceHealth;
    dkimGenerator: ServiceHealth;
    dmarcGenerator: ServiceHealth;
    dnsValidation: ServiceHealth;
  };
  performance: {
    averageResponseTime: number;
    errorRate: number;
    memoryUsage: {
      heapUsed: number;
      heapTotal: number;
      rss: number;
    };
  };
  metrics: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    recentErrors: string[];
  };
}

interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime?: number;
  lastChecked: string;
  error?: string;
  details?: Record<string, any>;
}

/**
 * Check database connectivity and performance
 */
async function checkDatabase(): Promise<ServiceHealth> {
  const startTime = Date.now();
  
  try {
    const supabase = await createServerSupabaseClient();
    
    // Test basic connectivity
    const { data, error } = await supabase
      .from('app_config')
      .select('key')
      .limit(1);

    if (error) {
      return {
        status: 'unhealthy',
        lastChecked: new Date().toISOString(),
        error: error.message,
        responseTime: Date.now() - startTime
      };
    }

    const responseTime = Date.now() - startTime;
    
    // Check if response time is within acceptable limits
    const status = responseTime > CONFIG.PERFORMANCE.DB_QUERY_CRITICAL 
      ? 'unhealthy' 
      : responseTime > CONFIG.PERFORMANCE.DB_QUERY_WARNING 
        ? 'degraded' 
        : 'healthy';

    return {
      status,
      responseTime,
      lastChecked: new Date().toISOString(),
      details: {
        queryTime: responseTime,
        recordsFound: data?.length || 0
      }
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      lastChecked: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown database error',
      responseTime: Date.now() - startTime
    };
  }
}

/**
 * Check DKIM generator service health
 */
async function checkDkimGenerator(): Promise<ServiceHealth> {
  try {
    // Get recent performance stats
    const stats = performanceMonitor.getStats('dkim_generation', 30);
    
    if (stats.count === 0) {
      return {
        status: 'healthy',
        lastChecked: new Date().toISOString(),
        details: {
          message: 'No recent activity',
          averageResponseTime: 0,
          successRate: 100
        }
      };
    }

    const status = stats.errorRate > 10 
      ? 'unhealthy' 
      : stats.errorRate > 5 || stats.averageDuration > CONFIG.PERFORMANCE.DKIM_GENERATION_MAX_TIME
        ? 'degraded' 
        : 'healthy';

    return {
      status,
      responseTime: stats.averageDuration,
      lastChecked: new Date().toISOString(),
      details: {
        recentRequests: stats.count,
        averageResponseTime: stats.averageDuration,
        successRate: stats.successRate,
        errorRate: stats.errorRate,
        recentErrors: stats.recentErrors.slice(0, 3)
      }
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      lastChecked: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown DKIM generator error'
    };
  }
}

/**
 * Check DMARC generator service health
 */
async function checkDmarcGenerator(): Promise<ServiceHealth> {
  try {
    const stats = performanceMonitor.getStats('dmarc_generation', 30);
    
    if (stats.count === 0) {
      return {
        status: 'healthy',
        lastChecked: new Date().toISOString(),
        details: {
          message: 'No recent activity',
          averageResponseTime: 0,
          successRate: 100
        }
      };
    }

    const status = stats.errorRate > 10 
      ? 'unhealthy' 
      : stats.errorRate > 5 || stats.averageDuration > CONFIG.PERFORMANCE.DMARC_GENERATION_MAX_TIME
        ? 'degraded' 
        : 'healthy';

    return {
      status,
      responseTime: stats.averageDuration,
      lastChecked: new Date().toISOString(),
      details: {
        recentRequests: stats.count,
        averageResponseTime: stats.averageDuration,
        successRate: stats.successRate,
        errorRate: stats.errorRate,
        recentErrors: stats.recentErrors.slice(0, 3)
      }
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      lastChecked: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown DMARC generator error'
    };
  }
}

/**
 * Check DNS validation service health
 */
async function checkDnsValidation(): Promise<ServiceHealth> {
  try {
    const stats = performanceMonitor.getStats('dns_validation', 30);
    
    if (stats.count === 0) {
      return {
        status: 'healthy',
        lastChecked: new Date().toISOString(),
        details: {
          message: 'No recent activity',
          averageResponseTime: 0,
          successRate: 100
        }
      };
    }

    const status = stats.errorRate > 20 
      ? 'unhealthy' 
      : stats.errorRate > 10 || stats.averageDuration > CONFIG.PERFORMANCE.DNS_VALIDATION_MAX_TIME
        ? 'degraded' 
        : 'healthy';

    return {
      status,
      responseTime: stats.averageDuration,
      lastChecked: new Date().toISOString(),
      details: {
        recentRequests: stats.count,
        averageResponseTime: stats.averageDuration,
        successRate: stats.successRate,
        errorRate: stats.errorRate,
        recentErrors: stats.recentErrors.slice(0, 3)
      }
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      lastChecked: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown DNS validation error'
    };
  }
}

/**
 * Determine overall system health status
 */
function determineOverallStatus(services: HealthCheckResult['services']): 'healthy' | 'degraded' | 'unhealthy' {
  const statuses = Object.values(services).map(service => service.status);
  
  if (statuses.includes('unhealthy')) {
    return 'unhealthy';
  }
  
  if (statuses.includes('degraded')) {
    return 'degraded';
  }
  
  return 'healthy';
}

export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();

    // Run all health checks in parallel
    const [database, dkimGenerator, dmarcGenerator, dnsValidation] = await Promise.all([
      checkDatabase(),
      checkDkimGenerator(),
      checkDmarcGenerator(),
      checkDnsValidation()
    ]);

    const services = {
      database,
      dkimGenerator,
      dmarcGenerator,
      dnsValidation
    };

    // Get overall performance metrics
    const overallStats = performanceMonitor.getStats(undefined, 60); // Last hour
    const memoryUsage = performanceMonitor.getMemoryUsage();

    // Get system metrics
    const uptime = typeof process !== 'undefined' ? process.uptime() : 0;

    const healthCheck: HealthCheckResult = {
      status: determineOverallStatus(services),
      timestamp: new Date().toISOString(),
      uptime,
      version: process.env.npm_package_version || '1.0.0',
      services,
      performance: {
        averageResponseTime: overallStats.averageDuration,
        errorRate: overallStats.errorRate,
        memoryUsage: {
          heapUsed: memoryUsage.heapUsed,
          heapTotal: memoryUsage.heapTotal,
          rss: memoryUsage.rss
        }
      },
      metrics: {
        totalRequests: overallStats.count,
        successfulRequests: Math.round(overallStats.count * (overallStats.successRate / 100)),
        failedRequests: Math.round(overallStats.count * (overallStats.errorRate / 100)),
        recentErrors: overallStats.recentErrors.slice(0, 5)
      }
    };

    const responseTime = Date.now() - startTime;

    // Set appropriate HTTP status code based on health
    const httpStatus = healthCheck.status === 'healthy' ? 200 
      : healthCheck.status === 'degraded' ? 200 
      : 503;

    return NextResponse.json(healthCheck, { 
      status: httpStatus,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Health-Check-Duration': responseTime.toString()
      }
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    const errorResponse: HealthCheckResult = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: typeof process !== 'undefined' ? process.uptime() : 0,
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: { status: 'unhealthy', lastChecked: new Date().toISOString(), error: 'Health check failed' },
        dkimGenerator: { status: 'unhealthy', lastChecked: new Date().toISOString(), error: 'Health check failed' },
        dmarcGenerator: { status: 'unhealthy', lastChecked: new Date().toISOString(), error: 'Health check failed' },
        dnsValidation: { status: 'unhealthy', lastChecked: new Date().toISOString(), error: 'Health check failed' }
      },
      performance: {
        averageResponseTime: 0,
        errorRate: 100,
        memoryUsage: { heapUsed: 0, heapTotal: 0, rss: 0 }
      },
      metrics: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        recentErrors: [error instanceof Error ? error.message : 'Unknown error']
      }
    };

    return NextResponse.json(errorResponse, { status: 503 });
  }
}
