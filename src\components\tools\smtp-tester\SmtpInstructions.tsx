/**
 * SMTP Instructions Component
 *
 * Setup instructions and guides for SMTP configuration
 */

'use client';

import React from 'react';

export default function SmtpInstructions() {
  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-[#4a3728]/20 p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-[#f59e0b] to-[#ce601c] flex items-center justify-center flex-shrink-0" style={{ aspectRatio: '1' }}>
          <svg className="w-6 h-6 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div>
          <h2 className="text-2xl font-bold" style={{ color: '#1b130e' }}>Setup Instructions</h2>
          <p className="text-sm" style={{ color: '#4a3728' }}>Quick setup guides for popular email providers</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Gmail Setup */}
        <div className="bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-4">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-.904.732-1.636 1.636-1.636h3.819v9.273L12 8.183l6.545 4.91V3.82h3.819c.904 0 1.636.733 1.636 1.637z"/>
              </svg>
            </div>
            <h3 className="font-semibold text-[#1b130e]">Gmail</h3>
          </div>
          <div className="space-y-2 text-sm text-[#4a3728]">
            <p><strong>Server:</strong> smtp.gmail.com</p>
            <p><strong>Port:</strong> 587 (TLS)</p>
            <p><strong>Auth:</strong> Use App Password</p>
            <p className="text-xs">Enable 2FA and generate App Password at Google Account → Security → App Passwords</p>
          </div>
        </div>

        {/* Outlook Setup */}
        <div className="bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-4">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M7.462 0C3.348 0 0 3.348 0 7.462v9.076C0 20.652 3.348 24 7.462 24h9.076C20.652 24 24 20.652 24 16.538V7.462C24 3.348 20.652 0 16.538 0H7.462zM12 6.462c3.038 0 5.538 2.5 5.538 5.538S15.038 17.538 12 17.538 6.462 15.038 6.462 12 8.962 6.462 12 6.462z"/>
              </svg>
            </div>
            <h3 className="font-semibold text-[#1b130e]">Outlook</h3>
          </div>
          <div className="space-y-2 text-sm text-[#4a3728]">
            <p><strong>Server:</strong> smtp-mail.outlook.com</p>
            <p><strong>Port:</strong> 587 (TLS)</p>
            <p><strong>Auth:</strong> Email & Password</p>
            <p className="text-xs">Use your Outlook.com or Hotmail address. May need app password if 2FA enabled.</p>
          </div>
        </div>

        {/* Yahoo Setup */}
        <div className="bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-4">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.568 8.16l-3.84 6.24h-1.92l2.88-4.32-2.88-4.32h1.92l1.92 2.88 1.92-2.88h1.92l-2.88 4.32 2.88 4.32h-1.92l-1.92-2.88z"/>
              </svg>
            </div>
            <h3 className="font-semibold text-[#1b130e]">Yahoo</h3>
          </div>
          <div className="space-y-2 text-sm text-[#4a3728]">
            <p><strong>Server:</strong> smtp.mail.yahoo.com</p>
            <p><strong>Port:</strong> 587 (TLS)</p>
            <p><strong>Auth:</strong> Use App Password</p>
            <p className="text-xs">Generate App Password at Yahoo Account Security → Generate app password</p>
          </div>
        </div>
      </div>

      {/* Security Note */}
      <div className="mt-6 p-4 bg-[#66b077]/5 border border-[#66b077]/20 rounded-lg">
        <div className="flex items-start gap-3">
          <svg className="w-5 h-5 text-[#66b077] mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <div>
            <h4 className="font-semibold text-[#1b130e] mb-1">Security Best Practices</h4>
            <ul className="text-sm text-[#4a3728] space-y-1">
              <li>• Always use App Passwords instead of your main account password</li>
              <li>• Enable two-factor authentication (2FA) on your email account</li>
              <li>• Use TLS encryption (port 587) for secure connections</li>
              <li>• Test with a dedicated email account, not your primary one</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
