# EMAIL_TESTER_DOMAIN Environment Variable

## Overview
The `EMAIL_TESTER_DOMAIN` environment variable allows you to configure the domain used for generating test email addresses in the VanishPost email tester tool without modifying the source code.

## Usage

### Setting the Environment Variable

Add the following to your `.env.local` file:

```bash
EMAIL_TESTER_DOMAIN=your-domain.com
```

### Examples

**Default behavior (no environment variable set):**
```bash
# No EMAIL_TESTER_DOMAIN set
# Generated addresses: <EMAIL>
```

**Custom domain:**
```bash
EMAIL_TESTER_DOMAIN=vanishpost.com
# Generated addresses: <EMAIL>
```

**Another custom domain:**
```bash
EMAIL_TESTER_DOMAIN=myemail.example.com
# Generated addresses: <EMAIL>
```

## Implementation Details

### Files Modified
- `src/lib/tools/email-tester/database.ts` - Email address generation
- `src/lib/config/domainService.ts` - Fallback domain configuration

### Backward Compatibility
- **Default Value**: `fademail.site` (maintains existing behavior)
- **Fallback**: If `EMAIL_TESTER_DOMAIN` is not set, the system uses `fademail.site`
- **No Breaking Changes**: Existing functionality works unchanged

### Code Implementation

**Email Address Generation:**
```typescript
const emailDomain = process.env.EMAIL_TESTER_DOMAIN || 'fademail.site';
const testAddress = `test-${uniqueId}@${emailDomain}`;
```

**Domain Service Fallbacks:**
```typescript
return process.env.EMAIL_TESTER_DOMAIN || 'fademail.site';
```

## Requirements for Custom Domains

When using a custom domain with `EMAIL_TESTER_DOMAIN`, ensure:

1. **MX Records**: The domain has proper MX records configured
2. **Email Routing**: Emails to the domain are routed to your Guerrilla Mail database
3. **Database Access**: Your application can read emails from the configured domain
4. **DNS Configuration**: The domain is properly configured for email delivery

## Testing

To test the configuration:

1. Set `EMAIL_TESTER_DOMAIN` in your `.env.local`
2. Restart your development server: `npm run dev`
3. Generate a test email address in the email tester tool
4. Verify the generated address uses your custom domain

## Security Considerations

- Only use domains you control and have configured for email reception
- Ensure the domain is properly secured and monitored
- Test email delivery before using in production

## Troubleshooting

**Issue**: Generated emails still use `fademail.site`
**Solution**: Ensure `EMAIL_TESTER_DOMAIN` is set in `.env.local` and restart the server

**Issue**: Emails not received on custom domain
**Solution**: Verify MX records and email routing configuration for your domain

**Issue**: Build errors after setting the variable
**Solution**: Ensure the domain value is a valid string without special characters
