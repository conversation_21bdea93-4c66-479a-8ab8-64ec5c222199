'use client';

/**
 * Rate Limit Configuration Component for Admin Dashboard
 * 
 * Provides interface for managing dynamic rate limit configurations
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Badge } from '@/components/ui/Badge';
import { AdminSwitch, SwitchWithLabel } from '@/components/ui/Switch';
import { Tabs, TabsContent, TabsList, AdminTabsTrigger } from '@/components/ui/Tabs';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { Separator } from '@/components/ui/Separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/Tooltip';
import { AdminFilterSelect } from '@/components/ui/Select';
import {
  Card as ShadcnCard,
  CardContent as ShadcnCardContent,
  CardHeader as ShadcnCardHeader,
  CardTitle as ShadcnCardTitle,
} from '@/components/ui/ShadcnCard';
import {
  Settings,
  Clock,
  Shield,
  Save,
  RefreshCw,
  Plus,
  Edit,
  Users,
  Timer
} from 'lucide-react';



interface RateLimitRule {
  id: string;
  endpoint: string;
  name: string;
  description: string;
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
  isActive: boolean;
  updatedAt: string;
  updatedBy: string;
}

export default function RateLimitConfiguration() {
  const [configs, setConfigs] = useState<RateLimitRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingConfig, setEditingConfig] = useState<RateLimitRule | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [toggleLoading, setToggleLoading] = useState<string | null>(null);

  // Session configuration state
  const [sessionConfig, setSessionConfig] = useState<any>(null);
  const [sessionLoading, setSessionLoading] = useState(false);

  // Form state for pending changes
  const [pendingSessionConfig, setPendingSessionConfig] = useState<any>({});
  const [pendingRateLimitConfig, setPendingRateLimitConfig] = useState<any>({});

  // Save states
  const [savingSession, setSavingSession] = useState(false);
  const [savingEmailLimits, setSavingEmailLimits] = useState(false);

  // Enable/disable toggles
  const [emailLimitsEnabled, setEmailLimitsEnabled] = useState(true);

  // Change tracking
  const [hasSessionChanges, setHasSessionChanges] = useState(false);
  const [hasEmailLimitChanges, setHasEmailLimitChanges] = useState(false);
  const [activeTab, setActiveTab] = useState<'rate-limits' | 'sessions'>('rate-limits');

  // Form state with human-friendly time inputs
  const [formData, setFormData] = useState({
    endpoint: '',
    name: '',
    description: '',
    windowMs: 3600000, // 1 hour (internal storage)
    maxRequests: 15,
    skipSuccessfulRequests: false,
    skipFailedRequests: true,
    isActive: true
  });

  // Human-friendly time inputs
  const [timeInput, setTimeInput] = useState({
    value: 1,
    unit: 'hours' // 'seconds', 'minutes', 'hours'
  });

  // Email generation time window state
  const [emailTimeWindow, setEmailTimeWindow] = useState({
    value: 60,
    unit: 'minutes' // 'seconds', 'minutes', 'hours'
  });

  // Helper function to convert time window to milliseconds
  const convertTimeToMs = (value: number, unit: string): number => {
    switch (unit) {
      case 'seconds':
        return value * 1000;
      case 'minutes':
        return value * 60 * 1000;
      case 'hours':
        return value * 60 * 60 * 1000;
      default:
        return value * 60 * 1000; // Default to minutes
    }
  };

  // Helper function to convert milliseconds to time window
  const convertMsToTime = (ms: number): { value: number; unit: string } => {
    if (ms % (60 * 60 * 1000) === 0) {
      return { value: ms / (60 * 60 * 1000), unit: 'hours' };
    } else if (ms % (60 * 1000) === 0) {
      return { value: ms / (60 * 1000), unit: 'minutes' };
    } else {
      return { value: ms / 1000, unit: 'seconds' };
    }
  };

  useEffect(() => {
    fetchConfigs();
    fetchSessionConfig();
  }, []);

  const fetchSessionConfig = async () => {
    try {
      setSessionLoading(true);
      const response = await fetch('/api/admin/session-config');

      if (!response.ok) {
        console.error('HTTP Error:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error response body:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setSessionConfig(result.data);
        // Initialize pending state with current values
        setPendingSessionConfig({
          sessionDuration: result.data.sessionDuration,
          maxSessionExtensions: result.data.maxSessionExtensions
        });
        setPendingRateLimitConfig({
          emailGeneration: {
            sessionLimits: result.data.rateLimits?.emailGeneration?.sessionLimits || {}
          }
        });
        // Initialize toggle states based on database values
        // We need to check the enabled field from the database


        // Get the enabled state from the database (this is the source of truth)
        const emailEnabledInDB = result.data.rateLimits?.emailGeneration?.enabled === true;

        // Initialize email time window from current window time
        const emailWindowMs = result.data.rateLimits?.emailGeneration?.sessionLimits?.windowMs || 3600000;
        setEmailTimeWindow(convertMsToTime(emailWindowMs));

        setEmailLimitsEnabled(emailEnabledInDB);
      } else {
        console.error('Failed to fetch session config:', result.error);
        console.error('Full error response:', result);
      }
    } catch (error) {
      console.error('Error fetching session config:', error);
    } finally {
      setSessionLoading(false);
    }
  };

  const updateSessionConfig = async (updates: any) => {
    try {
      setSessionLoading(true);
      const response = await fetch('/api/admin/session-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateSession',
          config: updates
        })
      });

      const result = await response.json();

      if (result.success) {
        setSessionConfig(result.data);
        alert('Session configuration updated successfully!');
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Error updating session config:', error);
      alert('Failed to update session configuration');
    } finally {
      setSessionLoading(false);
    }
  };

  const updateRateLimitConfig = async (updates: any) => {
    try {
      setSessionLoading(true);
      const response = await fetch('/api/admin/session-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateRateLimit',
          config: updates
        })
      });

      const result = await response.json();

      if (result.success) {
        // Refresh session config to get updated rate limits
        fetchSessionConfig();
        alert('Rate limit configuration updated successfully!');
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Error updating rate limit config:', error);
      alert('Failed to update rate limit configuration');
    } finally {
      setSessionLoading(false);
    }
  };

  // Save functions for each section
  const saveSessionSettings = async () => {
    try {
      setSavingSession(true);
      await updateSessionConfig(pendingSessionConfig);
      setHasSessionChanges(false);
    } catch (error) {
      console.error('Error saving session settings:', error);
    } finally {
      setSavingSession(false);
    }
  };

  const saveEmailLimits = async () => {
    try {
      setSavingEmailLimits(true);

      // Get current config to preserve ipLimits
      const currentEmailConfig = sessionConfig.rateLimits?.emailGeneration || {};

      const timeWindowMs = convertTimeToMs(emailTimeWindow.value, emailTimeWindow.unit);

      const config = emailLimitsEnabled ? {
        ...currentEmailConfig,
        ...pendingRateLimitConfig.emailGeneration,
        sessionLimits: {
          windowMs: timeWindowMs,
          maxRequests: pendingRateLimitConfig.emailGeneration?.sessionLimits?.maxRequests || 8,
          blockDuration: timeWindowMs // Block duration same as window time
        },
        enabled: true
      } : {
        ...currentEmailConfig,
        sessionLimits: { maxRequests: 0, windowMs: timeWindowMs, blockDuration: 0 },
        enabled: false
      };

      await updateRateLimitConfig({ emailGeneration: config });
      setHasEmailLimitChanges(false);

      // Refresh the configuration to ensure UI is in sync
      await fetchSessionConfig();
    } catch (error) {
      console.error('Error saving email limits:', error);
    } finally {
      setSavingEmailLimits(false);
    }
  };



  // Helper functions for updating pending state
  const updatePendingSessionConfig = (updates: any) => {
    setPendingSessionConfig((prev: any) => ({ ...prev, ...updates }));
    setHasSessionChanges(true);
  };

  const updatePendingEmailLimits = (updates: any) => {
    setPendingRateLimitConfig((prev: any) => ({
      ...prev,
      emailGeneration: { ...prev.emailGeneration, ...updates }
    }));
    setHasEmailLimitChanges(true);
  };



  // Helper functions for time conversion
  const msToHumanTime = (ms: number) => {
    if (ms < 60000) { // Less than 1 minute
      return { value: Math.round(ms / 1000), unit: 'seconds' };
    } else if (ms < 3600000) { // Less than 1 hour
      return { value: Math.round(ms / 60000), unit: 'minutes' };
    } else {
      return { value: Math.round(ms / 3600000), unit: 'hours' };
    }
  };

  const humanTimeToMs = (value: number, unit: string) => {
    switch (unit) {
      case 'seconds': return value * 1000;
      case 'minutes': return value * 60 * 1000;
      case 'hours': return value * 60 * 60 * 1000;
      default: return value * 1000;
    }
  };

  const fetchConfigs = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/rate-limit-config');
      const result = await response.json();

      if (result.success) {
        console.log('Fetched configs from API:', result.data);
        setConfigs(result.data);
      }
    } catch (error) {
      console.error('Error fetching rate limit configs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      const url = editingConfig
        ? '/api/admin/rate-limit-config'
        : '/api/admin/rate-limit-config';

      const method = editingConfig ? 'PUT' : 'POST';

      // Convert human-friendly time to milliseconds before sending
      const dataToSend = {
        ...formData,
        windowMs: humanTimeToMs(timeInput.value, timeInput.unit)
      };

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataToSend)
      });

      const result = await response.json();
      
      if (result.success) {
        await fetchConfigs();
        setEditingConfig(null);
        setShowCreateForm(false);
        resetForm();
      } else {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Error saving config:', error);
      alert('An error occurred while saving');
    }
  };

  const handleEdit = (config: RateLimitRule) => {
    setFormData({
      endpoint: config.endpoint || '',
      name: config.name || '',
      description: config.description || '',
      windowMs: config.windowMs || 3600000,
      maxRequests: config.maxRequests || 15,
      skipSuccessfulRequests: config.skipSuccessfulRequests || false,
      skipFailedRequests: config.skipFailedRequests || true,
      isActive: config.isActive !== undefined ? config.isActive : true
    });

    // Convert milliseconds to human-friendly format
    const humanTime = msToHumanTime(config.windowMs || 3600000);
    setTimeInput(humanTime);

    setEditingConfig(config);
    setShowCreateForm(false);
  };

  const handleToggleActive = async (config: RateLimitRule) => {
    if (toggleLoading === config.id) return; // Prevent double-clicks

    setToggleLoading(config.id);

    try {
      console.log('Toggling config:', {
        endpoint: config.endpoint,
        currentState: config.isActive,
        newState: !config.isActive
      });

      const response = await fetch('/api/admin/rate-limit-config', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          endpoint: config.endpoint,
          isActive: !config.isActive
        })
      });

      const result = await response.json();
      console.log('Toggle response:', result);

      if (result.success) {
        // Update local state immediately for better UX
        setConfigs(prevConfigs =>
          prevConfigs.map(c =>
            c.id === config.id
              ? { ...c, isActive: !c.isActive }
              : c
          )
        );
        // Also refresh from server to ensure consistency
        await fetchConfigs();
      } else {
        console.error('Toggle failed:', result.error);
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Error toggling config:', error);
      alert('Failed to toggle configuration');
    } finally {
      setToggleLoading(null);
    }
  };

  const resetForm = () => {
    setFormData({
      endpoint: '',
      name: '',
      description: '',
      windowMs: 3600000,
      maxRequests: 15,
      skipSuccessfulRequests: false,
      skipFailedRequests: true,
      isActive: true
    });
    setTimeInput({ value: 1, unit: 'hours' });
  };



  if (loading) {
    return <div className="p-6">Loading rate limit configurations...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Rate Limit & Session Configuration</h1>
        <div className="flex gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => {
                  fetchConfigs();
                  fetchSessionConfig();
                }}
                variant="outline"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Reload all configurations from database</p>
            </TooltipContent>
          </Tooltip>
          {activeTab === 'rate-limits' && (
            <Button
              onClick={() => {
                setShowCreateForm(true);
                setEditingConfig(null);
                resetForm();
              }}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Configuration
            </Button>
          )}
        </div>
      </div>

      {/* Enhanced Tab Navigation */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'rate-limits' | 'sessions')}>
        <TabsList className="w-full">
          <AdminTabsTrigger
            value="rate-limits"
            className="flex-1"
            icon={<Shield className="w-4 h-4" />}
          >
            Rate Limits
          </AdminTabsTrigger>
          <AdminTabsTrigger
            value="sessions"
            className="flex-1"
            icon={<Users className="w-4 h-4" />}
          >
            Session Management
          </AdminTabsTrigger>
        </TabsList>

        <TabsContent value="sessions" className="space-y-6">
          {sessionLoading ? (
            <div className="p-6">Loading session configuration...</div>
          ) : sessionConfig && Object.keys(sessionConfig).length > 0 ? (
            <>
              {/* Current Session Settings */}
              <ShadcnCard className="shadow-sm">
                <ShadcnCardHeader>
                  <ShadcnCardTitle className="flex items-center gap-2">
                    <Timer className="w-5 h-5 text-blue-600" />
                    Current Session Settings
                    <Badge variant="secondary" className="ml-2">
                      Live
                    </Badge>
                  </ShadcnCardTitle>
                </ShadcnCardHeader>

                <Separator />

                <ShadcnCardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="bg-blue-50 p-4 rounded-lg cursor-help hover:bg-blue-100 transition-colors">
                          <h3 className="font-medium text-blue-900 flex items-center gap-1">
                            <Timer className="w-4 h-4" />
                            Session Duration
                          </h3>
                          <p className="text-2xl font-bold text-blue-700">
                            {sessionConfig.sessionDuration ? Math.round(sessionConfig.sessionDuration / (60 * 60 * 1000)) : 2}h
                          </p>
                          <p className="text-sm text-blue-600">Hours per session</p>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>How long each user session remains active</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="bg-green-50 p-4 rounded-lg cursor-help hover:bg-green-100 transition-colors">
                          <h3 className="font-medium text-green-900 flex items-center gap-1">
                            <Plus className="w-4 h-4" />
                            Max Extensions
                          </h3>
                          <p className="text-2xl font-bold text-green-700">
                            {sessionConfig.maxSessionExtensions || 3}
                          </p>
                          <p className="text-sm text-green-600">Extension limit</p>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Maximum number of times a session can be extended</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="bg-purple-50 p-4 rounded-lg cursor-help hover:bg-purple-100 transition-colors">
                          <h3 className="font-medium text-purple-900 flex items-center gap-1">
                            <RefreshCw className="w-4 h-4" />
                            Activity Interval
                          </h3>
                          <p className="text-2xl font-bold text-purple-700">
                            {sessionConfig.activityUpdateInterval ? Math.round(sessionConfig.activityUpdateInterval / 60000) : 1}m
                          </p>
                          <p className="text-sm text-purple-600">Update frequency</p>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>How often session activity is tracked and updated</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="bg-orange-50 p-4 rounded-lg cursor-help hover:bg-orange-100 transition-colors">
                          <h3 className="font-medium text-orange-900 flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            Extension Duration
                          </h3>
                          <p className="text-2xl font-bold text-orange-700">
                            {sessionConfig.sessionExtensionDuration ? Math.round(sessionConfig.sessionExtensionDuration / (60 * 60 * 1000)) : 2}h
                          </p>
                          <p className="text-sm text-orange-600">Per extension</p>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Additional time added when session is extended</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </ShadcnCardContent>
              </ShadcnCard>

              {/* Session Configuration Controls */}
              <ShadcnCard className="shadow-sm">
                <ShadcnCardHeader>
                  <ShadcnCardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Settings className="w-5 h-5 text-green-600" />
                      Session Configuration Controls
                    </div>
                    {hasSessionChanges && (
                      <Badge variant="warning" className="bg-yellow-100 text-yellow-800">
                        Unsaved Changes
                      </Badge>
                    )}
                  </ShadcnCardTitle>
                </ShadcnCardHeader>

                <Separator />

                <ShadcnCardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2">Session Duration (hours)</label>
                      <Input
                        type="number"
                        min="2"
                        max="48"
                        value={pendingSessionConfig.sessionDuration ? Math.round(pendingSessionConfig.sessionDuration / (60 * 60 * 1000)) : 2}
                        onChange={(e) => {
                          const hours = parseInt(e.target.value);
                          if (!isNaN(hours)) {
                            updatePendingSessionConfig({
                              sessionDuration: hours * 60 * 60 * 1000
                            });
                          }
                        }}
                        className={`w-full ${hasSessionChanges ? 'border-yellow-300' : ''}`}
                      />
                      <p className="text-xs text-gray-500 mt-1">How long sessions persist (2-48 hours)</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Max Session Extensions</label>
                      <Input
                        type="number"
                        min="0"
                        max="10"
                        value={pendingSessionConfig.maxSessionExtensions || 3}
                        onChange={(e) => {
                          const extensions = parseInt(e.target.value);
                          if (!isNaN(extensions)) {
                            updatePendingSessionConfig({
                              maxSessionExtensions: extensions
                            });
                          }
                        }}
                        className={`w-full ${hasSessionChanges ? 'border-yellow-300' : ''}`}
                      />
                      <p className="text-xs text-gray-500 mt-1">Maximum number of extensions allowed</p>
                    </div>
                  </div>

                  {/* Save Button */}
                  <div className="flex justify-end pt-4 border-t">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          onClick={saveSessionSettings}
                          disabled={!hasSessionChanges || savingSession}
                          className="flex items-center gap-2"
                        >
                          {savingSession ? (
                            <>
                              <RefreshCw className="w-4 h-4 animate-spin" />
                              Saving...
                            </>
                          ) : (
                            <>
                              <Save className="w-4 h-4" />
                              Save Session Settings
                            </>
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Apply changes to session configuration</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </ShadcnCardContent>
              </ShadcnCard>

              {/* Custom Session Rate Limits */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Custom Session Rate Limits
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Email Generation Limits */}
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-medium">Email Generation Limits</h4>
                        <p className="text-sm text-gray-500 mt-1">
                          Current: {sessionConfig?.rateLimits?.emailGeneration?.sessionLimits?.maxRequests || 0} emails per {emailTimeWindow.value} {emailTimeWindow.unit}
                          {!sessionConfig?.rateLimits?.emailGeneration?.enabled &&
                            <span className="text-red-600 font-medium"> (DISABLED)</span>
                          }
                          {sessionConfig?.rateLimits?.emailGeneration?.enabled &&
                            <span className="text-green-600 font-medium"> (ENABLED)</span>
                          }
                        </p>
                      </div>
                      <div className="flex items-center gap-4">
                        {hasEmailLimitChanges && (
                          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                            Unsaved Changes
                          </Badge>
                        )}
                        <AdminSwitch
                          label="Enable Email Limits"
                          description={emailLimitsEnabled ? "Rate limits are active" : "Unlimited email generation"}
                          checked={emailLimitsEnabled}
                          status={emailLimitsEnabled ? 'enabled' : 'disabled'}
                          onCheckedChange={(checked) => {
                            setEmailLimitsEnabled(checked);
                            setHasEmailLimitChanges(true);
                          }}
                        />
                      </div>
                    </div>

                    <div className={`grid grid-cols-1 md:grid-cols-2 gap-4 ${!emailLimitsEnabled ? 'opacity-50' : ''}`}>
                      <div>
                        <label className="block text-sm font-medium mb-2">Max Emails</label>
                        <Input
                          type="number"
                          min="1"
                          max="100"
                          value={pendingRateLimitConfig.emailGeneration?.sessionLimits?.maxRequests || 8}
                          onChange={(e) => {
                            const maxRequests = parseInt(e.target.value);
                            if (!isNaN(maxRequests)) {
                              updatePendingEmailLimits({
                                sessionLimits: {
                                  ...pendingRateLimitConfig.emailGeneration?.sessionLimits,
                                  maxRequests
                                }
                              });
                            }
                          }}
                          disabled={!emailLimitsEnabled}
                          className={`w-full ${hasEmailLimitChanges ? 'border-yellow-300' : ''}`}
                        />
                        <p className="text-xs text-gray-500 mt-1">Maximum emails allowed per session within the time window</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Time Window</label>
                        <div className="flex gap-2">
                          <Input
                            type="number"
                            min="1"
                            max="24"
                            value={emailTimeWindow.value}
                            onChange={(e) => {
                              const value = parseInt(e.target.value);
                              if (!isNaN(value) && value > 0) {
                                setEmailTimeWindow(prev => ({ ...prev, value }));
                                setHasEmailLimitChanges(true);
                              }
                            }}
                            disabled={!emailLimitsEnabled}
                            className={`flex-1 ${hasEmailLimitChanges ? 'border-yellow-300' : ''}`}
                          />
                          <AdminFilterSelect
                            value={emailTimeWindow.unit}
                            onValueChange={(value) => {
                              setEmailTimeWindow(prev => ({ ...prev, unit: value }));
                              setHasEmailLimitChanges(true);
                            }}

                            placeholder="Select unit"
                            options={[
                              {
                                value: "seconds",
                                label: "Seconds",
                                icon: <Timer className="h-4 w-4" />
                              },
                              {
                                value: "minutes",
                                label: "Minutes",
                                icon: <Clock className="h-4 w-4" />
                              },
                              {
                                value: "hours",
                                label: "Hours",
                                icon: <Clock className="h-4 w-4" />
                              }
                            ]}
                            className={`${hasEmailLimitChanges ? 'border-yellow-300' : ''}`}
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-1">Time period for rate limiting and block duration</p>
                      </div>
                    </div>

                    {/* Save Button */}
                    <div className="flex justify-end pt-4 border-t mt-4">
                      <Button
                        onClick={saveEmailLimits}
                        disabled={!hasEmailLimitChanges || savingEmailLimits}
                        className="flex items-center gap-2"
                      >
                        {savingEmailLimits ? (
                          <>
                            <RefreshCw className="w-4 h-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="w-4 h-4" />
                            Save Email Limits
                          </>
                        )}
                      </Button>
                    </div>
                  </div>


                </CardContent>
              </Card>


            </>
          ) : (
            <div className="p-6">Failed to load session configuration</div>
          )}
        </TabsContent>

        <TabsContent value="rate-limits" className="space-y-6">
          {/* Configuration Form */}
          {(editingConfig || showCreateForm) && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingConfig ? 'Edit Rate Limit Configuration' : 'Create Rate Limit Configuration'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Endpoint</label>
                <Input
                  value={formData.endpoint}
                  onChange={(e) => setFormData({ ...formData, endpoint: e.target.value })}
                  placeholder="e.g., emailGeneration"
                  disabled={!!editingConfig}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="e.g., Email Generation"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Description</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Description of this rate limit"
                rows={2}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  Time Window
                </label>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    value={(timeInput.value || 1).toString()}
                    onChange={(e) => {
                      const newValue = parseInt(e.target.value);
                      setTimeInput({
                        ...timeInput,
                        value: isNaN(newValue) ? 1 : Math.max(1, newValue)
                      });
                    }}
                    min="1"
                    className="flex-1"
                    placeholder="Duration"
                  />
                  <AdminFilterSelect
                    value={timeInput.unit}
                    onValueChange={(value) => setTimeInput({ ...timeInput, unit: value })}
                    placeholder="Select unit"
                    options={[
                      {
                        value: "seconds",
                        label: "Seconds",
                        icon: <Timer className="h-4 w-4" />
                      },
                      {
                        value: "minutes",
                        label: "Minutes",
                        icon: <Clock className="h-4 w-4" />
                      },
                      {
                        value: "hours",
                        label: "Hours",
                        icon: <Clock className="h-4 w-4" />
                      }
                    ]}
                    className="w-32"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Rate limit window: {timeInput.value} {timeInput.unit}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Max Requests</label>
                <Input
                  type="number"
                  value={(formData.maxRequests || 1).toString()}
                  onChange={(e) => {
                    const newValue = parseInt(e.target.value);
                    setFormData({
                      ...formData,
                      maxRequests: isNaN(newValue) ? 1 : Math.max(1, newValue)
                    });
                  }}
                  min="1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <SwitchWithLabel
                label="Skip Successful Requests"
                checked={formData.skipSuccessfulRequests}
                onCheckedChange={(checked) => setFormData({ ...formData, skipSuccessfulRequests: checked })}
              />
              <SwitchWithLabel
                label="Skip Failed Requests"
                checked={formData.skipFailedRequests}
                onCheckedChange={(checked) => setFormData({ ...formData, skipFailedRequests: checked })}
              />
              <SwitchWithLabel
                label="Active"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                {editingConfig ? 'Update' : 'Create'}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setEditingConfig(null);
                  setShowCreateForm(false);
                  resetForm();
                }}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Configurations List */}
      <ShadcnCard className="shadow-sm">
        <ShadcnCardHeader>
          <ShadcnCardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2 text-blue-600" />
            Current Rate Limit Configurations
            {configs.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {configs.length}
              </Badge>
            )}
          </ShadcnCardTitle>
        </ShadcnCardHeader>

        <Separator />

        <ShadcnCardContent className="p-0">
          <ScrollArea className="h-96 w-full">
            <div className="space-y-3 p-4">
              {configs.map((config) => (
                <ShadcnCard key={config.id} className="py-3 hover:shadow-md transition-all duration-200">
                  <ShadcnCardContent className="px-4 py-0">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-medium">{config.name}</h3>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <code className="bg-gray-100 px-2 py-1 rounded text-sm cursor-help">
                                {config.endpoint}
                              </code>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>API endpoint: {config.endpoint}</p>
                            </TooltipContent>
                          </Tooltip>

                          <Badge variant={config.isActive ? 'default' : 'secondary'}>
                            {config.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>

                        <p className="text-sm text-gray-600 mb-3">{config.description}</p>

                        <Separator className="my-2" />

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-xs text-gray-500">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center gap-1 cursor-help">
                                <Clock className="w-3 h-3" />
                                <span className="font-medium">Rate:</span> {config.maxRequests} requests per {(() => {
                                  const humanTime = msToHumanTime(config.windowMs);
                                  return `${humanTime.value} ${humanTime.unit}`;
                                })()}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Maximum requests allowed per time window</p>
                            </TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="cursor-help">
                                <span className="font-medium">Skip Success:</span> {config.skipSuccessfulRequests ? 'Yes' : 'No'}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Whether successful requests are excluded from rate limiting</p>
                            </TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="cursor-help">
                                <span className="font-medium">Skip Failed:</span> {config.skipFailedRequests ? 'Yes' : 'No'}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Whether failed requests are excluded from rate limiting</p>
                            </TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="cursor-help">
                                <span className="font-medium">Updated by:</span> {config.updatedBy}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Administrator who last modified this configuration</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(config)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Edit this rate limit configuration</p>
                          </TooltipContent>
                        </Tooltip>
                  <div className="flex items-center gap-2">
                    <SwitchWithLabel
                      label={config.isActive ? 'Active' : 'Inactive'}
                      checked={config.isActive}
                      onCheckedChange={() => handleToggleActive(config)}
                      disabled={toggleLoading === config.id}
                      labelPosition="left"
                          />
                        </div>
                      </div>
                    </div>
                  </ShadcnCardContent>
                </ShadcnCard>
              ))}
              {configs.length === 0 && (
                <div className="text-center py-8">
                  <Settings className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No rate limit configurations found</p>
                  <p className="text-sm text-gray-400 mt-1">Create your first configuration to get started</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </ShadcnCardContent>
      </ShadcnCard>
        </TabsContent>
      </Tabs>
    </div>
  );
}
