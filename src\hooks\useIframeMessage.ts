import { useCallback, useEffect, useRef } from 'react';

interface MessageHandler {
  (event: MessageEvent): void;
}

/**
 * Hook for handling iframe postMessage communication
 *
 * This hook provides functionality to:
 * - Set up message event listeners
 * - Send messages to iframe
 * - Clean up listeners on unmount
 */
export function useIframeMessage(iframeRef: React.RefObject<HTMLIFrameElement | null>, handler: MessageHandler) {
  // Ref to store the handler to avoid recreating the listener on every render
  const handlerRef = useRef<MessageHandler>(handler);

  // Update the handler ref when the handler changes
  useEffect(() => {
    handlerRef.current = handler;
  }, [handler]);

  // Set up the message event listener
  useEffect(() => {
    // Create a stable reference to the handler
    const messageHandler = (event: MessageEvent) => {
      handlerRef.current(event);
    };

    // Add the event listener
    window.addEventListener('message', messageHandler);

    // Clean up
    return () => {
      window.removeEventListener('message', messageHandler);
    };
  }, []);

  // Function to send a message to the iframe
  const sendMessage = useCallback((message: any) => {
    if (!iframeRef.current || !iframeRef.current.contentWindow) return;

    iframeRef.current.contentWindow.postMessage(message, '*');
  }, [iframeRef]);

  return {
    sendMessage
  };
}
