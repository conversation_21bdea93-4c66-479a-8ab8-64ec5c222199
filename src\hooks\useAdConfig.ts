'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase';

export interface AdConfig {
  placementId: string;
  domain: string;
  adUnitId: string;
  adClientId: string;
  isEnabled: boolean;
  deviceTypes: string[] | any;
  format?: string;
  displayOptions?: {
    position?: string;
    margin?: string;
    padding?: string;
    backgroundColor?: string;
  };
  schedule?: {
    startTime?: string;
    endTime?: string;
    daysOfWeek?: number[];
  };
}

// Interface for the database response
interface AdConfigDB {
  placement_id: string;
  domain: string;
  ad_unit_id: string;
  ad_client_id: string;
  is_enabled: boolean;
  device_types: string[] | any;
  format?: string;
  display_options?: any;
  schedule?: any;
  created_at?: string;
  updated_at?: string;
}

/**
 * Hook to fetch ad configuration from the database
 *
 * @param placementId The ID of the ad placement
 * @returns The ad configuration, loading state, and any error
 */
export function useAdConfig(placementId: string) {
  const [adConfig, setAdConfig] = useState<AdConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Skip on server-side
    if (typeof window === 'undefined') return;

    const fetchAdConfig = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const supabase = createClient();
        const currentDomain = window.location.hostname;

        // Use fetch directly to avoid Supabase client issues
        const fetchOptions = {
          method: 'GET',
          headers: {
            'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        };

        // First try to get a domain-specific configuration
        const url = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/ad_config?select=*&placement_id=eq.${placementId}&domain=eq.${currentDomain}&limit=1`;

        const response = await fetch(url, fetchOptions);

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        const responseData = await response.json();
        const data = responseData.length > 0 ? responseData[0] : null;
        const fetchError = data ? null : { code: 'PGRST116', message: 'No rows returned' };

        // If no domain-specific config, try to get a wildcard config
        if (fetchError && fetchError.code === 'PGRST116') {
          // Use fetch directly for wildcard query too
          const wildcardUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/ad_config?select=*&placement_id=eq.${placementId}&domain=eq.*&limit=1`;

          const wildcardResponse = await fetch(wildcardUrl, fetchOptions);

          if (!wildcardResponse.ok) {
            throw new Error(`API error: ${wildcardResponse.status}`);
          }

          const wildcardResponseData = await wildcardResponse.json();
          const wildcardData = wildcardResponseData.length > 0 ? wildcardResponseData[0] : null;
          const wildcardError = wildcardData ? null : { message: 'No wildcard config found' };

          if (wildcardError) {
            console.warn(`No ad configuration found for placement ${placementId}`);
            setAdConfig(null);
          } else {
            // Map the database response to our AdConfig interface
            const dbConfig = wildcardData as AdConfigDB;
            setAdConfig({
              placementId: dbConfig.placement_id,
              domain: dbConfig.domain,
              adUnitId: dbConfig.ad_unit_id,
              adClientId: dbConfig.ad_client_id,
              isEnabled: dbConfig.is_enabled,
              deviceTypes: dbConfig.device_types,
              format: dbConfig.format,
              displayOptions: dbConfig.display_options,
              schedule: dbConfig.schedule
            });
          }
        } else if (fetchError) {
          throw new Error(fetchError.message);
        } else {
          // Map the database response to our AdConfig interface
          const dbConfig = data as AdConfigDB;
          setAdConfig({
            placementId: dbConfig.placement_id,
            domain: dbConfig.domain,
            adUnitId: dbConfig.ad_unit_id,
            adClientId: dbConfig.ad_client_id,
            isEnabled: dbConfig.is_enabled,
            deviceTypes: dbConfig.device_types,
            format: dbConfig.format,
            displayOptions: dbConfig.display_options,
            schedule: dbConfig.schedule
          });
        }
      } catch (err) {
        console.error('Error fetching ad configuration:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch ad configuration');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAdConfig();
  }, [placementId]);

  return { adConfig, isLoading, error };
}
