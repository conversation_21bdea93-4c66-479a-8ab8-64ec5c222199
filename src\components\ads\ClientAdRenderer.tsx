'use client';

import { useEffect, useState } from 'react';
import CollapsibleAdContainer from './CollapsibleAdContainer';

interface ClientAdRendererProps {
  domain: string;
  deviceType: 'desktop' | 'tablet' | 'mobile';
}

interface ClientAd {
  placementId: string;
  adUnitId: string;
  displayOptions?: any;
}

export default function ClientAdRenderer({ domain, deviceType }: ClientAdRendererProps) {
  const [ads, setAds] = useState<ClientAd[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch ads for the current domain and device type
  useEffect(() => {
    const fetchAds = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/ads?domain=${domain}&deviceType=${deviceType}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch ads: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
          setAds(data.data);
        } else {
          setError(data.error || 'Failed to fetch ads');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while fetching ads');
        console.error('Error fetching ads:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchAds();
  }, [domain, deviceType]);

  // Render nothing while loading or if there's an error
  if (loading || error || ads.length === 0) {
    return null;
  }

  return (
    <>
      {ads.map(ad => {
        // Check if this is a collapsible ad
        if (ad.displayOptions?.displayMode === 'collapsible') {
          return (
            <CollapsibleAdContainer
              key={`${ad.placementId}-${domain}`}
              adId={ad.placementId}
              adUnitId={ad.adUnitId}
              position={ad.displayOptions?.position || 'bottom'}
              displayOptions={{
                initiallyExpanded: ad.displayOptions?.initiallyExpanded,
                rememberState: ad.displayOptions?.rememberState,
                expandAfterMinutes: ad.displayOptions?.expandAfterMinutes,
                dismissible: ad.displayOptions?.dismissible
              }}
            />
          );
        }
        
        // For other ad types, we'll implement them later
        return null;
      })}
    </>
  );
}
