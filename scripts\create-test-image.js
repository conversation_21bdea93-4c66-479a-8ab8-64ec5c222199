// <PERSON>ript to create a simple test image
const fs = require('fs');
const { createCanvas } = require('canvas');

// Create a 300x200 canvas
const width = 300;
const height = 200;
const canvas = createCanvas(width, height);
const context = canvas.getContext('2d');

// Fill the background
context.fillStyle = '#4f46e5';
context.fillRect(0, 0, width, height);

// Add text
context.font = 'bold 24px Arial';
context.fillStyle = '#ffffff';
context.textAlign = 'center';
context.textBaseline = 'middle';
context.fillText('Test Image', width / 2, height / 2);

// Save the image
const buffer = canvas.toBuffer('image/png');
fs.writeFileSync('scripts/test-image.png', buffer);

console.log('Test image created: scripts/test-image.png');
