import { useCallback, useRef, useState, useEffect } from 'react';

/**
 * Simplified hook for managing iframe height adjustments
 *
 * This hook provides a simple, reliable way to:
 * - Adjust iframe height based on content
 * - Handle height changes from iframe messages
 * - Set minimum height constraints
 * - Coordinate height changes with scale transitions
 */
export function useIframeHeight(minHeight: number = 200) {
  // State to track current height
  const [height, setHeight] = useState<number>(minHeight);

  // Ref to track if height has been set
  const heightSetRef = useRef<boolean>(false);

  // Ref to track the last height for smoother transitions
  const lastHeightRef = useRef<number>(minHeight);

  // Ref to track animation frame request
  const animationFrameRef = useRef<number | null>(null);

  // Simple function to handle height messages from iframe
  const handleHeightMessage = useCallback((newHeight: number, isScaleChange: boolean = false) => {
    // Handle case when height is 0 - this can happen during initial load
    if (newHeight === 0) {
      // Just return the current height or minHeight if not set yet
      return heightSetRef.current ? lastHeightRef.current : minHeight;
    }

    // Ensure we're setting a valid height and not NaN or negative
    if (newHeight > 0 && !isNaN(newHeight)) {
      // Set a minimum height to prevent too small iframes
      const finalHeight = Math.max(minHeight, Math.ceil(newHeight));

      // Cancel any existing animation frame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      // Use requestAnimationFrame for smoother updates
      animationFrameRef.current = requestAnimationFrame(() => {
        // Update height state
        setHeight(finalHeight);
        lastHeightRef.current = finalHeight;
        heightSetRef.current = true;
        animationFrameRef.current = null;
      });

      return finalHeight;
    } else {
      console.warn('Invalid height received:', newHeight);
      return heightSetRef.current ? lastHeightRef.current : minHeight;
    }
  }, [minHeight]);

  // Function to reset height
  const resetHeight = useCallback(() => {
    // Cancel any ongoing animations
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // Reset height directly
    setHeight(minHeight);
    lastHeightRef.current = minHeight;
    heightSetRef.current = false;
  }, [minHeight]);

  // Function to set height directly (for external control)
  const setIframeHeight = useCallback((newHeight: number) => {
    const finalHeight = Math.max(minHeight, Math.ceil(newHeight));
    setHeight(finalHeight);
    lastHeightRef.current = finalHeight;
    heightSetRef.current = true;
  }, [minHeight]);

  // Clean up animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return {
    height,
    handleHeightMessage,
    resetHeight,
    setHeight: setIframeHeight,
    heightSet: heightSetRef.current
  };
}
