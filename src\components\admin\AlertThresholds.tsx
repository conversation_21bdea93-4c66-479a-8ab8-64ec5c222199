'use client';

import { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';
import { Spinner } from '../ui/Spinner';

interface AlertThreshold {
  category: string;
  threshold: number;
  description: string;
}

export default function AlertThresholds() {
  const [thresholds, setThresholds] = useState<AlertThreshold[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Descriptions for common alert categories
  const categoryDescriptions: Record<string, string> = {
    'ERROR': 'General errors across the application',
    'API_ERROR': 'Errors in API endpoints',
    'DATABASE_ERROR': 'Database connection or query errors',
    'AUTHENTICATION_FAILURE': 'Failed login attempts',
    'EMAIL_PROCESSING_ERROR': 'Errors processing emails',
    'CLEANUP_ERROR': 'Errors during email cleanup',
    'CONFIG_ERROR': 'Configuration service errors'
  };

  // Load thresholds on component mount
  useEffect(() => {
    fetchThresholds();
  }, []);

  // Fetch alert thresholds from API
  const fetchThresholds = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/monitoring');

      if (!response.ok) {
        throw new Error(`Failed to fetch thresholds: ${response.statusText}`);
      }

      const data = await response.json();

      // Convert thresholds object to array
      const thresholdsArray = Object.entries(data.thresholds).map(([category, threshold]) => ({
        category,
        threshold: threshold as number,
        description: categoryDescriptions[category] || 'Custom alert category'
      }));

      setThresholds(thresholdsArray);
    } catch (error) {
      setError(`Error loading alert thresholds: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle threshold change
  const handleThresholdChange = (index: number, value: number) => {
    const newThresholds = [...thresholds];
    newThresholds[index].threshold = value;
    setThresholds(newThresholds);
  };

  // Save thresholds
  const saveThresholds = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      // Convert thresholds array to object
      const thresholdsObject = thresholds.reduce((obj, item) => {
        obj[item.category] = item.threshold;
        return obj;
      }, {} as Record<string, number>);

      const response = await fetch('/api/management-portal-x7z9y2/monitoring', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ thresholds: thresholdsObject })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to save thresholds: ${response.statusText}`);
      }

      setSuccess('Alert thresholds updated successfully');

      // Refresh thresholds
      fetchThresholds();
    } catch (error) {
      setError(`Error saving alert thresholds: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setSaving(false);
    }
  };

  // Add new threshold
  const addThreshold = () => {
    setThresholds([
      ...thresholds,
      {
        category: '',
        threshold: 10,
        description: 'Custom alert category'
      }
    ]);
  };

  // Remove threshold
  const removeThreshold = (index: number) => {
    const newThresholds = [...thresholds];
    newThresholds.splice(index, 1);
    setThresholds(newThresholds);
  };

  // Update category
  const updateCategory = (index: number, category: string) => {
    const newThresholds = [...thresholds];
    newThresholds[index].category = category;
    newThresholds[index].description = categoryDescriptions[category] || 'Custom alert category';
    setThresholds(newThresholds);
  };

  return (
    <Card className="p-4">
      <h2 className="text-xl font-semibold mb-4">Alert Thresholds</h2>

      {error && (
        <div className="p-2 mb-4 bg-red-100 border border-red-300 rounded text-red-800">
          {error}
        </div>
      )}

      {success && (
        <div className="p-2 mb-4 bg-green-100 border border-green-300 rounded text-green-800">
          {success}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center p-4">
          <Spinner />
        </div>
      ) : (
        <>
          <div className="space-y-4">
            {thresholds.map((threshold, index) => (
              <div key={index} className="p-4 border rounded bg-gray-50">
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <input
                      type="text"
                      value={threshold.category}
                      onChange={(e) => updateCategory(index, e.target.value)}
                      className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      placeholder="Alert category"
                    />
                    <p className="text-sm text-gray-500 mt-1">{threshold.description}</p>
                  </div>

                  <div className="w-32">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Threshold
                    </label>
                    <Input
                      type="number"
                      min="1"
                      value={threshold.threshold}
                      onChange={(e) => handleThresholdChange(index, parseInt(e.target.value) || 1)}
                    />
                  </div>

                  <div className="flex items-end">
                    <Button
                      variant="danger"
                      onClick={() => removeThreshold(index)}
                      className="mt-4 md:mt-0"
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 flex flex-col sm:flex-row gap-2 justify-between">
            <Button onClick={addThreshold} variant="secondary">
              Add Threshold
            </Button>

            <Button onClick={saveThresholds} disabled={saving}>
              {saving ? <><Spinner size="sm" /> Saving...</> : 'Save Thresholds'}
            </Button>
          </div>
        </>
      )}
    </Card>
  );
}
