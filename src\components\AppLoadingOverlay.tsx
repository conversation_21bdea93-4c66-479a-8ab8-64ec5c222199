/**
 * AppLoadingOverlay Component
 * 
 * Displays a loading overlay for the entire application during initial load
 */

import React from 'react';

interface AppLoadingOverlayProps {
  /**
   * Whether the overlay is visible
   */
  isVisible: boolean;
}

export default function AppLoadingOverlay({ isVisible }: AppLoadingOverlayProps) {
  if (!isVisible) return null;
  
  return (
    <div 
      className="fixed inset-0 bg-white z-50 flex flex-col items-center justify-center transition-opacity duration-300"
      style={{ 
        opacity: isVisible ? 1 : 0,
        pointerEvents: isVisible ? 'auto' : 'none'
      }}
    >
      <div className="flex flex-col items-center">
        {/* Logo */}
        <div className="mb-6 flex items-center">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-10 w-10 text-[#ce601c]" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          >
            <path d="M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6z" />
            <path d="M22 6l-10 7L2 6" />
          </svg>
          <span className="ml-2 text-2xl font-bold text-neutral-800">VanishPost</span>
        </div>
        
        {/* Loading animation */}
        <div className="flex space-x-2">
          <div className="w-3 h-3 bg-[#ce601c] rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-3 h-3 bg-[#ce601c] rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-3 h-3 bg-[#ce601c] rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
        
        <p className="mt-4 text-sm text-neutral-500">Loading your temporary email...</p>
      </div>
    </div>
  );
}
