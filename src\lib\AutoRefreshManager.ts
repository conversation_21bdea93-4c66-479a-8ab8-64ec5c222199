/**
 * AutoRefreshManager - Singleton class to manage auto-refresh state across components
 *
 * This class ensures that only one auto-refresh interval is running at a time
 * by using localStorage to coordinate between different instances.
 */

import { TIMEOUTS, STORAGE } from './constants';

// Interface for auto-refresh state
interface AutoRefreshState {
  active: boolean;
  lastStartTime: number;
  interval: number;
  emailAddress: string | null;
}

class AutoRefreshManager {
  private static instance: AutoRefreshManager;
  private intervalId: NodeJS.Timeout | null = null;
  private timeoutId: NodeJS.Timeout | null = null;
  private refreshCallback: (() => Promise<void>) | null = null;
  private currentEmailAddress: string | null = null;
  private isPaused: boolean = false;
  private resumeTimeoutId: NodeJS.Timeout | null = null;

  // Private constructor to prevent direct instantiation
  private constructor() {
    // Initialize state from localStorage if available
    this.loadStateFromStorage();

    // Add storage event listener to sync state across tabs/instances
    if (typeof window !== 'undefined') {
      window.addEventListener('storage', this.handleStorageChange);
    }
  }

  // Get the singleton instance
  public static getInstance(): AutoRefreshManager {
    if (!AutoRefreshManager.instance) {
      AutoRefreshManager.instance = new AutoRefreshManager();
    }
    return AutoRefreshManager.instance;
  }

  // Handle storage changes from other tabs/instances
  private handleStorageChange = (event: StorageEvent) => {
    if (event.key === STORAGE.AUTO_REFRESH_ACTIVE_KEY ||
        event.key === STORAGE.AUTO_REFRESH_EMAIL_KEY) {
      this.loadStateFromStorage();

      // If auto-refresh was deactivated in another tab, stop it here too
      if (event.key === STORAGE.AUTO_REFRESH_ACTIVE_KEY && event.newValue === 'false') {
        this.stopAutoRefresh();
      }

      // If email address changed in another tab, restart auto-refresh
      if (event.key === STORAGE.AUTO_REFRESH_EMAIL_KEY &&
          event.newValue !== this.currentEmailAddress &&
          this.isAutoRefreshActive()) {
        this.stopAutoRefresh();
        if (this.refreshCallback && event.newValue) {
          this.currentEmailAddress = event.newValue;
          this.startAutoRefresh(this.refreshCallback, this.currentEmailAddress);
        }
      }
    }
  };

  // Load state from localStorage
  private loadStateFromStorage(): AutoRefreshState {
    if (typeof window === 'undefined') {
      return {
        active: false,
        lastStartTime: 0,
        interval: TIMEOUTS.AUTO_REFRESH_INTERVAL,
        emailAddress: null
      };
    }

    const active = localStorage.getItem(STORAGE.AUTO_REFRESH_ACTIVE_KEY) === 'true';
    const lastStartTime = parseInt(localStorage.getItem(STORAGE.AUTO_REFRESH_LAST_START_KEY) || '0', 10);
    const interval = parseInt(localStorage.getItem(STORAGE.AUTO_REFRESH_INTERVAL_KEY) ||
                             String(TIMEOUTS.AUTO_REFRESH_INTERVAL), 10);
    const emailAddress = localStorage.getItem(STORAGE.AUTO_REFRESH_EMAIL_KEY);

    return {
      active,
      lastStartTime,
      interval,
      emailAddress
    };
  }

  // Save state to localStorage
  private saveStateToStorage(state: Partial<AutoRefreshState>) {
    if (typeof window === 'undefined') return;

    if (state.active !== undefined) {
      localStorage.setItem(STORAGE.AUTO_REFRESH_ACTIVE_KEY, state.active.toString());
    }

    if (state.lastStartTime !== undefined) {
      localStorage.setItem(STORAGE.AUTO_REFRESH_LAST_START_KEY, state.lastStartTime.toString());
    }

    if (state.interval !== undefined) {
      localStorage.setItem(STORAGE.AUTO_REFRESH_INTERVAL_KEY, state.interval.toString());
    }

    if (state.emailAddress !== undefined && state.emailAddress !== null) {
      localStorage.setItem(STORAGE.AUTO_REFRESH_EMAIL_KEY, state.emailAddress);
    } else if (state.emailAddress === null) {
      localStorage.removeItem(STORAGE.AUTO_REFRESH_EMAIL_KEY);
    }
  }

  // Check if auto-refresh is active
  public isAutoRefreshActive(): boolean {
    return this.loadStateFromStorage().active;
  }

  // Get the current email address
  public getCurrentEmailAddress(): string | null {
    return this.loadStateFromStorage().emailAddress;
  }

  // Start auto-refresh
  public startAutoRefresh(
    refreshCallback: () => Promise<void>,
    emailAddress: string,
    initialDelayMs: number = 2000,
    intervalMs: number = TIMEOUTS.AUTO_REFRESH_INTERVAL
  ): void {
    console.log(`[${new Date().toISOString()}] [AutoRefreshManager] Starting auto-refresh for: ${emailAddress} with interval: ${intervalMs}ms`);

    // Stop any existing auto-refresh
    this.stopAutoRefresh();

    // Save callback and email address
    this.refreshCallback = refreshCallback;
    this.currentEmailAddress = emailAddress;

    // Update state in localStorage
    this.saveStateToStorage({
      active: true,
      lastStartTime: Date.now(),
      interval: intervalMs,
      emailAddress
    });

    // Start initial timeout
    this.timeoutId = setTimeout(() => {
      console.log(`[AutoRefreshManager] Performing initial refresh for: ${emailAddress}`);

      // Clear timeout ID
      this.timeoutId = null;

      // Perform initial refresh
      if (this.refreshCallback) {
        this.refreshCallback().catch(error => {
          console.error('[AutoRefreshManager] Error during initial refresh:', error);
        });
      }

      // Start interval for subsequent refreshes
      // Note: The actual interval between refreshes will be intervalMs + POST_FETCH_DELAY
      // because we add a delay after each successful fetch in the useAutoRefresh hook
      this.intervalId = setInterval(() => {
        console.log(`[AutoRefreshManager] Auto-refresh interval triggered for: ${emailAddress}`);

        // Check if auto-refresh is still active
        if (!this.isAutoRefreshActive() || this.getCurrentEmailAddress() !== emailAddress) {
          console.log(`[AutoRefreshManager] Auto-refresh conditions changed, stopping interval`);
          this.stopAutoRefresh();
          return;
        }

        // Perform refresh
        if (this.refreshCallback) {
          this.refreshCallback().catch(error => {
            console.error('[AutoRefreshManager] Error during auto-refresh:', error);
          });
        }
      }, intervalMs);

      const effectiveInterval = intervalMs + TIMEOUTS.POST_FETCH_DELAY;
      console.log(`[${new Date().toISOString()}] [AutoRefreshManager] Auto-refresh interval set up with ${intervalMs}ms base interval + ${TIMEOUTS.POST_FETCH_DELAY}ms post-fetch delay = ${effectiveInterval}ms effective interval for: ${emailAddress}`);
    }, initialDelayMs);

    console.log(`[${new Date().toISOString()}] [AutoRefreshManager] Auto-refresh will start in ${initialDelayMs}ms for: ${emailAddress}`);
  }

  // Stop auto-refresh
  public stopAutoRefresh(): void {
    console.log(`[${new Date().toISOString()}] [AutoRefreshManager] Stopping auto-refresh`);

    // Clear timeout
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    // Clear interval
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    // Clear resume timeout
    if (this.resumeTimeoutId) {
      clearTimeout(this.resumeTimeoutId);
      this.resumeTimeoutId = null;
    }

    // Reset paused state
    this.isPaused = false;

    // Update state in localStorage
    this.saveStateToStorage({
      active: false
    });
  }

  // Perform a manual refresh
  public performManualRefresh(): Promise<void> {
    console.log('[AutoRefreshManager] Performing manual refresh');

    if (this.refreshCallback) {
      return this.refreshCallback();
    }

    return Promise.resolve();
  }

  // Pause auto-refresh
  public pauseAutoRefresh(): void {
    console.log(`[${new Date().toISOString()}] [AutoRefreshManager] Pausing auto-refresh`);

    // Only pause if auto-refresh is active and not already paused
    if (this.isAutoRefreshActive() && !this.isPaused) {
      this.isPaused = true;

      // Clear the interval but don't update the active state in localStorage
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }

      // Clear any existing resume timeout
      if (this.resumeTimeoutId) {
        clearTimeout(this.resumeTimeoutId);
        this.resumeTimeoutId = null;
      }
    }
  }

  // Resume auto-refresh after a delay
  public resumeAutoRefresh(delayMs: number = 0): void {
    console.log(`[${new Date().toISOString()}] [AutoRefreshManager] Resuming auto-refresh after ${delayMs}ms delay`);

    // Only resume if auto-refresh is active and paused
    if (this.isAutoRefreshActive() && this.isPaused) {
      // Clear any existing resume timeout
      if (this.resumeTimeoutId) {
        clearTimeout(this.resumeTimeoutId);
        this.resumeTimeoutId = null;
      }

      // Resume after the specified delay
      if (delayMs > 0) {
        this.resumeTimeoutId = setTimeout(() => {
          this.doResumeAutoRefresh();
        }, delayMs);
      } else {
        this.doResumeAutoRefresh();
      }
    }
  }

  // Helper method to actually resume auto-refresh
  private doResumeAutoRefresh(): void {
    console.log(`[${new Date().toISOString()}] [AutoRefreshManager] Actually resuming auto-refresh now`);

    // Only resume if auto-refresh is active and paused
    if (this.isAutoRefreshActive() && this.isPaused) {
      this.isPaused = false;

      // Restart the interval if we have a callback and email address
      if (this.refreshCallback && this.currentEmailAddress) {
        const interval = this.loadStateFromStorage().interval;

        // Start interval for subsequent refreshes
        this.intervalId = setInterval(() => {
          console.log(`[AutoRefreshManager] Auto-refresh interval triggered for: ${this.currentEmailAddress}`);

          // Check if auto-refresh is still active
          if (!this.isAutoRefreshActive() || !this.currentEmailAddress) {
            console.log(`[AutoRefreshManager] Auto-refresh conditions changed, stopping interval`);
            this.stopAutoRefresh();
            return;
          }

          // Perform refresh
          if (this.refreshCallback) {
            this.refreshCallback().catch(error => {
              console.error('[AutoRefreshManager] Error during auto-refresh:', error);
            });
          }
        }, interval);

        const effectiveInterval = interval + TIMEOUTS.POST_FETCH_DELAY;
        console.log(`[${new Date().toISOString()}] [AutoRefreshManager] Auto-refresh interval resumed with ${interval}ms base interval + ${TIMEOUTS.POST_FETCH_DELAY}ms post-fetch delay = ${effectiveInterval}ms effective interval for: ${this.currentEmailAddress}`);
      }
    }
  }

  // Clean up resources
  public cleanup(): void {
    console.log(`[${new Date().toISOString()}] [AutoRefreshManager] Cleaning up resources`);

    this.stopAutoRefresh();

    if (typeof window !== 'undefined') {
      window.removeEventListener('storage', this.handleStorageChange);
    }
  }
}

export default AutoRefreshManager;
