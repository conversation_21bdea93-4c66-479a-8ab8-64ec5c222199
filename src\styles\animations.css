/* Loading dots animation */
@keyframes loadingDot {
  0%, 100% {
    transform: scale(0.5);
    opacity: 0.5;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-dot-1 {
  animation: loadingDot 1.4s ease-in-out infinite;
}

.loading-dot-2 {
  animation: loadingDot 1.4s ease-in-out 0.2s infinite;
}

.loading-dot-3 {
  animation: loadingDot 1.4s ease-in-out 0.4s infinite;
}

/* Loading bar animation */
@keyframes loadingBar {
  0% {
    width: 0%;
    left: 0%;
  }
  50% {
    width: 30%;
    left: 30%;
  }
  100% {
    width: 0%;
    left: 100%;
  }
}

/* Email Deliverability loading bar animation */
@keyframes emailDeliverabilityLoadingBar {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

.loading-bar {
  animation: loadingBar 2s ease-in-out infinite;
}

/* Transition animations */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.fade-in-scale {
  animation: fadeInScale 0.6s ease-out forwards;
}

@keyframes slideInRight {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out forwards;
}

@keyframes slideInLeft {
  0% {
    transform: translateX(20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out forwards;
}

/* Button click animation */
@keyframes click {
  0% {
    transform: scale(1);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  50% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    background-color: rgba(79, 70, 229, 0.1);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
}

.animate-click {
  animation: click 0.3s ease-in-out;
}
