/**
 * <PERSON><PERSON><PERSON> to add the schedule column to the ad_config table
 */
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Anon Key:', supabaseAnonKey ? 'Found' : 'Not found');

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function addScheduleColumn() {
  console.log('Adding schedule column to ad_config table...');

  try {
    // First, check if the table exists
    const { data: checkData, error: checkError } = await supabase
      .from('ad_config')
      .select('*')
      .limit(1);

    if (checkError) {
      console.error('Error checking ad_config table:', checkError.message);
      console.log('Make sure the ad_config table exists before adding columns.');
      return;
    }

    console.log('ad_config table exists. Checking for schedule column...');

    // Check if the schedule column already exists
    const { data: columnData, error: columnError } = await supabase.rpc('execute_sql', {
      sql: `
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'ad_config'
        AND column_name = 'schedule';
      `
    });

    if (columnError) {
      console.error('Error checking for schedule column:', columnError.message);
      console.log('Trying alternative approach...');
      
      // Try to add the column directly
      const { error: alterError } = await supabase.rpc('execute_sql', {
        sql: `
          ALTER TABLE ad_config
          ADD COLUMN IF NOT EXISTS schedule JSONB;
        `
      });

      if (alterError) {
        console.error('Error adding schedule column:', alterError.message);
        console.log('Please add the schedule column manually with the following SQL:');
        console.log(`
          ALTER TABLE ad_config
          ADD COLUMN IF NOT EXISTS schedule JSONB;
        `);
        return;
      }

      console.log('Schedule column added successfully!');
    } else {
      if (columnData && columnData.length > 0) {
        console.log('Schedule column already exists.');
      } else {
        console.log('Schedule column does not exist. Adding it now...');
        
        // Add the schedule column
        const { error: alterError } = await supabase.rpc('execute_sql', {
          sql: `
            ALTER TABLE ad_config
            ADD COLUMN IF NOT EXISTS schedule JSONB;
          `
        });

        if (alterError) {
          console.error('Error adding schedule column:', alterError.message);
          console.log('Please add the schedule column manually with the following SQL:');
          console.log(`
            ALTER TABLE ad_config
            ADD COLUMN IF NOT EXISTS schedule JSONB;
          `);
          return;
        }

        console.log('Schedule column added successfully!');
      }
    }

    // Verify the column was added
    const { data: verifyData, error: verifyError } = await supabase
      .from('ad_config')
      .select('*')
      .limit(1);

    if (verifyError) {
      console.error('Error verifying schedule column:', verifyError.message);
    } else {
      console.log('ad_config table structure:');
      console.log(Object.keys(verifyData[0] || {}));
      
      if (verifyData[0] && 'schedule' in verifyData[0]) {
        console.log('✅ Schedule column exists in the table!');
      } else {
        console.log('❌ Schedule column does not appear to be in the table.');
      }
    }
  } catch (error) {
    console.error('Error adding schedule column:', error.message);
  }
}

addScheduleColumn();
