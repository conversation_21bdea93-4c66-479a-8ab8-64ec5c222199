/**
 * SMTP Service Unit Tests
 * 
 * Comprehensive unit tests for SMTP testing functionality
 */

import { SmtpService } from '@/lib/tools/smtp-tester/smtpService';
import { SmtpConfig } from '@/types/smtp';

// Mock nodemailer
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    verify: jest.fn(),
    sendMail: jest.fn(),
    close: jest.fn()
  }))
}));

describe('SmtpService', () => {
  let smtpService: SmtpService;
  let mockTransporter: any;

  beforeEach(() => {
    smtpService = new SmtpService();
    const nodemailer = require('nodemailer');
    mockTransporter = {
      verify: jest.fn(),
      sendMail: jest.fn(),
      close: jest.fn()
    };
    nodemailer.createTransport.mockReturnValue(mockTransporter);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('testSmtpConnection', () => {
    const validConfig: SmtpConfig = {
      server: 'smtp.gmail.com',
      port: 587,
      encryption: 'tls',
      username: '<EMAIL>',
      password: 'testpassword',
      sender: '<EMAIL>'
    };

    it('should successfully test SMTP connection in auto mode', async () => {
      // Mock successful verification and email sending
      mockTransporter.verify.mockResolvedValue(true);
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-message-id' });

      const result = await smtpService.testSmtpConnection({
        config: validConfig,
        testMode: 'auto'
      });

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('test-message-id');
      expect(result.testAddress).toMatch(/^test-.+@fademail\.site$/);
      expect(result.logs).toContain('✓ SMTP test completed successfully');
      expect(mockTransporter.verify).toHaveBeenCalled();
      expect(mockTransporter.sendMail).toHaveBeenCalled();
    });

    it('should successfully test SMTP connection in custom mode', async () => {
      mockTransporter.verify.mockResolvedValue(true);
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'custom-message-id' });

      const result = await smtpService.testSmtpConnection({
        config: validConfig,
        testMode: 'custom',
        recipient: '<EMAIL>'
      });

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('custom-message-id');
      expect(result.testAddress).toBeUndefined(); // No test address in custom mode
      expect(result.logs).toContain('Using custom recipient: <EMAIL>');
    });

    it('should handle SMTP verification failure', async () => {
      mockTransporter.verify.mockRejectedValue(new Error('Connection refused'));

      const result = await smtpService.testSmtpConnection({
        config: validConfig,
        testMode: 'auto'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Connection refused');
      expect(result.logs).toContain('✗ SMTP connection verification failed');
    });

    it('should handle email sending failure', async () => {
      mockTransporter.verify.mockResolvedValue(true);
      mockTransporter.sendMail.mockRejectedValue(new Error('Authentication failed'));

      const result = await smtpService.testSmtpConnection({
        config: validConfig,
        testMode: 'auto'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Authentication failed');
      expect(result.logs).toContain('✗ Failed to send email');
    });

    it('should handle missing recipient in custom mode', async () => {
      const result = await smtpService.testSmtpConnection({
        config: validConfig,
        testMode: 'custom'
        // No recipient provided
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('No recipient address specified');
    });

    it('should configure SSL encryption correctly', async () => {
      const sslConfig = { ...validConfig, encryption: 'ssl' as const, port: 465 };
      mockTransporter.verify.mockResolvedValue(true);
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'ssl-test' });

      await smtpService.testSmtpConnection({
        config: sslConfig,
        testMode: 'auto'
      });

      const nodemailer = require('nodemailer');
      const transportConfig = nodemailer.createTransport.mock.calls[0][0];
      expect(transportConfig.secure).toBe(true);
      expect(transportConfig.port).toBe(465);
    });

    it('should configure TLS encryption correctly', async () => {
      mockTransporter.verify.mockResolvedValue(true);
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'tls-test' });

      await smtpService.testSmtpConnection({
        config: validConfig,
        testMode: 'auto'
      });

      const nodemailer = require('nodemailer');
      const transportConfig = nodemailer.createTransport.mock.calls[0][0];
      expect(transportConfig.secure).toBe(false);
      expect(transportConfig.requireTLS).toBe(true);
    });

    it('should configure no encryption correctly', async () => {
      const noEncConfig = { ...validConfig, encryption: 'none' as const, port: 25 };
      mockTransporter.verify.mockResolvedValue(true);
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'no-enc-test' });

      await smtpService.testSmtpConnection({
        config: noEncConfig,
        testMode: 'auto'
      });

      const nodemailer = require('nodemailer');
      const transportConfig = nodemailer.createTransport.mock.calls[0][0];
      expect(transportConfig.secure).toBe(false);
      expect(transportConfig.ignoreTLS).toBe(true);
    });

    it('should include proper timeouts in configuration', async () => {
      mockTransporter.verify.mockResolvedValue(true);
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'timeout-test' });

      await smtpService.testSmtpConnection({
        config: validConfig,
        testMode: 'auto'
      });

      const nodemailer = require('nodemailer');
      const transportConfig = nodemailer.createTransport.mock.calls[0][0];
      expect(transportConfig.connectionTimeout).toBe(10000);
      expect(transportConfig.greetingTimeout).toBe(5000);
      expect(transportConfig.socketTimeout).toBe(10000);
    });

    it('should measure test duration', async () => {
      mockTransporter.verify.mockResolvedValue(true);
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'duration-test' });

      const result = await smtpService.testSmtpConnection({
        config: validConfig,
        testMode: 'auto'
      });

      expect(result.duration).toBeGreaterThan(0);
      expect(typeof result.duration).toBe('number');
    });

    it('should generate proper email content', async () => {
      mockTransporter.verify.mockResolvedValue(true);
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'content-test' });

      await smtpService.testSmtpConnection({
        config: validConfig,
        testMode: 'auto'
      });

      const sendMailCall = mockTransporter.sendMail.mock.calls[0][0];
      expect(sendMailCall.from).toBe(validConfig.sender);
      expect(sendMailCall.subject).toContain('SMTP Test from VanishPost');
      expect(sendMailCall.text).toContain('This is a test email sent from VanishPost');
      expect(sendMailCall.html).toContain('SMTP Test from VanishPost');
    });
  });
});
