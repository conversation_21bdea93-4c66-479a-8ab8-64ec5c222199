/**
 * Analytics Caching System
 * 
 * Provides in-memory caching for analytics data with TTL support.
 * Designed to reduce database load and improve dashboard performance.
 * 
 * Features:
 * - TTL-based cache expiration
 * - Different cache strategies for different data types
 * - Cache hit/miss metrics
 * - Memory-efficient storage
 * - Cache invalidation support
 */

import { logInfo, logError, logDebug } from '../logging';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  hits: number;
}

interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  totalSize: number;
}

interface CacheConfig {
  maxSize: number; // Maximum number of entries
  defaultTTL: number; // Default TTL in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
}

/**
 * In-memory cache with TTL support for analytics data
 */
class AnalyticsCache {
  private cache = new Map<string, CacheEntry<any>>();
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    totalSize: 0
  };
  private config: CacheConfig;
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: config.maxSize || 1000,
      defaultTTL: config.defaultTTL || 5 * 60 * 1000, // 5 minutes
      cleanupInterval: config.cleanupInterval || 60 * 1000 // 1 minute
    };

    this.startCleanupTimer();
    logInfo('AnalyticsCache', 'Cache initialized', { config: this.config });
  }

  /**
   * Get data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.metrics.misses++;
      logDebug('AnalyticsCache', 'Cache miss', { key });
      return null;
    }

    // Check if entry has expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      this.metrics.misses++;
      this.metrics.evictions++;
      logDebug('AnalyticsCache', 'Cache expired', { key, age: Date.now() - entry.timestamp });
      return null;
    }

    // Update hit count and metrics
    entry.hits++;
    this.metrics.hits++;
    logDebug('AnalyticsCache', 'Cache hit', { key, hits: entry.hits });
    
    return entry.data;
  }

  /**
   * Set data in cache with optional TTL
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const actualTTL = ttl || this.config.defaultTTL;
    
    // Check if we need to evict entries to make space
    if (this.cache.size >= this.config.maxSize && !this.cache.has(key)) {
      this.evictOldest();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: actualTTL,
      hits: 0
    };

    this.cache.set(key, entry);
    this.metrics.sets++;
    this.updateTotalSize();
    
    logDebug('AnalyticsCache', 'Cache set', { 
      key, 
      ttl: actualTTL, 
      size: this.cache.size 
    });
  }

  /**
   * Delete specific key from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.metrics.deletes++;
      this.updateTotalSize();
      logDebug('AnalyticsCache', 'Cache delete', { key });
    }
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    this.updateTotalSize();
    logInfo('AnalyticsCache', 'Cache cleared', { entriesRemoved: size });
  }

  /**
   * Get cache metrics
   */
  getMetrics(): CacheMetrics & { hitRate: number } {
    const total = this.metrics.hits + this.metrics.misses;
    const hitRate = total > 0 ? (this.metrics.hits / total) * 100 : 0;
    
    return {
      ...this.metrics,
      hitRate: Math.round(hitRate * 100) / 100
    };
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    maxSize: number;
    memoryUsage: string;
    oldestEntry: number | null;
    newestEntry: number | null;
  } {
    let oldest: number | null = null;
    let newest: number | null = null;

    for (const entry of this.cache.values()) {
      if (oldest === null || entry.timestamp < oldest) {
        oldest = entry.timestamp;
      }
      if (newest === null || entry.timestamp > newest) {
        newest = entry.timestamp;
      }
    }

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      memoryUsage: this.getMemoryUsage(),
      oldestEntry: oldest,
      newestEntry: newest
    };
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidatePattern(pattern: string): number {
    let count = 0;
    const regex = new RegExp(pattern);
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        count++;
      }
    }
    
    if (count > 0) {
      this.updateTotalSize();
      logInfo('AnalyticsCache', 'Pattern invalidation', { pattern, count });
    }
    
    return count;
  }

  /**
   * Get or set with a factory function
   */
  async getOrSet<T>(
    key: string, 
    factory: () => Promise<T>, 
    ttl?: number
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    try {
      const data = await factory();
      this.set(key, data, ttl);
      return data;
    } catch (error) {
      logError('AnalyticsCache', 'Factory function failed', { key, error });
      throw error;
    }
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let evicted = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        evicted++;
      }
    }

    if (evicted > 0) {
      this.metrics.evictions += evicted;
      this.updateTotalSize();
      logDebug('AnalyticsCache', 'Cleanup completed', { evicted });
    }
  }

  /**
   * Evict oldest entry to make space
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.metrics.evictions++;
      logDebug('AnalyticsCache', 'Evicted oldest entry', { key: oldestKey });
    }
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Stop cleanup timer
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * Update total size metric
   */
  private updateTotalSize(): void {
    this.metrics.totalSize = this.cache.size;
  }

  /**
   * Get approximate memory usage
   */
  private getMemoryUsage(): string {
    const bytes = JSON.stringify([...this.cache.entries()]).length;
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${Math.round(bytes / 1024)} KB`;
    return `${Math.round(bytes / (1024 * 1024))} MB`;
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopCleanupTimer();
    this.clear();
    logInfo('AnalyticsCache', 'Cache destroyed');
  }
}

// Cache TTL constants
export const CACHE_TTL = {
  DASHBOARD_OVERVIEW: 5 * 60 * 1000,    // 5 minutes
  SESSION_ANALYTICS: 1 * 60 * 1000,     // 1 minute
  AGGREGATED_METRICS: 15 * 60 * 1000,   // 15 minutes
  LIVE_VISITORS: 30 * 1000,             // 30 seconds
  TIMELINE_DATA: 2 * 60 * 1000,         // 2 minutes
} as const;

// Global cache instance
export const analyticsCache = new AnalyticsCache({
  maxSize: 500,
  defaultTTL: CACHE_TTL.DASHBOARD_OVERVIEW,
  cleanupInterval: 60 * 1000
});

// Cache key generators
export const CacheKeys = {
  dashboardOverview: (timeRange: string) => `dashboard:overview:${timeRange}`,
  sessionAnalytics: (timeRange: string, limit: number) => `session:analytics:${timeRange}:${limit}`,
  aggregatedMetrics: (timeRange: string, aggregation: string) => `metrics:${aggregation}:${timeRange}`,
  liveVisitors: (threshold: number) => `live:visitors:${threshold}`,
  timelineData: (timeRange: string, aggregation: string) => `timeline:${aggregation}:${timeRange}`,
  customRange: (startDate: string, endDate: string, type: string) => `custom:${type}:${startDate}:${endDate}`,
} as const;

// Cache invalidation utilities
export const CacheInvalidation = {
  /**
   * Invalidate all dashboard-related cache entries
   */
  invalidateDashboard: () => {
    return analyticsCache.invalidatePattern('^dashboard:');
  },

  /**
   * Invalidate session analytics cache
   */
  invalidateSessionAnalytics: () => {
    return analyticsCache.invalidatePattern('^session:');
  },

  /**
   * Invalidate live visitors cache
   */
  invalidateLiveVisitors: () => {
    return analyticsCache.invalidatePattern('^live:');
  },

  /**
   * Invalidate all analytics cache
   */
  invalidateAll: () => {
    analyticsCache.clear();
    return 'all';
  },

  /**
   * Invalidate cache entries for a specific time range
   */
  invalidateTimeRange: (timeRange: string) => {
    return analyticsCache.invalidatePattern(`:${timeRange}:`);
  },

  /**
   * Smart invalidation based on event type
   */
  invalidateByEventType: (eventType: string) => {
    let invalidated = 0;

    // Invalidate relevant caches based on event type
    switch (eventType) {
      case 'email_generated':
      case 'email_copied':
      case 'email_deleted':
        // These affect dashboard overview and session analytics
        invalidated += analyticsCache.invalidatePattern('^dashboard:');
        invalidated += analyticsCache.invalidatePattern('^session:');
        invalidated += analyticsCache.invalidatePattern('^metrics:');
        break;

      case 'session_start':
      case 'session_end':
        // These affect session analytics and live visitors
        invalidated += analyticsCache.invalidatePattern('^session:');
        invalidated += analyticsCache.invalidatePattern('^live:');
        break;

      case 'page_view':
        // This affects live visitors primarily
        invalidated += analyticsCache.invalidatePattern('^live:');
        break;

      default:
        // For unknown events, invalidate dashboard and metrics
        invalidated += analyticsCache.invalidatePattern('^dashboard:');
        invalidated += analyticsCache.invalidatePattern('^metrics:');
    }

    return invalidated;
  }
} as const;

export default analyticsCache;
