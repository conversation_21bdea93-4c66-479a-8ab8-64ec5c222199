/**
 * Breadcrumb Navigation Component
 * SEO-friendly breadcrumb navigation with structured data
 */

import Link from 'next/link';
import { BreadcrumbSchema } from './StructuredData';

interface BreadcrumbItem {
  name: string;
  url: string;
  current?: boolean;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
}

export default function Breadcrumbs({ items, className = '' }: BreadcrumbsProps) {
  // Prepare items for structured data (exclude current page)
  const schemaItems = items.filter(item => !item.current);

  return (
    <>
      <BreadcrumbSchema items={schemaItems} />
      <nav 
        aria-label="Breadcrumb navigation" 
        className={`mb-6 ${className}`}
      >
        <ol className="flex items-center space-x-2 text-sm">
          {items.map((item, index) => (
            <li key={item.url} className="flex items-center">
              {index > 0 && (
                <svg 
                  className="w-4 h-4 mx-2 flex-shrink-0" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  style={{ color: '#956b50' }}
                  aria-hidden="true"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M9 5l7 7-7 7" 
                  />
                </svg>
              )}
              {item.current ? (
                <span 
                  className="font-medium"
                  style={{ color: '#1b130e' }}
                  aria-current="page"
                >
                  {item.name}
                </span>
              ) : (
                <Link
                  href={item.url}
                  className="transition-colors duration-200 hover:underline"
                  style={{ color: '#956b50' }}
                >
                  {item.name}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}
