/**
 * Authentication utilities for the admin dashboard
 */

import { cookies } from 'next/headers';
import { verify } from 'jsonwebtoken';
import { verifyAdminCredentials } from './admin/user-service';
import { NextRequest } from 'next/server';

// Define the user type
export interface User {
  userId: number;
  username: string;
  role: string;
}

/**
 * Verify an admin token and return the user if valid
 * @param token The JWT token to verify
 * @returns The user object if the token is valid, null otherwise
 */
export function verifyAdminToken(token: string): User | null {
  try {
    if (!token) {
      return null;
    }

    // Ensure JWT_SECRET is configured
    if (!process.env.JWT_SECRET) {
      console.error('Missing required environment variable: JWT_SECRET');
      return null;
    }

    // Verify the token with the JWT secret
    const decoded = verify(token, process.env.JWT_SECRET);

    // Type guard to check if decoded is a JwtPayload with our expected properties
    if (typeof decoded === 'object' && decoded !== null && 'userId' in decoded && 'username' in decoded && 'role' in decoded) {
      // Extract user information from the decoded token
      return {
        userId: decoded.userId,
        username: decoded.username,
        role: decoded.role
      };
    } else {
      console.error('Invalid token format');
      return null;
    }
  } catch (error) {
    console.error('Error verifying admin token:', error);
    return null;
  }
}

/**
 * Get the current user from the request cookies
 * @returns The user object if authenticated, null otherwise
 */
export async function getCurrentUser(): Promise<User | null> {
  try {
    // Use the cookies API to get the admin_token cookie
    const cookieStore = await cookies();
    const token = cookieStore.get('admin_token')?.value;

    if (!token) {
      return null;
    }

    return verifyAdminToken(token);
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Check if the current user has admin privileges
 * @param user The user object to check
 * @returns True if the user is an admin, false otherwise
 */
export function isAdmin(user: User | null): boolean {
  return user?.role === 'admin';
}

/**
 * Authenticate a user with username and password
 * @param username The username to authenticate
 * @param password The password to authenticate
 * @returns An object with success status and user data or error message
 */
export async function authenticateUser(username: string, password: string) {
  try {
    const { user, valid } = await verifyAdminCredentials(username, password);

    if (valid && user) {
      return {
        success: true,
        user: {
          userId: user.id,
          username: user.username,
          role: user.role
        }
      };
    }

    return {
      success: false,
      message: 'Invalid username or password'
    };
  } catch (error) {
    console.error('Error authenticating user:', error);
    return {
      success: false,
      message: 'An error occurred during authentication'
    };
  }
}

/**
 * Log out the current user
 * @returns An object with success status
 */
export async function logout() {
  try {
    // In production, this would clear the session token from cookies
    // We don't need to use the cookies API here since we're handling this in the route handler
    return { success: true };
  } catch (error) {
    console.error('Error logging out:', error);
    return { success: false, message: 'An error occurred during logout' };
  }
}

/**
 * Verify admin authentication from request
 * @param request The NextRequest object
 * @returns An object with success status and user data or error message
 */
export async function verifyAdminAuth(request: NextRequest) {
  try {
    // Check for cookie-based authentication first
    const token = request.cookies.get('admin_token')?.value;
    if (token) {
      const user = verifyAdminToken(token);
      if (user) {
        return {
          success: true,
          user
        };
      }
    }

    // Fall back to Authorization header if no valid cookie
    const authHeader = request.headers.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const headerToken = authHeader.substring(7);
      const user = verifyAdminToken(headerToken);
      if (user) {
        return {
          success: true,
          user
        };
      }
    }

    return {
      success: false,
      message: 'Unauthorized'
    };
  } catch (error) {
    console.error('Error verifying admin auth:', error);
    return {
      success: false,
      message: 'Authentication error'
    };
  }
}
