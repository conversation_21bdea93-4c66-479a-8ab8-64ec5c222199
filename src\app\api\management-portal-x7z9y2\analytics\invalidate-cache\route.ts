/**
 * Cache Invalidation API Endpoint
 * 
 * This endpoint allows manual cache invalidation for immediate effect of fixes
 */

import { NextRequest, NextResponse } from 'next/server';
import { CacheInvalidation } from '@/lib/analytics/cache';
import { logInfo } from '@/lib/logging';

/**
 * POST /api/management-portal-x7z9y2/analytics/invalidate-cache
 * Invalidate specific cache patterns
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { pattern } = body;

    let invalidatedCount = 0;
    let message = '';

    switch (pattern) {
      case 'session':
        invalidatedCount = CacheInvalidation.invalidateSessionAnalytics();
        message = 'Session analytics cache invalidated';
        break;
      case 'dashboard':
        invalidatedCount = CacheInvalidation.invalidateDashboard();
        message = 'Dashboard cache invalidated';
        break;
      case 'analytics':
        // Invalidate analytics-related cache patterns
        invalidatedCount = CacheInvalidation.invalidateDashboard();
        invalidatedCount += CacheInvalidation.invalidateSessionAnalytics();
        message = 'Analytics cache invalidated';
        break;
      case 'all':
        CacheInvalidation.invalidateAll();
        invalidatedCount = 1;
        message = 'All cache invalidated';
        break;
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid pattern. Use: session, dashboard, analytics, or all' },
          { status: 400 }
        );
    }

    logInfo('CacheInvalidation', message, { pattern, invalidatedCount });

    return NextResponse.json({
      success: true,
      message,
      invalidatedCount,
      pattern
    });

  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Failed to invalidate cache' },
      { status: 500 }
    );
  }
}
