/**
 * Session Persistence Test Suite
 * Tests for the critical session bypass vulnerability fix
 */

import { 
  initializeSessionManagement, 
  getCurrentSessionId, 
  clearSession,
  getSessionMetadata,
  isSessionValid,
  updateSessionActivity
} from '@/lib/session/persistentSessionManager';

// Mock localStorage for testing
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value; },
    removeItem: (key: string) => { delete store[key]; },
    clear: () => { store = {}; }
  };
})();

// Mock window object for testing
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('Session Persistence - Critical Security Fix', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorageMock.clear();
    
    // Mock Date.now for consistent testing
    jest.spyOn(Date, 'now').mockReturnValue(1642694400000); // Fixed timestamp
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Session Bypass Vulnerability Fix', () => {
    it('should maintain same session ID across multiple initializations (simulating page refreshes)', () => {
      // This is the critical test that validates the vulnerability fix
      
      // First initialization (simulates initial page load)
      const session1 = initializeSessionManagement();
      const sessionId1 = session1.sessionId;
      
      // Second initialization (simulates page refresh)
      const session2 = initializeSessionManagement();
      const sessionId2 = session2.sessionId;
      
      // Third initialization (simulates another page refresh)
      const session3 = initializeSessionManagement();
      const sessionId3 = session3.sessionId;
      
      // CRITICAL: All session IDs must be the same
      expect(sessionId1).toBe(sessionId2);
      expect(sessionId2).toBe(sessionId3);
      expect(sessionId1).toBe(sessionId3);
      
      // Verify session ID is not empty or undefined
      expect(sessionId1).toBeDefined();
      expect(sessionId1).not.toBe('');
      expect(sessionId1.length).toBeGreaterThan(10);
    });

    it('should persist session data in localStorage', () => {
      const session = initializeSessionManagement();
      
      // Check that session data is stored in localStorage
      const storedSessionId = localStorageMock.getItem('vanishpost_session_id');
      const storedMetadata = localStorageMock.getItem('vanishpost_session_metadata');
      
      expect(storedSessionId).toBe(session.sessionId);
      expect(storedMetadata).toBeDefined();
      
      // Parse and validate metadata
      const metadata = JSON.parse(storedMetadata!);
      expect(metadata.sessionId).toBe(session.sessionId);
      expect(metadata.createdAt).toBeDefined();
      expect(metadata.expiresAt).toBeDefined();
      expect(metadata.lastActivity).toBeDefined();
    });

    it('should retrieve existing session from localStorage', () => {
      // Manually set session data in localStorage
      const testSessionId = 'test-session-12345';
      const testMetadata = {
        sessionId: testSessionId,
        createdAt: Date.now(),
        expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
        lastActivity: Date.now(),
        deviceType: 'desktop',
        browser: 'Chrome'
      };
      
      localStorageMock.setItem('vanishpost_session_id', testSessionId);
      localStorageMock.setItem('vanishpost_session_metadata', JSON.stringify(testMetadata));
      
      // Initialize session management - should retrieve existing session
      const session = initializeSessionManagement();
      
      expect(session.sessionId).toBe(testSessionId);
      expect(session.deviceType).toBe('desktop');
      expect(session.browser).toBe('Chrome');
    });

    it('should create new session when localStorage is empty', () => {
      // Ensure localStorage is empty
      localStorageMock.clear();
      
      const session = initializeSessionManagement();
      
      expect(session.sessionId).toBeDefined();
      expect(session.sessionId.length).toBeGreaterThan(10);
      expect(session.createdAt).toBeDefined();
      expect(session.expiresAt).toBeDefined();
    });

    it('should handle expired sessions correctly', () => {
      // Create an expired session
      const expiredSessionId = 'expired-session-12345';
      const expiredMetadata = {
        sessionId: expiredSessionId,
        createdAt: Date.now() - (48 * 60 * 60 * 1000), // 48 hours ago
        expiresAt: Date.now() - (24 * 60 * 60 * 1000), // Expired 24 hours ago
        lastActivity: Date.now() - (25 * 60 * 60 * 1000),
        deviceType: 'desktop',
        browser: 'Chrome'
      };
      
      localStorageMock.setItem('vanishpost_session_id', expiredSessionId);
      localStorageMock.setItem('vanishpost_session_metadata', JSON.stringify(expiredMetadata));
      
      // Initialize session management - should create new session
      const session = initializeSessionManagement();
      
      expect(session.sessionId).not.toBe(expiredSessionId);
      expect(session.sessionId).toBeDefined();
      expect(session.expiresAt).toBeGreaterThan(Date.now());
    });
  });

  describe('Session Management Functions', () => {
    it('should return current session ID', () => {
      const session = initializeSessionManagement();
      const currentId = getCurrentSessionId();
      
      expect(currentId).toBe(session.sessionId);
    });

    it('should validate session correctly', () => {
      initializeSessionManagement();
      
      expect(isSessionValid()).toBe(true);
    });

    it('should clear session data', () => {
      initializeSessionManagement();
      
      // Verify session exists
      expect(getCurrentSessionId()).toBeDefined();
      expect(localStorageMock.getItem('vanishpost_session_id')).toBeDefined();
      
      // Clear session
      clearSession();
      
      // Verify session is cleared
      expect(localStorageMock.getItem('vanishpost_session_id')).toBeNull();
      expect(localStorageMock.getItem('vanishpost_session_metadata')).toBeNull();
    });

    it('should update session activity', () => {
      const session = initializeSessionManagement();
      const originalActivity = session.lastActivity;
      
      // Mock a later time
      jest.spyOn(Date, 'now').mockReturnValue(originalActivity + 60000); // 1 minute later
      
      updateSessionActivity();
      
      const metadata = getSessionMetadata();
      expect(metadata?.lastActivity).toBeGreaterThan(originalActivity);
    });

    it('should get session metadata', () => {
      const session = initializeSessionManagement();
      const metadata = getSessionMetadata();
      
      expect(metadata).toBeDefined();
      expect(metadata?.sessionId).toBe(session.sessionId);
      expect(metadata?.createdAt).toBe(session.createdAt);
      expect(metadata?.expiresAt).toBe(session.expiresAt);
      expect(metadata?.deviceType).toBeDefined();
      expect(metadata?.browser).toBeDefined();
    });
  });

  describe('Session Security', () => {
    it('should generate cryptographically secure session IDs', () => {
      const session1 = initializeSessionManagement();
      clearSession();
      const session2 = initializeSessionManagement();
      
      // Session IDs should be different
      expect(session1.sessionId).not.toBe(session2.sessionId);
      
      // Session IDs should be long enough to be secure
      expect(session1.sessionId.length).toBeGreaterThan(20);
      expect(session2.sessionId.length).toBeGreaterThan(20);
      
      // Session IDs should contain alphanumeric characters and hyphens (UUID format)
      expect(session1.sessionId).toMatch(/^[a-f0-9-]+$/);
      expect(session2.sessionId).toMatch(/^[a-f0-9-]+$/);
    });

    it('should set appropriate session expiration (24 hours)', () => {
      const session = initializeSessionManagement();
      const expectedExpiration = session.createdAt + (24 * 60 * 60 * 1000);
      
      // Allow 1 second variance for test execution time
      expect(Math.abs(session.expiresAt - expectedExpiration)).toBeLessThan(1000);
    });

    it('should include device and browser information', () => {
      const session = initializeSessionManagement();
      
      expect(session.deviceType).toBeDefined();
      expect(session.browser).toBeDefined();
      expect(['desktop', 'mobile', 'tablet', 'unknown']).toContain(session.deviceType);
    });
  });

  describe('Error Handling', () => {
    it('should handle localStorage errors gracefully', () => {
      // Mock localStorage to throw errors
      const originalSetItem = localStorageMock.setItem;
      localStorageMock.setItem = jest.fn(() => {
        throw new Error('localStorage quota exceeded');
      });
      
      // Should not throw error, but create session anyway
      expect(() => {
        const session = initializeSessionManagement();
        expect(session.sessionId).toBeDefined();
      }).not.toThrow();
      
      // Restore original function
      localStorageMock.setItem = originalSetItem;
    });

    it('should handle corrupted localStorage data', () => {
      // Set corrupted data in localStorage
      localStorageMock.setItem('vanishpost_session_metadata', 'invalid-json');
      
      // Should create new session instead of crashing
      const session = initializeSessionManagement();
      
      expect(session.sessionId).toBeDefined();
      expect(session.createdAt).toBeDefined();
      expect(session.expiresAt).toBeDefined();
    });
  });
});

describe('Session Persistence Integration', () => {
  beforeEach(() => {
    localStorageMock.clear();
  });

  it('should simulate the original vulnerability scenario and confirm it is fixed', () => {
    // Simulate the original attack scenario:
    // 1. User generates emails and hits rate limit
    // 2. User refreshes page (used to get new session)
    // 3. User tries to generate more emails
    
    // Step 1: Initial session
    const initialSession = initializeSessionManagement();
    const initialSessionId = initialSession.sessionId;
    
    // Step 2: Simulate page refresh (multiple times)
    const refreshSession1 = initializeSessionManagement();
    const refreshSession2 = initializeSessionManagement();
    const refreshSession3 = initializeSessionManagement();
    
    // Step 3: Verify all sessions are the same (vulnerability is fixed)
    expect(refreshSession1.sessionId).toBe(initialSessionId);
    expect(refreshSession2.sessionId).toBe(initialSessionId);
    expect(refreshSession3.sessionId).toBe(initialSessionId);
    
    // This confirms that the session bypass vulnerability is FIXED
    // Users can no longer get new sessions by refreshing the page
  });

  it('should maintain session consistency across multiple tabs (same localStorage)', () => {
    // Simulate multiple tabs by calling initializeSessionManagement multiple times
    const tab1Session = initializeSessionManagement();
    const tab2Session = initializeSessionManagement();
    const tab3Session = initializeSessionManagement();
    
    // All tabs should use the same session
    expect(tab1Session.sessionId).toBe(tab2Session.sessionId);
    expect(tab2Session.sessionId).toBe(tab3Session.sessionId);
  });
});
