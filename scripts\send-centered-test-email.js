// <PERSON>ript to send a test email with centered content
const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');

// Create a test account
async function main() {
  // Create a test SMTP service account
  const testAccount = await nodemailer.createTestAccount();

  // Create a transporter object using the default SMTP transport
  const transporter = nodemailer.createTransport({
    host: 'smtp.ethereal.email',
    port: 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: testAccount.user,
      pass: testAccount.pass,
    },
  });

  // Get the recipient email address from command line arguments
  const recipientEmail = process.argv[2];
  if (!recipientEmail) {
    console.error('Please provide a recipient email address as a command line argument');
    process.exit(1);
  }

  console.log(`Sending test email to: ${recipientEmail}`);

  // Create HTML content with centered text and images
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #4f46e5; text-align: center;">Centered Test Email</h1>

      <p style="text-align: center;">This paragraph is centered using text-align: center style.</p>

      <center>This text is centered using the center tag.</center>

      <p align="center">This paragraph is centered using the align attribute.</p>

      <div style="text-align: center;">
        <img src="cid:test-image" alt="Test Image" style="max-width: 100%; height: auto;" />
        <p>This image should be centered (inside a centered div)</p>
      </div>

      <center>
        <img src="cid:test-image2" alt="Test Image 2" style="max-width: 100%; height: auto;" />
        <p>This image should be centered (inside a center tag)</p>
      </center>

      <p>
        <img src="cid:test-image3" alt="Test Image 3" style="max-width: 100%; height: auto; display: block; margin: 0 auto;" />
        <span style="display: block; text-align: center;">This image should be centered (using margin: 0 auto)</span>
      </p>

      <p align="right">This paragraph is right-aligned using the align attribute.</p>

      <p style="text-align: left;">This paragraph is left-aligned using text-align: left style.</p>

      <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center;">
        <p style="margin: 0;">This is a centered styled box to test CSS rendering.</p>
      </div>

      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr>
          <td style="text-align: center; padding: 10px; border: 1px solid #e5e7eb;">Centered cell</td>
          <td style="text-align: left; padding: 10px; border: 1px solid #e5e7eb;">Left-aligned cell</td>
          <td style="text-align: right; padding: 10px; border: 1px solid #e5e7eb;">Right-aligned cell</td>
        </tr>
      </table>
    </div>
  `;

  // Read test images
  const testImage = fs.readFileSync(path.join(__dirname, 'test-image.svg'));
  const testImage2 = fs.readFileSync(path.join(__dirname, 'test-image.svg'));
  const testImage3 = fs.readFileSync(path.join(__dirname, 'test-image.svg'));

  // Send mail with defined transport object
  const info = await transporter.sendMail({
    from: '"Fademail Test" <<EMAIL>>',
    to: recipientEmail,
    subject: 'Test Email with Centered Content',
    text: 'This is a test email with centered content. Please view in HTML mode.',
    html: htmlContent,
    attachments: [
      {
        filename: 'test-image.svg',
        content: testImage,
        cid: 'test-image'
      },
      {
        filename: 'test-image2.svg',
        content: testImage2,
        cid: 'test-image2'
      },
      {
        filename: 'test-image3.svg',
        content: testImage3,
        cid: 'test-image3'
      }
    ]
  });

  console.log('Message sent: %s', info.messageId);
  console.log('Preview URL: %s', nodemailer.getTestMessageUrl(info));
}

main().catch(console.error);
