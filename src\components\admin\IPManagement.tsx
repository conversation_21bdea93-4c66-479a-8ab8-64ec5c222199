'use client';

/**
 * Enhanced IP Management Component for Admin Dashboard
 *
 * Provides scalable interface for managing IP blocks, whitelist, and rate limit violations
 * with pagination, search, filtering, and sorting capabilities
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Badge } from '@/components/ui/Badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/Dialog';
import {
  AlertTriangle,
  Shield,
  ShieldCheck,
  Clock,
  User,
  Calendar,
  Ban,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  Download,
  ChevronLeft,
  ChevronRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  RefreshCw,
  Trash2
} from 'lucide-react';

interface BlockedIP {
  id: string;
  ip_address: string;
  reason: string;
  blocked_at: string;
  blocked_by: string;
  expires_at?: string;
  is_active: boolean;
  metadata?: any;
}

interface WhitelistIP {
  id: string;
  ip_address: string;
  reason: string;
  added_at: string;
  added_by: string;
  removed_at?: string;
  removed_by?: string;
  is_active: boolean;
}

interface RateLimitViolation {
  id: string;
  ip_address: string;
  endpoint: string;
  violation_count: number;
  first_violation: string;
  last_violation: string;
  user_agent?: string;
  is_resolved: boolean;
  blockedStatus?: {
    isBlocked: boolean;
    blockedAt?: string;
    blockedBy?: string;
    blockReason?: string;
    expiresAt?: string;
  };
}

interface IPManagementData {
  blockedIPs?: BlockedIP[];
  whitelistIPs?: WhitelistIP[];
  violations?: RateLimitViolation[];
}

interface PaginationInfo {
  page: number;
  limit: number;
  hasMore: boolean;
  total?: number;
}

interface TabCounts {
  blocked: number;
  whitelist: number;
  violations: number;
}

interface FilterState {
  search: string;
  status: string;
  dateFrom: string;
  dateTo: string;
  endpoint: string;
}

interface SortState {
  field: string;
  direction: 'asc' | 'desc';
}

export default function IPManagement() {
  const [data, setData] = useState<IPManagementData>({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('blocked');

  // Tab counts state
  const [tabCounts, setTabCounts] = useState<TabCounts>({
    blocked: 0,
    whitelist: 0,
    violations: 0
  });

  // Pagination states
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 25,
    hasMore: false
  });

  // Search and filter states
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    status: 'all',
    dateFrom: '',
    dateTo: '',
    endpoint: 'all'
  });

  // Sorting state
  const [sort, setSort] = useState<SortState>({
    field: 'created_at',
    direction: 'desc'
  });

  // Form states
  const [newBlockIP, setNewBlockIP] = useState('');
  const [blockReason, setBlockReason] = useState('');
  const [blockExpiry, setBlockExpiry] = useState('');
  const [newWhitelistIP, setNewWhitelistIP] = useState('');
  const [whitelistReason, setWhitelistReason] = useState('');

  // UI states
  const [showFilters, setShowFilters] = useState(false);
  const [exporting, setExporting] = useState(false);

  // Selection states for bulk operations
  const [selectedItems, setSelectedItems] = useState<{
    blocked: Set<string>;
    whitelist: Set<string>;
    violation: Set<string>;
  }>({
    blocked: new Set(),
    whitelist: new Set(),
    violation: new Set()
  });

  // Delete confirmation states
  const [deleteConfirm, setDeleteConfirm] = useState<{
    show: boolean;
    type: 'single' | 'bulk';
    recordType: 'blocked' | 'whitelist' | 'violation';
    recordId?: string;
    recordIds?: string[];
    ipAddress?: string;
    details?: string;
  }>({
    show: false,
    type: 'single',
    recordType: 'blocked'
  });

  // Initial data load
  useEffect(() => {
    fetchCounts();
    fetchData(true);
  }, []);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (filters.search !== '') {
        fetchData(true);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [filters.search]);

  // Fetch data when tab, pagination, or filters change
  useEffect(() => {
    fetchData(true);
  }, [activeTab, pagination.page, pagination.limit, filters.status, filters.dateFrom, filters.dateTo, filters.endpoint, sort]);

  const fetchCounts = async () => {
    try {
      const response = await fetch('/api/admin/ip-management?countsOnly=true');
      const result = await response.json();

      if (result.success) {
        setTabCounts(result.counts);
      }
    } catch (error) {
      console.error('Error fetching counts:', error);
    }
  };

  const buildQueryParams = useCallback(() => {
    const params = new URLSearchParams();
    params.set('type', activeTab);
    params.set('page', pagination.page.toString());
    params.set('limit', pagination.limit.toString());

    if (filters.search) params.set('search', filters.search);
    if (filters.status !== 'all') params.set('status', filters.status);
    if (filters.dateFrom) params.set('dateFrom', filters.dateFrom);
    if (filters.dateTo) params.set('dateTo', filters.dateTo);
    if (filters.endpoint !== 'all') params.set('endpoint', filters.endpoint);
    if (sort.field) params.set('sortField', sort.field);
    if (sort.direction) params.set('sortDirection', sort.direction);

    return params.toString();
  }, [activeTab, pagination.page, pagination.limit, filters, sort]);

  const fetchData = async (resetPage = false) => {
    try {
      setLoading(true);

      if (resetPage && pagination.page !== 1) {
        setPagination(prev => ({ ...prev, page: 1 }));
        return;
      }

      const queryParams = buildQueryParams();
      const response = await fetch(`/api/admin/ip-management?${queryParams}`);
      const result = await response.json();

      if (result.success) {
        setData(result.data);
        setPagination(prev => ({
          ...prev,
          hasMore: result.pagination?.hasMore || false,
          total: result.pagination?.total
        }));

        // Clear selections when data changes
        clearSelection(activeTab as 'blocked' | 'whitelist' | 'violation');
      }
    } catch (error) {
      console.error('Error fetching IP management data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handlePageSizeChange = (newSize: number) => {
    setPagination(prev => ({ ...prev, limit: newSize, page: 1 }));
  };

  // Filter handlers
  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      dateFrom: '',
      dateTo: '',
      endpoint: 'all'
    });
  };

  // Sort handlers
  const handleSort = (field: string) => {
    setSort(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Export functionality
  const handleExport = async () => {
    try {
      setExporting(true);
      const params = new URLSearchParams();
      params.set('type', activeTab);
      params.set('export', 'true');
      params.set('limit', '10000'); // Large limit for export

      if (filters.search) params.set('search', filters.search);
      if (filters.status !== 'all') params.set('status', filters.status);
      if (filters.dateFrom) params.set('dateFrom', filters.dateFrom);
      if (filters.dateTo) params.set('dateTo', filters.dateTo);
      if (filters.endpoint !== 'all') params.set('endpoint', filters.endpoint);

      const response = await fetch(`/api/admin/ip-management?${params.toString()}`);
      const result = await response.json();

      if (result.success) {
        // Convert to CSV and download
        const csvContent = convertToCSV(result.data, activeTab);
        downloadCSV(csvContent, `ip-management-${activeTab}-${new Date().toISOString().split('T')[0]}.csv`);
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Failed to export data');
    } finally {
      setExporting(false);
    }
  };

  const convertToCSV = (data: any, type: string) => {
    let headers: string[] = [];
    let rows: any[] = [];

    switch (type) {
      case 'blocked':
        headers = ['IP Address', 'Reason', 'Blocked By', 'Blocked At', 'Expires At', 'Status'];
        rows = data.blockedIPs?.map((ip: BlockedIP) => [
          ip.ip_address,
          ip.reason,
          ip.blocked_by,
          ip.blocked_at,
          ip.expires_at || 'Never',
          ip.is_active ? 'Active' : 'Inactive'
        ]) || [];
        break;
      case 'whitelist':
        headers = ['IP Address', 'Reason', 'Added By', 'Added At', 'Status'];
        rows = data.whitelistIPs?.map((ip: WhitelistIP) => [
          ip.ip_address,
          ip.reason,
          ip.added_by,
          ip.added_at,
          ip.is_active ? 'Active' : 'Inactive'
        ]) || [];
        break;
      case 'violations':
        headers = ['IP Address', 'Endpoint', 'Violation Count', 'First Violation', 'Last Violation', 'Status'];
        rows = data.violations?.map((v: RateLimitViolation) => [
          v.ip_address,
          v.endpoint,
          v.violation_count,
          v.first_violation,
          v.last_violation,
          v.is_resolved ? 'Resolved' : 'Unresolved'
        ]) || [];
        break;
    }

    const csvRows = [headers.join(','), ...rows.map(row => row.join(','))];
    return csvRows.join('\n');
  };

  const downloadCSV = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Delete functions
  const handleDeleteRecord = (
    recordType: 'blocked' | 'whitelist' | 'violation',
    recordId: string,
    ipAddress: string,
    details?: string
  ) => {
    setDeleteConfirm({
      show: true,
      type: 'single',
      recordType,
      recordId,
      ipAddress,
      details
    });
  };

  const handleBulkDelete = (
    recordType: 'blocked' | 'whitelist' | 'violation',
    recordIds: string[]
  ) => {
    setDeleteConfirm({
      show: true,
      type: 'bulk',
      recordType,
      recordIds
    });
  };

  const confirmDelete = async () => {
    try {
      if (deleteConfirm.type === 'single' && deleteConfirm.recordId) {
        // Single record deletion
        const params = new URLSearchParams();
        params.set('action', 'delete_record');
        params.set('id', deleteConfirm.recordId);
        params.set('type', deleteConfirm.recordType);
        if (deleteConfirm.ipAddress) {
          params.set('ipAddress', deleteConfirm.ipAddress);
        }

        const response = await fetch(`/api/admin/ip-management?${params.toString()}`, {
          method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
          await Promise.all([fetchData(), fetchCounts()]);
          alert(`Record deleted successfully`);
        } else {
          alert(`Error: ${result.error}`);
        }

      } else if (deleteConfirm.type === 'bulk' && deleteConfirm.recordIds) {
        // Bulk deletion
        const response = await fetch('/api/admin/ip-management?action=bulk_delete', {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            recordIds: deleteConfirm.recordIds,
            recordType: deleteConfirm.recordType
          })
        });

        const result = await response.json();

        if (result.success) {
          await Promise.all([fetchData(), fetchCounts()]);
          clearSelection(deleteConfirm.recordType);
          alert(result.message);
        } else {
          alert(`Error: ${result.error}`);
        }
      }

    } catch (error) {
      console.error('Error deleting record(s):', error);
      alert('An error occurred while deleting');
    } finally {
      setDeleteConfirm({ show: false, type: 'single', recordType: 'blocked' });
    }
  };

  const cancelDelete = () => {
    setDeleteConfirm({ show: false, type: 'single', recordType: 'blocked' });
  };

  // Selection handlers
  const handleSelectItem = (recordType: 'blocked' | 'whitelist' | 'violation', itemId: string) => {
    setSelectedItems(prev => {
      const newSelected = { ...prev };
      if (newSelected[recordType].has(itemId)) {
        newSelected[recordType].delete(itemId);
      } else {
        newSelected[recordType].add(itemId);
      }
      return newSelected;
    });
  };

  const handleSelectAll = (recordType: 'blocked' | 'whitelist' | 'violation', items: any[]) => {
    setSelectedItems(prev => {
      const newSelected = { ...prev };
      const allIds = items.map(item => item.id);

      if (newSelected[recordType].size === allIds.length) {
        // All selected, deselect all
        newSelected[recordType].clear();
      } else {
        // Not all selected, select all
        newSelected[recordType] = new Set(allIds);
      }
      return newSelected;
    });
  };

  const clearSelection = (recordType: 'blocked' | 'whitelist' | 'violation') => {
    setSelectedItems(prev => ({
      ...prev,
      [recordType]: new Set()
    }));
  };

  const getSelectedCount = (recordType: 'blocked' | 'whitelist' | 'violation') => {
    return selectedItems[recordType].size;
  };

  const handleBulkDeleteSelected = (recordType: 'blocked' | 'whitelist' | 'violation') => {
    const selectedIds = Array.from(selectedItems[recordType]);
    if (selectedIds.length > 0) {
      handleBulkDelete(recordType, selectedIds);
    }
  };

  const performAction = async (action: string, ipAddress: string, additionalData?: any) => {
    try {
      const response = await fetch('/api/admin/ip-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          ipAddress,
          ...additionalData
        })
      });

      const result = await response.json();

      if (result.success) {
        await Promise.all([fetchData(), fetchCounts()]); // Refresh both data and counts
        return true;
      } else {
        alert(`Error: ${result.error}`);
        return false;
      }
    } catch (error) {
      console.error('Error performing action:', error);
      alert('An error occurred');
      return false;
    }
  };

  const handleBlockIP = async () => {
    if (!newBlockIP || !blockReason) {
      alert('IP address and reason are required');
      return;
    }

    const success = await performAction('block', newBlockIP, {
      reason: blockReason,
      expiresAt: blockExpiry || undefined
    });

    if (success) {
      setNewBlockIP('');
      setBlockReason('');
      setBlockExpiry('');
    }
  };

  const handleWhitelistIP = async () => {
    if (!newWhitelistIP || !whitelistReason) {
      alert('IP address and reason are required');
      return;
    }

    const success = await performAction('whitelist', newWhitelistIP, {
      reason: whitelistReason
    });

    if (success) {
      setNewWhitelistIP('');
      setWhitelistReason('');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const isExpired = (expiresAt?: string) => {
    return expiresAt && new Date(expiresAt) < new Date();
  };

  // Pagination component
  const PaginationControls = () => (
    <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700">Show:</span>
          <select
            value={pagination.limit}
            onChange={(e) => handlePageSizeChange(Number(e.target.value))}
            className="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
          <span className="text-sm text-gray-700">per page</span>
        </div>
        {pagination.total && (
          <span className="text-sm text-gray-700">
            Total: {pagination.total} records
          </span>
        )}
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(pagination.page - 1)}
          disabled={pagination.page <= 1}
        >
          <ChevronLeft className="w-4 h-4" />
          Previous
        </Button>

        <span className="text-sm text-gray-700 px-3">
          Page {pagination.page}
        </span>

        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(pagination.page + 1)}
          disabled={!pagination.hasMore}
        >
          Next
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );

  // Search and filter controls
  const SearchAndFilters = () => (
    <Card className="mb-4">
      <CardContent className="pt-6">
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search IP addresses..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="w-4 h-4" />
              Filters
            </Button>
            <Button
              variant="outline"
              onClick={handleExport}
              disabled={exporting}
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              {exporting ? 'Exporting...' : 'Export'}
            </Button>
            <Button
              variant="outline"
              onClick={() => fetchData()}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  {activeTab === 'violations' && (
                    <>
                      <option value="resolved">Resolved</option>
                      <option value="unresolved">Unresolved</option>
                      <option value="blocked">Blocked IPs</option>
                      <option value="unblocked">Unblocked IPs</option>
                    </>
                  )}
                </select>
              </div>

              {activeTab === 'violations' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Endpoint</label>
                  <select
                    value={filters.endpoint}
                    onChange={(e) => handleFilterChange('endpoint', e.target.value)}
                    className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                  >
                    <option value="all">All Endpoints</option>
                    <option value="emailGeneration">Email Generation</option>
                    <option value="api">API</option>
                    <option value="tools">Tools</option>
                  </select>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                <Input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                <Input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="text-sm"
                />
              </div>

              <div className="md:col-span-4 flex justify-end">
                <Button variant="outline" onClick={clearFilters} size="sm">
                  Clear Filters
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  // Sort button component
  const SortButton = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <button
      onClick={() => handleSort(field)}
      className="flex items-center gap-1 text-sm font-medium text-gray-700 hover:text-gray-900"
    >
      {children}
      {sort.field === field ? (
        sort.direction === 'asc' ? <ArrowUp className="w-3 h-3" /> : <ArrowDown className="w-3 h-3" />
      ) : (
        <ArrowUpDown className="w-3 h-3 opacity-50" />
      )}
    </button>
  );

  // Bulk actions toolbar
  const BulkActionsToolbar = ({ recordType }: { recordType: 'blocked' | 'whitelist' | 'violation' }) => {
    const selectedCount = getSelectedCount(recordType);

    if (selectedCount === 0) return null;

    return (
      <div className="bg-blue-50 border-b border-blue-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium text-blue-900">
              {selectedCount} item{selectedCount !== 1 ? 's' : ''} selected
            </span>
            <Button
              size="sm"
              variant="outline"
              onClick={() => clearSelection(recordType)}
              className="text-blue-700 border-blue-300 hover:bg-blue-100"
            >
              Clear Selection
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="danger"
              onClick={() => handleBulkDeleteSelected(recordType)}
              className="flex items-center gap-1"
            >
              <Trash2 className="w-3 h-3" />
              Delete Selected ({selectedCount})
            </Button>
          </div>
        </div>
      </div>
    );
  };

  // Delete confirmation dialog using the professional Dialog component
  const DeleteConfirmDialog = () => {
    const recordTypeLabel = deleteConfirm.recordType === 'blocked' ? 'Blocked IP' :
                           deleteConfirm.recordType === 'whitelist' ? 'Whitelist Entry' :
                           'Rate Limit Violation';

    return (
      <Dialog open={deleteConfirm.show} onOpenChange={(open) => !open && cancelDelete()}>
        <DialogContent size="lg" className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                <AlertTriangle className="w-5 h-5 text-red-600" />
              </div>
              {deleteConfirm.type === 'single' ? 'Delete Record' : 'Bulk Delete Records'}
            </DialogTitle>
            <DialogDescription>
              This action cannot be undone. The {deleteConfirm.type === 'single' ? 'record' : 'records'} will be permanently removed from the database.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {deleteConfirm.type === 'single' ? (
              <div>
                <p className="text-gray-700 mb-4">
                  Are you sure you want to permanently delete this {recordTypeLabel.toLowerCase()}?
                </p>
                {deleteConfirm.ipAddress && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-gray-900 mb-1">IP Address:</p>
                        <code className="text-sm bg-white px-2 py-1 rounded border font-mono">
                          {deleteConfirm.ipAddress}
                        </code>
                      </div>
                      {deleteConfirm.details && (
                        <div>
                          <p className="text-sm font-medium text-gray-900 mb-1">Details:</p>
                          <p className="text-sm text-gray-700 bg-white px-3 py-2 rounded border">
                            {deleteConfirm.details}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div>
                <p className="text-gray-700 mb-4">
                  Are you sure you want to permanently delete <span className="font-semibold">{deleteConfirm.recordIds?.length}</span> {recordTypeLabel.toLowerCase()} records?
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-yellow-800">Bulk Deletion Warning</p>
                      <p className="text-sm text-yellow-700 mt-1">
                        This will permanently remove all selected records from the database. This action affects multiple records and cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={cancelDelete}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={confirmDelete}
              className="flex items-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              {deleteConfirm.type === 'single' ? 'Delete Record' : `Delete ${deleteConfirm.recordIds?.length} Records`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  if (loading && !data.blockedIPs && !data.whitelistIPs && !data.violations) {
    return <div className="p-6">Loading IP management data...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">IP Management</h1>
          <p className="text-gray-600 mt-1">
            Manage IP blocking, whitelisting, and rate limit violations with advanced filtering and pagination
          </p>
        </div>
      </div>

      <SearchAndFilters />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="blocked" className="flex items-center gap-2">
            <Ban className="w-4 h-4" />
            Blocked IPs ({tabCounts.blocked})
          </TabsTrigger>
          <TabsTrigger value="whitelist" className="flex items-center gap-2">
            <ShieldCheck className="w-4 h-4" />
            Whitelist ({tabCounts.whitelist})
          </TabsTrigger>
          <TabsTrigger value="violations" className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4" />
            Violations ({tabCounts.violations})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="blocked" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Block New IP Address</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  placeholder="IP Address (e.g., ***********)"
                  value={newBlockIP}
                  onChange={(e) => setNewBlockIP(e.target.value)}
                />
                <Input
                  type="datetime-local"
                  placeholder="Expiry (optional)"
                  value={blockExpiry}
                  onChange={(e) => setBlockExpiry(e.target.value)}
                />
                <Button onClick={handleBlockIP}>Block IP</Button>
              </div>
              <Textarea
                placeholder="Reason for blocking (required)"
                value={blockReason}
                onChange={(e) => setBlockReason(e.target.value)}
                rows={2}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <CardTitle>Blocked IP Addresses</CardTitle>
                  {data.blockedIPs && data.blockedIPs.length > 0 && (
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={selectedItems.blocked.size === data.blockedIPs.length && data.blockedIPs.length > 0}
                        onChange={() => handleSelectAll('blocked', data.blockedIPs || [])}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm text-gray-600">Select All</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <SortButton field="ip_address">IP Address</SortButton>
                  <SortButton field="blocked_at">Date Blocked</SortButton>
                  <SortButton field="expires_at">Expiry</SortButton>
                </div>
              </div>
            </CardHeader>

            <BulkActionsToolbar recordType="blocked" />

            <CardContent className="p-0">
              {loading && (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
                  <span className="ml-2 text-gray-600">Loading...</span>
                </div>
              )}

              {!loading && (
                <>
                  <div className="space-y-0">
                    {data.blockedIPs?.map((blocked, index) => (
                      <div key={blocked.id} className={`flex items-center justify-between p-4 ${index !== 0 ? 'border-t' : ''}`}>
                        <div className="flex items-center gap-4">
                          <input
                            type="checkbox"
                            checked={selectedItems.blocked.has(blocked.id)}
                            onChange={() => handleSelectItem('blocked', blocked.id)}
                            className="rounded border-gray-300 flex-shrink-0"
                          />
                          <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                              {blocked.ip_address}
                            </code>
                            <Badge variant={blocked.is_active ? "destructive" : "secondary"}>
                              {blocked.is_active ? "Active" : "Inactive"}
                            </Badge>
                            {blocked.expires_at && (
                              <Badge variant={isExpired(blocked.expires_at) ? "outline" : "default"}>
                                <Clock className="w-3 h-3 mr-1" />
                                {isExpired(blocked.expires_at) ? "Expired" : "Temporary"}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">{blocked.reason}</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500 mt-2">
                            <span className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              {blocked.blocked_by}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {formatDate(blocked.blocked_at)}
                            </span>
                            {blocked.expires_at && (
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                Expires: {formatDate(blocked.expires_at)}
                              </span>
                            )}
                          </div>
                          </div>
                        </div>
                        <div className="flex gap-2 ml-4">
                          {blocked.is_active && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => performAction('unblock', blocked.ip_address)}
                            >
                              Unblock
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleDeleteRecord(
                              'blocked',
                              blocked.id,
                              blocked.ip_address,
                              blocked.reason
                            )}
                            className="flex items-center gap-1"
                          >
                            <Trash2 className="w-3 h-3" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    ))}

                    {!loading && !data.blockedIPs?.length && (
                      <div className="text-center py-12">
                        <Ban className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500">No blocked IP addresses found</p>
                        {filters.search && (
                          <p className="text-sm text-gray-400 mt-1">
                            Try adjusting your search or filters
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {data.blockedIPs && data.blockedIPs.length > 0 && <PaginationControls />}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="whitelist" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Add IP to Whitelist</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  placeholder="IP Address (e.g., ***********)"
                  value={newWhitelistIP}
                  onChange={(e) => setNewWhitelistIP(e.target.value)}
                />
                <Button onClick={handleWhitelistIP}>Add to Whitelist</Button>
              </div>
              <Textarea
                placeholder="Reason for whitelisting (required)"
                value={whitelistReason}
                onChange={(e) => setWhitelistReason(e.target.value)}
                rows={2}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <CardTitle>Whitelisted IP Addresses</CardTitle>
                  {data.whitelistIPs && data.whitelistIPs.length > 0 && (
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={selectedItems.whitelist.size === data.whitelistIPs.length && data.whitelistIPs.length > 0}
                        onChange={() => handleSelectAll('whitelist', data.whitelistIPs || [])}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm text-gray-600">Select All</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <SortButton field="ip_address">IP Address</SortButton>
                  <SortButton field="added_at">Date Added</SortButton>
                </div>
              </div>
            </CardHeader>

            <BulkActionsToolbar recordType="whitelist" />

            <CardContent className="p-0">
              {loading && (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
                  <span className="ml-2 text-gray-600">Loading...</span>
                </div>
              )}

              {!loading && (
                <>
                  <div className="space-y-0">
                    {data.whitelistIPs?.map((whitelisted, index) => (
                      <div key={whitelisted.id} className={`flex items-center justify-between p-4 ${index !== 0 ? 'border-t' : ''}`}>
                        <div className="flex items-center gap-4">
                          <input
                            type="checkbox"
                            checked={selectedItems.whitelist.has(whitelisted.id)}
                            onChange={() => handleSelectItem('whitelist', whitelisted.id)}
                            className="rounded border-gray-300 flex-shrink-0"
                          />
                          <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <code className="bg-green-100 px-2 py-1 rounded text-sm font-mono">
                              {whitelisted.ip_address}
                            </code>
                            <Badge variant={whitelisted.is_active ? "default" : "secondary"}>
                              <ShieldCheck className="w-3 h-3 mr-1" />
                              {whitelisted.is_active ? "Active" : "Removed"}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">{whitelisted.reason}</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500 mt-2">
                            <span className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              Added by {whitelisted.added_by}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {formatDate(whitelisted.added_at)}
                            </span>
                            {!whitelisted.is_active && whitelisted.removed_at && (
                              <span className="flex items-center gap-1 text-red-600">
                                <XCircle className="w-3 h-3" />
                                Removed {formatDate(whitelisted.removed_at)}
                              </span>
                            )}
                          </div>
                          </div>
                        </div>
                        <div className="flex gap-2 ml-4">
                          {whitelisted.is_active ? (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => performAction('remove_whitelist', whitelisted.ip_address)}
                            >
                              Remove
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => performAction('whitelist', whitelisted.ip_address, {
                                reason: whitelisted.reason
                              })}
                            >
                              Re-add
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleDeleteRecord(
                              'whitelist',
                              whitelisted.id,
                              whitelisted.ip_address,
                              whitelisted.reason
                            )}
                            className="flex items-center gap-1"
                          >
                            <Trash2 className="w-3 h-3" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    ))}

                    {!loading && !data.whitelistIPs?.length && (
                      <div className="text-center py-12">
                        <ShieldCheck className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500">No whitelisted IP addresses found</p>
                        {filters.search && (
                          <p className="text-sm text-gray-400 mt-1">
                            Try adjusting your search or filters
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {data.whitelistIPs && data.whitelistIPs.length > 0 && <PaginationControls />}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="violations" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <CardTitle>Rate Limit Violations</CardTitle>
                  {data.violations && data.violations.length > 0 && (
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={selectedItems.violation.size === data.violations.length && data.violations.length > 0}
                        onChange={() => handleSelectAll('violation', data.violations || [])}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm text-gray-600">Select All</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <SortButton field="ip_address">IP Address</SortButton>
                  <SortButton field="violation_count">Count</SortButton>
                  <SortButton field="last_violation">Last Violation</SortButton>
                </div>
              </div>
            </CardHeader>

            <BulkActionsToolbar recordType="violation" />

            <CardContent className="p-0">
              {loading && (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
                  <span className="ml-2 text-gray-600">Loading...</span>
                </div>
              )}

              {!loading && (
                <>
                  <div className="space-y-0">
                    {data.violations?.map((violation, index) => (
                      <div key={violation.id} className={`flex items-center justify-between p-4 ${index !== 0 ? 'border-t' : ''}`}>
                        <div className="flex items-center gap-4">
                          <input
                            type="checkbox"
                            checked={selectedItems.violation.has(violation.id)}
                            onChange={() => handleSelectItem('violation', violation.id)}
                            className="rounded border-gray-300 flex-shrink-0"
                          />
                          <div className="flex-1">
                          <div className="flex items-center gap-2 flex-wrap">
                            <code className="bg-red-100 px-2 py-1 rounded text-sm font-mono">
                              {violation.ip_address}
                            </code>

                            {/* Blocked Status Indicator */}
                            {violation.blockedStatus?.isBlocked && (
                              <Badge
                                variant="destructive"
                                className="flex items-center gap-1"
                                title={violation.blockedStatus.blockReason ?
                                  `Blocked: ${violation.blockedStatus.blockReason}` :
                                  'IP address is currently blocked'
                                }
                              >
                                <Ban className="w-3 h-3" />
                                Blocked
                              </Badge>
                            )}

                            <Badge variant="outline">{violation.endpoint}</Badge>
                            <Badge variant={violation.is_resolved ? "default" : "destructive"}>
                              {violation.is_resolved ? (
                                <><CheckCircle className="w-3 h-3 mr-1" />Resolved</>
                              ) : (
                                <><XCircle className="w-3 h-3 mr-1" />Unresolved</>
                              )}
                            </Badge>
                            <Badge variant="secondary">
                              {violation.violation_count} violations
                            </Badge>
                          </div>
                          {violation.user_agent && (
                            <p className="text-xs text-gray-500 mt-1 truncate">
                              User Agent: {violation.user_agent}
                            </p>
                          )}
                          <div className="flex items-center gap-4 text-xs text-gray-500 mt-2">
                            <span>First: {formatDate(violation.first_violation)}</span>
                            <span>Last: {formatDate(violation.last_violation)}</span>
                            {violation.blockedStatus?.isBlocked && violation.blockedStatus.blockedAt && (
                              <span className="flex items-center gap-1 text-red-600 font-medium">
                                <Ban className="w-3 h-3" />
                                Blocked: {formatDate(violation.blockedStatus.blockedAt)}
                                {violation.blockedStatus.blockedBy && ` by ${violation.blockedStatus.blockedBy}`}
                              </span>
                            )}
                          </div>
                          </div>
                        </div>
                        <div className="flex gap-2 ml-4">
                          {!violation.is_resolved && (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => performAction('resolve_violation', violation.ip_address)}
                              >
                                Resolve
                              </Button>

                              {/* Dynamic Block/Unblock Button */}
                              {violation.blockedStatus?.isBlocked ? (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => performAction('unblock', violation.ip_address)}
                                  className="flex items-center gap-1"
                                >
                                  <ShieldCheck className="w-3 h-3" />
                                  Unblock IP
                                </Button>
                              ) : (
                                <Button
                                  size="sm"
                                  variant="danger"
                                  onClick={() => performAction('block', violation.ip_address, {
                                    reason: `Rate limit violations: ${violation.violation_count} times on ${violation.endpoint}`
                                  })}
                                  className="flex items-center gap-1"
                                >
                                  <Ban className="w-3 h-3" />
                                  Block IP
                                </Button>
                              )}
                            </>
                          )}

                          <Button
                            size="sm"
                            variant="danger"
                            onClick={() => handleDeleteRecord(
                              'violation',
                              violation.id,
                              violation.ip_address,
                              `${violation.violation_count} violations on ${violation.endpoint}`
                            )}
                            className="flex items-center gap-1"
                          >
                            <Trash2 className="w-3 h-3" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    ))}

                    {!loading && !data.violations?.length && (
                      <div className="text-center py-12">
                        <AlertTriangle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500">No rate limit violations found</p>
                        {filters.search && (
                          <p className="text-sm text-gray-400 mt-1">
                            Try adjusting your search or filters
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {data.violations && data.violations.length > 0 && <PaginationControls />}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog />
    </div>
  );
}
