// DKIM Generator Types - Isolated from existing types

export interface DkimKeyPair {
  privateKey: string;
  publicKey: string;
  keyStrength: 1024 | 2048;
  generatedAt: string;
}

export interface DkimRecord {
  selector: string;
  domain: string;
  publicKey: string;
  dnsRecord: string;
  recordName: string;
  keyStrength: 1024 | 2048;
}

export interface DkimGenerateRequest {
  domain: string;
  selector: string;
  keyStrength: 1024 | 2048;
  sessionId?: string;
}

export interface DkimGenerateResponse {
  success: boolean;
  data?: {
    privateKey: string;
    publicKey: string;
    dnsRecord: string;
    recordName: string;
    selector: string;
    domain: string;
    keyStrength: 1024 | 2048;
    instructions: string[];
    generatedAt: string;
    expiresAt: string;
  };
  message?: string;
  error?: string;
}

export interface DkimValidationRequest {
  domain: string;
  selector: string;
  expectedPublicKey?: string;
}

export interface DkimValidationResponse {
  success: boolean;
  data?: {
    exists: boolean;
    records: string[];
    isValid: boolean;
    errors: string[];
    lastChecked: string;
    publicKeyMatch?: boolean;
  };
  message?: string;
  error?: string;
}

export interface DkimFormData {
  domain: string;
  selector: string;
  keyStrength: 1024 | 2048;
}

export interface DkimFormErrors {
  domain?: string;
  selector?: string;
  keyStrength?: string;
}

// Database record type for DKIM
export interface DkimDatabaseRecord {
  id: string;
  session_id: string;
  user_id?: string;
  record_type: 'dkim';
  domain: string;
  selector: string;
  dns_record: string;
  record_name: string;
  private_key_encrypted: string;
  metadata: {
    keyStrength: 1024 | 2048;
    publicKey: string;
    instructions: string[];
  };
  created_at: string;
  expires_at: string;
  validated_at?: string;
  validation_status: 'pending' | 'valid' | 'invalid' | 'error';
  validation_errors: string[];
}
