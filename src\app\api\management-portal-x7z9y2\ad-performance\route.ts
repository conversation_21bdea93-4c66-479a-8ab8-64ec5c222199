/**
 * API route for ad performance metrics
 */
import { NextRequest, NextResponse } from 'next/server';
import { getCacheStats } from '@/lib/config/adCacheService';
import { logInfo, logError } from '@/lib/logging';
import { getAdLoadTimes, addAdLoadTime } from '@/lib/ad-performance/adLoadTimeService';

// The addAdLoadTime function has been moved to @/lib/ad-performance/adLoadTimeService.ts
// A new API route has been created at /api/management-portal-x7z9y2/ad-performance/load-time

/**
 * GET /api/management-portal-x7z9y2/ad-performance
 *
 * Get ad performance metrics
 */
export async function GET(request: NextRequest) {
  try {
    // Get cache stats
    const cacheStats = getCacheStats();

    // Return performance metrics
    return NextResponse.json({
      success: true,
      data: {
        ...cacheStats,
        loadTimes: getAdLoadTimes()
      }
    });
  } catch (error) {
    logError('api', 'Error getting ad performance metrics', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to get ad performance metrics' },
      { status: 500 }
    );
  }
}
