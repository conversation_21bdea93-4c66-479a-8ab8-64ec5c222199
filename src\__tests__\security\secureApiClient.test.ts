/**
 * Secure API Client Test Suite
 * Tests for session header inclusion and API security
 */

import { 
  SecureApiClient, 
  generateEmailWithSession,
  fetchWithSession 
} from '@/lib/api/secureApiClient';
import { 
  initializeSessionManagement,
  clearSession 
} from '@/lib/session/persistentSessionManager';

// Mock fetch globally
global.fetch = jest.fn();

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value; },
    removeItem: (key: string) => { delete store[key]; },
    clear: () => { store = {}; }
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('Secure API Client - Session Header Integration', () => {
  let mockFetch: jest.MockedFunction<typeof fetch>;
  
  beforeEach(() => {
    mockFetch = fetch as jest.MockedFunction<typeof fetch>;
    mockFetch.mockClear();
    localStorageMock.clear();
    
    // Mock successful API response
    mockFetch.mockResolvedValue({
      ok: true,
      status: 200,
      json: async () => ({
        success: true,
        data: {
          emailAddress: '<EMAIL>',
          expirationDate: new Date().toISOString(),
          success: true
        }
      }),
      headers: new Headers()
    } as Response);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Session Header Inclusion', () => {
    it('should include session ID in request headers', async () => {
      // Initialize session
      const session = initializeSessionManagement();
      
      const apiClient = new SecureApiClient();
      await apiClient.post('/api/test', { test: 'data' });
      
      // Verify fetch was called with session headers
      expect(mockFetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        headers: expect.objectContaining({
          'x-session-id': session.sessionId,
          'session-id': session.sessionId
        })
      }));
    });

    it('should include session headers in generateEmailWithSession', async () => {
      const session = initializeSessionManagement();
      
      await generateEmailWithSession();
      
      expect(mockFetch).toHaveBeenCalledWith('/api/generate', expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'x-session-id': session.sessionId,
          'session-id': session.sessionId,
          'Content-Type': 'application/json'
        })
      }));
    });

    it('should include session headers in fetchWithSession utility', async () => {
      const session = initializeSessionManagement();
      
      await fetchWithSession('/api/test', {
        method: 'POST',
        body: JSON.stringify({ test: 'data' })
      });
      
      expect(mockFetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        headers: expect.objectContaining({
          'x-session-id': session.sessionId,
          'session-id': session.sessionId
        })
      }));
    });

    it('should work without session ID when not available', async () => {
      // Don't initialize session
      clearSession();
      
      const apiClient = new SecureApiClient();
      await apiClient.get('/api/test');
      
      // Should still make request, but without session headers
      expect(mockFetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        headers: expect.objectContaining({
          'Content-Type': 'application/json'
        })
      }));
      
      // Verify session headers are not included
      const callArgs = mockFetch.mock.calls[0];
      const headers = callArgs[1]?.headers as Record<string, string>;
      expect(headers['x-session-id']).toBeUndefined();
      expect(headers['session-id']).toBeUndefined();
    });
  });

  describe('API Client Functionality', () => {
    it('should handle successful responses', async () => {
      initializeSessionManagement();
      
      const apiClient = new SecureApiClient();
      const response = await apiClient.post('/api/test', { test: 'data' });
      
      expect(response.success).toBe(true);
      expect(response.data).toBeDefined();
    });

    it('should handle error responses', async () => {
      initializeSessionManagement();
      
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({
          success: false,
          error: 'Bad request'
        }),
        headers: new Headers()
      } as Response);
      
      const apiClient = new SecureApiClient();
      const response = await apiClient.post('/api/test', { test: 'data' });
      
      expect(response.success).toBe(false);
      expect(response.error).toBe('Bad request');
    });

    it('should handle rate limiting responses', async () => {
      initializeSessionManagement();
      
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        json: async () => ({
          success: false,
          message: 'Rate limit exceeded'
        }),
        headers: new Headers({
          'X-RateLimit-Remaining': '0',
          'Retry-After': '60'
        })
      } as Response);
      
      const apiClient = new SecureApiClient();
      const response = await apiClient.post('/api/test', { test: 'data' });
      
      expect(response.success).toBe(false);
      expect(response.error).toContain('Rate limit exceeded');
    });

    it('should handle network errors', async () => {
      initializeSessionManagement();
      
      mockFetch.mockRejectedValueOnce(new Error('Network error'));
      
      const apiClient = new SecureApiClient();
      const response = await apiClient.post('/api/test', { test: 'data' });
      
      expect(response.success).toBe(false);
      expect(response.error).toBe('Network error');
    });

    it('should handle timeout errors', async () => {
      initializeSessionManagement();
      
      // Mock AbortError
      const abortError = new Error('The operation was aborted');
      abortError.name = 'AbortError';
      mockFetch.mockRejectedValueOnce(abortError);
      
      const apiClient = new SecureApiClient();
      const response = await apiClient.post('/api/test', { test: 'data' }, { timeout: 1000 });
      
      expect(response.success).toBe(false);
      expect(response.error).toBe('Request timeout');
    });
  });

  describe('HTTP Methods', () => {
    beforeEach(() => {
      initializeSessionManagement();
    });

    it('should support GET requests', async () => {
      const apiClient = new SecureApiClient();
      await apiClient.get('/api/test');
      
      expect(mockFetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        method: 'GET'
      }));
    });

    it('should support POST requests', async () => {
      const apiClient = new SecureApiClient();
      await apiClient.post('/api/test', { data: 'test' });
      
      expect(mockFetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        method: 'POST',
        body: JSON.stringify({ data: 'test' })
      }));
    });

    it('should support PUT requests', async () => {
      const apiClient = new SecureApiClient();
      await apiClient.put('/api/test', { data: 'test' });
      
      expect(mockFetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        method: 'PUT',
        body: JSON.stringify({ data: 'test' })
      }));
    });

    it('should support DELETE requests', async () => {
      const apiClient = new SecureApiClient();
      await apiClient.delete('/api/test');
      
      expect(mockFetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        method: 'DELETE'
      }));
    });
  });

  describe('Session Activity Tracking', () => {
    it('should update session activity on API calls', async () => {
      const session = initializeSessionManagement();
      const originalActivity = session.lastActivity;
      
      // Mock a later time
      jest.spyOn(Date, 'now').mockReturnValue(originalActivity + 60000);
      
      const apiClient = new SecureApiClient();
      await apiClient.get('/api/test');
      
      // Session activity should be updated
      const storedMetadata = localStorageMock.getItem('vanishpost_session_metadata');
      expect(storedMetadata).toBeDefined();
      
      const metadata = JSON.parse(storedMetadata!);
      expect(metadata.lastActivity).toBeGreaterThan(originalActivity);
    });
  });

  describe('Security Features', () => {
    it('should include both primary and fallback session headers', async () => {
      const session = initializeSessionManagement();
      
      const apiClient = new SecureApiClient();
      await apiClient.post('/api/test');
      
      const callArgs = mockFetch.mock.calls[0];
      const headers = callArgs[1]?.headers as Record<string, string>;
      
      // Both headers should be present for maximum compatibility
      expect(headers['x-session-id']).toBe(session.sessionId);
      expect(headers['session-id']).toBe(session.sessionId);
    });

    it('should set appropriate content type', async () => {
      initializeSessionManagement();
      
      const apiClient = new SecureApiClient();
      await apiClient.post('/api/test', { data: 'test' });
      
      const callArgs = mockFetch.mock.calls[0];
      const headers = callArgs[1]?.headers as Record<string, string>;
      
      expect(headers['Content-Type']).toBe('application/json');
    });

    it('should handle custom headers while preserving session headers', async () => {
      const session = initializeSessionManagement();
      
      const apiClient = new SecureApiClient();
      await apiClient.post('/api/test', { data: 'test' }, {
        headers: {
          'Custom-Header': 'custom-value',
          'Authorization': 'Bearer token'
        }
      });
      
      const callArgs = mockFetch.mock.calls[0];
      const headers = callArgs[1]?.headers as Record<string, string>;
      
      // Custom headers should be included
      expect(headers['Custom-Header']).toBe('custom-value');
      expect(headers['Authorization']).toBe('Bearer token');
      
      // Session headers should still be present
      expect(headers['x-session-id']).toBe(session.sessionId);
      expect(headers['session-id']).toBe(session.sessionId);
    });
  });
});

describe('Email Generation API Integration', () => {
  beforeEach(() => {
    (fetch as jest.MockedFunction<typeof fetch>).mockClear();
    localStorageMock.clear();
  });

  it('should call correct endpoint for email generation', async () => {
    initializeSessionManagement();
    
    await generateEmailWithSession();
    
    expect(fetch).toHaveBeenCalledWith('/api/generate', expect.objectContaining({
      method: 'POST'
    }));
  });

  it('should handle email generation success response', async () => {
    initializeSessionManagement();
    
    const mockResponse = {
      emailAddress: '<EMAIL>',
      expirationDate: new Date().toISOString(),
      success: true
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
      headers: new Headers()
    } as Response);
    
    const result = await generateEmailWithSession();
    
    expect(result.success).toBe(true);
    expect(result.data?.emailAddress).toBe('<EMAIL>');
  });

  it('should handle email generation rate limit response', async () => {
    initializeSessionManagement();
    
    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
      ok: false,
      status: 429,
      json: async () => ({
        success: false,
        message: 'Rate limit exceeded'
      }),
      headers: new Headers()
    } as Response);
    
    const result = await generateEmailWithSession();
    
    expect(result.success).toBe(false);
    expect(result.error).toContain('Rate limit exceeded');
  });
});
