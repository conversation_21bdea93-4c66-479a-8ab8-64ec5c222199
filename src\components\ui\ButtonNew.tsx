'use client';

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Define button variants using class-variance-authority
const buttonVariants = cva(
  // Base styles applied to all buttons
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-70 active:scale-98 relative overflow-hidden",
  {
    variants: {
      variant: {
        primary: "bg-[#605f5f] text-white border border-[#605f5f] hover:bg-[#505050] hover:border-[#505050] hover:shadow-md focus-visible:ring-[#605f5f]",
        secondary: "bg-white text-[#605f5f] border border-[#605f5f] hover:bg-neutral-50 hover:shadow-md focus-visible:ring-[#605f5f]",
        danger: "bg-error-500 text-white border border-error-500 hover:bg-error-600 hover:border-error-600 hover:shadow-md focus-visible:ring-error-400",
        ghost: "bg-transparent text-neutral-700 hover:bg-neutral-100 hover:text-neutral-800 focus-visible:ring-neutral-500",
        link: "bg-transparent text-[#605f5f] hover:text-[#505050] underline-offset-4 hover:underline p-0 h-auto focus-visible:ring-[#605f5f]",
      },
      size: {
        sm: "h-9 px-3 py-2",
        md: "h-10 px-4 py-2.5",
        lg: "h-12 px-6 py-3.5",
        icon: "h-10 w-10",
      },
      fullWidth: {
        true: "w-full",
        false: "",
      },
      withAnimation: {
        true: "hover:-translate-y-0.5",
        false: "",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
      fullWidth: false,
      withAnimation: true,
    },
  }
);

// Define the props for the Button component
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  children: React.ReactNode;
  asChild?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

/**
 * Button Component
 *
 * A customizable button component with various styles and sizes.
 *
 * @param {string} variant - The style variant of the button (primary, secondary, danger, ghost, link)
 * @param {string} size - The size of the button (sm, md, lg, icon)
 * @param {boolean} fullWidth - Whether the button should take up the full width of its container
 * @param {boolean} withAnimation - Whether the button should have hover animation
 * @param {boolean} loading - Whether the button is in a loading state
 * @param {React.ReactNode} icon - An optional icon to display in the button
 * @param {string} iconPosition - The position of the icon (left or right)
 * @param {React.ReactNode} children - The content of the button
 * @param {React.ButtonHTMLAttributes<HTMLButtonElement>} ...props - Any other button props
 * @returns {JSX.Element} The rendered button component
 */
const ButtonNew = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant,
    size,
    fullWidth,
    withAnimation,
    loading = false,
    icon,
    iconPosition = 'left',
    children,
    ...props
  }, ref) => {
    // Determine if the button has an icon
    const hasIcon = icon || loading;

    return (
      <button
        className={cn(buttonVariants({ variant, size, fullWidth, withAnimation }), className)}
        ref={ref}
        disabled={props.disabled || loading}
        {...props}
      >
        {/* Gradient overlay for hover effect */}
        {variant === 'primary' && (
          <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-white/10 to-white/0 transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out"></span>
        )}
        {variant === 'secondary' && (
          <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-neutral-100/0 via-neutral-100/40 to-neutral-100/0 transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out"></span>
        )}

        <div className="relative flex items-center justify-center">
          {/* Loading spinner or left-positioned icon */}
          {hasIcon && iconPosition === 'left' && (
            <span className="mr-2.5">
              {loading ? (
                <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                icon
              )}
            </span>
          )}

          {/* Button text */}
          <span className="font-semibold">{children}</span>

          {/* Right-positioned icon */}
          {hasIcon && iconPosition === 'right' && (
            <span className="ml-2.5">
              {loading ? (
                <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                icon
              )}
            </span>
          )}
        </div>
      </button>
    );
  }
);

ButtonNew.displayName = "ButtonNew";

export { ButtonNew, buttonVariants };
