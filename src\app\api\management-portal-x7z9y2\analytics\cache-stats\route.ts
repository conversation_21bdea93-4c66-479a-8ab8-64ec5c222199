/**
 * Cache Statistics API Endpoint
 * 
 * This endpoint provides cache performance metrics and statistics
 * for monitoring the analytics caching system effectiveness.
 */

import { NextRequest, NextResponse } from 'next/server';
import { analyticsCache } from '@/lib/analytics/cache';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/management-portal-x7z9y2/analytics/cache-stats
 * Get cache performance metrics and statistics
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeDetails = searchParams.get('includeDetails') === 'true';

    // Get cache metrics
    const metrics = analyticsCache.getMetrics();
    const stats = analyticsCache.getStats();

    // Calculate additional performance metrics
    const totalRequests = metrics.hits + metrics.misses;
    const efficiency = totalRequests > 0 ? (metrics.hits / totalRequests) * 100 : 0;
    
    // Determine cache health status
    let healthStatus: 'excellent' | 'good' | 'fair' | 'poor';
    if (efficiency >= 80) {
      healthStatus = 'excellent';
    } else if (efficiency >= 60) {
      healthStatus = 'good';
    } else if (efficiency >= 40) {
      healthStatus = 'fair';
    } else {
      healthStatus = 'poor';
    }

    // Calculate cache utilization
    const utilization = (stats.size / stats.maxSize) * 100;

    const result = {
      performance: {
        hitRate: metrics.hitRate,
        efficiency: Math.round(efficiency * 100) / 100,
        totalRequests,
        hits: metrics.hits,
        misses: metrics.misses,
        healthStatus
      },
      operations: {
        sets: metrics.sets,
        deletes: metrics.deletes,
        evictions: metrics.evictions
      },
      storage: {
        currentSize: stats.size,
        maxSize: stats.maxSize,
        utilization: Math.round(utilization * 100) / 100,
        memoryUsage: stats.memoryUsage
      },
      timing: {
        oldestEntry: stats.oldestEntry ? new Date(stats.oldestEntry).toISOString() : null,
        newestEntry: stats.newestEntry ? new Date(stats.newestEntry).toISOString() : null,
        cacheAge: stats.oldestEntry ? Date.now() - stats.oldestEntry : 0
      }
    };

    // Add detailed breakdown if requested
    if (includeDetails) {
      // Get sample of cache keys (first 20)
      const sampleKeys: string[] = [];
      let count = 0;
      for (const key of (analyticsCache as any).cache.keys()) {
        if (count >= 20) break;
        sampleKeys.push(key);
        count++;
      }

      (result as any).details = {
        sampleKeys,
        recommendations: generateRecommendations(metrics, stats, efficiency, utilization)
      };
    }

    logInfo('CacheStatsAPI', 'Retrieved cache statistics', { 
      hitRate: metrics.hitRate,
      efficiency,
      utilization,
      healthStatus
    });

    return NextResponse.json({
      success: true,
      data: result,
      meta: {
        includeDetails,
        queryTime: new Date().toISOString(),
        cacheSystemVersion: '1.0.0'
      }
    });

  } catch (error) {
    logError('CacheStatsAPI', 'Error retrieving cache statistics', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve cache statistics' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/management-portal-x7z9y2/analytics/cache-stats
 * Perform cache management operations
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, pattern } = body;

    if (!action) {
      return NextResponse.json(
        { success: false, error: 'Action is required' },
        { status: 400 }
      );
    }

    let result: any = {};

    switch (action) {
      case 'clear':
        analyticsCache.clear();
        result = { message: 'Cache cleared successfully' };
        logInfo('CacheStatsAPI', 'Cache cleared manually');
        break;

      case 'invalidate':
        if (!pattern) {
          return NextResponse.json(
            { success: false, error: 'Pattern is required for invalidate action' },
            { status: 400 }
          );
        }
        const invalidatedCount = analyticsCache.invalidatePattern(pattern);
        result = { 
          message: `Invalidated ${invalidatedCount} cache entries`,
          pattern,
          count: invalidatedCount
        };
        logInfo('CacheStatsAPI', 'Cache pattern invalidated', { pattern, count: invalidatedCount });
        break;

      case 'stats-reset':
        // Reset metrics (this would require extending the cache class)
        result = { message: 'Cache statistics reset (not implemented)' };
        break;

      default:
        return NextResponse.json(
          { success: false, error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data: result,
      meta: {
        action,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logError('CacheStatsAPI', 'Error in cache management operation', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to perform cache operation' },
      { status: 500 }
    );
  }
}

/**
 * Generate cache performance recommendations
 */
function generateRecommendations(
  metrics: any, 
  stats: any, 
  efficiency: number, 
  utilization: number
): string[] {
  const recommendations: string[] = [];

  // Hit rate recommendations
  if (efficiency < 40) {
    recommendations.push('Cache hit rate is low. Consider increasing TTL values or reviewing cache key strategies.');
  } else if (efficiency < 60) {
    recommendations.push('Cache hit rate could be improved. Review frequently accessed data patterns.');
  }

  // Utilization recommendations
  if (utilization > 90) {
    recommendations.push('Cache is near capacity. Consider increasing maxSize or implementing more aggressive cleanup.');
  } else if (utilization < 20) {
    recommendations.push('Cache utilization is low. Consider reducing maxSize to optimize memory usage.');
  }

  // Eviction recommendations
  if (metrics.evictions > metrics.sets * 0.1) {
    recommendations.push('High eviction rate detected. Consider increasing cache size or reducing TTL values.');
  }

  // Memory recommendations
  if (stats.memoryUsage && stats.memoryUsage.includes('MB')) {
    const mbUsage = parseInt(stats.memoryUsage);
    if (mbUsage > 50) {
      recommendations.push('High memory usage detected. Monitor cache size and consider optimization.');
    }
  }

  // Performance recommendations
  if (metrics.hits > 1000 && efficiency > 80) {
    recommendations.push('Excellent cache performance! Current configuration is optimal.');
  }

  if (recommendations.length === 0) {
    recommendations.push('Cache performance is within normal parameters.');
  }

  return recommendations;
}
