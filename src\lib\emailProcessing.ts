/**
 * Email processing utilities for VanishPost
 */

import { ParsedEmail } from './types';
import { UI } from './constants';

/**
 * Interface for the email object used in the UI
 */
export interface Email {
  id: string;
  sender: string;
  subject: string;
  preview: string;
  timestamp: string;
  date: string; // ISO string date
  content: string;
  fromName: string;
  fromEmail: string;
  to: string; // Recipient email address
  html: string;
  isRead: boolean;
  attachments: {
    filename: string;
    size: number;
    contentType: string;
  }[];
}

/**
 * Maps an API email to a UI email
 *
 * @param apiEmail The email from the API
 * @param readStatusMap Map of email IDs to read status
 * @returns A UI-ready email object
 */
export const mapApiEmailToUiEmail = (
  apiEmail: ParsedEmail,
  readStatusMap: Record<string, boolean>
): Email => {
  return {
    id: apiEmail.mail_id,
    sender: apiEmail.fromName || apiEmail.from,
    subject: apiEmail.subject,
    preview: apiEmail.text.substring(0, UI.MAX_PREVIEW_LENGTH),
    timestamp: new Date(apiEmail.date).toLocaleTimeString([], UI.TIME_FORMAT),
    date: apiEmail.date, // Store the original date string
    content: apiEmail.text,
    fromName: apiEmail.fromName,
    fromEmail: apiEmail.fromEmail,
    to: apiEmail.to, // Include the recipient email address
    html: apiEmail.html,
    isRead: readStatusMap[apiEmail.mail_id] || false,
    attachments: apiEmail.attachments.map(attachment => ({
      filename: attachment.filename,
      size: attachment.size,
      contentType: attachment.contentType
    }))
  };
};

/**
 * Creates a map of email IDs to read status
 *
 * @param emails List of emails
 * @returns Map of email IDs to read status
 */
export const createReadStatusMap = (emails: Email[]): Record<string, boolean> => {
  const readStatusMap: Record<string, boolean> = {};

  emails.forEach(email => {
    readStatusMap[email.id] = email.isRead;
  });

  return readStatusMap;
};

/**
 * Loads read status from localStorage
 *
 * @param key The localStorage key
 * @returns Map of email IDs to read status
 */
export const loadReadStatus = (key: string): Record<string, boolean> => {
  try {
    const storedData = localStorage.getItem(key);
    return storedData ? JSON.parse(storedData) : {};
  } catch (error) {
    console.error('Error loading read status from localStorage:', error);
    return {};
  }
};

/**
 * Saves read status to localStorage
 *
 * @param key The localStorage key
 * @param emailId The email ID
 * @param isRead Whether the email is read
 */
export const saveReadStatus = (key: string, emailId: string, isRead: boolean): void => {
  try {
    const storedData = localStorage.getItem(key);
    const readStatus = storedData ? JSON.parse(storedData) : {};

    readStatus[emailId] = isRead;

    localStorage.setItem(key, JSON.stringify(readStatus));
  } catch (error) {
    console.error('Error saving read status to localStorage:', error);
  }
};

/**
 * Creates guide emails for new users
 *
 * @returns List of guide emails
 */
export const createGuideEmails = (): Email[] => {
  const now = new Date();
  const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
  const tenMinutesAgo = new Date(now.getTime() - 10 * 60 * 1000);

  return [
    {
      id: 'guide-1',
      sender: 'VanishPost Team',
      subject: 'Welcome to VanishPost!',
      preview: 'Welcome to VanishPost, your temporary email service. Here\'s how to get started...',
      timestamp: fiveMinutesAgo.toLocaleTimeString([], UI.TIME_FORMAT),
      date: fiveMinutesAgo.toISOString(),
      to: '<EMAIL>',
      content: `Welcome to VanishPost, your temporary email service!

VanishPost provides you with temporary email addresses that automatically expire after 15 minutes, helping you protect your privacy online.

To get started:
1. Click the "New Address" button to generate a temporary email address
2. Use this address when signing up for services you don't want to give your real email to
3. Any emails sent to this address will appear in your inbox automatically
4. After 15 minutes, the address will expire and be deleted

Enjoy using VanishPost!

- The VanishPost Team`,
      fromName: 'VanishPost Team',
      fromEmail: '<EMAIL>',
      html: `<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 600px; margin: 0 auto; background-color: #fbfaf8; padding: 32px; border-radius: 8px; border: 1px solid #f3ece8;">
  <h2 style="color: #ce601c; margin-top: 0; margin-bottom: 16px; font-size: 24px; font-weight: 600;">Welcome to VanishPost!</h2>
  <p style="color: #4a3728; line-height: 1.6; margin-bottom: 20px;">VanishPost provides you with <strong style="color: #1b130e;">temporary email addresses</strong> that automatically expire after 15 minutes, helping you protect your privacy online.</p>
  <h3 style="color: #ce601c; margin-top: 24px; margin-bottom: 12px; font-size: 18px; font-weight: 600;">To get started:</h3>
  <ol style="color: #4a3728; line-height: 1.6; padding-left: 20px;">
    <li style="margin-bottom: 8px;">Click the <strong style="color: #1b130e;">"New Address"</strong> button to generate a temporary email address</li>
    <li style="margin-bottom: 8px;">Use this address when signing up for services you don't want to give your real email to</li>
    <li style="margin-bottom: 8px;">Any emails sent to this address will appear in your inbox automatically</li>
    <li style="margin-bottom: 8px;">After <strong style="color: #1b130e;">15 minutes</strong>, the address expires</li>
  </ol>
  <p style="color: #4a3728; line-height: 1.6; margin-top: 24px; margin-bottom: 16px;">Enjoy using VanishPost!</p>
  <p style="color: #4a3728; line-height: 1.6; margin-bottom: 0;">- The VanishPost Team</p>
</div>`,
      isRead: false,
      attachments: []
    },
    {
      id: 'guide-2',
      sender: 'VanishPost Tips',
      subject: 'Tips for using VanishPost',
      preview: 'Here are some tips to get the most out of your VanishPost experience...',
      timestamp: tenMinutesAgo.toLocaleTimeString([], UI.TIME_FORMAT),
      date: tenMinutesAgo.toISOString(),
      to: '<EMAIL>',
      content: `Tips for using VanishPost

Here are some tips to get the most out of your VanishPost experience:

1. Refresh: Click the refresh button to check for new emails manually
2. Auto-refresh: Emails are automatically checked every 14 seconds
3. Read status: Unread emails are highlighted with an orange indicator
4. Copy address: Click the copy button to copy your email address to the clipboard

If you need any help, feel free to contact <NAME_EMAIL>

- VanishPost Tips`,
      fromName: 'VanishPost Tips',
      fromEmail: '<EMAIL>',
      html: `<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 600px; margin: 0 auto; background-color: #fbfaf8; padding: 32px; border-radius: 8px; border: 1px solid #f3ece8;">
  <h2 style="color: #ce601c; margin-top: 0; margin-bottom: 16px; font-size: 24px; font-weight: 600;">Tips for using VanishPost</h2>
  <p style="color: #4a3728; line-height: 1.6; margin-bottom: 20px;">Here are some tips to get the most out of your VanishPost experience:</p>
  <ol style="color: #4a3728; line-height: 1.6; padding-left: 20px;">
    <li style="margin-bottom: 8px;"><strong style="color: #1b130e;">Refresh:</strong> Click the refresh button to check for new emails manually</li>
    <li style="margin-bottom: 8px;"><strong style="color: #1b130e;">Auto-refresh:</strong> Emails are automatically checked every 14 seconds</li>
    <li style="margin-bottom: 8px;"><strong style="color: #1b130e;">Read status:</strong> Unread emails are highlighted with an <span style="color: #ce601c; font-weight: 600;">orange indicator</span></li>
    <li style="margin-bottom: 8px;"><strong style="color: #1b130e;">Copy address:</strong> Click the copy button to copy your email address to the clipboard</li>
  </ol>
  <p style="color: #4a3728; line-height: 1.6; margin-top: 24px; margin-bottom: 16px;">If you need any help, feel free to contact us at <a href="mailto:<EMAIL>" style="color: #ce601c; text-decoration: none;"><EMAIL></a></p>
  <p style="color: #4a3728; line-height: 1.6; margin-bottom: 0;">- VanishPost Tips</p>
</div>`,
      isRead: false,
      attachments: []
    },
    {
      id: 'guide-3',
      sender: 'VanishPost Security',
      subject: 'Protecting your privacy with VanishPost',
      preview: 'Learn how VanishPost helps protect your privacy online...',
      timestamp: now.toLocaleTimeString([], UI.TIME_FORMAT),
      date: now.toISOString(),
      to: '<EMAIL>',
      content: `Protecting your privacy with VanishPost

VanishPost is designed with privacy in mind. Here's how we help protect your information:

1. Temporary addresses: All email addresses expire after 15 minutes
2. No registration: We don't require any personal information to use our service
3. Automatic cleanup: Expired emails are automatically deleted from our servers
4. Secure connections: All connections to VanishPost are encrypted with HTTPS

Stay safe online!

- VanishPost Security Team`,
      fromName: 'VanishPost Security',
      fromEmail: '<EMAIL>',
      html: `<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 600px; margin: 0 auto; background-color: #fbfaf8; padding: 32px; border-radius: 8px; border: 1px solid #f3ece8;">
  <h2 style="color: #ce601c; margin-top: 0; margin-bottom: 16px; font-size: 24px; font-weight: 600;">Protecting your privacy with VanishPost</h2>
  <p style="color: #4a3728; line-height: 1.6; margin-bottom: 20px;">VanishPost is designed with privacy in mind. Here's how we help protect your information:</p>
  <ol style="color: #4a3728; line-height: 1.6; padding-left: 20px;">
    <li style="margin-bottom: 8px;"><strong style="color: #1b130e;">Temporary addresses:</strong> All email addresses expire after 15 minutes</li>
    <li style="margin-bottom: 8px;"><strong style="color: #1b130e;">No registration:</strong> We don't require any personal information to use our service</li>
    <li style="margin-bottom: 8px;"><strong style="color: #1b130e;">Automatic cleanup:</strong> Expired emails are automatically deleted from our servers</li>
    <li style="margin-bottom: 8px;"><strong style="color: #1b130e;">Secure connections:</strong> All connections to VanishPost are encrypted with HTTPS</li>
  </ol>
  <p style="color: #4a3728; line-height: 1.6; margin-top: 24px; margin-bottom: 16px;">Stay safe online!</p>
  <p style="color: #4a3728; line-height: 1.6; margin-bottom: 0;">- VanishPost Security Team</p>
</div>`,
      isRead: false,
      attachments: []
    }
  ];
};
