/**
 * API routes for admin user activity logs
 */
import { NextRequest, NextResponse } from 'next/server';
import { getAdminActivityLogs } from '@/lib/admin/user-service';
import { getCurrentUser } from '@/lib/auth';
import { AdminUserActivityFilter } from '@/lib/types/admin-users';

/**
 * GET /api/admin/users/activity - Get admin user activity logs
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin privileges
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const filter: AdminUserActivityFilter = {};

    // Apply filters from query parameters
    if (searchParams.has('userId')) {
      filter.userId = parseInt(searchParams.get('userId') || '0');
    }

    if (searchParams.has('actionType')) {
      filter.actionType = searchParams.get('actionType') || undefined;
    }

    if (searchParams.has('resourceType')) {
      filter.resourceType = searchParams.get('resourceType') || undefined;
    }

    if (searchParams.has('startDate')) {
      filter.startDate = searchParams.get('startDate') || undefined;
    }

    if (searchParams.has('endDate')) {
      filter.endDate = searchParams.get('endDate') || undefined;
    }

    // Apply pagination
    if (searchParams.has('limit')) {
      filter.limit = parseInt(searchParams.get('limit') || '20');
    }

    if (searchParams.has('offset')) {
      filter.offset = parseInt(searchParams.get('offset') || '0');
    }

    // Get activity logs
    const logs = await getAdminActivityLogs(filter);

    return NextResponse.json({ success: true, data: logs });
  } catch (error) {
    console.error('Error getting admin activity logs:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get admin activity logs' },
      { status: 500 }
    );
  }
}
