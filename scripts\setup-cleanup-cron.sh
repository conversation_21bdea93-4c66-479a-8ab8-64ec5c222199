#!/bin/bash

# Analytics Cleanup Cron Setup Script
# 
# This script sets up automated cleanup for analytics data using cron.
# It creates a cron job that runs daily at 2 AM to clean up old data.

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CLEANUP_SCRIPT="$SCRIPT_DIR/analytics-cleanup.js"
LOG_DIR="$PROJECT_DIR/logs"
CRON_LOG="$LOG_DIR/analytics-cleanup.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Setting up Analytics Cleanup Cron Job${NC}"
echo "Project Directory: $PROJECT_DIR"
echo "Cleanup Script: $CLEANUP_SCRIPT"
echo "Log Directory: $LOG_DIR"

# Create logs directory if it doesn't exist
if [ ! -d "$LOG_DIR" ]; then
    echo "Creating logs directory..."
    mkdir -p "$LOG_DIR"
fi

# Make cleanup script executable
if [ -f "$CLEANUP_SCRIPT" ]; then
    chmod +x "$CLEANUP_SCRIPT"
    echo -e "${GREEN}✓${NC} Made cleanup script executable"
else
    echo -e "${RED}✗${NC} Cleanup script not found: $CLEANUP_SCRIPT"
    exit 1
fi

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo -e "${RED}✗${NC} Node.js is not installed or not in PATH"
    exit 1
fi

# Test the cleanup script (dry run)
echo "Testing cleanup script..."
cd "$PROJECT_DIR"
if node "$CLEANUP_SCRIPT" --dry-run --stats-only; then
    echo -e "${GREEN}✓${NC} Cleanup script test successful"
else
    echo -e "${RED}✗${NC} Cleanup script test failed"
    exit 1
fi

# Create cron job entry
CRON_ENTRY="0 2 * * * cd $PROJECT_DIR && node $CLEANUP_SCRIPT >> $CRON_LOG 2>&1"

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "analytics-cleanup.js"; then
    echo -e "${YELLOW}⚠${NC} Analytics cleanup cron job already exists"
    echo "Current cron jobs:"
    crontab -l | grep "analytics-cleanup.js" || true
    
    read -p "Do you want to replace the existing cron job? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Keeping existing cron job"
        exit 0
    fi
    
    # Remove existing cron job
    crontab -l | grep -v "analytics-cleanup.js" | crontab -
    echo "Removed existing cron job"
fi

# Add new cron job
(crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -

echo -e "${GREEN}✓${NC} Analytics cleanup cron job added successfully"
echo "Schedule: Daily at 2:00 AM"
echo "Log file: $CRON_LOG"

# Display current cron jobs
echo
echo "Current cron jobs:"
crontab -l

# Create log rotation configuration
LOGROTATE_CONFIG="/etc/logrotate.d/analytics-cleanup"
if [ -w "/etc/logrotate.d" ]; then
    echo "Creating log rotation configuration..."
    sudo tee "$LOGROTATE_CONFIG" > /dev/null << EOF
$CRON_LOG {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 $(whoami) $(whoami)
}
EOF
    echo -e "${GREEN}✓${NC} Log rotation configured"
else
    echo -e "${YELLOW}⚠${NC} Cannot create log rotation config (no write access to /etc/logrotate.d)"
    echo "You may want to manually configure log rotation for: $CRON_LOG"
fi

# Create monitoring script
MONITOR_SCRIPT="$SCRIPT_DIR/check-cleanup-status.sh"
cat > "$MONITOR_SCRIPT" << 'EOF'
#!/bin/bash

# Analytics Cleanup Status Monitor
# Check the status of the last cleanup run

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_DIR/logs/analytics-cleanup.log"

if [ ! -f "$LOG_FILE" ]; then
    echo "No cleanup log found at: $LOG_FILE"
    exit 1
fi

echo "Analytics Cleanup Status"
echo "========================"
echo "Log file: $LOG_FILE"
echo "Last modified: $(stat -c %y "$LOG_FILE" 2>/dev/null || stat -f %Sm "$LOG_FILE" 2>/dev/null || echo "Unknown")"
echo

echo "Last 20 lines of cleanup log:"
echo "------------------------------"
tail -20 "$LOG_FILE"

echo
echo "Recent cleanup summary:"
echo "----------------------"
grep -E "(Starting analytics cleanup|Cleanup Summary|completed successfully|failed)" "$LOG_FILE" | tail -10
EOF

chmod +x "$MONITOR_SCRIPT"
echo -e "${GREEN}✓${NC} Created monitoring script: $MONITOR_SCRIPT"

echo
echo -e "${GREEN}Setup Complete!${NC}"
echo
echo "Next steps:"
echo "1. Monitor the first run: tail -f $CRON_LOG"
echo "2. Check status anytime: $MONITOR_SCRIPT"
echo "3. Test manual run: cd $PROJECT_DIR && node $CLEANUP_SCRIPT --dry-run"
echo
echo "The cleanup will run daily at 2:00 AM and:"
echo "- Delete analytics events older than 90 days"
echo "- Delete completed sessions older than 30 days"
echo "- Delete incomplete sessions older than 24 hours"
echo
echo "To remove the cron job: crontab -e (then delete the analytics-cleanup line)"
