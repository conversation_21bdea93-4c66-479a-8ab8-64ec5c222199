// Script to add sample analytics data for testing the dashboard
const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'tempmail_web_user',
  password: process.env.DB_PASSWORD || 'yiWTpFChtFW3neWCS',
  database: process.env.DB_NAME || 'tempmail_web',
};

// Sample page paths
const pagePaths = [
  '/',
  '/about',
  '/privacy',
  '/terms',
  '/contact',
  '/faq',
  '/features'
];

// Sample event types
const eventTypes = [
  'page_view',
  'email_generated',
  'email_viewed',
  'email_deleted',
  'email_address_copied'
];

// Sample browsers
const browsers = [
  'Chrome',
  'Firefox',
  'Safari',
  'Edge',
  'Opera'
];

// Sample device types
const deviceTypes = [
  'desktop',
  'mobile',
  'tablet'
];

// Sample countries
const countries = [
  'US',
  'UK',
  'CA',
  'DE',
  'FR',
  'JP',
  'AU'
];

// Function to generate a random date within the last 30 days
function getRandomDate() {
  const now = new Date();
  const daysAgo = Math.floor(Math.random() * 30);
  now.setDate(now.getDate() - daysAgo);
  return now;
}

// Function to get a random item from an array
function getRandomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Function to add sample analytics data
async function addSampleData() {
  console.log('Connecting to database...');
  const connection = await mysql.createConnection(dbConfig);
  
  try {
    console.log('Connected to database successfully');
    
    // Generate 100 sample events
    const sampleSize = 100;
    console.log(`Generating ${sampleSize} sample analytics events...`);
    
    for (let i = 0; i < sampleSize; i++) {
      const eventType = getRandomItem(eventTypes);
      const pagePath = getRandomItem(pagePaths);
      const browser = getRandomItem(browsers);
      const deviceType = getRandomItem(deviceTypes);
      const country = getRandomItem(countries);
      const timestamp = getRandomDate();
      
      // Additional data for specific event types
      let additionalData = null;
      if (eventType === 'email_generated') {
        additionalData = JSON.stringify({
          emailAddress: `user${Math.floor(Math.random() * 10000)}@fademail.site`
        });
      } else if (eventType === 'email_viewed' || eventType === 'email_deleted') {
        additionalData = JSON.stringify({
          emailId: Math.floor(Math.random() * 1000)
        });
      }
      
      // Insert the event into the database
      await connection.execute(
        `INSERT INTO analytics_events
         (event_type, page_path, referrer, country, browser, device_type, timestamp, additional_data)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          eventType,
          pagePath,
          'https://google.com',
          country,
          browser,
          deviceType,
          timestamp,
          additionalData
        ]
      );
      
      // Log progress
      if ((i + 1) % 20 === 0) {
        console.log(`Added ${i + 1} events...`);
      }
    }
    
    console.log('Sample data added successfully');
  } catch (error) {
    console.error('Error adding sample data:', error);
  } finally {
    await connection.end();
    console.log('Database connection closed');
  }
}

// Run the function
addSampleData().catch(console.error);
