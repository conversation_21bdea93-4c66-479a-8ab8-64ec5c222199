'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type AuthContextType = {
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Check if user is already authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check authentication status with the server using direct admin API path
        const response = await fetch('/api/admin/auth', {
          method: 'GET',
          credentials: 'include', // Include cookies
        });

        const data = await response.json();

        // Set authentication state based on server response
        setIsAuthenticated(data.authenticated === true);

        // Log the authentication state for debugging
        console.log('Auth check - Server says authenticated:', data.authenticated);
      } catch (error) {
        console.error('Error checking authentication with server:', error);
        setIsAuthenticated(false);
      } finally {
        // Finish loading after authentication check
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Login function that calls the server API
  const login = async (username: string, password: string): Promise<boolean> => {
    setIsLoading(true);

    try {
      // Call the server-side authentication API using direct admin API path
      const response = await fetch('/api/admin/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
        credentials: 'include', // Include cookies in the request
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Authentication successful
        console.log('Login successful - Server authenticated');

        // Update authentication state
        setIsAuthenticated(true);

        return true;
      } else {
        // Authentication failed
        console.warn('Login failed:', data.message);
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);

    // Get admin path from client-side environment variable
    const adminPath = process.env.NEXT_PUBLIC_ADMIN_UI_PATH || '';

    try {
      // Call logout endpoint to clear the cookie using direct admin API path
      const response = await fetch('/api/admin/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });

      if (!response.ok) {
        console.error('Logout failed with status:', response.status);
      }

      // Force a page reload to clear any client-side state
      window.location.href = `/${adminPath}/login`;
      return;
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Update state regardless of server response
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
