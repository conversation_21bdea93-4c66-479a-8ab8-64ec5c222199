import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // Create a response with explicit cookie clearing
    const response = NextResponse.json({ success: true });

    // Set an expired cookie in the response headers
    response.cookies.set({
      name: 'admin_token',
      value: '',
      expires: new Date(0),
      path: '/',
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      httpOnly: true
    });

    // We don't need to use the cookies API here since we're already setting the cookie in the response
    // The response.cookies.set with an empty value and expired date is sufficient to clear the cookie

    return response;
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { success: false, message: 'Logout failed' },
      { status: 500 }
    );
  }
}
