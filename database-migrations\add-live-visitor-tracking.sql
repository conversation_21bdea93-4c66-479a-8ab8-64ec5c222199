-- Migration: Add Live Visitor Tracking
-- This adds real-time visitor tracking capabilities to the analytics system

-- Add columns to session_analytics table for live tracking
ALTER TABLE session_analytics 
ADD COLUMN IF NOT EXISTS last_seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Create index for efficient live visitor queries
CREATE INDEX IF NOT EXISTS idx_session_analytics_last_seen 
ON session_analytics(last_seen_at) 
WHERE is_active = true;

-- Create index for active sessions
CREATE INDEX IF NOT EXISTS idx_session_analytics_active 
ON session_analytics(is_active, last_seen_at);

-- Update existing sessions to have last_seen_at set to session_start_time
UPDATE session_analytics 
SET last_seen_at = session_start_time 
WHERE last_seen_at IS NULL;

-- Create a function to automatically mark sessions as inactive after 5 minutes
CREATE OR REPLACE FUNCTION mark_inactive_sessions()
RET<PERSON><PERSON> void AS $$
BEGIN
    UPDATE session_analytics 
    SET is_active = false 
    WHERE is_active = true 
    AND last_seen_at < NOW() - INTERVAL '5 minutes';
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run the cleanup function every minute
-- Note: This requires pg_cron extension, which may not be available in all environments
-- SELECT cron.schedule('cleanup-inactive-sessions', '* * * * *', 'SELECT mark_inactive_sessions();');

COMMENT ON COLUMN session_analytics.last_seen_at IS 'Timestamp of when the user was last seen active (heartbeat)';
COMMENT ON COLUMN session_analytics.is_active IS 'Whether the session is currently active (user is online)';
COMMENT ON FUNCTION mark_inactive_sessions() IS 'Marks sessions as inactive if no heartbeat received in 5 minutes';
