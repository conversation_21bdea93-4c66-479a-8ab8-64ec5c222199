/**
 * This script tests the security of the authentication system.
 * 
 * Usage:
 * node security/auth-security.js
 */

const fetch = require('node-fetch');
const crypto = require('crypto');

// Target URL
const baseUrl = 'http://localhost:3000';

// Function to generate a random string
function generateRandomString(length) {
  return crypto.randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
}

// Function to test for brute force protection
async function testBruteForceProtection() {
  console.log('Testing brute force protection...');
  
  const username = 'test-user';
  const password = 'wrong-password';
  
  const loginUrl = `${baseUrl}/api/admin/login`;
  
  const results = {
    attempts: 0,
    blocked: false,
    blockAfterAttempts: null
  };
  
  // Try to login multiple times with wrong credentials
  for (let i = 0; i < 10; i++) {
    const response = await fetch(loginUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username,
        password
      })
    });
    
    results.attempts++;
    
    // Check if we're blocked
    if (response.status === 429) {
      results.blocked = true;
      results.blockAfterAttempts = i + 1;
      break;
    }
  }
  
  if (results.blocked) {
    console.log(`Brute force protection detected after ${results.blockAfterAttempts} attempts`);
  } else {
    console.log('No brute force protection detected after 10 attempts');
  }
  
  return results;
}

// Function to test for SQL injection in the login form
async function testSqlInjection() {
  console.log('Testing SQL injection protection...');
  
  const loginUrl = `${baseUrl}/api/admin/login`;
  
  const sqlInjectionPayloads = [
    "' OR '1'='1",
    "admin' --",
    "admin' OR '1'='1' --",
    "' OR 1=1 --",
    "' OR 'x'='x",
    "' OR 1=1 LIMIT 1; --"
  ];
  
  const results = {
    vulnerable: false,
    successfulPayload: null
  };
  
  // Try different SQL injection payloads
  for (const payload of sqlInjectionPayloads) {
    const response = await fetch(loginUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: payload,
        password: payload
      })
    });
    
    const data = await response.json();
    
    // If we get a successful login, the application might be vulnerable
    if (response.status === 200 && data.success) {
      results.vulnerable = true;
      results.successfulPayload = payload;
      break;
    }
  }
  
  if (results.vulnerable) {
    console.log(`SQL injection vulnerability detected with payload: ${results.successfulPayload}`);
  } else {
    console.log('No SQL injection vulnerability detected');
  }
  
  return results;
}

// Function to test for CSRF protection
async function testCsrfProtection() {
  console.log('Testing CSRF protection...');
  
  const loginUrl = `${baseUrl}/api/admin/login`;
  
  // First, get a valid session by logging in
  const loginResponse = await fetch(loginUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username: 'test-admin',
      password: 'test-password'
    })
  });
  
  const cookies = loginResponse.headers.get('set-cookie');
  
  // Now try to make a request without a CSRF token
  const changePasswordUrl = `${baseUrl}/api/admin/change-password`;
  const changePasswordResponse = await fetch(changePasswordUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Cookie': cookies
    },
    body: JSON.stringify({
      newPassword: 'new-password'
    })
  });
  
  const results = {
    csrfProtected: changePasswordResponse.status === 403
  };
  
  if (results.csrfProtected) {
    console.log('CSRF protection detected');
  } else {
    console.log('No CSRF protection detected');
  }
  
  return results;
}

// Function to test for secure cookies
async function testSecureCookies() {
  console.log('Testing secure cookies...');
  
  const loginUrl = `${baseUrl}/api/admin/login`;
  
  // Log in to get cookies
  const loginResponse = await fetch(loginUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username: 'test-admin',
      password: 'test-password'
    })
  });
  
  const cookies = loginResponse.headers.get('set-cookie');
  
  const results = {
    httpOnly: cookies && cookies.includes('HttpOnly'),
    secure: cookies && cookies.includes('Secure'),
    sameSite: cookies && (cookies.includes('SameSite=Strict') || cookies.includes('SameSite=Lax'))
  };
  
  console.log('Cookie security:');
  console.log(`  HttpOnly: ${results.httpOnly ? 'Yes' : 'No'}`);
  console.log(`  Secure: ${results.secure ? 'Yes' : 'No'}`);
  console.log(`  SameSite: ${results.sameSite ? 'Yes' : 'No'}`);
  
  return results;
}

// Main function to run all security tests
async function runSecurityTests() {
  try {
    console.log('Starting authentication security tests...');
    
    const bruteForceResults = await testBruteForceProtection();
    console.log('');
    
    const sqlInjectionResults = await testSqlInjection();
    console.log('');
    
    const csrfResults = await testCsrfProtection();
    console.log('');
    
    const cookieResults = await testSecureCookies();
    console.log('');
    
    // Print a summary of the results
    console.log('Authentication Security Test Summary:');
    console.log(`Brute Force Protection: ${bruteForceResults.blocked ? 'Enabled' : 'Disabled'}`);
    console.log(`SQL Injection Protection: ${!sqlInjectionResults.vulnerable ? 'Enabled' : 'Disabled'}`);
    console.log(`CSRF Protection: ${csrfResults.csrfProtected ? 'Enabled' : 'Disabled'}`);
    console.log(`Secure Cookies: ${cookieResults.httpOnly && cookieResults.secure && cookieResults.sameSite ? 'Enabled' : 'Partially Enabled'}`);
    
    // Return the results
    return {
      bruteForceProtection: bruteForceResults,
      sqlInjectionProtection: sqlInjectionResults,
      csrfProtection: csrfResults,
      secureCookies: cookieResults
    };
  } catch (error) {
    console.error('Error running authentication security tests:', error);
    throw error;
  }
}

// Run the security tests
runSecurityTests()
  .then(results => {
    console.log('Authentication security tests completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Authentication security tests failed:', error);
    process.exit(1);
  });
