-- Admin Users Table
CREATE TABLE IF NOT EXISTS admin_users (
  id SERIAL PRIMARY KEY,
  username VA<PERSON>HAR(50) NOT NULL UNIQUE,
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  full_name VA<PERSON>HA<PERSON>(100),
  role VARCHAR(20) NOT NULL DEFAULT 'editor', -- 'admin' or 'editor'
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  last_login TIMESTAMPTZ
);

-- Admin User Activity Log
CREATE TABLE IF NOT EXISTS admin_activity_log (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES admin_users(id),
  action_type VARCHAR(50) NOT NULL, -- 'login', 'logout', 'create', 'update', 'delete'
  resource_type VARCHAR(50), -- 'user', 'domain', 'ad', etc.
  resource_id VARCHAR(100), -- ID of the affected resource
  details JSONB, -- Additional details about the action
  ip_address VARCHAR(45), -- IPv4 or IPv6 address
  user_agent TEXT, -- Browser user agent
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_admin_users_role ON admin_users(role);
CREATE INDEX IF NOT EXISTS idx_admin_users_is_active ON admin_users(is_active);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_user_id ON admin_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_action_type ON admin_activity_log(action_type);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_created_at ON admin_activity_log(created_at);

-- Insert default admin user (password: admin123)
-- In production, use a proper password hashing function
INSERT INTO admin_users (username, email, password_hash, full_name, role)
VALUES ('admin', '<EMAIL>', '$2b$10$JqPJY6Gg9RRXL4QwXlJH8O4BFGMCUAYNwfGh8htlHfyxjxA8maXJy', 'System Administrator', 'admin')
ON CONFLICT (username) DO NOTHING;
