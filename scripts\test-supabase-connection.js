/**
 * <PERSON><PERSON><PERSON> to test the connection to Supabase
 */
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create a Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testSupabaseConnection() {
  console.log('Testing connection to Supabase...');

  try {
    // First, test basic connection to Supabase
    const { data: versionData, error: versionError } = await supabase
      .from('_prisma_migrations')
      .select('*')
      .limit(1);

    if (versionError && versionError.code !== '42P01') {
      // If error is not 'relation does not exist', it's a connection issue
      console.error('❌ Error connecting to Supa<PERSON>:', versionError.message);
      return false;
    } else {
      console.log('✅ Successfully connected to Supabase');
    }

    // Check if the temp_emails table exists
    console.log('\nChecking if temp_emails table exists...');
    const { data: tempEmailsData, error: tempEmailsError } = await supabase
      .from('temp_emails')
      .select('id')
      .limit(1);

    if (tempEmailsError) {
      console.error('❌ temp_emails table error:', tempEmailsError.message);
      console.log('\nPlease create the temp_emails table using the SQL in the setup script or SUPABASE_MIGRATION.md');
      console.log('\nSQL to create temp_emails table:');
      console.log(`
        CREATE TABLE temp_emails (
          id SERIAL PRIMARY KEY,
          email_address VARCHAR(255) NOT NULL UNIQUE,
          creation_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          expiration_date TIMESTAMP WITH TIME ZONE NOT NULL
        );

        -- Create index on expiration_date for cleanup queries
        CREATE INDEX idx_expiration_date ON temp_emails(expiration_date);
      `);
    } else {
      console.log('✅ temp_emails table exists');

      // Get email count
      const { data: countData, error: countError } = await supabase
        .from('temp_emails')
        .select('id', { count: 'exact' });

      if (countError) {
        console.error('❌ Error getting email count:', countError.message);
      } else {
        console.log(`Total emails in temp_emails: ${countData.length}`);
      }

      // Test inserting a temporary email
      const testEmail = `test-${Date.now()}@fademail.site`;
      const expirationDate = new Date();
      expirationDate.setHours(expirationDate.getHours() + 1); // Expire in 1 hour

      const { data: insertData, error: insertError } = await supabase
        .from('temp_emails')
        .insert([
          {
            email_address: testEmail,
            expiration_date: expirationDate.toISOString()
          }
        ])
        .select();

      if (insertError) {
        console.error('❌ Error inserting test email:', insertError.message);
      } else {
        console.log('✅ Successfully inserted test email');
        console.log('Test email details:', insertData[0]);

        // Clean up test data
        const { error: deleteError } = await supabase
          .from('temp_emails')
          .delete()
          .eq('email_address', testEmail);

        if (deleteError) {
          console.error('❌ Error cleaning up test data:', deleteError.message);
        } else {
          console.log('✅ Successfully cleaned up test data');
        }
      }
    }

    // Check if the analytics_events table exists
    console.log('\nChecking if analytics_events table exists...');
    const { data: analyticsData, error: analyticsError } = await supabase
      .from('analytics_events')
      .select('id')
      .limit(1);

    if (analyticsError) {
      console.error('❌ analytics_events table error:', analyticsError.message);
      console.log('\nPlease create the analytics_events table using the SQL in the setup script or SUPABASE_MIGRATION.md');
      console.log('\nSQL to create analytics_events table:');
      console.log(`
        CREATE TABLE analytics_events (
          id SERIAL PRIMARY KEY,
          event_type VARCHAR(50) NOT NULL,
          page_path VARCHAR(255),
          referrer VARCHAR(255),
          country VARCHAR(50),
          browser VARCHAR(50),
          device_type VARCHAR(20),
          timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          additional_data JSONB
        );

        -- Create indexes for analytics queries
        CREATE INDEX idx_event_type ON analytics_events(event_type);
        CREATE INDEX idx_timestamp ON analytics_events(timestamp);
        CREATE INDEX idx_page_path ON analytics_events(page_path);
      `);
    } else {
      console.log('✅ analytics_events table exists');

      // Get analytics count
      const { data: countData, error: countError } = await supabase
        .from('analytics_events')
        .select('id', { count: 'exact' });

      if (countError) {
        console.error('❌ Error getting analytics count:', countError.message);
      } else {
        console.log(`Total events in analytics_events: ${countData.length}`);
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Error testing Supabase connection:', error.message);
    return false;
  }
}

async function testGuerrillaDatabase() {
  // This function remains unchanged as we're keeping the Guerrilla database as is
  console.log('\nNote: The Guerrilla database connection remains unchanged.');
  console.log('You can use the existing test-database-connections.js script to test the Guerrilla database connection.');
}

async function runTests() {
  console.log('=== Supabase Connection Test ===\n');

  const supabaseSuccess = await testSupabaseConnection();

  console.log('\n=== Test Summary ===');
  console.log(`Supabase Connection: ${supabaseSuccess ? '✅ Connected' : '❌ Failed'}`);

  if (!supabaseSuccess) {
    process.exit(1);
  }
}

// Run the tests
runTests().catch(console.error);
