#!/bin/bash

# <PERSON><PERSON><PERSON> to manually sync the development repository with the production repository

# Clone the production repository
git clone https://github.com/johnshawa1/js_production.git temp_production

# Copy all files from the development repository to the production repository
cp -r ./* temp_production/

# Remove the .git directory from the production repository
rm -rf temp_production/.git

# Navigate to the production repository
cd temp_production

# Initialize a new git repository
git init

# Add all files
git add .

# Commit the changes
git commit -m "Sync with development repository"

# Add the remote
git remote add origin https://github.com/johnshawa1/js_production.git

# Push to the production repository
git push -f origin main

# Navigate back to the development repository
cd ..

# Remove the temporary production repository
rm -rf temp_production

echo "Sync completed successfully!"
