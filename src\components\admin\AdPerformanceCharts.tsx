'use client';

import { useState, useEffect } from 'react';
import { format, parseISO, subDays } from 'date-fns';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import { AdConfig } from '@/lib/config/types';

interface AdPerformanceChartsProps {
  adPlacement?: AdConfig;
  dateRange: '7d' | '30d' | '90d';
  metrics: any[];
}

export default function AdPerformanceCharts({ adPlacement, dateRange, metrics }: AdPerformanceChartsProps) {
  const [chartData, setChartData] = useState<any[]>([]);
  const [deviceData, setDeviceData] = useState<any[]>([]);
  const [hourlyData, setHourlyData] = useState<any[]>([]);
  const [comparisonData, setComparisonData] = useState<any[]>([]);

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];
  
  // Process metrics data for charts
  useEffect(() => {
    if (!metrics || metrics.length === 0) {
      setChartData([]);
      setDeviceData([]);
      setHourlyData([]);
      setComparisonData([]);
      return;
    }

    // Process daily metrics for line chart
    const processedData = metrics.map(metric => ({
      date: format(new Date(metric.date), 'MMM d'),
      impressions: metric.impressions,
      clicks: metric.clicks,
      ctr: parseFloat(metric.ctr.toFixed(2))
    }));
    setChartData(processedData);

    // Generate mock device distribution data
    // In a real implementation, this would come from the backend
    const mockDeviceData = [
      { name: 'Desktop', value: Math.floor(Math.random() * 60) + 30 },
      { name: 'Mobile', value: Math.floor(Math.random() * 40) + 20 },
      { name: 'Tablet', value: Math.floor(Math.random() * 20) + 5 }
    ];
    setDeviceData(mockDeviceData);

    // Generate mock hourly distribution data
    // In a real implementation, this would come from the backend
    const mockHourlyData = Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      impressions: Math.floor(Math.random() * 100) + 10,
      clicks: Math.floor(Math.random() * 10) + 1
    }));
    setHourlyData(mockHourlyData);

    // Generate mock comparison data (this week vs last week)
    // In a real implementation, this would come from the backend
    const mockComparisonData = Array.from({ length: 7 }, (_, i) => {
      const day = format(subDays(new Date(), i), 'EEE');
      return {
        day,
        current: Math.floor(Math.random() * 100) + 50,
        previous: Math.floor(Math.random() * 80) + 30
      };
    }).reverse();
    setComparisonData(mockComparisonData);
  }, [metrics]);

  if (!adPlacement || metrics.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No performance data available for the selected date range.</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Impressions and Clicks Line Chart */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Impressions & Clicks Over Time</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Legend />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="impressions"
                stroke="#8884d8"
                activeDot={{ r: 8 }}
                name="Impressions"
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="clicks"
                stroke="#82ca9d"
                name="Clicks"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* CTR Area Chart */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Click-Through Rate (CTR) Trend</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={chartData}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis tickFormatter={(value) => `${value}%`} />
              <Tooltip formatter={(value) => [`${value}%`, 'CTR']} />
              <Area type="monotone" dataKey="ctr" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Device Distribution Pie Chart */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Device Distribution</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={deviceData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {deviceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value}%`, 'Percentage']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Hourly Distribution Bar Chart */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Hourly Performance</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={hourlyData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" tickFormatter={(hour) => `${hour}:00`} />
                <YAxis />
                <Tooltip labelFormatter={(hour) => `Hour: ${hour}:00`} />
                <Legend />
                <Bar dataKey="impressions" fill="#8884d8" name="Impressions" />
                <Bar dataKey="clicks" fill="#82ca9d" name="Clicks" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Week Comparison Chart */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Current vs Previous Period</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={comparisonData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="current" fill="#8884d8" name="Current Period" />
              <Bar dataKey="previous" fill="#82ca9d" name="Previous Period" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}
