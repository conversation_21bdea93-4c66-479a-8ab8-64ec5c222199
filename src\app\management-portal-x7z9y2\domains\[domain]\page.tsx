'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { use } from 'react';
import { DomainConfig, DomainSettings } from '@/lib/config/types';

interface DomainEditPageProps {
  params: Promise<{
    domain: string;
  }>;
}

/**
 * Domain Edit Page
 */
export default function DomainEditPage({ params }: DomainEditPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const domain = resolvedParams.domain;
  const decodedDomain = decodeURIComponent(domain);

  const [domainConfig, setDomainConfig] = useState<DomainConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch domain on component mount
  useEffect(() => {
    fetchDomain();
  }, [decodedDomain]);

  // Fetch domain from the API
  const fetchDomain = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/management-portal-x7z9y2/domains/${decodedDomain}`);
      const data = await response.json();

      if (data.success) {
        setDomainConfig(data.data);
      } else {
        setError(data.error || 'Failed to fetch domain');
      }
    } catch (err) {
      setError('An error occurred while fetching domain');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!domainConfig) return;

    try {
      setSaving(true);
      const response = await fetch(`/api/management-portal-x7z9y2/domains/${decodedDomain}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: domainConfig.isActive,
          settings: domainConfig.settings,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Domain updated successfully');
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(data.error || 'Failed to update domain');
      }
    } catch (err) {
      setError('An error occurred while updating the domain');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  // Handle setting domain as default
  const handleSetDefault = async () => {
    try {
      setSaving(true);
      const response = await fetch(`/api/management-portal-x7z9y2/domains/${decodedDomain}/default`, {
        method: 'POST',
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Domain set as default successfully');
        // Refresh domain data
        fetchDomain();
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(data.error || 'Failed to set domain as default');
      }
    } catch (err) {
      setError('An error occurred while setting the domain as default');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    if (!domainConfig) return;

    if (field === 'isActive') {
      setDomainConfig({
        ...domainConfig,
        isActive: value,
      });
    } else if (field === 'weight') {
      setDomainConfig({
        ...domainConfig,
        settings: {
          ...domainConfig.settings,
          weight: value,
        },
      });
    } else if (field.startsWith('features.')) {
      const featureKey = field.split('.')[1] as keyof typeof domainConfig.settings.features;
      setDomainConfig({
        ...domainConfig,
        settings: {
          ...domainConfig.settings,
          features: {
            ...domainConfig.settings.features,
            [featureKey]: value,
          },
        },
      });
    }
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      </div>
    );
  }

  if (!domainConfig) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow overflow-hidden rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Domain Not Found</h1>
          <p className="text-gray-600 mb-4">
            The domain you are looking for does not exist or could not be loaded.
          </p>
          <Link
            href="/management-portal-x7z9y2/domains"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Domains
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Domain: {decodedDomain}</h1>
          <p className="mt-2 text-sm text-gray-600">
            Configure settings for this domain.
          </p>
        </div>
        <Link
          href="/management-portal-x7z9y2/domains"
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Domains
        </Link>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600">{error}</p>
          <button
            className="mt-2 text-sm text-red-600 underline"
            onClick={() => setError(null)}
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Success message */}
      {success && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
          <p className="text-green-600">{success}</p>
        </div>
      )}

      <div className="bg-white shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Domain Settings</h2>
            <p className="mt-1 text-sm text-gray-500">
              Configure how this domain behaves in the system.
            </p>
          </div>
          {domainConfig.settings.isDefault ? (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
              Default Domain
            </span>
          ) : (
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              onClick={handleSetDefault}
              disabled={saving}
            >
              Set as Default
            </button>
          )}
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    checked={domainConfig.isActive}
                    onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-700">Active</span>
                </label>
                <p className="mt-1 text-xs text-gray-500">
                  When inactive, this domain will not be used for email generation.
                </p>
              </div>

              <div>
                <label htmlFor="weight" className="block text-sm font-medium text-gray-700 mb-1">
                  Weight (for domain rotation)
                </label>
                <input
                  type="number"
                  id="weight"
                  className="w-full sm:w-1/3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  min="1"
                  max="1000"
                  value={domainConfig.settings.weight}
                  onChange={(e) => handleInputChange('weight', parseInt(e.target.value))}
                  required
                />
                <p className="mt-1 text-xs text-gray-500">
                  Higher weight means the domain will be used more frequently when generating email addresses.
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Features</h3>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      checked={domainConfig.settings.features.adsEnabled}
                      onChange={(e) => handleInputChange('features.adsEnabled', e.target.checked)}
                    />
                    <span className="ml-2 text-sm text-gray-700">Enable Ads</span>
                  </label>
                  <p className="ml-6 text-xs text-gray-500">
                    When enabled, ads will be displayed for users on this domain.
                  </p>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      checked={domainConfig.settings.features.analyticsEnabled}
                      onChange={(e) => handleInputChange('features.analyticsEnabled', e.target.checked)}
                    />
                    <span className="ml-2 text-sm text-gray-700">Enable Analytics</span>
                  </label>
                  <p className="ml-6 text-xs text-gray-500">
                    When enabled, usage analytics will be collected for this domain.
                  </p>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      checked={domainConfig.settings.features.autoRefreshEnabled}
                      onChange={(e) => handleInputChange('features.autoRefreshEnabled', e.target.checked)}
                    />
                    <span className="ml-2 text-sm text-gray-700">Enable Auto-Refresh</span>
                  </label>
                  <p className="ml-6 text-xs text-gray-500">
                    When enabled, the inbox will automatically refresh for users on this domain.
                  </p>
                </div>
              </div>

              <div className="pt-5 border-t border-gray-200">
                <div className="flex justify-end">
                  <Link
                    href="/management-portal-x7z9y2/domains"
                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Cancel
                  </Link>
                  <button
                    type="submit"
                    className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    disabled={saving}
                  >
                    {saving ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Domain Statistics */}
      <div className="mt-8 bg-white shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h2 className="text-lg font-medium text-gray-900">Domain Statistics</h2>
          <p className="mt-1 text-sm text-gray-500">
            Usage statistics for this domain.
          </p>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500 uppercase">Active Email Addresses</h3>
              <p className="mt-2 text-3xl font-bold text-gray-900">--</p>
              <p className="mt-1 text-sm text-gray-500">Coming soon</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500 uppercase">Emails Received</h3>
              <p className="mt-2 text-3xl font-bold text-gray-900">--</p>
              <p className="mt-1 text-sm text-gray-500">Coming soon</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-500 uppercase">Usage Share</h3>
              <p className="mt-2 text-3xl font-bold text-gray-900">--</p>
              <p className="mt-1 text-sm text-gray-500">Coming soon</p>
            </div>
          </div>
        </div>
      </div>

      {/* Ad Placements */}
      <div className="mt-8 bg-white shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Ad Placements</h2>
            <p className="mt-1 text-sm text-gray-500">
              Configure ad placements for this domain.
            </p>
          </div>
          <Link
            href={`/management-portal-x7z9y2/ads?domain=${encodeURIComponent(decodedDomain)}`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Manage Ad Placements
          </Link>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
          <p className="text-sm text-gray-500">
            Configure ad placements specific to this domain in the ad management section.
          </p>
        </div>
      </div>
    </div>
  );
}
