/**
 * API route for flushing ad caches
 */
import { NextRequest, NextResponse } from 'next/server';
import { flushAllCaches } from '@/lib/config/adCacheService';
import { logInfo, logError } from '@/lib/logging';

/**
 * POST /api/admin/ad-performance/flush-cache
 * 
 * Flush all ad caches
 */
export async function POST(request: NextRequest) {
  try {
    // Flush all caches
    flushAllCaches();
    
    logInfo('api', 'Flushed all ad caches');
    
    return NextResponse.json({
      success: true,
      message: 'All ad caches flushed successfully'
    });
  } catch (error) {
    logError('api', 'Error flushing ad caches', { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to flush ad caches' },
      { status: 500 }
    );
  }
}
