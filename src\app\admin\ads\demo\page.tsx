'use client';

import { useState } from 'react';
import CollapsibleAdContainer from '@/components/ads/CollapsibleAdContainer';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/Select';
import { Checkbox } from '@/components/ui/Checkbox';

/**
 * Ad Demo Page
 *
 * This page demonstrates the collapsible ad functionality
 */
export default function AdDemoPage() {
  const [position, setPosition] = useState<'top' | 'bottom' | 'left' | 'right'>('bottom');
  const [initiallyExpanded, setInitiallyExpanded] = useState(true);
  const [rememberState, setRememberState] = useState(true);
  const [expandAfterMinutes, setExpandAfterMinutes] = useState(1);
  const [dismissible, setDismissible] = useState(true);
  const [showAd, setShowAd] = useState(false);

  const handleShowAd = () => {
    setShowAd(true);
  };

  const handleHideAd = () => {
    setShowAd(false);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex items-center mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
        </svg>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Ad Preview</h1>
          <p className="mt-1 text-sm text-gray-600">
            This page demonstrates the collapsible ad functionality. Configure the options below and click "Show Ad" to see the ad in action.
          </p>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden rounded-lg mb-8">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
            </svg>
            Ad Configuration
          </h3>
        </div>
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label htmlFor="position" className="block text-sm font-medium text-gray-700">
                Position
              </label>
              <Select value={position} onValueChange={(value) => setPosition(value as "top" | "right" | "left" | "bottom")}>
                <SelectTrigger>
                  <SelectValue placeholder="Select position" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="top">Top</SelectItem>
                  <SelectItem value="bottom">Bottom</SelectItem>
                  <SelectItem value="left">Left</SelectItem>
                  <SelectItem value="right">Right</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label htmlFor="expand-after" className="block text-sm font-medium text-gray-700">
                Auto-Expand After (minutes)
              </label>
              <Input
                type="number"
                id="expand-after"
                value={expandAfterMinutes}
                onChange={(e) => setExpandAfterMinutes(parseInt(e.target.value))}
                min="1"
                max="60"
                leftIcon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                }
              />
            </div>

            <div className="flex items-center">
              <Checkbox
                id="initially-expanded"
                checked={initiallyExpanded}
                onCheckedChange={(checked) => setInitiallyExpanded(checked)}
                label="Initially Expanded"
              />
            </div>

            <div className="flex items-center">
              <Checkbox
                id="remember-state"
                checked={rememberState}
                onCheckedChange={(checked) => setRememberState(checked)}
                label="Remember State"
              />
            </div>

            <div className="flex items-center">
              <Checkbox
                id="dismissible"
                checked={dismissible}
                onCheckedChange={(checked) => setDismissible(checked)}
                label="Dismissible"
              />
            </div>
          </div>

          <div className="mt-6 flex space-x-3">
            <Button
              onClick={handleShowAd}
              leftIcon={
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                </svg>
              }
            >
              Show Ad
            </Button>
            <Button
              onClick={handleHideAd}
              variant="secondary"
              leftIcon={
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                  <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                </svg>
              }
            >
              Hide Ad
            </Button>
          </div>
        </div>
      </div>

      <div className="bg-white shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
            Demo Content
          </h3>
        </div>
        <div className="px-4 py-5 sm:p-6">
          <div className="prose max-w-none">
            <h2>Fademail - Temporary Email Service</h2>
            <p>
              Fademail provides temporary email addresses that automatically expire after 30 minutes.
              Use these disposable email addresses to protect your privacy when signing up for services
              or testing applications.
            </p>
            <h3>Key Features</h3>
            <ul>
              <li>Temporary email addresses that expire after 30 minutes</li>
              <li>No registration required</li>
              <li>Automatic cleanup of expired emails</li>
              <li>Simple and intuitive user interface</li>
              <li>Protection against spam and unwanted marketing emails</li>
            </ul>
            <h3>How It Works</h3>
            <p>
              When you visit Fademail, you'll be provided with a randomly generated email address.
              You can use this address to receive emails for the next 30 minutes. After that, the
              address and all associated emails will be automatically deleted from our system.
            </p>
            <p>
              This service is perfect for situations where you need to provide an email address
              but don't want to use your personal email. For example, when signing up for a free
              trial, downloading a whitepaper, or testing a new service.
            </p>
            <h3>Privacy and Security</h3>
            <p>
              At Fademail, we take your privacy seriously. We don't store any personal information
              about our users. All email addresses and messages are automatically deleted after
              30 minutes, leaving no trace of your activity.
            </p>
            <p>
              Our service is designed to be simple and secure, providing you with a temporary
              email solution that protects your privacy without any hassle.
            </p>
          </div>
        </div>
      </div>

      {showAd && (
        <CollapsibleAdContainer
          adId="demo-ad"
          adUnitId="custom-demo-ad"
          position={position}
          displayOptions={{
            initiallyExpanded,
            rememberState,
            expandAfterMinutes,
            dismissible
          }}
        />
      )}
    </div>
  );
}
