'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { DomainConfig } from '@/lib/config/types';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Checkbox } from '@/components/ui/Checkbox';
import { Badge } from '@/components/ui/Badge';
import { Spinner } from '@/components/ui/Spinner';
import { AdminSwitch, SwitchWithLabel } from '@/components/ui/Switch';

/**
 * Domain Management Page
 */
export default function DomainManagementPage() {
  const router = useRouter();
  const [domains, setDomains] = useState<DomainConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddDomain, setShowAddDomain] = useState(false);
  const [newDomain, setNewDomain] = useState({
    domain: '',
    isActive: true,
    settings: {
      weight: 100,
      features: {
        adsEnabled: true,
        analyticsEnabled: true,
        autoRefreshEnabled: true
      }
    }
  });

  // Fetch domains on component mount
  useEffect(() => {
    fetchDomains();
  }, []);

  // Fetch domains from the API
  const fetchDomains = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/management-portal-x7z9y2/domains');
      const data = await response.json();

      if (data.success) {
        setDomains(data.data);
      } else {
        setError(data.error || 'Failed to fetch domains');
      }
    } catch (err) {
      setError('An error occurred while fetching domains');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Handle adding a new domain
  const handleAddDomain = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      const response = await fetch('/api/management-portal-x7z9y2/domains', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: newDomain.domain,
          isActive: newDomain.isActive,
          settings: {
            weight: newDomain.settings.weight,
            features: newDomain.settings.features
          }
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Reset form and hide it
        setNewDomain({
          domain: '',
          isActive: true,
          settings: {
            weight: 100,
            features: {
              adsEnabled: true,
              analyticsEnabled: true,
              autoRefreshEnabled: true
            }
          }
        });
        setShowAddDomain(false);

        // Refresh domains list
        fetchDomains();
      } else {
        setError(data.error || 'Failed to add domain');
      }
    } catch (err) {
      setError('An error occurred while adding the domain');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Handle setting a domain as default
  const handleSetDefault = async (domain: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/management-portal-x7z9y2/domains/${domain}/default`, {
        method: 'POST',
      });

      const data = await response.json();

      if (data.success) {
        // Refresh domains list
        fetchDomains();
      } else {
        setError(data.error || 'Failed to set domain as default');
      }
    } catch (err) {
      setError('An error occurred while setting the domain as default');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Handle toggling domain active status
  const handleToggleActive = async (domain: string, isActive: boolean) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/management-portal-x7z9y2/domains/${domain}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !isActive,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Refresh domains list
        fetchDomains();
      } else {
        setError(data.error || 'Failed to update domain');
      }
    } catch (err) {
      setError('An error occurred while updating the domain');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Handle deleting a domain
  const handleDeleteDomain = async (domain: string) => {
    if (!window.confirm(`Are you sure you want to delete the domain ${domain}?`)) {
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`/api/management-portal-x7z9y2/domains/${domain}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        // Refresh domains list
        fetchDomains();
      } else {
        setError(data.error || 'Failed to delete domain');
      }
    } catch (err) {
      setError('An error occurred while deleting the domain');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
          </svg>
          <h1 className="text-2xl font-bold text-gray-900">Domain Management</h1>
        </div>
        <p className="mt-2 text-sm text-gray-600 ml-11">
          Manage the domains used for email generation and configure domain-specific settings.
        </p>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md animate-slideIn flex justify-between items-start">
          <div>
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-red-600 font-medium">Error</p>
            </div>
            <p className="mt-1 text-red-600 ml-7">{error}</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setError(null)}
          >
            Dismiss
          </Button>
        </div>
      )}

      {/* Add domain button */}
      <div className="mb-6">
        <Button
          variant={showAddDomain ? "secondary" : "primary"}
          onClick={() => setShowAddDomain(!showAddDomain)}
          leftIcon={
            showAddDomain ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
            )
          }
        >
          {showAddDomain ? 'Cancel' : 'Add Domain'}
        </Button>
      </div>

      {/* Add domain form */}
      {showAddDomain && (
        <Card className="mb-8 animate-slideIn" variant="elevated">
          <CardHeader>
            <CardTitle>Add New Domain</CardTitle>
            <CardDescription>
              Configure a new domain for email generation with custom settings.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAddDomain} className="space-y-6">
              <div className="space-y-4">
                <div>
                  <label htmlFor="domain" className="block text-sm font-medium text-gray-700 mb-1">
                    Domain Name
                  </label>
                  <Input
                    type="text"
                    id="domain"
                    placeholder="example.com"
                    value={newDomain.domain}
                    onChange={(e) => setNewDomain({ ...newDomain, domain: e.target.value })}
                    required
                    leftIcon={
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
                      </svg>
                    }
                  />
                </div>

                <div>
                  <AdminSwitch
                    label="Domain Active"
                    description="When active, this domain will be used for email generation."
                    checked={newDomain.isActive}
                    status={newDomain.isActive ? 'enabled' : 'disabled'}
                    onCheckedChange={(checked) => setNewDomain({ ...newDomain, isActive: checked })}
                  />
                </div>

                <div>
                  <label htmlFor="weight" className="block text-sm font-medium text-gray-700 mb-1">
                    Weight (for domain rotation)
                  </label>
                  <Input
                    type="number"
                    id="weight"
                    min="1"
                    max="1000"
                    value={newDomain.settings.weight.toString()}
                    onChange={(e) => setNewDomain({
                      ...newDomain,
                      settings: {
                        ...newDomain.settings,
                        weight: parseInt(e.target.value)
                      }
                    })}
                    required
                    leftIcon={
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1.323l3.954 1.582 1.599-.8a1 1 0 01.894 1.79l-1.233.616 1.738 5.42a1 1 0 01-.285 1.05A3.989 3.989 0 0115 15a3.989 3.989 0 01-2.667-1.019 1 1 0 01-.285-1.05l1.715-5.349L11 6.477V16h2a1 1 0 110 2H7a1 1 0 110-2h2V6.477L6.237 7.582l1.715 5.349a1 1 0 01-.285 1.05A3.989 3.989 0 015 15a3.989 3.989 0 01-2.667-1.019 1 1 0 01-.285-1.05l1.738-5.42-1.233-.617a1 1 0 01.894-1.788l1.599.799L9 4.323V3a1 1 0 011-1zm-5 8.274l-.818 2.552c.25.112.526.174.818.174.292 0 .569-.062.818-.174L5 10.274zm10 0l-.818 2.552c.25.112.526.174.818.174.292 0 .569-.062.818-.174L15 10.274z" clipRule="evenodd" />
                      </svg>
                    }
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Higher weight means the domain will be used more frequently when generating email addresses.
                  </p>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                    </svg>
                    Features
                  </h3>
                  <div className="space-y-3 ml-6">
                    <AdminSwitch
                      label="Enable Ads"
                      description="Show advertisements on pages using this domain."
                      checked={newDomain.settings.features.adsEnabled}
                      status={newDomain.settings.features.adsEnabled ? 'enabled' : 'disabled'}
                      onCheckedChange={(checked) => setNewDomain({
                        ...newDomain,
                        settings: {
                          ...newDomain.settings,
                          features: {
                            ...newDomain.settings.features,
                            adsEnabled: checked
                          }
                        }
                      })}
                    />

                    <AdminSwitch
                      label="Enable Analytics"
                      description="Collect anonymous usage data for this domain."
                      checked={newDomain.settings.features.analyticsEnabled}
                      status={newDomain.settings.features.analyticsEnabled ? 'enabled' : 'disabled'}
                      onCheckedChange={(checked) => setNewDomain({
                        ...newDomain,
                        settings: {
                          ...newDomain.settings,
                          features: {
                            ...newDomain.settings.features,
                            analyticsEnabled: checked
                          }
                        }
                      })}
                    />

                    <AdminSwitch
                      label="Enable Auto-Refresh"
                      description="Automatically refresh inbox to check for new emails."
                      checked={newDomain.settings.features.autoRefreshEnabled}
                      status={newDomain.settings.features.autoRefreshEnabled ? 'enabled' : 'disabled'}
                      onCheckedChange={(checked) => setNewDomain({
                        ...newDomain,
                        settings: {
                          ...newDomain.settings,
                          features: {
                            ...newDomain.settings.features,
                            autoRefreshEnabled: checked
                          }
                        }
                      })}
                    />
                  </div>
                </div>
              </div>

              <CardFooter className="px-0 pt-4 pb-0 flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => setShowAddDomain(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={loading}
                  disabled={loading}
                >
                  Add Domain
                </Button>
              </CardFooter>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Domains list */}
      <Card variant="elevated" hover={false}>
        <CardHeader>
          <CardTitle>Domains</CardTitle>
          <CardDescription>
            List of all configured domains for email generation.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          {loading && domains.length === 0 ? (
            <div className="p-6 text-center">
              <Spinner variant="primary" size="lg" className="mx-auto mb-4" />
              <p className="text-gray-600">Loading domains...</p>
            </div>
          ) : domains.length === 0 ? (
            <div className="p-6 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
              </svg>
              <p className="text-gray-600">No domains configured yet.</p>
              <p className="text-sm text-gray-500 mt-1">Click the "Add Domain" button to get started.</p>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
                      </svg>
                      Domain
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Status
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1.323l3.954 1.582 1.599-.8a1 1 0 01.894 1.79l-1.233.616 1.738 5.42a1 1 0 01-.285 1.05A3.989 3.989 0 0115 15a3.989 3.989 0 01-2.667-1.019 1 1 0 01-.285-1.05l1.715-5.349L11 6.477V16h2a1 1 0 110 2H7a1 1 0 110-2h2V6.477L6.237 7.582l1.715 5.349a1 1 0 01-.285 1.05A3.989 3.989 0 015 15a3.989 3.989 0 01-2.667-1.019 1 1 0 01-.285-1.05l1.738-5.42-1.233-.617a1 1 0 01.894-1.788l1.599.799L9 4.323V3a1 1 0 011-1z" clipRule="evenodd" />
                      </svg>
                      Weight
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                      </svg>
                      Features
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center justify-end">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
                      </svg>
                      Actions
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {domains.map((domain) => (
                  <tr key={domain.domain}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900">
                          {domain.domain}
                          {domain.settings.isDefault && (
                            <Badge variant="success" className="ml-2 transition-all duration-200">
                              Default
                            </Badge>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge
                        variant={domain.isActive ? 'success' : 'destructive'}
                        className="transition-all duration-200"
                      >
                        {domain.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {domain.settings.weight}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex space-x-2">
                        <Badge
                          variant={domain.settings.features.adsEnabled ? 'info' : 'secondary'}
                          outline={!domain.settings.features.adsEnabled}
                          className="transition-all duration-200"
                        >
                          Ads: {domain.settings.features.adsEnabled ? 'On' : 'Off'}
                        </Badge>
                        <Badge
                          variant={domain.settings.features.analyticsEnabled ? 'info' : 'secondary'}
                          outline={!domain.settings.features.analyticsEnabled}
                          className="transition-all duration-200"
                        >
                          Analytics: {domain.settings.features.analyticsEnabled ? 'On' : 'Off'}
                        </Badge>
                        <Badge
                          variant={domain.settings.features.autoRefreshEnabled ? 'info' : 'secondary'}
                          outline={!domain.settings.features.autoRefreshEnabled}
                          className="transition-all duration-200"
                        >
                          Auto-Refresh: {domain.settings.features.autoRefreshEnabled ? 'On' : 'Off'}
                        </Badge>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Button
                          as={Link}
                          href={`/management-portal-x7z9y2/domains/${domain.domain}`}
                          variant="ghost"
                          size="sm"
                          className="text-indigo-600 hover:text-indigo-900"
                          leftIcon={
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                            </svg>
                          }
                        >
                          Edit
                        </Button>
                        {!domain.settings.isDefault && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-green-600 hover:text-green-900"
                            onClick={() => handleSetDefault(domain.domain)}
                            disabled={loading}
                            leftIcon={
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                            }
                          >
                            Default
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          className={domain.isActive ? "text-yellow-600 hover:text-yellow-900" : "text-green-600 hover:text-green-900"}
                          onClick={() => handleToggleActive(domain.domain, domain.isActive)}
                          disabled={loading}
                          leftIcon={
                            domain.isActive ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clipRule="evenodd" />
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                              </svg>
                            )
                          }
                        >
                          {domain.isActive ? 'Disable' : 'Enable'}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-900"
                          onClick={() => handleDeleteDomain(domain.domain)}
                          disabled={loading || domain.settings.isDefault}
                          leftIcon={
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                          }
                        >
                          Delete
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
