/**
 * Custom hook for managing auto-refresh functionality
 *
 * This hook uses the AutoRefreshManager singleton to ensure only one
 * auto-refresh interval is running at a time across the entire application.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { TIMEOUTS } from '@/lib/constants';
import { logError } from '@/lib/errorHandling';
import AutoRefreshManager from '@/lib/AutoRefreshManager';

interface AutoRefreshOptions {
  /** Email address to refresh */
  emailAddress: string | null;

  /** Whether auto-refresh is disabled */
  disableAutoRefresh: boolean;

  /** Whether guide emails are being shown */
  showingGuideEmails: boolean;

  /** Whether a new address is being generated */
  isGeneratingAddress: boolean;

  /** Whether a new address was just generated */
  justGeneratedAddress: boolean;

  /** Function to call when refreshing */
  onRefresh: () => Promise<void>;

  /** Initial delay before starting auto-refresh (ms) */
  initialDelayMs?: number;

  /** Interval between auto-refreshes (ms) */
  refreshIntervalMs?: number;
}

interface AutoRefreshResult {
  /** Whether a refresh is currently in progress */
  isRefreshing: boolean;

  /** Function to start auto-refresh */
  startAutoRefresh: () => void;

  /** Function to stop auto-refresh */
  stopAutoRefresh: () => void;

  /** Function to perform a manual refresh */
  performRefresh: () => Promise<void>;
}

/**
 * Custom hook for managing auto-refresh functionality
 */
export const useAutoRefresh = ({
  emailAddress,
  disableAutoRefresh,
  showingGuideEmails,
  isGeneratingAddress,
  justGeneratedAddress,
  onRefresh,
  initialDelayMs,
  refreshIntervalMs
}: AutoRefreshOptions): AutoRefreshResult => {
  // State for tracking whether a refresh is in progress
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Get the singleton instance of AutoRefreshManager
  const autoRefreshManager = AutoRefreshManager.getInstance();

  // Ref for tracking auto-refresh state
  const autoRefreshRef = useRef<{
    active: boolean;
    intervalId: NodeJS.Timeout | null;
    initialTimeoutId: NodeJS.Timeout | null;
    lastRefreshTime: number;
    isSettingUp: boolean; // Track if we're in the process of setting up auto-refresh
    currentEmailAddress: string | null; // Track the current email address
  }>({
    active: false,
    intervalId: null,
    initialTimeoutId: null,
    lastRefreshTime: 0,
    isSettingUp: false,
    currentEmailAddress: null
  });

  // Function to perform a refresh
  const performRefresh = useCallback(async () => {
    // Store the current email address to ensure we're refreshing the correct inbox
    const currentAddress = emailAddress;

    // Skip if refreshing is already in progress
    if (isRefreshing) {
      console.log(`[${new Date().toISOString()}] Auto-refresh skipped: already refreshing`);
      return;
    }

    // Skip if auto-refresh is disabled or if we're generating an address
    if (disableAutoRefresh || isGeneratingAddress) {
      console.log(`[${new Date().toISOString()}] Auto-refresh skipped:`, {
        disableAutoRefresh,
        isGeneratingAddress
      });
      return;
    }

    // Log if we're refreshing with justGeneratedAddress flag
    if (justGeneratedAddress) {
      console.log(`[${new Date().toISOString()}] Auto-refresh continuing with justGeneratedAddress flag:`, {
        justGeneratedAddress
      });
    }

    // Skip if no email address
    if (!currentAddress) {
      console.log(`[${new Date().toISOString()}] Auto-refresh skipped: no email address`);
      return;
    }

    // Skip if auto-refresh is not active for this address
    if (!autoRefreshManager.isAutoRefreshActive() ||
        !autoRefreshRef.current.active ||
        autoRefreshRef.current.currentEmailAddress !== currentAddress) {
      console.log(`[${new Date().toISOString()}] Auto-refresh skipped: not active for this address`);
      return;
    }

    // If showing guide emails, we'll still auto-refresh but won't update the UI
    const isShowingGuideEmails = showingGuideEmails;

    const now = new Date();
    const refreshStartTime = Date.now();
    console.log(`[${new Date().toISOString()}] AUTO-REFRESH-TIMESTAMP: ${now.getTime()} - Auto-refresh started at ${now.toLocaleTimeString()} for address: ${currentAddress}`);
    console.log(`[${new Date().toISOString()}] AUTO-REFRESH-PERFORM-START: ${refreshStartTime} - Starting refresh for: ${currentAddress}`);

    // Set refreshing state
    setIsRefreshing(true);

    try {
      // Check again that essential conditions haven't changed
      if (emailAddress !== currentAddress || disableAutoRefresh || !autoRefreshManager.isAutoRefreshActive()) {
        console.log('Essential conditions changed during auto-refresh. Skipping refresh for:', currentAddress);
        return;
      }

      // Call the onRefresh function with a timeout
      const refreshPromise = onRefresh();

      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('Auto-refresh timed out after 10 seconds'));
        }, 10000); // 10 second timeout
      });

      // Race the refresh promise against the timeout
      await Promise.race([refreshPromise, timeoutPromise]);

      // Check again that conditions haven't changed after the refresh
      if (emailAddress !== currentAddress) {
        console.log('Email address changed during auto-refresh. Ignoring results for:', currentAddress);
        return;
      }

      const refreshEndTime = Date.now();
      const refreshDuration = refreshEndTime - refreshStartTime;

      if (isShowingGuideEmails) {
        console.log(`AUTO-REFRESH-PERFORM-END: ${refreshEndTime} - Auto-refresh completed in ${refreshDuration}ms, but not updating UI because guide emails are showing`);
      } else {
        console.log(`AUTO-REFRESH-PERFORM-END: ${refreshEndTime} - Auto-refresh completed in ${refreshDuration}ms successfully for: ${currentAddress}`);
      }

      // Add a delay after a successful fetch before the next auto-refresh cycle
      // This helps reduce the frequency of API calls
      const postFetchDelay = TIMEOUTS.POST_FETCH_DELAY;
      console.log(`[${new Date().toISOString()}] Adding post-fetch delay of ${postFetchDelay}ms before next auto-refresh cycle`);
      await new Promise(resolve => setTimeout(resolve, postFetchDelay));
    } catch (error) {
      // Only log error if the email address hasn't changed
      if (emailAddress === currentAddress) {
        logError('auto-refresh', 'Error during auto-refresh', error);

        // If there's an error, we'll still continue with auto-refresh on the next interval
        console.log('Auto-refresh will continue on next interval despite error');
      } else {
        console.log('Error occurred during auto-refresh, but email address changed. Ignoring error for:', currentAddress);
      }
    } finally {
      // Always reset refreshing state
      setIsRefreshing(false);
    }
  }, [
    emailAddress,
    disableAutoRefresh,
    isGeneratingAddress,
    justGeneratedAddress,
    showingGuideEmails,
    isRefreshing,
    onRefresh,
    autoRefreshManager
  ]);

  // Function to start auto-refresh
  const startAutoRefresh = useCallback(() => {
    // Skip if essential conditions aren't met
    if (!emailAddress || disableAutoRefresh || isGeneratingAddress) {
      console.log('Not starting auto-refresh due to essential conditions not being met:', {
        emailAddress,
        disableAutoRefresh,
        isGeneratingAddress
      });
      return;
    }

    // Skip if we're already setting up auto-refresh for this email address
    if (autoRefreshRef.current.isSettingUp && autoRefreshRef.current.currentEmailAddress === emailAddress) {
      console.log('Already setting up auto-refresh for address:', emailAddress);
      return;
    }

    // Skip if auto-refresh is already active for this email address
    if (autoRefreshRef.current.active && autoRefreshRef.current.currentEmailAddress === emailAddress &&
        autoRefreshManager.getCurrentEmailAddress() === emailAddress) {
      console.log('Auto-refresh is already active for address:', emailAddress);
      return;
    }

    // Log if we're starting auto-refresh with justGeneratedAddress flag
    if (justGeneratedAddress) {
      console.log('Starting auto-refresh with justGeneratedAddress flag:', {
        justGeneratedAddress
      });
    }

    // Mark that we're setting up auto-refresh
    autoRefreshRef.current.isSettingUp = true;
    autoRefreshRef.current.currentEmailAddress = emailAddress;

    // Note: We're now allowing auto-refresh even when showing guide emails
    console.log('Starting auto-refresh for address:', emailAddress);
    autoRefreshRef.current.active = true;

    // Start auto-refresh using the manager
    autoRefreshManager.startAutoRefresh(
      onRefresh,
      emailAddress,
      initialDelayMs ?? 2000, // 2 seconds instead of 15
      refreshIntervalMs ?? TIMEOUTS.AUTO_REFRESH_INTERVAL
    );

    // Reset the setting up flag
    autoRefreshRef.current.isSettingUp = false;
  }, [
    emailAddress,
    disableAutoRefresh,
    isGeneratingAddress,
    justGeneratedAddress,
    onRefresh,
    initialDelayMs,
    refreshIntervalMs,
    autoRefreshManager
  ]);

  // Function to stop auto-refresh
  const stopAutoRefresh = useCallback(() => {
    // Only stop if auto-refresh is actually active
    if (autoRefreshRef.current.active) {
      console.log('Stopping auto-refresh at', new Date().toLocaleTimeString());
      autoRefreshRef.current.active = false;
      autoRefreshRef.current.currentEmailAddress = null;
      autoRefreshManager.stopAutoRefresh();
    } else {
      console.log('Auto-refresh stop requested, but it was not active');
    }
  }, [autoRefreshManager]);

  // Effect to manage auto-refresh lifecycle
  useEffect(() => {
    // Skip if we're already in the process of setting up auto-refresh
    if (autoRefreshRef.current.isSettingUp) {
      console.log('Skipping auto-refresh effect because setup is already in progress');
      return;
    }

    // Log the current state for debugging
    console.log('Auto-refresh effect triggered with:', {
      emailAddress,
      disableAutoRefresh,
      isGeneratingAddress,
      justGeneratedAddress,
      active: autoRefreshRef.current.active,
      currentEmailAddress: autoRefreshRef.current.currentEmailAddress
    });

    // Determine if auto-refresh should be active
    const shouldBeActive = emailAddress && !disableAutoRefresh && !isGeneratingAddress;

    // Check if we need to make any changes
    const isAlreadyActiveForSameAddress =
      autoRefreshRef.current.active &&
      autoRefreshRef.current.currentEmailAddress === emailAddress &&
      autoRefreshManager.getCurrentEmailAddress() === emailAddress;

    // Only take action if the current state doesn't match what it should be
    if (shouldBeActive && !isAlreadyActiveForSameAddress) {
      // Stop any existing auto-refresh for a different address
      if (autoRefreshRef.current.active && autoRefreshRef.current.currentEmailAddress !== emailAddress) {
        console.log('Stopping auto-refresh for different address before starting new one');
        stopAutoRefresh();
      }

      // If we just generated an address, we'll log it but still start auto-refresh
      if (justGeneratedAddress) {
        console.log('Starting auto-refresh with justGeneratedAddress flag for:', emailAddress);
      } else {
        console.log('Starting auto-refresh for address:', emailAddress);
      }

      startAutoRefresh();
    } else if (!shouldBeActive && autoRefreshRef.current.active) {
      console.log('Stopping auto-refresh because conditions changed:', {
        emailAddress,
        disableAutoRefresh,
        isGeneratingAddress
      });
      stopAutoRefresh();
    } else if (shouldBeActive && isAlreadyActiveForSameAddress) {
      console.log('Auto-refresh is already active for this address, no changes needed');
    } else {
      console.log('Not starting auto-refresh because:',
        !emailAddress ? 'no email address' :
        disableAutoRefresh ? 'auto-refresh is disabled' :
        'generating a new address');
    }

    // Clean up on unmount
    return () => {
      if (autoRefreshRef.current.active) {
        console.log('Cleaning up auto-refresh on unmount');
        stopAutoRefresh();
      }
    };
  }, [
    emailAddress,
    disableAutoRefresh,
    isGeneratingAddress,
    justGeneratedAddress,
    stopAutoRefresh,
    startAutoRefresh,
    autoRefreshManager
  ]);

  return {
    isRefreshing,
    startAutoRefresh,
    stopAutoRefresh,
    performRefresh
  };
};
