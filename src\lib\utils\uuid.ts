/**
 * Browser-compatible UUID generation utilities
 * Provides UUID v4 generation that works in both browser and Node.js environments
 */

/**
 * Generate a UUID v4 string using browser-compatible crypto.getRandomValues()
 * Falls back to Math.random() if crypto is not available
 * @returns A UUID v4 string
 */
export function generateUUID(): string {
  // Check if we're in a browser environment with crypto.getRandomValues
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    // Use browser crypto API
    const array = new Uint8Array(16);
    window.crypto.getRandomValues(array);

    // Set version (4) and variant bits according to RFC 4122
    array[6] = (array[6] & 0x0f) | 0x40; // Version 4
    array[8] = (array[8] & 0x3f) | 0x80; // Variant 10

    // Convert to hex string with proper formatting
    const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    return [
      hex.slice(0, 8),
      hex.slice(8, 12),
      hex.slice(12, 16),
      hex.slice(16, 20),
      hex.slice(20, 32)
    ].join('-');
  }

  // Fallback for environments without crypto.getRandomValues
  // This is less secure but provides compatibility
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Generate a session ID for tracking purposes
 * Uses the same UUID generation but with a more descriptive name
 * @returns A UUID v4 string suitable for session tracking
 */
export function generateSessionId(): string {
  return generateUUID();
}

/**
 * Validate if a string is a valid UUID format (any version)
 * @param uuid The string to validate
 * @returns True if the string is a valid UUID format
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Generate a short unique identifier (8 characters)
 * Useful for shorter IDs when full UUID is not needed
 * @returns A short unique identifier string
 */
export function generateShortId(): string {
  const uuid = generateUUID();
  return uuid.replace(/-/g, '').substring(0, 8);
}
