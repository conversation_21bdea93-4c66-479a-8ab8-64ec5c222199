/**
 * TestInstructions Component
 *
 * This component displays instructions for using the email deliverability test.
 */

'use client';

interface TestInstructionsProps {
  testAddress: string;
}

export default function TestInstructions({ testAddress }: TestInstructionsProps) {
  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="text-center px-2">
        <p className="text-base sm:text-lg leading-relaxed" style={{ color: '#4a3728' }}>
          Send an email from your server to the test address to evaluate your email deliverability and authentication setup.
        </p>
      </div>

      <div className="rounded-2xl p-4 sm:p-6 shadow-sm" style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8' }}>
        <div className="flex items-center mb-4">
          <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-xl mr-3 shadow-lg" style={{ backgroundColor: '#1b130e' }}>
            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg sm:text-xl font-bold" style={{ color: '#1b130e' }}>Step-by-Step Instructions</h3>
        </div>
        <div className="space-y-3 sm:space-y-4">
          {[
            { step: 1, text: "Use your regular email sending system or server", icon: "🖥️" },
            { step: 2, text: "Send an email to:", extra: testAddress, icon: "📧" },
            { step: 3, text: "Include a subject line and some content in the email body", icon: "✍️" },
            { step: 4, text: "After sending, wait a few moments for the email to be processed", icon: "⏱️" },
            { step: 5, text: "Click \"Check for Results\" below to analyze your email", icon: "🔍" }
          ].map((item) => (
            <div key={item.step} className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 rounded-xl shadow-sm" style={{ backgroundColor: '#f3ece8', border: '1px solid #f3ece8' }}>
              <div className="flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 text-white rounded-lg font-bold text-sm shadow-sm flex-shrink-0" style={{ backgroundColor: '#1b130e' }}>
                {item.step}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-start sm:items-center space-x-2">
                  <span className="text-base sm:text-lg flex-shrink-0">{item.icon}</span>
                  <p className="font-medium text-sm sm:text-base" style={{ color: '#1b130e' }}>{item.text}</p>
                </div>
                {item.extra && (
                  <div className="mt-2 p-2 sm:p-3 rounded-lg" style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8' }}>
                    <span className="font-mono font-semibold text-xs sm:text-sm break-all" style={{ color: '#ce601c' }}>{item.extra}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="rounded-2xl p-4 sm:p-6 shadow-sm" style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8' }}>
        <div className="flex items-center mb-4">
          <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-xl mr-3 shadow-lg" style={{ backgroundColor: '#956b50' }}>
            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h3 className="text-lg sm:text-xl font-bold" style={{ color: '#1b130e' }}>Pro Tips for Best Results</h3>
        </div>
        <div className="space-y-3 sm:space-y-4">
          {[
            {
              text: "Send from the same email server you normally use for production emails",
              icon: "🏢",
              tip: "This ensures authentic testing conditions"
            },
            {
              text: "Use your actual domain name in the From address",
              icon: "🌐",
              tip: "Helps validate your domain's authentication setup"
            },
            {
              text: "Include all authentication headers your server normally sends",
              icon: "🔐",
              tip: "Enables comprehensive SPF, DKIM, and DMARC analysis"
            }
          ].map((item, index) => (
            <div key={index} className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 rounded-xl shadow-sm" style={{ backgroundColor: '#f3ece8', border: '1px solid #f3ece8' }}>
              <div className="flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 text-white rounded-lg text-sm shadow-sm flex-shrink-0" style={{ backgroundColor: '#956b50' }}>
                {item.icon}
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium mb-1 text-sm sm:text-base" style={{ color: '#1b130e' }}>{item.text}</p>
                <p className="text-xs sm:text-sm" style={{ color: '#4a3728' }}>{item.tip}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
