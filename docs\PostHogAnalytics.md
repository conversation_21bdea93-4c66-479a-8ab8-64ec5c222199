# PostHog Analytics Integration for VanishPost

This document outlines the integration of PostHog analytics into the VanishPost application.

## Overview

PostHog is an open-source product analytics platform that helps you understand user behavior. We've integrated PostHog into VanishPost to track essential user interactions while respecting user privacy.

## Implementation Details

### 1. Dependencies

We've added the following dependency:

```bash
npm install posthog-js
```

### 2. Configuration

PostHog is configured in `src/lib/analytics/posthog.ts` with the following privacy-focused settings:

- Disabled automatic page view capturing
- Disabled autocapture of clicks and form submissions
- Disabled session recording
- Respect for Do Not Track browser settings
- In-memory persistence (no cookies by default)
- Disabled IP address capturing
- Disabled UTM parameter capturing
- Disabled performance metrics capturing

### 3. Environment Variables

PostHog requires the following environment variables in `.env.local`:

```
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_api_key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

For production, these should be set in your hosting environment (e.g., Vercel).

### 4. Provider Setup

The PostHog provider is set up in the root layout (`src/app/layout.tsx`) to wrap the entire application:

```tsx
<PHProvider>
  {/* Application content */}
</PHProvider>
```

### 5. Page View Tracking

Page views are tracked using the `PostHogAnalytics` component in the root layout:

```tsx
<Suspense fallback={null}>
  <PostHogAnalytics />
</Suspense>
```

This component uses the `usePathname` and `useSearchParams` hooks to track page views when the URL changes.

## Event Tracking

We track the following events:

| Event Name | Description | Properties |
|------------|-------------|------------|
| `page_view` | User views a page | `{ url: string }` |
| `email_generated` | User generates a new email address | `{ emailAddress: string }` |
| `email_received` | Email is received | `{ count: number }` |
| `email_viewed` | User views an email | `{ emailId: string }` |
| `email_deleted` | User deletes an email | `{ emailId: string }` |
| `email_address_copied` | User copies email address | `{ emailAddress: string }` |
| `inbox_refreshed` | Inbox is refreshed | `{ isManual: boolean }` |
| `renderer_changed` | Email renderer is changed | `{ renderer: string }` |

## Usage Examples

### Tracking Page Views

Page views are automatically tracked by the `PostHogAnalytics` component.

### Tracking Custom Events

To track custom events, import the tracking functions from `src/lib/analytics/posthog.ts`:

```tsx
import { trackEvent } from '@/lib/analytics/posthog';

// Track a custom event
trackEvent('button_clicked', { buttonName: 'submit' })
  .catch(err => console.error('Failed to track event:', err));
```

### Tracking Email Generation

```tsx
import { trackEmailGenerated } from '@/lib/analytics/posthog';

// Track email generation
trackEmailGenerated('<EMAIL>')
  .catch(err => console.error('Failed to track email generation:', err));
```

### Tracking Email Views

```tsx
import { trackEmailViewed } from '@/lib/analytics/posthog';

// Track email view
trackEmailViewed('email-123')
  .catch(err => console.error('Failed to track email view:', err));
```

## Privacy Considerations

Our PostHog implementation respects user privacy in several ways:

1. **Minimal Data Collection**: We only track essential events needed to understand application usage.

2. **No Personal Information**: We don't track any personally identifiable information (PII) beyond temporary email addresses.

3. **Respect for DNT**: We respect the Do Not Track browser setting.

4. **No Cookies by Default**: We use in-memory persistence by default, avoiding cookies.

5. **No IP Address Tracking**: We've disabled IP address capturing.

6. **No Session Recording**: We've disabled session recording features.

7. **Admin Areas Excluded**: We don't track activity in admin or management portal areas.

## Transition Strategy

We've implemented a dual-tracking approach during the transition from our previous analytics system to PostHog:

1. Both the old Supabase-based analytics and new PostHog analytics are called for each event.

2. This ensures data continuity during the transition period.

3. Once we're confident in the PostHog implementation, we can remove the Supabase analytics calls.

## Future Improvements

Potential future improvements to our analytics implementation:

1. **User Consent**: Implement a user consent mechanism for analytics tracking.

2. **Feature Usage Tracking**: Track usage of specific features to guide product development.

3. **Performance Monitoring**: Selectively enable performance metrics for key user journeys.

4. **Funnel Analysis**: Set up conversion funnels to analyze user flows.

5. **A/B Testing**: Implement A/B testing for new features using PostHog's experimentation capabilities.

## Troubleshooting

If you encounter issues with the PostHog integration:

1. Check that the environment variables are correctly set.

2. Verify that the PostHog provider is properly wrapped around the application.

3. Check the browser console for any errors related to PostHog.

4. Ensure that the PostHog API key has the correct permissions.

5. Verify that the PostHog host URL is accessible from your environment.

## Accessing and Using the PostHog Dashboard

After implementing PostHog analytics, you'll need to access the PostHog dashboard to view and analyze the collected data.

### Accessing the Dashboard

1. **Go to the PostHog Website**:
   - Visit [https://app.posthog.com](https://app.posthog.com) (or your self-hosted PostHog instance URL if you're self-hosting)

2. **Log In to Your Account**:
   - Enter the email and password you used when creating your PostHog account
   - If you don't have an account yet, you'll need to sign up first

3. **Navigate to Your Project**:
   - After logging in, you'll see your projects dashboard
   - Click on the "VanishPost" project (or whatever you named your project during setup)
   - If you haven't created a project yet, click "Create new project" and follow the setup instructions

### Finding Your Project's Data

Once you're in your project dashboard, you'll see several sections where you can access different types of analytics data:

#### 1. Events Dashboard

- **Path**: Click on "Events" in the left sidebar
- **What You'll Find**:
  - List of all events being tracked
  - Event counts and trends
  - Filter options to analyze specific events

#### 2. Live Events

- **Path**: Click on "Live events" in the left sidebar
- **What You'll Find**:
  - Real-time stream of events as they occur
  - Great for verifying that your implementation is working correctly

#### 3. Insights

- **Path**: Click on "Insights" in the left sidebar
- **What You'll Find**:
  - Create custom charts and visualizations
  - Analyze trends over time
  - Compare different metrics

#### 4. Sessions

- **Path**: Click on "Sessions" in the left sidebar
- **What You'll Find**:
  - User session recordings (if enabled)
  - Note: We've disabled this in our implementation for privacy reasons

#### 5. Funnels

- **Path**: Click on "Funnels" in the left sidebar
- **What You'll Find**:
  - Create conversion funnels to track user journeys
  - Identify drop-off points in your user flow

### Key Metrics to Verify Integration

To verify that your PostHog integration is working correctly, check these key metrics:

#### 1. Event Counts

- **Path**: Events → All events
- **What to Look For**:
  - Verify that the events you're tracking (`page_view`, `email_generated`, etc.) are appearing in the list
  - Check that the event counts are increasing as you use the application

#### 2. Page Views

- **Path**: Insights → Create new insight → Select "Trends" → Select event "$pageview"
- **What to Look For**:
  - Confirm that page views are being tracked
  - Check that different pages in your application are showing up

#### 3. User Paths

- **Path**: Insights → Create new insight → Select "Paths"
- **What to Look For**:
  - Visualize how users navigate through your application
  - Verify that the flow matches expected user behavior

#### 4. Custom Events

- **Path**: Events → Search for your custom events (e.g., "email_generated")
- **What to Look For**:
  - Confirm that your custom events are being tracked
  - Check that the properties (e.g., `emailAddress`) are being captured correctly

### Creating a Verification Dashboard

To make ongoing verification easier, create a dashboard with key metrics:

1. **Create a Dashboard**:
   - Click on "Dashboards" in the left sidebar
   - Click "Create new dashboard"
   - Name it "VanishPost Verification"

2. **Add Key Insights**:
   - Click "Add insight"
   - Create insights for:
     - Daily page views
     - Email generation counts
     - Email view counts
     - Email deletion counts
     - Inbox refresh counts

3. **Save and Pin**:
   - Save your dashboard
   - Pin it to your favorites for easy access

### Troubleshooting Integration Issues

If you don't see data in your PostHog dashboard, check these common issues:

#### 1. API Key Issues

- **Verify**: Check that you're using the correct API key in your `.env.local` file
- **Solution**: Replace the placeholder `phc_placeholder` with your actual PostHog API key

#### 2. Event Capturing Issues

- **Verify**: Open your browser's developer console and look for any PostHog-related errors
- **Solution**: Fix any JavaScript errors that might be preventing events from being captured

#### 3. Ad Blockers

- **Verify**: Temporarily disable ad blockers or privacy extensions
- **Solution**: Some ad blockers might block PostHog analytics; consider adding a note to users about this

#### 4. Network Issues

- **Verify**: Check network requests in your browser's developer tools
- **Solution**: Ensure that requests to the PostHog API are not being blocked by firewalls or CORS issues

### Setting Up Alerts and Notifications

To stay informed about important analytics events:

1. **Create Alerts**:
   - Go to "Alerts" in the left sidebar
   - Click "Create alert"
   - Set up alerts for significant changes in key metrics

2. **Configure Notifications**:
   - Go to your account settings
   - Set up email or Slack notifications for your alerts

### Next Steps After Verification

Once you've verified that your integration is working correctly:

1. **Create More Detailed Insights**:
   - Set up funnels to track user conversion
   - Create cohort analyses to understand user retention
   - Build dashboards for different aspects of your application

2. **Share Access with Team Members**:
   - Invite team members to your PostHog project
   - Set appropriate permissions for different team roles

3. **Implement A/B Testing**:
   - Use PostHog's experimentation features to test different features
   - Make data-driven decisions based on user behavior

4. **Remove Dual Tracking**:
   - Once you're confident in the PostHog implementation, remove the Supabase analytics calls
   - This will simplify your codebase and reduce unnecessary API calls

### PostHog Documentation Resources

For more detailed information, refer to these PostHog documentation resources:

- [PostHog Documentation](https://posthog.com/docs)
- [JavaScript SDK Documentation](https://posthog.com/docs/libraries/js)
- [Next.js Integration Guide](https://posthog.com/docs/libraries/next-js)
- [Event Tracking Guide](https://posthog.com/docs/product-analytics/events)
- [Insights Documentation](https://posthog.com/docs/product-analytics/insights)
