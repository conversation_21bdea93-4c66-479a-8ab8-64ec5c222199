/**
 * Types and interfaces for the contact messaging system
 */

/**
 * Represents the status of a contact message
 */
export type MessageStatus = 'unread' | 'read' | 'replied' | 'archived';

/**
 * Represents a contact message in the database
 */
export interface ContactMessage {
  id: number;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: MessageStatus;
  threadId: string | null;
  parentId: number | null;
  isAdminReply: boolean;
  createdAt: string | null; // ISO string format for dates
  updatedAt: string | null; // ISO string format for dates
}

/**
 * Represents the data needed to create a new contact message
 */
export interface ContactMessageCreate {
  name: string;
  email: string;
  subject: string;
  message: string;
  threadId?: string | null;
  parentId?: number | null;
  isAdminReply?: boolean;
}

/**
 * Represents the data needed to update a contact message
 */
export interface ContactMessageUpdate {
  id: number;
  status?: MessageStatus;
  threadId?: string | null;
}

/**
 * Represents the data needed to reply to a contact message
 */
export interface ContactMessageReply {
  parentId: number;
  message: string;
  threadId: string;
}
