import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logInfo, logError } from '@/lib/logging';

/**
 * API route to initialize the domain configuration
 * This should be called once during setup or deployment
 */
export async function GET(request: NextRequest) {
  try {
    // In production, you would add authentication here
    // to ensure only admins can initialize the database

    const supabase = createServerSupabaseClient();

    // Check if domain_config table exists
    try {
      const { data, error } = await supabase
        .from('domain_config')
        .select('domain')
        .limit(1);

      if (error && error.code === 'PGRST116') {
        // Table doesn't exist, create it
        logInfo('admin', 'Creating domain_config table');
        
        // Create the domain_config table
        const { error: createError } = await supabase.rpc('execute_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS domain_config (
              domain VARCHAR(100) PRIMARY KEY,
              is_active BOOLEAN NOT NULL DEFAULT TRUE,
              settings JSONB NOT NULL,
              created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
              updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
            );
          `
        });

        if (createError) {
          throw new Error(`Failed to create domain_config table: ${createError.message}`);
        }
      }
    } catch (error) {
      // If the execute_sql function doesn't exist, provide instructions
      logError('admin', 'Error checking domain_config table', { error });
      return NextResponse.json({
        success: false,
        message: 'Could not check or create domain_config table. Please create it manually in the Supabase dashboard.',
        sql: `
          CREATE TABLE IF NOT EXISTS domain_config (
            domain VARCHAR(100) PRIMARY KEY,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            settings JSONB NOT NULL,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
          );
        `
      }, { status: 500 });
    }

    // Insert default domains
    const defaultDomains = [
      {
        domain: 'fademail.site',
        is_active: true,
        settings: {
          isDefault: true,
          weight: 100,
          features: {
            adsEnabled: true,
            analyticsEnabled: true,
            autoRefreshEnabled: true
          }
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        domain: 'flickmail.site',
        is_active: true,
        settings: {
          isDefault: false,
          weight: 100,
          features: {
            adsEnabled: true,
            analyticsEnabled: true,
            autoRefreshEnabled: true
          }
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    // Insert domains with upsert to avoid duplicates
    for (const domain of defaultDomains) {
      const { error } = await supabase
        .from('domain_config')
        .upsert(domain, { onConflict: 'domain' });

      if (error) {
        logError('admin', `Error inserting domain ${domain.domain}`, { error });
        return NextResponse.json({
          success: false,
          message: `Failed to insert domain ${domain.domain}: ${error.message}`
        }, { status: 500 });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Domain configuration initialized successfully',
      domains: defaultDomains.map(d => d.domain)
    });
  } catch (error) {
    logError('admin', 'Error initializing domain configuration', { error });
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
