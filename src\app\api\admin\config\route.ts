/**
 * API routes for configuration management
 */
import { NextRequest, NextResponse } from 'next/server';
import { getAllConfig, updateMultipleConfig } from '@/lib/config/configService';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/admin/config
 * 
 * Get all configuration values
 */
export async function GET(request: NextRequest) {
  try {
    // Get all configuration values
    const config = await getAllConfig();
    
    return NextResponse.json({
      success: true,
      data: config
    });
  } catch (error) {
    logError('api', 'Error getting configuration', { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to get configuration' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/config
 * 
 * Update configuration values
 */
export async function POST(request: NextRequest) {
  try {
    // Get updates from request body
    const updates = await request.json();
    
    // Validate updates
    if (!updates || typeof updates !== 'object') {
      return NextResponse.json(
        { success: false, error: 'Invalid updates' },
        { status: 400 }
      );
    }
    
    // Update configuration values
    const success = await updateMultipleConfig(updates);
    
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to update configuration' },
        { status: 500 }
      );
    }
    
    logInfo('api', 'Updated configuration', { updates });
    
    return NextResponse.json({
      success: true,
      message: 'Configuration updated successfully'
    });
  } catch (error) {
    logError('api', 'Error updating configuration', { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to update configuration' },
      { status: 500 }
    );
  }
}
