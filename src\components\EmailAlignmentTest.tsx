'use client';

import React, { useState } from 'react';
import <PERSON>rame<PERSON>mail<PERSON><PERSON><PERSON> from './IframeEmailRenderer';

/**
 * EmailAlignmentTest Component
 *
 * A component to test email alignment rendering
 */
export default function EmailAlignmentTest() {
  const [testHtml, setTestHtml] = useState<string>(`
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #4f46e5; text-align: center;">Centered Test Email</h1>

      <p style="text-align: center;">This paragraph is centered using text-align: center style.</p>

      <center>This text is centered using the center tag.</center>

      <p align="center">This paragraph is centered using the align attribute.</p>

      <div style="text-align: center;">
        <img src="/test-image.svg" alt="Test Image" style="max-width: 100%; height: auto;" />
        <p>This image should be centered (inside a centered div)</p>
      </div>

      <center>
        <img src="/test-image.svg" alt="Test Image 2" style="max-width: 100%; height: auto;" />
        <p>This image should be centered (inside a center tag)</p>
      </center>

      <p>
        <img src="/test-image.svg" alt="Test Image 3" style="max-width: 100%; height: auto; display: block; margin: 0 auto;" />
        <span style="display: block; text-align: center;">This image should be centered (using margin: 0 auto)</span>
      </p>

      <p align="right">This paragraph is right-aligned using the align attribute.</p>

      <p style="text-align: left;">This paragraph is left-aligned using text-align: left style.</p>

      <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center;">
        <p style="margin: 0;">This is a centered styled box to test CSS rendering.</p>
      </div>

      <h2 style="color: #4f46e5; text-align: center; margin-top: 30px;">Table Alignment Tests</h2>

      <!-- Table with centered cells -->
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr>
          <td style="text-align: center; padding: 10px; border: 1px solid #e5e7eb;">Centered cell</td>
          <td style="text-align: left; padding: 10px; border: 1px solid #e5e7eb;">Left-aligned cell</td>
          <td style="text-align: right; padding: 10px; border: 1px solid #e5e7eb;">Right-aligned cell</td>
        </tr>
      </table>

      <!-- Table with align attribute on TR -->
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr align="center">
          <td style="padding: 10px; border: 1px solid #e5e7eb;">This entire row should be centered (TR with align)</td>
        </tr>
      </table>

      <!-- Table with align attribute on TABLE -->
      <table align="center" style="width: 80%; border-collapse: collapse; margin: 20px 0;">
        <tr>
          <td style="padding: 10px; border: 1px solid #e5e7eb;">This table should be centered (TABLE with align)</td>
        </tr>
      </table>

      <!-- Table with style for centering -->
      <table style="width: 80%; border-collapse: collapse; margin: 20px auto;">
        <tr>
          <td style="padding: 10px; border: 1px solid #e5e7eb;">This table should be centered (margin: auto)</td>
        </tr>
      </table>

      <!-- Table with centered content in cells -->
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr>
          <td align="center" style="padding: 10px; border: 1px solid #e5e7eb;">
            <img src="/test-image.svg" alt="Test Image in TD" style="max-width: 100px; height: auto;" />
            <p>Image in centered TD</p>
          </td>
        </tr>
      </table>

      <h2 style="color: #4f46e5; text-align: center; margin-top: 30px;">List Alignment Tests</h2>

      <!-- Centered list -->
      <ul style="text-align: center; list-style-position: inside; padding: 0;">
        <li>This list should be centered</li>
        <li>Using text-align: center</li>
        <li>On the UL element</li>
      </ul>

      <!-- Centered list with align -->
      <ol align="center" style="list-style-position: inside; padding: 0;">
        <li>This ordered list should be centered</li>
        <li>Using align="center"</li>
        <li>On the OL element</li>
      </ol>
    </div>
  `);

  return (
    <div className="p-4 sm:p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-lg sm:text-xl font-semibold text-gray-700 mb-4">Email Alignment Test</h2>

      <div className="mb-4">
        <label htmlFor="test-html" className="block text-sm font-medium text-gray-700 mb-1">Test HTML</label>
        <textarea
          id="test-html"
          className="w-full h-40 p-2 border border-gray-300 rounded-md text-sm"
          value={testHtml}
          onChange={(e) => setTestHtml(e.target.value)}
        />
      </div>

      <div className="border-t border-gray-100 pt-4 mt-4">
        <h3 className="text-base font-medium text-gray-700 mb-2">Rendered Output</h3>
        <div className="border border-gray-200 rounded-lg p-4">
          <IframeEmailRenderer
            html={testHtml}
            height="500px"
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}
