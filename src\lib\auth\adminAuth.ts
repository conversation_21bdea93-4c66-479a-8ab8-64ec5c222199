/**
 * Admin authentication utilities
 */

import { NextRequest } from 'next/server';
import { cookies } from 'next/headers';
import { verify, JwtPayload } from 'jsonwebtoken';
import { ADMIN } from '@/lib/constants';
import { logger } from '@/lib/logging/Logger';

// Define the user type
export interface User {
  userId: number;
  username: string;
  role: string;
}

/**
 * Verify an admin token and return the user if valid
 * @param token The JWT token to verify
 * @returns The user object if the token is valid, null otherwise
 */
export function verifyAdminToken(token: string): User | null {
  try {
    // Check if JWT_SECRET is set
    if (!process.env.JWT_SECRET) {
      logger.error('AUTH', 'Missing required environment variable: JWT_SECRET');
      return null;
    }

    if (!token) {
      return null;
    }

    // Verify the token with the JWT secret
    const decoded = verify(token, process.env.JWT_SECRET);

    // Type guard to check if decoded is a JwtPayload with our expected properties
    if (typeof decoded === 'object' && decoded !== null && 'username' in decoded && 'role' in decoded) {
      // Return the user from the token payload
      return {
        userId: 'userId' in decoded ? decoded.userId : 1, // Default ID if not in token
        username: decoded.username as string,
        role: decoded.role as string
      };
    } else {
      logger.warning('AUTH', 'Token is missing required fields');
      return null;
    }
  } catch (error) {
    logger.error('AUTH', 'Error verifying admin token', { error });
    return null;
  }
}

/**
 * Verify admin authentication from request
 * @param request The NextRequest object
 * @returns An object with success status and user data or error message
 */
export async function verifyAdminAuth(request: NextRequest) {
  try {
    // Check for cookie-based authentication first
    const token = request.cookies.get('admin_token')?.value;
    if (token) {
      const user = verifyAdminToken(token);
      if (user) {
        return {
          success: true,
          user
        };
      }
    }

    // Fall back to Authorization header if no valid cookie
    const authHeader = request.headers.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const headerToken = authHeader.substring(7);
      const user = verifyAdminToken(headerToken);
      if (user) {
        return {
          success: true,
          user
        };
      }
    }

    // No valid authentication found
    return {
      success: false,
      message: 'Unauthorized'
    };
  } catch (error) {
    logger.error('AUTH', 'Error verifying admin authentication', { error });
    return {
      success: false,
      message: 'Authentication error'
    };
  }
}

/**
 * Get the current user from the request cookies
 * @returns The user object if authenticated, null otherwise
 */
export async function getCurrentUser(): Promise<User | null> {
  try {
    // Use the cookies API to get the admin_token cookie
    const cookieStore = await cookies();
    const token = cookieStore.get('admin_token')?.value;

    if (!token) {
      return null;
    }

    return verifyAdminToken(token);
  } catch (error) {
    logger.error('AUTH', 'Error getting current user', { error });
    return null;
  }
}

/**
 * Check if the current user has admin privileges
 * @param user The user object to check
 * @returns True if the user is an admin, false otherwise
 */
export function isAdmin(user: User | null): boolean {
  return user?.role === 'admin';
}
