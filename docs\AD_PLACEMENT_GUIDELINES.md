# Fademail Ad Placement Guidelines

This document provides guidelines and best practices for configuring ad placements in the Fademail application.

## Table of Contents

1. [Introduction](#introduction)
2. [Ad Placement Types](#ad-placement-types)
3. [Device-Specific Considerations](#device-specific-considerations)
4. [Performance Optimization](#performance-optimization)
5. [Non-Intrusive Ad Strategies](#non-intrusive-ad-strategies)
6. [Compliance Guidelines](#compliance-guidelines)
7. [Testing and Monitoring](#testing-and-monitoring)

## Introduction

Fademail's ad system is designed to provide revenue while maintaining a good user experience. These guidelines will help you configure ad placements that balance monetization with user satisfaction.

### Key Principles

1. **User Experience First**: Ads should not interfere with the core functionality of the application.
2. **Performance Matters**: Ad loading should not significantly impact page load times.
3. **Responsive Design**: Ads should adapt to different screen sizes and devices.
4. **Compliance**: All ad placements should comply with advertising network policies and privacy regulations.

## Ad Placement Types

Fademail supports several types of ad placements, each suited for different parts of the application.

### Standard Placements

| Placement ID | Location | Recommended Sizes | Best For |
|--------------|----------|-------------------|----------|
| sidebar-top | Top of sidebar | 300x250, 336x280 | Desktop, Tablet |
| sidebar-bottom | Bottom of sidebar | 300x250, 336x280 | Desktop, Tablet |
| inbox-bottom | Below email list | 728x90, 970x90 | Desktop |
| inbox-mobile | Below email list on mobile | 320x100, 320x50 | Mobile |
| email-view-right | Right side of email view | 300x600, 160x600 | Desktop |
| footer-banner | Footer area | 728x90, 970x90 | All devices |

### Example Configuration

```json
{
  "placement_id": "sidebar-top",
  "domain": "fademail.site",
  "ad_unit_id": "ca-pub-XXXXXXXXXXXXXXXX",
  "is_enabled": true,
  "device_types": ["desktop", "tablet"],
  "display_options": {
    "lazy_loading": true,
    "refresh_interval": 60
  }
}
```

## Device-Specific Considerations

### Desktop

- Larger ad formats (728x90, 300x600) work well
- Can support multiple ad placements without cluttering the interface
- Consider sidebar ads that remain visible while scrolling

### Tablet

- Medium-sized ads (300x250, 468x60) are most effective
- Landscape and portrait orientations need different placements
- Avoid ads that take up too much vertical space

### Mobile

- Smaller formats (320x50, 300x250) are necessary
- Limit the number of ads to avoid overwhelming the small screen
- Consider using collapsible ads to save space
- Ensure touch targets are not obscured by ads

### Configuration Example

```json
{
  "placement_id": "inbox-responsive",
  "domain": "fademail.site",
  "ad_unit_id": "ca-pub-XXXXXXXXXXXXXXXX",
  "is_enabled": true,
  "device_types": ["desktop", "tablet", "mobile"],
  "display_options": {
    "sizes": {
      "desktop": [[728, 90], [970, 90]],
      "tablet": [[468, 60], [728, 90]],
      "mobile": [[320, 50], [320, 100]]
    }
  }
}
```

## Performance Optimization

### Lazy Loading

Lazy loading delays the loading of ads until they are about to enter the viewport, improving initial page load performance.

```json
{
  "lazyLoading": {
    "enabled": true,
    "threshold": 0.1
  }
}
```

The `threshold` value (0-1) determines how close the ad needs to be to the viewport before loading.

### Ad Refresh

Periodic ad refresh can increase revenue but should be used carefully to avoid affecting user experience.

```json
{
  "refreshInterval": 60,
  "maxRefreshes": 3,
  "refreshOnlyWhenVisible": true
}
```

### Limiting Ad Load

Limit the number of ads per page to maintain performance:

```json
{
  "maxAdsPerPage": 3,
  "maxAdsPerViewport": 2
}
```

### Caching

Configure caching to reduce API calls:

```json
{
  "caching": {
    "adCacheTtl": 300,
    "domainCacheTtl": 900
  }
}
```

## Non-Intrusive Ad Strategies

### Timed Display Ads

These ads appear after a user has been on the page for a certain amount of time.

```json
{
  "placement_id": "timed-banner",
  "domain": "fademail.site",
  "ad_unit_id": "ca-pub-XXXXXXXXXXXXXXXX",
  "is_enabled": true,
  "device_types": ["desktop", "tablet", "mobile"],
  "display_options": {
    "delay_seconds": 30,
    "display_duration": 15,
    "max_displays_per_session": 2
  }
}
```

### Idle-Time Ads

These ads appear when the user has been inactive for a period of time.

```json
{
  "placement_id": "idle-notification",
  "domain": "fademail.site",
  "ad_unit_id": "ca-pub-XXXXXXXXXXXXXXXX",
  "is_enabled": true,
  "device_types": ["desktop", "tablet"],
  "display_options": {
    "idle_threshold_seconds": 60,
    "display_mode": "overlay",
    "dismissible": true
  }
}
```

### Between-Actions Ads

These ads appear between user actions, such as after selecting an email.

```json
{
  "placement_id": "between-emails",
  "domain": "fademail.site",
  "ad_unit_id": "ca-pub-XXXXXXXXXXXXXXXX",
  "is_enabled": true,
  "device_types": ["desktop", "tablet", "mobile"],
  "display_options": {
    "trigger_action": "email_selection",
    "frequency": 3,
    "display_duration": 2
  }
}
```

### Collapsible Ads

These ads can be collapsed by the user to minimize disruption.

```json
{
  "placement_id": "collapsible-footer",
  "domain": "fademail.site",
  "ad_unit_id": "ca-pub-XXXXXXXXXXXXXXXX",
  "is_enabled": true,
  "device_types": ["desktop", "tablet", "mobile"],
  "display_options": {
    "initially_expanded": true,
    "collapse_option": true,
    "remember_state": true,
    "expand_after_minutes": 10
  }
}
```

## Compliance Guidelines

### Google AdSense Policies

When using Google AdSense, ensure all placements comply with their [program policies](https://support.google.com/adsense/answer/48182):

1. **Content Guidelines**: Ads should not appear on pages with prohibited content
2. **Ad Placement**: Ads should not be placed in ways that encourage accidental clicks
3. **Ad Density**: Avoid excessive ads on a single page
4. **Invalid Clicks**: Do not implement any mechanism that artificially generates clicks

### Privacy Regulations

Ensure compliance with privacy regulations:

1. **GDPR**: Implement proper consent mechanisms for European users
2. **CCPA**: Provide opt-out options for California residents
3. **Cookie Notices**: Include clear information about advertising cookies

### Implementation Example

```javascript
// Example of GDPR consent implementation
const consentManager = {
  checkConsent: () => {
    // Check if user has given consent
    return localStorage.getItem('adConsent') === 'true';
  },
  
  requestConsent: () => {
    // Show consent dialog
    showConsentDialog().then(result => {
      localStorage.setItem('adConsent', result.toString());
      if (result) {
        // Load ads if consent given
        loadAds();
      }
    });
  }
};

// Only load ads if consent is given
if (consentManager.checkConsent()) {
  loadAds();
} else {
  consentManager.requestConsent();
}
```

## Testing and Monitoring

### A/B Testing

Test different ad placements to find the optimal configuration:

1. Create multiple placement configurations
2. Assign users randomly to different configurations
3. Monitor performance metrics for each configuration
4. Select the best performing configuration

### Performance Monitoring

Monitor these key metrics:

1. **Page Load Time**: Measure the impact of ads on page load time
2. **Ad Load Time**: Track how long ads take to load
3. **Viewability**: Measure what percentage of ads are actually viewed
4. **User Engagement**: Monitor how ads affect user behavior (time on site, bounce rate)

### Revenue Tracking

Track these revenue metrics:

1. **RPM (Revenue Per Mille)**: Revenue per 1,000 impressions
2. **CTR (Click-Through Rate)**: Percentage of impressions that result in clicks
3. **CPC (Cost Per Click)**: Average revenue earned per click
4. **Fill Rate**: Percentage of ad requests that are filled

### Monitoring Dashboard

Use the built-in ad performance dashboard to monitor these metrics:

```
/admin/ads/performance
```

## Conclusion

Effective ad placement is a balance between monetization and user experience. By following these guidelines, you can implement ads that generate revenue while maintaining a positive user experience.

Remember to regularly review ad performance and user feedback, and adjust your strategy accordingly. The most successful ad implementations are those that evolve based on data and user behavior.
