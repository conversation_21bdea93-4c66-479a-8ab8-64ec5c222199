/**
 * API route for admin-portal login
 * This route redirects to the admin auth endpoint
 */
import { NextRequest, NextResponse } from 'next/server';
import { ADMIN } from '@/lib/constants';

/**
 * POST /api/admin-portal/login
 * 
 * Redirect to the admin auth endpoint
 */
export async function POST(request: NextRequest) {
  // Get the secure admin path from constants
  const securePath = ADMIN.SECURE_PATH;
  
  // Clone the request to forward it
  const clonedRequest = request.clone();
  
  // Forward the request to the admin auth endpoint
  return NextResponse.rewrite(new URL(`/api/${securePath}/auth`, request.url));
}

/**
 * GET /api/admin-portal/login
 * 
 * Redirect to the admin auth endpoint
 */
export async function GET(request: NextRequest) {
  // Get the secure admin path from constants
  const securePath = ADMIN.SECURE_PATH;
  
  // Forward the request to the admin auth endpoint
  return NextResponse.rewrite(new URL(`/api/${securePath}/auth`, request.url));
}
