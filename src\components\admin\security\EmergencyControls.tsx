'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { AdminCard, AdminCardHeader, AdminCardContent, AdminCardTitle } from '../ui/AdminCard';
import { AdminButton } from '../ui/AdminButton';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/Dialog';
import {
  AlertTriangle,
  Shield,
  Ban,
  CheckCircle,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Trash2
} from 'lucide-react';
import { AdminFilterSelect } from '@/components/ui/Select';

// Simple wrapper component for AdminCard with title support
function AdminCardWithTitle({ title, children, className }: { title: string | React.ReactNode; children: React.ReactNode; className?: string }) {
  return (
    <AdminCard className={className}>
      <AdminCardHeader>
        <AdminCardTitle>{title}</AdminCardTitle>
      </AdminCardHeader>
      <AdminCardContent>
        {children}
      </AdminCardContent>
    </AdminCard>
  );
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

interface BlockedItem {
  id: number;
  identifier: string;
  type: 'ip' | 'ip_range' | 'session';
  reason: string;
  blocked_at: string;
  blocked_by: string;
  expires_at?: string;
  is_active: boolean;
}

export function EmergencyControls() {
  const [blockedItems, setBlockedItems] = useState<BlockedItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [blockForm, setBlockForm] = useState({
    type: 'ip' as 'ip' | 'ip_range' | 'session',
    identifier: '',
    reason: '',
    duration: '24' // hours
  });

  // Pagination and filtering state
  const [filters, setFilters] = useState({
    search: '',
    type: 'all' as 'all' | 'ip' | 'ip_range' | 'session',
    status: 'active' as 'all' | 'active' | 'expired'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    hasMore: false
  });
  const [sort, setSort] = useState({
    field: 'blocked_at',
    direction: 'desc' as 'asc' | 'desc'
  });
  const [showFilters, setShowFilters] = useState(false);

  // Selection states for bulk operations
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

  // Delete confirmation states
  const [deleteConfirm, setDeleteConfirm] = useState<{
    show: boolean;
    type: 'single' | 'bulk';
    itemId?: string;
    itemIds?: string[];
    itemType?: 'ip' | 'ip_range' | 'session';
    identifier?: string;
    details?: string;
  }>({
    show: false,
    type: 'single'
  });

  // Fetch blocked items with pagination and filtering
  const fetchBlockedItems = async (resetPage = false) => {
    try {
      setLoading(true);

      if (resetPage && pagination.page !== 1) {
        setPagination(prev => ({ ...prev, page: 1 }));
        return;
      }

      const offset = (pagination.page - 1) * pagination.limit;
      let allItems: BlockedItem[] = [];

      // Fetch from different tables based on type filter
      const fetchFromTable = async (tableName: string, itemType: 'ip' | 'ip_range' | 'session', identifierField: string) => {
        let query = supabase
          .from(tableName)
          .select('*', { count: 'exact' });

        // Apply status filter
        if (filters.status === 'active') {
          query = query.eq('is_active', true);
        } else if (filters.status === 'expired') {
          query = query.eq('is_active', false);
        }

        // Apply search filter
        if (filters.search) {
          query = query.ilike(identifierField, `%${filters.search}%`);
        }

        // Apply sorting
        query = query.order(sort.field, { ascending: sort.direction === 'asc' });

        const { data, error, count } = await query;

        if (error) throw error;

        return {
          items: (data || []).map(item => ({
            id: item.id,
            identifier: item[identifierField],
            type: itemType,
            reason: item.reason || 'No reason provided',
            blocked_at: item.blocked_at,
            blocked_by: item.blocked_by || 'System',
            expires_at: item.expires_at,
            is_active: item.is_active
          })),
          count: count || 0
        };
      };

      // Fetch based on type filter
      if (filters.type === 'all' || filters.type === 'ip') {
        const result = await fetchFromTable('blocked_ips', 'ip', 'ip_address');
        allItems.push(...result.items);
      }

      if (filters.type === 'all' || filters.type === 'ip_range') {
        const result = await fetchFromTable('blocked_ip_ranges', 'ip_range', 'ip_range');
        allItems.push(...result.items);
      }

      if (filters.type === 'all' || filters.type === 'session') {
        const result = await fetchFromTable('blocked_sessions', 'session', 'session_id');
        allItems.push(...result.items);
      }

      // Sort combined results
      allItems.sort((a, b) => {
        let aValue: string | number;
        let bValue: string | number;

        if (sort.field === 'blocked_at' || sort.field === 'expires_at') {
          aValue = new Date(a[sort.field as keyof BlockedItem] as string).getTime();
          bValue = new Date(b[sort.field as keyof BlockedItem] as string).getTime();
        } else {
          aValue = String(a[sort.field as keyof BlockedItem] || '');
          bValue = String(b[sort.field as keyof BlockedItem] || '');
        }

        if (sort.direction === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });

      // Apply pagination
      const paginatedItems = allItems.slice(offset, offset + pagination.limit);

      setBlockedItems(paginatedItems);
      setPagination(prev => ({
        ...prev,
        total: allItems.length,
        hasMore: offset + pagination.limit < allItems.length
      }));

      // Clear selections when data changes
      clearSelection();

    } catch (error) {
      console.error('Error fetching blocked items:', error);
    } finally {
      setLoading(false);
    }
  };

  // Helper functions
  const handleSort = (field: string) => {
    setSort(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Selection handlers
  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(itemId)) {
        newSelected.delete(itemId);
      } else {
        newSelected.add(itemId);
      }
      return newSelected;
    });
  };

  const handleSelectAll = () => {
    setSelectedItems(prev => {
      const allIds = blockedItems.map(item => `${item.type}-${item.id}`);

      if (prev.size === allIds.length) {
        // All selected, deselect all
        return new Set();
      } else {
        // Not all selected, select all
        return new Set(allIds);
      }
    });
  };

  const clearSelection = () => {
    setSelectedItems(new Set());
  };

  const getSelectedCount = () => {
    return selectedItems.size;
  };

  // Delete handlers
  const handleDeleteRecord = (item: BlockedItem) => {
    setDeleteConfirm({
      show: true,
      type: 'single',
      itemId: `${item.type}-${item.id}`,
      itemType: item.type,
      identifier: item.identifier,
      details: `${item.type.replace('_', ' ').toUpperCase()}: ${item.reason}`
    });
  };

  const handleBulkDelete = () => {
    const selectedIds = Array.from(selectedItems);
    if (selectedIds.length > 0) {
      setDeleteConfirm({
        show: true,
        type: 'bulk',
        itemIds: selectedIds
      });
    }
  };

  const confirmDelete = async () => {
    try {
      setLoading(true);

      if (deleteConfirm.type === 'single' && deleteConfirm.itemId && deleteConfirm.itemType) {
        await deleteSingleRecord(deleteConfirm.itemId, deleteConfirm.itemType);
      } else if (deleteConfirm.type === 'bulk' && deleteConfirm.itemIds) {
        await deleteBulkRecords(deleteConfirm.itemIds);
      }

      await fetchBlockedItems();
      clearSelection();
      setDeleteConfirm({ show: false, type: 'single' });
      alert('Records deleted successfully!');
    } catch (error) {
      console.error('Error deleting records:', error);
      alert('Failed to delete records. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const cancelDelete = () => {
    setDeleteConfirm({ show: false, type: 'single' });
  };

  // Database delete operations
  const deleteSingleRecord = async (itemId: string, itemType: 'ip' | 'ip_range' | 'session') => {
    const [type, id] = itemId.split('-');
    const numericId = parseInt(id);

    // Get record details for audit log
    const item = blockedItems.find(item => `${item.type}-${item.id}` === itemId);
    if (!item) throw new Error('Record not found');

    // Determine table name
    const tableName = itemType === 'ip' ? 'blocked_ips' :
                     itemType === 'ip_range' ? 'blocked_ip_ranges' :
                     'blocked_sessions';

    // Delete from database
    const { error } = await supabase
      .from(tableName)
      .delete()
      .eq('id', numericId);

    if (error) throw error;

    // Log the deletion
    await supabase
      .from('admin_activity_log')
      .insert({
        admin_id: 'emergency-controls',
        action: 'delete_blocked_item',
        target_type: itemType,
        target_id: numericId.toString(),
        details: {
          deleted_item: item.identifier,
          item_type: itemType,
          original_reason: item.reason,
          deleted_by: 'Emergency Controls Admin'
        },
        action_taken: 'permanent_deletion'
      });
  };

  const deleteBulkRecords = async (itemIds: string[]) => {
    const deletePromises = itemIds.map(async (itemId) => {
      const [type, id] = itemId.split('-');
      const numericId = parseInt(id);
      const item = blockedItems.find(item => `${item.type}-${item.id}` === itemId);

      if (!item) return;

      const tableName = item.type === 'ip' ? 'blocked_ips' :
                       item.type === 'ip_range' ? 'blocked_ip_ranges' :
                       'blocked_sessions';

      // Delete from database
      const { error } = await supabase
        .from(tableName)
        .delete()
        .eq('id', numericId);

      if (error) throw error;

      // Log the deletion
      await supabase
        .from('admin_activity_log')
        .insert({
          admin_id: 'emergency-controls',
          action: 'bulk_delete_blocked_items',
          target_type: item.type,
          target_id: numericId.toString(),
          details: {
            deleted_item: item.identifier,
            item_type: item.type,
            original_reason: item.reason,
            deleted_by: 'Emergency Controls Admin',
            bulk_operation: true
          },
          action_taken: 'permanent_deletion'
        });
    });

    await Promise.all(deletePromises);
  };

  // Sort button component
  const SortButton = ({ field, children }: { field: string; children: React.ReactNode }) => (
    <button
      onClick={() => handleSort(field)}
      className="flex items-center gap-1 text-sm font-medium text-gray-700 hover:text-gray-900"
    >
      {children}
      {sort.field === field ? (
        sort.direction === 'asc' ? <ArrowUp className="w-3 h-3" /> : <ArrowDown className="w-3 h-3" />
      ) : (
        <ArrowUpDown className="w-3 h-3 opacity-50" />
      )}
    </button>
  );

  // Bulk actions toolbar
  const BulkActionsToolbar = () => {
    const selectedCount = getSelectedCount();

    if (selectedCount === 0) return null;

    return (
      <div className="bg-blue-50 border-b border-blue-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium text-blue-900">
              {selectedCount} item{selectedCount !== 1 ? 's' : ''} selected
            </span>
            <AdminButton
              size="sm"
              variant="outline"
              onClick={clearSelection}
              className="text-blue-700 border-blue-300 hover:bg-blue-100"
            >
              Clear Selection
            </AdminButton>
          </div>
          <div className="flex items-center gap-2">
            <AdminButton
              size="sm"
              variant="danger"
              onClick={handleBulkDelete}
              className="flex items-center gap-1"
            >
              <Trash2 className="w-3 h-3" />
              Delete Selected ({selectedCount})
            </AdminButton>
          </div>
        </div>
      </div>
    );
  };

  // Delete confirmation dialog using the professional Dialog component
  const DeleteConfirmDialog = () => {
    return (
      <Dialog open={deleteConfirm.show} onOpenChange={(open) => !open && cancelDelete()}>
        <DialogContent size="lg" className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                <AlertTriangle className="w-5 h-5 text-red-600" />
              </div>
              {deleteConfirm.type === 'single' ? 'Delete Blocked Item' : 'Bulk Delete Blocked Items'}
            </DialogTitle>
            <DialogDescription>
              This action cannot be undone. The {deleteConfirm.type === 'single' ? 'item' : 'items'} will be permanently removed from the database.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {deleteConfirm.type === 'single' ? (
              <div>
                <p className="text-gray-700 mb-4">
                  Are you sure you want to permanently delete this blocked item?
                </p>
                {deleteConfirm.identifier && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-gray-900 mb-1">Identifier:</p>
                        <code className="text-sm bg-white px-2 py-1 rounded border font-mono">
                          {deleteConfirm.identifier}
                        </code>
                      </div>
                      {deleteConfirm.details && (
                        <div>
                          <p className="text-sm font-medium text-gray-900 mb-1">Details:</p>
                          <p className="text-sm text-gray-700 bg-white px-3 py-2 rounded border">
                            {deleteConfirm.details}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div>
                <p className="text-gray-700 mb-4">
                  Are you sure you want to permanently delete <span className="font-semibold">{deleteConfirm.itemIds?.length}</span> blocked items?
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-yellow-800">Bulk Deletion Warning</p>
                      <p className="text-sm text-yellow-700 mt-1">
                        This will permanently remove all selected items from the database. This action affects multiple records and cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <AdminButton
              variant="outline"
              onClick={cancelDelete}
            >
              Cancel
            </AdminButton>
            <AdminButton
              variant="danger"
              onClick={confirmDelete}
              className="flex items-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              {deleteConfirm.type === 'single' ? 'Delete Item' : `Delete ${deleteConfirm.itemIds?.length} Items`}
            </AdminButton>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  // Block IP/Range/Session
  const blockItem = async () => {
    if (!blockForm.identifier.trim() || !blockForm.reason.trim()) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      const expiresAt = new Date(Date.now() + parseInt(blockForm.duration) * 60 * 60 * 1000);

      if (blockForm.type === 'ip_range' || blockForm.type === 'ip') {
        // For single IPs, convert to /32 CIDR
        const cidr = blockForm.type === 'ip' ? `${blockForm.identifier}/32` : blockForm.identifier;
        
        const { error } = await supabase
          .from('blocked_ip_ranges')
          .insert({
            ip_range: cidr,
            reason: blockForm.reason,
            blocked_by: 'Admin Emergency Controls',
            expires_at: expiresAt.toISOString(),
            is_active: true
          });

        if (error) throw error;
      } else if (blockForm.type === 'session') {
        const { error } = await supabase
          .from('blocked_sessions')
          .insert({
            session_id: blockForm.identifier,
            reason: blockForm.reason,
            blocked_by: 'Admin Emergency Controls',
            expires_at: expiresAt.toISOString(),
            is_active: true
          });

        if (error) throw error;
      }

      // Log security event
      await supabase
        .from('security_events')
        .insert({
          event_type: 'manual_block',
          session_id: blockForm.type === 'session' ? blockForm.identifier : null,
          ip_address: blockForm.type === 'ip' ? blockForm.identifier : null,
          severity: 'high',
          description: `Manual block via emergency controls: ${blockForm.type} ${blockForm.identifier}`,
          metadata: {
            blocked_item: blockForm.identifier,
            block_type: blockForm.type,
            reason: blockForm.reason,
            duration_hours: blockForm.duration,
            blocked_by: 'Admin Emergency Controls'
          },
          action_taken: 'manual_block'
        });

      alert(`${blockForm.type.toUpperCase()} blocked successfully!`);
      setBlockForm({ type: 'ip', identifier: '', reason: '', duration: '24' });
      fetchBlockedItems();
    } catch (error) {
      console.error('Error blocking item:', error);
      alert('Failed to block item. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Unblock item with proper table synchronization
  const unblockItem = async (item: BlockedItem) => {
    if (!confirm(`Are you sure you want to unblock ${item.identifier}?`)) {
      return;
    }

    try {
      setLoading(true);

      // Handle different item types with correct table mapping
      if (item.type === 'ip') {
        // Update blocked_ips table for IP addresses
        const { error } = await supabase
          .from('blocked_ips')
          .update({ is_active: false })
          .eq('id', item.id);

        if (error) throw error;
      } else if (item.type === 'ip_range') {
        // Update blocked_ip_ranges table for IP ranges
        const { error } = await supabase
          .from('blocked_ip_ranges')
          .update({ is_active: false })
          .eq('id', item.id);

        if (error) throw error;
      } else if (item.type === 'session') {
        // Update blocked_sessions table for sessions
        const { error } = await supabase
          .from('blocked_sessions')
          .update({ is_active: false })
          .eq('id', item.id);

        if (error) throw error;
      }

      // Log security event
      await supabase
        .from('security_events')
        .insert({
          event_type: 'manual_unblock',
          session_id: item.type === 'session' ? item.identifier : null,
          ip_address: item.type === 'ip' ? item.identifier : null,
          severity: 'medium',
          description: `Manual unblock via emergency controls: ${item.type} ${item.identifier}`,
          metadata: {
            unblocked_item: item.identifier,
            block_type: item.type,
            original_reason: item.reason,
            unblocked_by: 'Admin Emergency Controls'
          },
          action_taken: 'manual_unblock'
        });

      alert('Item unblocked successfully!');
      fetchBlockedItems();
    } catch (error) {
      console.error('Error unblocking item:', error);
      alert('Failed to unblock item. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Emergency block current threats
  const emergencyBlockThreats = async () => {
    if (!confirm('This will block all sessions with critical risk levels. Continue?')) {
      return;
    }

    try {
      setLoading(true);

      // Get sessions with high activity in last hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
      
      const { data: suspiciousSessions } = await supabase
        .from('analytics_events')
        .select('session_id, additional_data')
        .gte('timestamp', oneHourAgo)
        .eq('event_type', 'email_address_generated');

      if (!suspiciousSessions) return;

      // Group by session and count
      const sessionCounts = suspiciousSessions.reduce((acc, event) => {
        const sessionId = event.session_id;
        acc[sessionId] = (acc[sessionId] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Block sessions with >10 generations in last hour
      const threatsToBlock = Object.entries(sessionCounts)
        .filter(([_, count]) => count > 10)
        .map(([sessionId]) => sessionId);

      if (threatsToBlock.length === 0) {
        alert('No immediate threats detected');
        return;
      }

      // Block each threat
      const blockPromises = threatsToBlock.map(sessionId =>
        supabase
          .from('blocked_sessions')
          .insert({
            session_id: sessionId,
            reason: 'Emergency block - High activity detected (>10 emails/hour)',
            blocked_by: 'Emergency Threat Response',
            expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            is_active: true
          })
      );

      await Promise.all(blockPromises);

      alert(`Emergency block completed! Blocked ${threatsToBlock.length} suspicious sessions.`);
      fetchBlockedItems();
    } catch (error) {
      console.error('Error in emergency block:', error);
      alert('Emergency block failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBlockedItems();
  }, []);

  useEffect(() => {
    fetchBlockedItems();
  }, [filters, pagination.page, sort]);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'ip_range': return 'bg-red-100 text-red-800';
      case 'ip': return 'bg-orange-100 text-orange-800';
      case 'session': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const isExpired = (expiresAt?: string) => {
    return expiresAt && new Date(expiresAt) < new Date();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center">
          <AlertTriangle className="h-6 w-6 mr-2 text-red-600" />
          Emergency Security Controls
        </h2>
        <AdminButton
          onClick={emergencyBlockThreats}
          variant="danger"
          disabled={loading}
        >
          <AlertTriangle className="h-4 w-4 mr-1" />
          Emergency Block Threats
        </AdminButton>
      </div>

      {/* Block Form */}
      <AdminCardWithTitle title={<><Shield className="h-4 w-4 mr-1 inline text-red-600" />Manual Block Controls</>} className="border-red-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Block Type
            </label>
            <select
              value={blockForm.type}
              onChange={(e) => setBlockForm({ ...blockForm, type: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="ip">Single IP Address</option>
              <option value="ip_range">IP Range (CIDR)</option>
              <option value="session">Session ID</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration (hours)
            </label>
            <select
              value={blockForm.duration}
              onChange={(e) => setBlockForm({ ...blockForm, duration: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="1">1 hour</option>
              <option value="6">6 hours</option>
              <option value="24">24 hours</option>
              <option value="168">1 week</option>
              <option value="720">1 month</option>
            </select>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {blockForm.type === 'ip' ? 'IP Address' : 
               blockForm.type === 'ip_range' ? 'IP Range (e.g., ***********/24)' : 
               'Session ID'}
            </label>
            <input
              type="text"
              value={blockForm.identifier}
              onChange={(e) => setBlockForm({ ...blockForm, identifier: e.target.value })}
              placeholder={
                blockForm.type === 'ip' ? '*************' :
                blockForm.type === 'ip_range' ? '***********/24' :
                'session_1234567890_abc_def'
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reason for Blocking
            </label>
            <textarea
              value={blockForm.reason}
              onChange={(e) => setBlockForm({ ...blockForm, reason: e.target.value })}
              placeholder="Describe why this item should be blocked..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="md:col-span-2">
            <AdminButton
              onClick={blockItem}
              variant="danger"
              disabled={loading || !blockForm.identifier.trim() || !blockForm.reason.trim()}
              className="w-full"
            >
              {loading ? 'Blocking...' : (
                <>
                  <Ban className="h-4 w-4 mr-1" />
                  Block {blockForm.type.replace('_', ' ').toUpperCase()}
                </>
              )}
            </AdminButton>
          </div>
        </div>
      </AdminCardWithTitle>

      {/* Enhanced Blocked Items Section */}
      <div className="space-y-4">
        {/* Header with Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex items-center gap-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Ban className="h-5 w-5 mr-2 text-orange-600" />
              Currently Blocked Items
            </h3>
            <span className="text-sm text-gray-500">
              {pagination.total} total items
            </span>
          </div>

          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search blocked items..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
              />
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Filter className="w-4 h-4" />
              Filters
            </button>
          </div>
        </div>

        {/* Filter Controls */}
        {showFilters && (
          <div className="bg-gray-50 p-4 rounded-lg border">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                <AdminFilterSelect
                  value={filters.type}
                  onValueChange={(value) => handleFilterChange('type', value)}
                  placeholder="All Types"
                  options={[
                    { value: "all", label: "All Types", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg> },
                    { value: "ip", label: "IP Address", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" /></svg> },
                    { value: "ip_range", label: "IP Range", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" /></svg> },
                    { value: "session", label: "Session", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" /></svg> }
                  ]}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <AdminFilterSelect
                  value={filters.status}
                  onValueChange={(value) => handleFilterChange('status', value)}
                  placeholder="All Status"
                  options={[
                    { value: "all", label: "All Status", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg> },
                    { value: "active", label: "Active", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /></svg> },
                    { value: "expired", label: "Expired", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-500" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" /></svg> }
                  ]}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Items per page</label>
                <AdminFilterSelect
                  value={pagination.limit.toString()}
                  onValueChange={(value) => setPagination(prev => ({ ...prev, limit: parseInt(value), page: 1 }))}
                  placeholder="Items per page"
                  options={[
                    { value: "5", label: "5 per page", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" /></svg> },
                    { value: "10", label: "10 per page", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" /></svg> },
                    { value: "25", label: "25 per page", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" /></svg> },
                    { value: "50", label: "50 per page", icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" /></svg> }
                  ]}
                  className="w-full"
                  size="sm"
                />
              </div>
            </div>
          </div>
        )}

        {/* Blocked Items Table */}
        <AdminCard className="border-orange-200">
          <AdminCardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <AdminCardTitle>Blocked Items</AdminCardTitle>
                {blockedItems && blockedItems.length > 0 && (
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={selectedItems.size === blockedItems.length && blockedItems.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm text-gray-600">Select All</span>
                  </div>
                )}
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <SortButton field="identifier">Identifier</SortButton>
                <SortButton field="blocked_at">Date Blocked</SortButton>
                <SortButton field="expires_at">Expiry</SortButton>
              </div>
            </div>
          </AdminCardHeader>

          <BulkActionsToolbar />

          <AdminCardContent className="p-0">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-600 mt-2">Loading blocked items...</p>
              </div>
            ) : (
              <div className="space-y-0">
                {blockedItems.length === 0 ? (
                  <div className="text-center py-8">
                    <Ban className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">No blocked items found</p>
                    <p className="text-sm text-gray-400 mt-1">
                      {filters.search || filters.type !== 'all' || filters.status !== 'all'
                        ? 'Try adjusting your filters'
                        : 'No items are currently blocked'}
                    </p>
                  </div>
                ) : (
                  blockedItems.map((item, index) => (
                    <div key={`${item.type}-${item.id}`} className={`flex items-center justify-between p-4 ${index !== 0 ? 'border-t' : ''}`}>
                      <div className="flex items-center gap-4">
                        <input
                          type="checkbox"
                          checked={selectedItems.has(`${item.type}-${item.id}`)}
                          onChange={() => handleSelectItem(`${item.type}-${item.id}`)}
                          className="rounded border-gray-300 flex-shrink-0"
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(item.type)}`}>
                              {item.type.replace('_', ' ').toUpperCase()}
                            </span>
                            <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                              {item.identifier}
                            </code>
                            {isExpired(item.expires_at) && (
                              <span className="px-2 py-1 rounded-full text-xs font-medium text-yellow-600 bg-yellow-100">
                                EXPIRED
                              </span>
                            )}
                          </div>

                          <p className="text-sm text-gray-600 mb-2">{item.reason}</p>

                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>Blocked: {formatDate(item.blocked_at)}</span>
                            <span>By: {item.blocked_by}</span>
                            {item.expires_at && (
                              <span>Expires: {formatDate(item.expires_at)}</span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2 ml-4">
                        <AdminButton
                          onClick={() => unblockItem(item)}
                          variant="secondary"
                          size="sm"
                          disabled={loading}
                          className="flex items-center gap-1"
                        >
                          <CheckCircle className="h-3 w-3" />
                          Unblock
                        </AdminButton>

                        <AdminButton
                          onClick={() => handleDeleteRecord(item)}
                          variant="danger"
                          size="sm"
                          disabled={loading}
                          className="flex items-center gap-1"
                        >
                          <Trash2 className="h-3 w-3" />
                          Delete
                        </AdminButton>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </AdminCardContent>
        </AdminCard>

        {/* Pagination */}
        {blockedItems.length > 0 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
            </div>

            <div className="flex items-center gap-2">
              <AdminButton
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                variant="outline"
                size="sm"
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </AdminButton>

              <span className="px-3 py-1 text-sm text-gray-700">
                Page {pagination.page} of {Math.ceil(pagination.total / pagination.limit)}
              </span>

              <AdminButton
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.hasMore}
                variant="outline"
                size="sm"
              >
                Next
                <ChevronRight className="w-4 h-4" />
              </AdminButton>
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog />
    </div>
  );
}
