'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';

interface TimeRangeSelectorProps {
  value: string;
  onChange: (timeRange: string) => void;
  onCustomRangeChange: (startDate: string, endDate: string) => void;
}

/**
 * Time Range Selector Component
 * 
 * Allows users to select predefined time ranges or custom date ranges
 * for analytics data filtering.
 */
export default function TimeRangeSelector({ 
  value, 
  onChange, 
  onCustomRangeChange 
}: TimeRangeSelectorProps) {
  const [isCustomRange, setIsCustomRange] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [isStartCalendarOpen, setIsStartCalendarOpen] = useState(false);
  const [isEndCalendarOpen, setIsEndCalendarOpen] = useState(false);

  const timeRangeOptions = [
    { value: '1h', label: 'Last Hour' },
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: 'custom', label: 'Custom Range' },
  ];

  /**
   * Handle time range selection
   */
  const handleTimeRangeChange = (newValue: string) => {
    if (newValue === 'custom') {
      setIsCustomRange(true);
    } else {
      setIsCustomRange(false);
      setStartDate(undefined);
      setEndDate(undefined);
      onChange(newValue);
    }
  };

  /**
   * Handle custom date range application
   */
  const handleApplyCustomRange = () => {
    if (startDate && endDate) {
      // Set start date to beginning of day (00:00:00)
      const startOfDay = new Date(startDate);
      startOfDay.setHours(0, 0, 0, 0);

      // Set end date to end of day (23:59:59.999)
      const endOfDay = new Date(endDate);
      endOfDay.setHours(23, 59, 59, 999);

      const startISO = startOfDay.toISOString();
      const endISO = endOfDay.toISOString();
      onCustomRangeChange(startISO, endISO);
      setIsCustomRange(false);
    }
  };

  /**
   * Handle custom range cancellation
   */
  const handleCancelCustomRange = () => {
    setIsCustomRange(false);
    setStartDate(undefined);
    setEndDate(undefined);
    // Reset to default time range
    onChange('24h');
  };

  /**
   * Check if custom range is valid
   */
  const isCustomRangeValid = startDate && endDate && startDate <= endDate;

  return (
    <div className="flex flex-col sm:flex-row gap-2">
      {/* Time Range Selector */}
      <Select value={value} onValueChange={handleTimeRangeChange}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select time range" />
        </SelectTrigger>
        <SelectContent>
          {timeRangeOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Custom Date Range Selectors */}
      {isCustomRange && (
        <div className="flex flex-col sm:flex-row gap-2 p-3 border rounded-lg bg-gray-50">
          <div className="flex flex-col sm:flex-row gap-2">
            {/* Start Date Picker */}
            <div className="flex flex-col gap-1">
              <label className="text-xs font-medium text-gray-600">Start Date</label>
              <Popover open={isStartCalendarOpen} onOpenChange={setIsStartCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-[140px] justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, 'MMM dd, yyyy') : 'Start date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => {
                      setStartDate(date);
                      setIsStartCalendarOpen(false);
                    }}
                    disabled={(date) => date > new Date() || (endDate ? date > endDate : false)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* End Date Picker */}
            <div className="flex flex-col gap-1">
              <label className="text-xs font-medium text-gray-600">End Date</label>
              <Popover open={isEndCalendarOpen} onOpenChange={setIsEndCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-[140px] justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, 'MMM dd, yyyy') : 'End date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => {
                      setEndDate(date);
                      setIsEndCalendarOpen(false);
                    }}
                    disabled={(date) => date > new Date() || (startDate ? date < startDate : false)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 items-end">
            <Button
              size="sm"
              onClick={handleApplyCustomRange}
              disabled={!isCustomRangeValid}
              className="h-9"
            >
              Apply
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleCancelCustomRange}
              className="h-9"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Custom Range Display */}
      {value === 'custom' && !isCustomRange && startDate && endDate && (
        <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg text-sm">
          <CalendarIcon className="h-4 w-4 text-blue-600" />
          <span className="text-blue-800 font-medium">
            {format(startDate, 'MMM dd, yyyy')} - {format(endDate, 'MMM dd, yyyy')}
          </span>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsCustomRange(true)}
            className="h-6 px-2 text-blue-600 hover:text-blue-800"
          >
            Edit
          </Button>
        </div>
      )}
    </div>
  );
}

/**
 * Quick Time Range Buttons Component
 * 
 * Alternative component with button-based time range selection
 */
export function QuickTimeRangeButtons({ 
  value, 
  onChange 
}: { 
  value: string; 
  onChange: (timeRange: string) => void; 
}) {
  const quickRanges = [
    { value: '1h', label: '1H' },
    { value: '24h', label: '24H' },
    { value: '7d', label: '7D' },
    { value: '30d', label: '30D' },
  ];

  return (
    <div className="flex gap-1 p-1 bg-gray-100 rounded-lg">
      {quickRanges.map((range) => (
        <Button
          key={range.value}
          size="sm"
          variant={value === range.value ? 'default' : 'ghost'}
          onClick={() => onChange(range.value)}
          className="h-8 px-3 text-xs"
        >
          {range.label}
        </Button>
      ))}
    </div>
  );
}
