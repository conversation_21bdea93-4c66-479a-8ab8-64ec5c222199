import React from 'react';

interface AdminSkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
  animation?: 'pulse' | 'wave' | 'none';
  width?: string | number;
  height?: string | number;
}

/**
 * A skeleton loader component for admin interfaces
 */
export function AdminSkeleton({
  className = '',
  variant = 'text',
  animation = 'pulse',
  width,
  height
}: AdminSkeletonProps) {
  // Base classes
  const baseClasses = 'bg-gray-200 dark:bg-gray-700';
  
  // Animation classes
  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-shimmer',
    none: ''
  };
  
  // Variant classes
  const variantClasses = {
    text: 'rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-md'
  };
  
  // Default dimensions based on variant
  const getDefaultDimensions = () => {
    switch (variant) {
      case 'text':
        return { width: width || '100%', height: height || '1rem' };
      case 'circular':
        return { width: width || '2.5rem', height: height || '2.5rem' };
      case 'rectangular':
        return { width: width || '100%', height: height || '6rem' };
    }
  };
  
  const dimensions = getDefaultDimensions();
  
  // Combine all classes
  const skeletonClasses = `
    ${baseClasses}
    ${animationClasses[animation]}
    ${variantClasses[variant]}
    ${className}
  `;
  
  return (
    <div 
      className={skeletonClasses}
      style={{
        width: dimensions.width,
        height: dimensions.height
      }}
    />
  );
}

/**
 * A skeleton loader for text lines
 */
export function AdminSkeletonText({
  lines = 3,
  className = '',
  lastLineWidth = '75%',
  spacing = 'mb-2'
}: {
  lines?: number;
  className?: string;
  lastLineWidth?: string | number;
  spacing?: string;
}) {
  return (
    <div className={className}>
      {Array.from({ length: lines }).map((_, index) => (
        <AdminSkeleton
          key={index}
          variant="text"
          className={index < lines - 1 ? spacing : ''}
          width={index === lines - 1 && lastLineWidth !== '100%' ? lastLineWidth : '100%'}
        />
      ))}
    </div>
  );
}

/**
 * A skeleton loader for a card
 */
export function AdminSkeletonCard({
  className = '',
  headerHeight = '2rem',
  contentLines = 3
}: {
  className?: string;
  headerHeight?: string | number;
  contentLines?: number;
}) {
  return (
    <div className={`rounded-lg border border-gray-200 overflow-hidden ${className}`}>
      <AdminSkeleton
        variant="rectangular"
        height={headerHeight}
        className="rounded-none"
      />
      <div className="p-4">
        <AdminSkeletonText lines={contentLines} />
      </div>
    </div>
  );
}
