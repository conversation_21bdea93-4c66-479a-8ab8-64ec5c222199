'use client';

import IPManagement from '@/components/admin/IPManagement';

/**
 * IP Management Page for Secure Admin Portal
 */
export default function IPManagementPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">IP Management & Security</h1>
        <p className="mt-2 text-sm text-gray-600">
          Manage IP blocking, whitelisting, and rate limit violations to protect your VanishPost service from abuse.
        </p>
      </div>

      <IPManagement />
    </div>
  );
}
