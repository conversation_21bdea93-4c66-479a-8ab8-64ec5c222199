/**
 * API route for clearing system caches
 */
import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth/adminAuth';
import { logger } from '@/lib/logging/Logger';
import { clearAllCaches, clearCache, getAllCacheNames } from '@/lib/cacheRegistry';
import { flushAllCaches } from '@/lib/config/adCacheService';

/**
 * POST /api/admin/cache/clear
 *
 * Clear all system caches
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const cacheName = searchParams.get('name');

    let clearedCaches: string[] = [];

    if (cacheName) {
      // Clear specific cache
      if (clearCache(cacheName)) {
        clearedCaches.push(cacheName);
        await logger.info('CACHE_CLEAR', `Cleared cache: ${cacheName}`);
      }
    } else {
      // Clear all caches
      clearedCaches = clearAllCaches();

      // Also clear ad caches
      flushAllCaches();
      clearedCaches.push('adCache');

      await logger.info('CACHE_CLEAR', `Cleared all caches: ${clearedCaches.join(', ')}`);
    }

    return NextResponse.json({
      success: true,
      message: `Successfully cleared ${clearedCaches.length} cache(s)`,
      clearedCaches
    });
  } catch (error) {
    await logger.error('CACHE_CLEAR', `Error clearing caches: ${error instanceof Error ? error.message : String(error)}`, { error });

    return NextResponse.json(
      { success: false, error: 'Failed to clear caches' },
      { status: 500 }
    );
  }
}
