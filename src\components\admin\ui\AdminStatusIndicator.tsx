import React from 'react';

type StatusType = 'online' | 'offline' | 'warning' | 'error' | 'success' | 'loading';

interface AdminStatusIndicatorProps {
  status: StatusType;
  label?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  pulseAnimation?: boolean;
}

/**
 * A status indicator component for admin interfaces
 */
export function AdminStatusIndicator({
  status,
  label,
  size = 'md',
  className = '',
  pulseAnimation = true
}: AdminStatusIndicatorProps) {
  // Status colors
  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
    success: 'bg-green-500',
    loading: 'bg-blue-500'
  };
  
  // Size classes
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };
  
  // Text size classes
  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };
  
  // Animation class
  const animationClass = pulseAnimation ? 'animate-pulse' : '';
  
  return (
    <div className={`inline-flex items-center ${className}`}>
      <div className={`relative rounded-full ${sizeClasses[size]} ${statusColors[status]} ${animationClass}`}>
        {pulseAnimation && (
          <span className={`absolute inset-0 rounded-full ${statusColors[status]} opacity-75 animate-ping`}></span>
        )}
      </div>
      {label && (
        <span className={`ml-2 ${textSizeClasses[size]} text-gray-700`}>
          {label}
        </span>
      )}
    </div>
  );
}
