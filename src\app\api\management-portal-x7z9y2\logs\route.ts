/**
 * API routes for system logs
 */
import { NextRequest, NextResponse } from 'next/server';
import { getLogs, clearLogs } from '@/lib/logging/dbLogger';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/management-portal-x7z9y2/logs
 *
 * Get system logs with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const level = searchParams.get('level');
    const category = searchParams.get('category');
    const limit = parseInt(searchParams.get('limit') || '100', 10);
    const offset = parseInt(searchParams.get('offset') || '0', 10);

    // Parse date filters if provided
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (searchParams.has('startDate')) {
      startDate = new Date(searchParams.get('startDate')!);
    }

    if (searchParams.has('endDate')) {
      endDate = new Date(searchParams.get('endDate')!);
    }

    // Get logs with filters
    const logs = await getLogs({
      level: level || undefined,
      category: category || undefined,
      startDate,
      endDate,
      limit,
      offset
    });

    return NextResponse.json({
      success: true,
      data: logs,
      pagination: {
        limit,
        offset
      }
    });
  } catch (error) {
    logError('api', 'Error getting logs', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to get logs' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/management-portal-x7z9y2/logs
 *
 * Clear logs with optional filtering
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const level = searchParams.get('level');
    const category = searchParams.get('category');

    // Parse olderThan filter if provided
    let olderThan: Date | undefined;

    if (searchParams.has('olderThan')) {
      olderThan = new Date(searchParams.get('olderThan')!);
    }

    // Clear logs with filters
    const success = await clearLogs({
      level: level || undefined,
      category: category || undefined,
      olderThan
    });

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to clear logs' },
        { status: 500 }
      );
    }

    logInfo('api', 'Cleared logs', { level, category, olderThan });

    return NextResponse.json({
      success: true,
      message: 'Logs cleared successfully'
    });
  } catch (error) {
    logError('api', 'Error clearing logs', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to clear logs' },
      { status: 500 }
    );
  }
}
