// DKIM DNS Record Formatting Utilities - Isolated implementation
import { DkimRecord } from '@/types/dkim';
import { publicKeyToBase64 } from './keyGeneration';

/**
 * Format DKIM public key as DNS TXT record
 * @param domain - Domain name
 * @param selector - DKIM selector
 * @param publicKeyPem - Public key in PEM format
 * @param keyStrength - Key strength in bits
 * @returns DkimRecord - Formatted DKIM record
 */
export function formatDkimRecord(
  domain: string,
  selector: string,
  publicKeyPem: string,
  keyStrength: 1024 | 2048
): DkimRecord {
  try {
    // Convert public key to base64 format
    const publicKeyBase64 = publicKeyToBase64(publicKeyPem);

    // Create DKIM DNS record value
    const dnsRecord = createDkimDnsRecord(publicKeyBase64, keyStrength);

    // Create record name (selector._domainkey.domain)
    const recordName = `${selector}._domainkey.${domain}`;

    return {
      selector,
      domain,
      publicKey: publicKeyBase64,
      dnsRecord,
      recordName,
      keyStrength,
    };
  } catch (error) {
    console.error('DKIM record formatting error:', error);
    throw new Error(`Failed to format DKIM record: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create DKIM DNS TXT record value
 * @param publicKeyBase64 - Base64 encoded public key
 * @param keyStrength - Key strength in bits
 * @returns Formatted DNS TXT record value
 */
export function createDkimDnsRecord(publicKeyBase64: string, keyStrength: 1024 | 2048): string {
  try {
    // DKIM record components
    const components = [
      'v=DKIM1', // Version
      'k=rsa', // Key type
      `p=${publicKeyBase64}`, // Public key
    ];

    // Add optional components based on key strength
    if (keyStrength === 2048) {
      // For 2048-bit keys, we can add additional security flags
      components.splice(2, 0, 't=s'); // Testing flag (strict mode)
    }

    return components.join('; ');
  } catch (error) {
    console.error('DKIM DNS record creation error:', error);
    throw new Error(`Failed to create DKIM DNS record: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate setup instructions for DKIM implementation
 * @param dkimRecord - DKIM record information
 * @returns Array of instruction steps
 */
export function generateDkimInstructions(dkimRecord: DkimRecord): string[] {
  const { recordName, dnsRecord, domain, selector } = dkimRecord;

  return [
    `Add a TXT record to your DNS settings for domain: ${domain}`,
    `Record Name: ${recordName}`,
    `Record Type: TXT`,
    `Record Value: ${dnsRecord}`,
    `TTL: 3600 (or your DNS provider's default)`,
    '',
    'DNS Provider Specific Instructions:',
    '• For Cloudflare: Go to DNS > Records > Add record',
    '• For GoDaddy: Go to DNS Management > Add new record',
    '• For Namecheap: Go to Advanced DNS > Add new record',
    '• For Route 53: Go to Hosted zones > Create record',
    '',
    'Email Server Configuration:',
    `• Configure your email server to use selector "${selector}"`,
    '• Install the private key in your email server\'s DKIM configuration',
    '• Test email sending after DNS propagation (up to 48 hours)',
    '',
    'Verification:',
    '• Use the DNS validation tool to verify record propagation',
    '• Send test emails to check DKIM signature validation',
    '• Monitor email deliverability and authentication reports',
  ];
}

/**
 * Validate DKIM record format
 * @param dnsRecord - DNS record value to validate
 * @returns boolean - True if valid DKIM record format
 */
export function validateDkimRecordFormat(dnsRecord: string): boolean {
  try {
    // Check for required DKIM components
    const requiredComponents = ['v=DKIM1', 'k=rsa', 'p='];

    return requiredComponents.every(component =>
      dnsRecord.includes(component)
    );
  } catch (error) {
    console.error('DKIM record validation error:', error);
    return false;
  }
}

/**
 * Parse DKIM DNS record to extract components
 * @param dnsRecord - DNS record value to parse
 * @returns Object with parsed DKIM components
 */
export function parseDkimRecord(dnsRecord: string): Record<string, string> {
  try {
    const components: Record<string, string> = {};

    // Split by semicolon and parse key-value pairs
    const parts = dnsRecord.split(';').map(part => part.trim());

    for (const part of parts) {
      const [key, value] = part.split('=', 2);
      if (key && value) {
        components[key.trim()] = value.trim();
      }
    }

    return components;
  } catch (error) {
    console.error('DKIM record parsing error:', error);
    return {};
  }
}

/**
 * Format record name for display
 * @param domain - Domain name
 * @param selector - DKIM selector
 * @returns Formatted record name
 */
export function formatRecordName(domain: string, selector: string): string {
  return `${selector}._domainkey.${domain}`;
}

/**
 * Validate domain, selector, and key strength
 * @param domain - Domain to validate
 * @param selector - Selector to validate
 * @param keyStrength - Key strength to validate (optional)
 * @returns Object with validation results
 */
export function validateDkimInputs(domain: string, selector: string, keyStrength?: number): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate domain format
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
  if (!domainRegex.test(domain)) {
    errors.push('Invalid domain format');
  }

  // Validate selector format
  const selectorRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,62}[a-zA-Z0-9]$|^[a-zA-Z0-9]$/;
  if (!selectorRegex.test(selector)) {
    errors.push('Invalid selector format. Use alphanumeric characters and hyphens only.');
  }

  // Check selector length
  if (selector.length > 63) {
    errors.push('Selector must be 63 characters or less');
  }

  // Validate key strength if provided
  if (keyStrength !== undefined && keyStrength !== 1024 && keyStrength !== 2048) {
    errors.push('Key strength must be 1024 or 2048 bits');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
