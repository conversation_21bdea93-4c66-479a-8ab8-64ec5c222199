'use client';

import React from 'react';
import * as AccordionPrimitive from '@radix-ui/react-accordion';
import { ChevronDownIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * shadcn/ui Accordion Component adapted for VanishPost
 * 
 * Collapsible content sections perfect for organizing complex configuration
 * pages and admin interface sections. Adapted for VanishPost's theme.
 */

function Accordion({
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Root>) {
  return <AccordionPrimitive.Root data-slot="accordion" {...props} />;
}

function AccordionItem({
  className,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Item>) {
  return (
    <AccordionPrimitive.Item
      data-slot="accordion-item"
      className={cn(
        "border-b border-gray-200 last:border-b-0",
        "dark:border-gray-700", // Dark mode support
        className
      )}
      {...props}
    />
  );
}

function AccordionTrigger({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {
  return (
    <AccordionPrimitive.Header className="flex">
      <AccordionPrimitive.Trigger
        data-slot="accordion-trigger"
        className={cn(
          // Base styles with VanishPost theme
          "flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none",
          // Focus and hover states
          "focus-visible:border-blue-500 focus-visible:ring-blue-500/50 focus-visible:ring-[3px]",
          "hover:text-blue-600 hover:underline",
          // Disabled state
          "disabled:pointer-events-none disabled:opacity-50",
          // Icon rotation
          "[&[data-state=open]>svg]:rotate-180",
          // VanishPost colors
          "text-gray-900 dark:text-gray-100",
          className
        )}
        {...props}
      >
        {children}
        <ChevronDownIcon className="text-gray-500 pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200" />
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  );
}

function AccordionContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Content>) {
  return (
    <AccordionPrimitive.Content
      data-slot="accordion-content"
      className="data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm"
      {...props}
    >
      <div className={cn("pt-0 pb-4 text-gray-700 dark:text-gray-300", className)}>
        {children}
      </div>
    </AccordionPrimitive.Content>
  );
}

/**
 * Enhanced Accordion variants for VanishPost admin use cases
 */

// Admin Accordion with enhanced styling for configuration sections
interface AdminAccordionProps extends React.ComponentProps<typeof AccordionPrimitive.Root> {
  variant?: 'default' | 'card' | 'outline';
}

export function AdminAccordion({ 
  className, 
  variant = 'default', 
  children, 
  ...props 
}: AdminAccordionProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'card':
        return 'bg-white rounded-lg shadow-sm border border-gray-200 p-4';
      case 'outline':
        return 'border border-gray-200 rounded-lg p-4';
      default:
        return '';
    }
  };

  return (
    <Accordion 
      className={cn(getVariantClasses(), className)} 
      {...props}
    >
      {children}
    </Accordion>
  );
}

// Configuration Section Accordion for admin settings
interface ConfigSectionProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  value: string;
  defaultOpen?: boolean;
}

export function ConfigSection({ 
  title, 
  description, 
  icon, 
  children, 
  value,
  defaultOpen = false 
}: ConfigSectionProps) {
  return (
    <AccordionItem value={value}>
      <AccordionTrigger className="hover:no-underline">
        <div className="flex items-start space-x-3 text-left">
          {icon && (
            <div className="flex-shrink-0 mt-0.5 text-blue-600">
              {icon}
            </div>
          )}
          <div>
            <h3 className="font-medium text-gray-900">{title}</h3>
            {description && (
              <p className="text-sm text-gray-500 mt-1">{description}</p>
            )}
          </div>
        </div>
      </AccordionTrigger>
      <AccordionContent>
        <div className="pl-8 space-y-4">
          {children}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
}

// Settings Group for organizing related settings
interface SettingsGroupProps {
  title: string;
  sections: Array<{
    value: string;
    title: string;
    description?: string;
    icon?: React.ReactNode;
    content: React.ReactNode;
  }>;
  defaultValue?: string;
  type?: 'single' | 'multiple';
}

export function SettingsGroup({ 
  title, 
  sections, 
  defaultValue,
  type = 'single' 
}: SettingsGroupProps) {
  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
      <AdminAccordion 
        variant="card"
        type={type}
        collapsible={type === 'single'}
        defaultValue={defaultValue}
      >
        {sections.map((section) => (
          <ConfigSection
            key={section.value}
            value={section.value}
            title={section.title}
            description={section.description}
            icon={section.icon}
          >
            {section.content}
          </ConfigSection>
        ))}
      </AdminAccordion>
    </div>
  );
}

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent };
