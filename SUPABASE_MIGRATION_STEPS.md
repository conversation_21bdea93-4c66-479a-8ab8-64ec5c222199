# Supabase Migration Steps

Follow these steps to complete the migration from MySQL to Supabase:

## Step 1: Create the Supabase Tables

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Navigate to your project (https://pfxzwsjgnpycqopkfyuh.supabase.co)
3. Go to the SQL Editor
4. Create a new query
5. Copy and paste the SQL from `scripts/create-supabase-tables.sql`
6. Run the query

## Step 2: Verify the Tables Were Created

Run the test script to verify that the tables were created successfully:

```bash
npm run test-supabase
```

You should see output indicating that both tables exist and that you can successfully insert and delete data.

## Step 3: Migrate Data from MySQL to Supabase

Run the migration script to transfer existing data:

```bash
npm run migrate-to-supabase
```

This will copy all temporary emails and analytics events from MySQL to Supabase.

## Step 4: Test the Application

Start the application and test that everything works with Supabase:

```bash
npm run dev
```

Verify that:
- You can generate new email addresses
- You can view emails in the inbox
- The admin dashboard shows analytics data

## Step 5: Update Environment Variables in Production

If everything works locally, update your production environment variables to use Supabase:

```
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Guerrilla Database Configuration
GUERRILLA_DB_HOST=your-guerrilla-db-host
GUERRILLA_DB_USER=your-guerrilla-db-user
GUERRILLA_DB_PASSWORD=your-guerrilla-db-password
GUERRILLA_DB_NAME=your-guerrilla-db-name
GUERRILLA_DB_PORT=3306
```

## Troubleshooting

### Common Issues

1. **Table Creation Errors**
   - Make sure you have the necessary permissions in Supabase
   - Check for syntax errors in the SQL

2. **Migration Errors**
   - Ensure MySQL is running and accessible
   - Check that the MySQL connection parameters are correct

3. **Application Errors**
   - Check browser console for JavaScript errors
   - Check server logs for backend errors

### Rollback Plan

If you encounter issues, you can roll back to MySQL by:

1. Reverting the code changes
2. Restoring the original `.env.local` file
3. Restarting the application
