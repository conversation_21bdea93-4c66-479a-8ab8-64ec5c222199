'use client';

import { Metadata } from 'next';
import React, { useState, useEffect, useRef } from 'react';
import { generateSessionId } from '@/lib/utils/uuid';
import { useDmarcGenerator } from '@/hooks/useDmarcGenerator';
import { DmarcFormData, DmarcPolicy } from '@/types/dmarc';
import DmarcGeneratorForm from '@/components/tools/dmarc-generator/DmarcGeneratorForm';
import DmarcResults from '@/components/tools/dmarc-generator/DmarcResults';
import Breadcrumbs from '@/components/seo/Breadcrumbs';

// Note: Metadata export moved to metadata.ts file for client components
export default function DmarcGeneratorPage() {
  const { generateRecord, validateDns, result, validation, loading, error, clearError } = useDmarcGenerator();
  const [validating, setValidating] = useState(false);
  const resultsRef = useRef<HTMLDivElement>(null);

  const handleGenerate = async (formData: DmarcFormData) => {
    clearError();

    // Generate session ID for tracking (browser-compatible)
    const sessionId = generateSessionId();

    await generateRecord({
      domain: formData.domain,
      policy: formData.policy,
      rua: formData.rua,
      pct: formData.pct,
      sp: formData.sp,
      adkim: formData.adkim,
      aspf: formData.aspf,
      sessionId,
    });
  };

  const handleValidate = async (domain: string, policy: string) => {
    setValidating(true);
    try {
      await validateDns(domain, policy as DmarcPolicy);
    } finally {
      setValidating(false);
    }
  };

  // Smooth scroll to results when they appear
  useEffect(() => {
    if (result && resultsRef.current) {
      setTimeout(() => {
        resultsRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }, 100);
    }
  }, [result]);

  // Breadcrumb items
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Tools', url: '/tools' },
    { name: 'DMARC Generator', url: '/tools/dmarc-generator', current: true },
  ];

  return (
    <div className="min-h-screen bg-[#fbfaf8]">
      <div className="container mx-auto px-4 py-8 max-w-5xl">
        {/* Breadcrumbs */}
        <div className="mb-6">
          <Breadcrumbs items={breadcrumbItems} />
        </div>

        {/* Modern Header with Enhanced Visual Hierarchy */}
        <div className="mb-10">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-[#956b50] to-[#4a3728] rounded-2xl mb-6 shadow-lg flex-shrink-0" style={{ aspectRatio: '1' }}>
              <svg className="w-8 h-8 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#1b130e] mb-4 tracking-tight">
              DMARC Generator
            </h1>
            <p className="text-xl text-[#4a3728] max-w-2xl mx-auto leading-relaxed">
              Create comprehensive DMARC policy records for email authentication, reporting, and domain protection
            </p>
          </div>

          {/* Feature Highlights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div className="bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-4 text-center">
              <div className="w-10 h-10 bg-[#956b50]/10 rounded-lg flex items-center justify-center mx-auto mb-3 flex-shrink-0" style={{ aspectRatio: '1' }}>
                <svg className="w-5 h-5 text-[#956b50] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-semibold text-[#1b130e] text-sm">Policy Creation</h3>
              <p className="text-xs text-[#4a3728] mt-1">Flexible policy options</p>
            </div>
            <div className="bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-4 text-center">
              <div className="w-10 h-10 bg-[#956b50]/10 rounded-lg flex items-center justify-center mx-auto mb-3 flex-shrink-0" style={{ aspectRatio: '1' }}>
                <svg className="w-5 h-5 text-[#956b50] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="font-semibold text-[#1b130e] text-sm">Email Reporting</h3>
              <p className="text-xs text-[#4a3728] mt-1">Comprehensive analytics</p>
            </div>
            <div className="bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-xl p-4 text-center">
              <div className="w-10 h-10 bg-[#956b50]/10 rounded-lg flex items-center justify-center mx-auto mb-3 flex-shrink-0" style={{ aspectRatio: '1' }}>
                <svg className="w-5 h-5 text-[#956b50] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="font-semibold text-[#1b130e] text-sm">Domain Protection</h3>
              <p className="text-xs text-[#4a3728] mt-1">Anti-spoofing security</p>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="h-5 w-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <span className="text-red-700 font-medium">Error:</span>
              <span className="text-red-600 ml-2">{error}</span>
            </div>
            <button
              onClick={clearError}
              className="mt-2 text-red-600 hover:text-red-800 text-sm underline"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Validation Results */}
        {validation && (
          <div className={`mb-6 border rounded-lg p-4 ${
            validation.isValid
              ? 'bg-[#66b077] bg-opacity-10 border-[#66b077]'
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-center mb-2">
              <svg
                className={`h-5 w-5 mr-2 ${validation.isValid ? 'text-[#66b077]' : 'text-red-500'}`}
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                {validation.isValid ? (
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                ) : (
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                )}
              </svg>
              <span className={`font-medium ${validation.isValid ? 'text-[#1b130e]' : 'text-red-700'}`}>
                DNS Validation {validation.isValid ? 'Successful' : 'Failed'}
              </span>
            </div>

            {validation.exists && validation.records.length > 0 && (
              <div className="mt-3">
                <p className="text-sm font-medium text-[#1b130e] mb-1">Found DNS Records:</p>
                {validation.records.map((record, index) => (
                  <div key={index} className="bg-white bg-opacity-50 p-2 rounded text-xs font-mono break-all">
                    {record}
                  </div>
                ))}
              </div>
            )}

            {validation.parsedPolicy && (
              <div className="mt-3">
                <p className="text-sm font-medium text-[#1b130e] mb-1">Parsed Policy:</p>
                <div className="bg-white bg-opacity-50 p-2 rounded text-sm">
                  <span className="font-medium">Policy:</span> {validation.parsedPolicy.policy?.toUpperCase() || 'Unknown'}
                  {validation.parsedPolicy.pct && (
                    <span className="ml-4"><span className="font-medium">Coverage:</span> {validation.parsedPolicy.pct}%</span>
                  )}
                  {validation.parsedPolicy.rua && (
                    <div className="mt-1">
                      <span className="font-medium">Reports to:</span> {validation.parsedPolicy.rua}
                    </div>
                  )}
                </div>
              </div>
            )}

            {validation.errors.length > 0 && (
              <div className="mt-3">
                <p className="text-sm font-medium text-red-700 mb-1">Issues Found:</p>
                <ul className="text-sm text-red-600 space-y-1">
                  {validation.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            <p className="text-xs text-gray-600 mt-2">
              Last checked: {new Date(validation.lastChecked).toLocaleString()}
            </p>
          </div>
        )}

        {/* Main Content */}
        <div className="space-y-8">
          {/* Form */}
          <DmarcGeneratorForm
            onSubmit={handleGenerate}
            loading={loading}
          />

          {/* Results */}
          {result && (
            <div ref={resultsRef}>
              <DmarcResults
                result={result}
                loading={loading}
                onValidate={handleValidate}
                validating={validating}
              />
            </div>
          )}
        </div>

        {/* Information Section */}
        <div className="mt-12 bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-6 shadow-sm transition-all duration-300 hover:bg-white/90 hover:shadow-md">
          <h2 className="text-xl font-semibold text-[#1b130e] mb-4">
            About DMARC Authentication
          </h2>
          <div className="prose prose-sm text-[#4a3728] max-w-none">
            <p className="mb-4">
              <strong>Domain-based Message Authentication, Reporting, and Conformance (DMARC)</strong> is
              an email authentication protocol that builds upon SPF and DKIM to provide domain owners
              with the ability to protect their domain from unauthorized use.
            </p>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-[#1b130e] mb-2">How DMARC Works:</h3>
                <ul className="space-y-1 text-sm">
                  <li>• Builds on existing SPF and DKIM authentication</li>
                  <li>• Provides policy instructions for email receivers</li>
                  <li>• Generates reports on email authentication results</li>
                  <li>• Enables gradual policy enforcement</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-[#1b130e] mb-2">Benefits:</h3>
                <ul className="space-y-1 text-sm">
                  <li>• Protection against email spoofing and phishing</li>
                  <li>• Improved email deliverability</li>
                  <li>• Visibility into email authentication failures</li>
                  <li>• Enhanced brand protection</li>
                </ul>
              </div>
            </div>
            <div className="mt-6">
              <h3 className="font-semibold text-[#1b130e] mb-2">Policy Types:</h3>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="bg-transparent border border-[#66b077] p-3 rounded">
                  <h4 className="font-medium text-[#66b077] mb-1">None (p=none)</h4>
                  <p className="text-[#4a3728]">Monitor mode - no action taken, reports generated</p>
                </div>
                <div className="bg-transparent border border-[#f59e0b] p-3 rounded">
                  <h4 className="font-medium text-[#f59e0b] mb-1">Quarantine (p=quarantine)</h4>
                  <p className="text-[#4a3728]">Failed emails sent to spam/junk folder</p>
                </div>
                <div className="bg-transparent border border-red-500 p-3 rounded">
                  <h4 className="font-medium text-red-600 mb-1">Reject (p=reject)</h4>
                  <p className="text-[#4a3728]">Failed emails are rejected and not delivered</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
