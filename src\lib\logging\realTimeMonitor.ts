import { updateConfig, getConfig } from '../config/configService';
// Import logger after initialization to avoid circular dependency
let logger: any;

/**
 * Real-time log monitoring and alerting system
 */
export class RealTimeMonitor {
  private static instance: RealTimeMonitor;
  private alertThresholds: Record<string, number> = {};
  private alertCounts: Record<string, { count: number; timestamp: number }> = {};
  private alertCallbacks: Array<(alert: AlertNotification) => void> = [];
  private isInitialized = false;
  private recentAlerts: Map<string, number> = new Map(); // Track recent alerts to prevent duplicates
  private alertCooldownMs = 5 * 60 * 1000; // 5 minute cooldown between same alerts

  /**
   * Get the singleton instance
   */
  public static getInstance(): RealTimeMonitor {
    if (!RealTimeMonitor.instance) {
      RealTimeMonitor.instance = new RealTimeMonitor();
    }
    return RealTimeMonitor.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}

  /**
   * Initialize the monitor with configuration
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Import logger here to avoid circular dependency
      if (!logger) {
        const { logger: loggerInstance } = await import('./Logger');
        logger = loggerInstance;
      }

      // Load alert thresholds from configuration
      const alertConfig = await getConfig('alertThresholds');

      if (alertConfig) {
        this.alertThresholds = alertConfig;
      } else {
        // Default thresholds if not configured
        this.alertThresholds = {
          'ERROR': 5,  // 5 errors in the time window triggers an alert
          'API_ERROR': 10,  // 10 API errors in the time window triggers an alert
          'DATABASE_ERROR': 3,  // 3 database errors in the time window triggers an alert
          'AUTHENTICATION_FAILURE': 10  // 10 auth failures in the time window triggers an alert
        };

        // Save default thresholds to configuration
        await updateConfig('alertThresholds', this.alertThresholds);
      }

      // Start the cleanup interval for alert counts
      setInterval(() => this.cleanupAlertCounts(), 60 * 60 * 1000); // Clean up every hour

      this.isInitialized = true;
      await logger.info('MONITOR_INIT', 'Real-time monitor initialized');
    } catch (error) {
      // If logger is not available, log to console
      if (logger) {
        await logger.error('MONITOR_INIT', `Failed to initialize real-time monitor: ${error}`);
      } else {
        console.error(`Failed to initialize real-time monitor: ${error}`);
      }
      throw error;
    }
  }

  /**
   * Register a callback for alerts
   * @param callback Function to call when an alert is triggered
   */
  public registerAlertCallback(callback: (alert: AlertNotification) => void): void {
    this.alertCallbacks.push(callback);
  }

  /**
   * Process a log entry and trigger alerts if thresholds are exceeded
   * @param level Log level
   * @param category Log category
   * @param message Log message
   * @param metadata Additional metadata
   */
  public async processLogEntry(
    level: string,
    category: string,
    message: string,
    metadata?: any
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    // Import logger if not already imported
    if (!logger) {
      try {
        const { logger: loggerInstance } = await import('./Logger');
        logger = loggerInstance;
      } catch (error) {
        console.error('Failed to import logger:', error);
      }
    }

    try {
      // Check if this log entry should trigger an alert
      if (level === 'error' || level === 'critical') {
        // Increment error count for the category
        this.incrementAlertCount(category);

        // Also increment the general ERROR count
        this.incrementAlertCount('ERROR');

        // Check if thresholds are exceeded
        await this.checkAlertThresholds(category, level, message, metadata);

        if (category !== 'ERROR') {
          await this.checkAlertThresholds('ERROR', level, message, metadata);
        }
      }
    } catch (error) {
      // Don't use the logger here to avoid infinite recursion
      console.error('Error processing log entry for alerts:', error);
    }
  }

  /**
   * Increment the alert count for a category
   * @param category Category to increment
   */
  private incrementAlertCount(category: string): void {
    const now = Date.now();
    const timeWindow = 15 * 60 * 1000; // 15 minutes

    if (!this.alertCounts[category]) {
      this.alertCounts[category] = { count: 0, timestamp: now };
    }

    // Reset count if outside the time window
    if (now - this.alertCounts[category].timestamp > timeWindow) {
      this.alertCounts[category] = { count: 0, timestamp: now };
    }

    // Increment the count
    this.alertCounts[category].count++;
  }

  /**
   * Check if alert thresholds are exceeded
   * @param category Log category
   * @param level Log level
   * @param message Log message
   * @param metadata Additional metadata
   */
  private async checkAlertThresholds(
    category: string,
    level: string,
    message: string,
    metadata?: any
  ): Promise<void> {
    // Check if we have a threshold for this category
    if (this.alertThresholds[category]) {
      const threshold = this.alertThresholds[category];
      const count = this.alertCounts[category]?.count || 0;

      // If threshold is exceeded, trigger an alert
      if (count >= threshold) {
        // Create alert key for deduplication
        const alertKey = `${category}_${threshold}`;
        const now = Date.now();
        const lastAlertTime = this.recentAlerts.get(alertKey) || 0;

        // Check if we're in cooldown period to prevent spam
        if (now - lastAlertTime < this.alertCooldownMs) {
          console.log(`Alert suppressed for ${category} - still in cooldown period`);
          return; // Skip this alert to prevent spam
        }

        // Update last alert time
        this.recentAlerts.set(alertKey, now);

        const alert: AlertNotification = {
          category,
          level,
          message: `Alert threshold exceeded: ${count} ${category} events in the last 15 minutes`,
          count,
          threshold,
          timestamp: new Date().toISOString(),
          sampleMessage: message,
          metadata
        };

        // Trigger callbacks
        this.triggerAlertCallbacks(alert);

        // IMPORTANT: Do NOT log ALERT_TRIGGERED events to prevent recursive loops
        // Instead, just log to console for debugging
        console.warn(`🚨 ALERT: ${alert.message}`, {
          category,
          count,
          threshold,
          sampleMessage: message
        });

        // Reset the count to avoid repeated alerts
        this.alertCounts[category].count = 0;
      }
    }
  }

  /**
   * Trigger all registered alert callbacks
   * @param alert Alert notification
   */
  private triggerAlertCallbacks(alert: AlertNotification): void {
    for (const callback of this.alertCallbacks) {
      try {
        callback(alert);
      } catch (error) {
        // Don't use the logger here to avoid potential infinite recursion
        console.error('Error in alert callback:', error);
      }
    }
  }

  /**
   * Clean up old alert counts and recent alerts
   */
  private cleanupAlertCounts(): void {
    const now = Date.now();
    const timeWindow = 24 * 60 * 60 * 1000; // 24 hours

    // Clean up old alert counts
    for (const category in this.alertCounts) {
      if (now - this.alertCounts[category].timestamp > timeWindow) {
        delete this.alertCounts[category];
      }
    }

    // Clean up old recent alerts (older than cooldown period)
    for (const [alertKey, timestamp] of this.recentAlerts.entries()) {
      if (now - timestamp > this.alertCooldownMs) {
        this.recentAlerts.delete(alertKey);
      }
    }
  }

  /**
   * Update alert thresholds
   * @param thresholds New thresholds
   */
  public async updateAlertThresholds(thresholds: Record<string, number>): Promise<void> {
    this.alertThresholds = { ...this.alertThresholds, ...thresholds };
    await updateConfig('alertThresholds', this.alertThresholds);

    // Import logger if not already imported
    if (!logger) {
      try {
        const { logger: loggerInstance } = await import('./Logger');
        logger = loggerInstance;
      } catch (error) {
        console.error('Failed to import logger:', error);
      }
    }

    if (logger) {
      await logger.info('MONITOR_CONFIG', 'Alert thresholds updated', { thresholds });
    } else {
      console.info('MONITOR_CONFIG: Alert thresholds updated', { thresholds });
    }
  }

  /**
   * Get current alert thresholds
   */
  public getAlertThresholds(): Record<string, number> {
    return { ...this.alertThresholds };
  }

  /**
   * Get current alert counts
   */
  public getAlertCounts(): Record<string, { count: number; timestamp: number }> {
    return { ...this.alertCounts };
  }
}

/**
 * Alert notification interface
 */
export interface AlertNotification {
  category: string;
  level: string;
  message: string;
  count: number;
  threshold: number;
  timestamp: string;
  sampleMessage: string;
  metadata?: any;
}

// Export singleton instance
export const realTimeMonitor = RealTimeMonitor.getInstance();
