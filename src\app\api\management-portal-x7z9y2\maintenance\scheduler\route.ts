/**
 * API route for managing the maintenance scheduler
 * 
 * This API provides functionality to start, stop, and check the status of the maintenance scheduler.
 */
import { NextRequest, NextResponse } from 'next/server';
import { logInfo, logError } from '@/lib/logging';
import { 
  startMaintenanceScheduler, 
  stopMaintenanceScheduler, 
  isMaintenanceSchedulerRunning 
} from '@/lib/maintenance/maintenanceScheduler';
import { getConfig, updateConfig } from '@/lib/config/configService';

/**
 * POST /api/management-portal-x7z9y2/maintenance/scheduler
 * 
 * Start or stop the maintenance scheduler
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, intervalHours } = body;
    
    if (action === 'start') {
      // Validate interval
      if (!intervalHours || intervalHours <= 0) {
        return NextResponse.json({
          success: false,
          error: 'Invalid maintenance interval'
        }, { status: 400 });
      }
      
      // Update configuration
      await updateConfig('maintenanceIntervalHours', intervalHours);
      
      // Start scheduler
      const started = await startMaintenanceScheduler();
      
      if (!started) {
        return NextResponse.json({
          success: false,
          error: 'Failed to start maintenance scheduler'
        }, { status: 500 });
      }
      
      logInfo('maintenance', 'Maintenance scheduler started', { intervalHours });
      
      return NextResponse.json({
        success: true,
        message: 'Maintenance scheduler started',
        intervalHours
      });
    } else if (action === 'stop') {
      // Stop scheduler
      stopMaintenanceScheduler();
      
      logInfo('maintenance', 'Maintenance scheduler stopped');
      
      return NextResponse.json({
        success: true,
        message: 'Maintenance scheduler stopped'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }
  } catch (error) {
    logError('maintenance', 'Error managing maintenance scheduler', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

/**
 * GET /api/management-portal-x7z9y2/maintenance/scheduler
 * 
 * Get the status of the maintenance scheduler
 */
export async function GET(request: NextRequest) {
  try {
    const running = isMaintenanceSchedulerRunning();
    const intervalHours = await getConfig('maintenanceIntervalHours');
    
    // Calculate next run time if running
    let nextRunAt = null;
    if (running && intervalHours) {
      nextRunAt = new Date(Date.now() + intervalHours * 60 * 60 * 1000).toISOString();
    }
    
    return NextResponse.json({
      success: true,
      status: {
        running,
        intervalHours,
        nextRunAt
      }
    });
  } catch (error) {
    logError('maintenance', 'Error getting maintenance scheduler status', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
