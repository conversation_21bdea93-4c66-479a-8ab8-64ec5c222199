'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// Use dynamic import with ssr: false inside a client component
const AdSenseScript = dynamic(() => import('@/components/AdSenseScript'), { 
  ssr: false,
  loading: () => null
});

interface AdSenseScriptLoaderProps {
  clientId: string;
}

/**
 * AdSenseScriptLoader Component
 * 
 * A client component wrapper that dynamically imports the AdSenseScript component.
 * This pattern allows us to use dynamic imports with { ssr: false } in the App Router
 * by ensuring the dynamic import happens within a Client Component.
 * 
 * @param clientId The Google AdSense publisher ID (ca-pub-XXXXXXXXXXXXXXXX)
 */
export default function AdSenseScriptLoader({ clientId }: AdSenseScriptLoaderProps) {
  const [mounted, setMounted] = useState(false);
  
  // Only render on client-side
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted) {
    return null;
  }
  
  return <AdSenseScript clientId={clientId} />;
}
