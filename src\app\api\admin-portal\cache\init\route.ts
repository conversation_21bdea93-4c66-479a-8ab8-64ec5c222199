/**
 * API route for initializing caches
 */
import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/logging/Logger';
import { initializeCaches } from '@/lib/cacheInit';

/**
 * POST /api/admin-portal/cache/init
 * 
 * Initialize all caches in the application
 */
export async function POST(request: NextRequest) {
  try {
    // Initialize all caches
    const caches = await initializeCaches();
    
    // Log initialization
    await logger.info('CACHE_INIT', `Initialized ${caches.length} caches`);
    
    return NextResponse.json({
      success: true,
      message: `Successfully initialized ${caches.length} caches`,
      caches
    });
  } catch (error) {
    await logger.error('CACHE_INIT', `Error initializing caches: ${error instanceof Error ? error.message : String(error)}`, { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to initialize caches' },
      { status: 500 }
    );
  }
}
