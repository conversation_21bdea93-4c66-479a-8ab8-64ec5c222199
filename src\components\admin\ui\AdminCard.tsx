import React from 'react';

interface AdminCardProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * A styled card component for admin interfaces
 */
export function AdminCard({ children, className = '' }: AdminCardProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden transition-shadow duration-300 hover:shadow-md ${className}`}>
      {children}
    </div>
  );
}

interface AdminCardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Header section for AdminCard
 */
export function AdminCardHeader({ children, className = '' }: AdminCardHeaderProps) {
  return (
    <div className={`p-5 border-b border-gray-200 ${className}`}>
      {children}
    </div>
  );
}

interface AdminCardContentProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Content section for AdminCard
 */
export function AdminCardContent({ children, className = '' }: AdminCardContentProps) {
  return (
    <div className={`p-5 ${className}`}>
      {children}
    </div>
  );
}

interface AdminCardFooterProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Footer section for AdminCard
 */
export function AdminCardFooter({ children, className = '' }: AdminCardFooterProps) {
  return (
    <div className={`p-4 bg-gray-50 border-t border-gray-200 ${className}`}>
      {children}
    </div>
  );
}

interface AdminCardTitleProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Title component for AdminCard
 */
export function AdminCardTitle({ children, className = '' }: AdminCardTitleProps) {
  return (
    <h3 className={`text-lg font-medium text-gray-900 ${className}`}>
      {children}
    </h3>
  );
}

interface AdminCardDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Description component for AdminCard
 */
export function AdminCardDescription({ children, className = '' }: AdminCardDescriptionProps) {
  return (
    <p className={`text-sm text-gray-500 mt-1 ${className}`}>
      {children}
    </p>
  );
}
