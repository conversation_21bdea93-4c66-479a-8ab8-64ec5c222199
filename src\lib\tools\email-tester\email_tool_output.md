    '6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\n' +
    'urWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\n' +
    'f/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\n' +
    'qbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n' +
    '-----END RSA PUBLIC KEY-----\n',
  modulusLength: 2048,
  rr: 'v=DKIM1;k=rsa;p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58F3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFTurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YLf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbHqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB',
  info: 'dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b="TGNb9f/Y"'
}
✅ DKIM result extracted from results[0].status.result: pass
DKIM extracted values: {
  result: 'pass',
  domain: 'finovamail.org',
  selector: 'dkim',
  algorithm: 'rsa-sha256'
}
Extracted sender domain for MX validation: finovamail.org
=== STARTING MX VALIDATION ===
=== MX RECORD VALIDATION FOR finovamail.org ===
Found 1 MX records for finovamail.org: [ { exchange: 'srv1.finovamail.org', priority: 10 } ]
Validating MX record: srv1.finovamail.org (priority: 10)
✅ srv1.finovamail.org resolves to: [ '**************' ]
MX validation result: {
  result: 'warning',
  domain: 'finovamail.org',
  records: [
    {
      exchange: 'srv1.finovamail.org',
      priority: 10,
      resolved: true,
      ipAddresses: [Array]
    }
  ],
  totalRecords: 1,
  lowestPriority: 10,
  issues: [
    'Only one MX record configured - consider adding backup MX records for redundancy'
  ],
  info: 'Found 1 MX record(s), 1 resolved successfully'
}
=== MX VALIDATION COMPLETED ===
MX validation successful: {
  result: 'warning',
  domain: 'finovamail.org',
  records: [
    {
      exchange: 'srv1.finovamail.org',
      priority: 10,
      resolved: true,
      ipAddresses: [Array]
    }
  ],
  totalRecords: 1,
  lowestPriority: 10,
  issues: [
    'Only one MX record configured - consider adding backup MX records for redundancy'
  ],
  info: 'Found 1 MX record(s), 1 resolved successfully'
}
=== EXTRACTING DNS RECORDS FROM MAILAUTH ===
Raw Mailauth result structure: {
  "dkim": {
    "headerFrom": [
      "<EMAIL>"
    ],
    "envelopeFrom": "<EMAIL>",
    "results": [
      {
        "id": "5afb98a70c195716e9aeb0326a33a8cfd9e1340330e4258fd09a57c2e49b7481",
        "signingDomain": "finovamail.org",
        "selector": "dkim",
        "signature": "TGNb9f/YJA0uliGWtpMsk4to5F8SNRl8hjU7VTbzz0V8UmnDPTCfi2IOWqK/VoqkWDfFhJjzOgd7B5P19YiI9cNFV3DSARGXBa0JWc+9Ewv7n/77FoVxKwn+UH6+AOvTRNKCFiL/o0W2HDhDTUhclDCVEoyHTcspmPATCB3wuMoa25K2xunGkfQe8HokMvGiz8pYM4U3pd75i8LHrSIKlt/bqwY2o/tfxHA3GQ3dYRfplQE0bN9B6V6mi6sca1pjUrgiTgQcQiOFe1QQ5mkRdrbQa1tY7RfSVU4XLSG8ZE5d6+aIb9Fgok1BatqN7m+PTQHuJ290xDInPHtEjea7iw==",
        "algo": "rsa-sha256",
        "format": "relaxed/relaxed",
        "bodyHash": "4qPm/nWNEENGJBmBDrdP5uoSw4qJeM7V5gLIfUTweJ8=",
        "bodyHashExpecting": "4qPm/nWNEENGJBmBDrdP5uoSw4qJeM7V5gLIfUTweJ8=",
        "signingHeaders": {
          "keys": "From: Date: Subject: Message-Id: To: MIME-Version: Content-Type",
          "headers": [
            "From: <EMAIL>",
            "Date: Sat, 24 May 2025 19:35:16 +0000",
            "Subject: SMTP test from srv.finovamail.org",
            "Message-Id: <4AH0EYXU8QU4.141M5GADA17D2@WIN-AUIR3RRGP88>",
            "To: <EMAIL>",
            "MIME-Version: 1.0",
            "Content-Type: multipart/alternative; boundary=\"=-/pprnDrJH+sMG7WCN+ge8g==\""
          ],
          "canonicalizedHeader": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        },
        "status": {
          "result": "pass",
          "header": {
            "i": "@finovamail.org",
            "s": "dkim",
            "a": "rsa-sha256",
            "b": "TGNb9f/Y"
          },
          "aligned": "finovamail.org"
        },
        "sourceBodyLength": 270,
        "canonBodyLength": 270,
        "canonBodyLengthTotal": 270,
        "canonBodyLengthLimited": false,
        "mimeStructureStart": 0,
        "publicKey": "-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
        "modulusLength": 2048,
        "rr": "v=DKIM1;k=rsa;p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58F3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFTurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YLf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbHqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB",
        "info": "dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\""
      }
    ]
  },
  "spf": {
    "domain": "finovamail.org",
    "client-ip": "**************",
    "helo": "[**************]",
    "envelope-from": "<EMAIL>",
    "rr": "v=spf1 a mx ip4:************** ip4:************** ip4:*************** ip4:************** ip4:************* ~all",
    "status": {
      "result": "pass",
      "comment": "DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender",       
      "smtp": {
        "mailfrom": "<EMAIL>",
        "helo": "[**************]"
      }
    },
    "header": "Received-SPF: pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) client-ip=**************;",
    "info": "spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL> smtp.helo=\"[**************]\"",
    "lookups": {
      "limit": 10,
      "count": 2,
      "void": 1,
      "subqueries": {
        "mx": 1,
        "mx:void": 0
      }
    }
  },
  "dmarc": {
    "status": {
      "result": "pass",
      "comment": "p=REJECT arc=none",
      "header": {
        "from": "finovamail.org",
        "d": "finovamail.org"
      }
    },
    "domain": "finovamail.org",
    "policy": "reject",
    "p": "reject",
    "sp": "reject",
    "rr": "v=DMARC1; p=reject; rua=mailto:<EMAIL>",
    "alignment": {
      "spf": {
        "result": "finovamail.org",
        "strict": false
      },
      "dkim": {
        "result": "finovamail.org",
        "strict": false
      }
    },
    "info": "dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org"
  },
  "arc": {
    "status": {
      "result": "none"
    },
    "i": 0,
    "authResults": "DESKTOP-KTQ2OST;\r\n dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\";\r\n spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>\r\n smtp.helo=\"[**************]\";\r\n dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org;\r\n bimi=none"
  },
  "bimi": {
    "status": {
      "header": {},
      "result": "none"
    },
    "info": "bimi=none"
  },
  "receivedChain": [
    {
      "from": {
        "value": "**************",
        "comment": "[**************]"
      },
      "by": {
        "value": "fademail.site"
      },
      "with": {
        "value": "ESMTP"
      },
      "id": {
        "value": "<EMAIL>"
      },
      "timestamp": "Sat, 24 May 2025 19:38:55 +0000",
      "full": "Received: from ************** ([**************]) by fademail.site with <NAME_EMAIL>; Sat, 24 May 2025 19:38:55 +0000"
    }
  ],
  "headers": "Received-SPF: pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) client-ip=**************;\r\nAuthentication-Results: DESKTOP-KTQ2OST;\r\n dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\";\r\n spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>\r\n smtp.helo=\"[**************]\";\r\n dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org;\r\n bimi=none\r\n"
}
Extracting SPF DNS data from Mailauth...
SPF object structure: {
  "domain": "finovamail.org",
  "client-ip": "**************",
  "helo": "[**************]",
  "envelope-from": "<EMAIL>",
  "rr": "v=spf1 a mx ip4:************** ip4:************** ip4:*************** ip4:************** ip4:************* ~all",
  "status": {
    "result": "pass",
    "comment": "DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender",
    "smtp": {
      "mailfrom": "<EMAIL>",
      "helo": "[**************]"
    }
  },
  "header": "Received-SPF: pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) client-ip=**************;",
  "info": "spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL> smtp.helo=\"[**************]\"",
  "lookups": {
    "limit": 10,
    "count": 2,
    "void": 1,
    "subqueries": {
      "mx": 1,
      "mx:void": 0
    }
  }
}
⚠️ No SPF record found in Mailauth results
Extracting DKIM DNS data from Mailauth...
✅ DKIM DNS record extracted from results array
Extracting DMARC DNS data from Mailauth...
DMARC object structure: {
  "status": {
    "result": "pass",
    "comment": "p=REJECT arc=none",
    "header": {
      "from": "finovamail.org",
      "d": "finovamail.org"
    }
  },
  "domain": "finovamail.org",
  "policy": "reject",
  "p": "reject",
  "sp": "reject",
  "rr": "v=DMARC1; p=reject; rua=mailto:<EMAIL>",
  "alignment": {
    "spf": {
      "result": "finovamail.org",
      "strict": false
    },
    "dkim": {
      "result": "finovamail.org",
      "strict": false
    }
  },
  "info": "dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org"
}
✅ DMARC record extracted: reject
Extracting ARC data from Mailauth...
Extracting BIMI data from Mailauth...
✅ DNS record extraction completed successfully
Extracted DNS data: {
  "spf": {},
  "dkim": {
    "dnsRecord": "v=DKIM1; k=rsa; p=-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
    "publicKey": "-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
    "keyType": "rsa",
    "keyStrength": 2048,
    "issues": []
  },
  "dmarc": {
    "record": "reject",
    "percentage": 100,
    "reportingEmails": {},
    "alignmentMode": {
      "spf": "r",
      "dkim": "r"
    },
    "issues": [
      "No DMARC reporting addresses configured"
    ]
  },
  "arc": {
    "authResults": "DESKTOP-KTQ2OST;\r\n dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\";\r\n spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>\r\n smtp.helo=\"[**************]\";\r\n dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org;\r\n bimi=none",
    "instance": 0,
    "info": "ARC validation preserves authentication through email forwarding"
  },
  "bimi": {
    "location": "",
    "authority": null,
    "info": "bimi=none"
  }
}
=== DNS RECORD EXTRACTION COMPLETED ===
=== EXTRACTED AUTH RESULTS ===
Final authResults object: {
  "spf": {
    "result": {
      "result": "pass",
      "comment": "DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender",       
      "smtp": {
        "mailfrom": "<EMAIL>",
        "helo": "[**************]"
      }
    },
    "domain": "finovamail.org",
    "info": "spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL> smtp.helo=\"[**************]\""
  },
  "dkim": {
    "result": "pass",
    "domain": "finovamail.org",
    "selector": "dkim",
    "algorithm": "rsa-sha256",
    "canonicalization": "",
    "info": "",
    "keyStrength": 2048,
    "bodyHashMatch": true,
    "signatureValid": true,
    "publicKey": "-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
    "keyType": "rsa",
    "dnsRecord": "v=DKIM1; k=rsa; p=-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
    "issues": []
  },
  "arc": {
    "result": "none",
    "instance": 0,
    "authResults": "DESKTOP-KTQ2OST;\r\n dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\";\r\n spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>\r\n smtp.helo=\"[**************]\";\r\n dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org;\r\n bimi=none",
    "info": "ARC validation preserves authentication through email forwarding"
  },
  "bimi": {
    "result": "none",
    "location": "",
    "authority": null,
    "info": "bimi=none"
  },
  "mx": {
    "result": "warning",
    "domain": "finovamail.org",
    "records": [
      {
        "exchange": "srv1.finovamail.org",
        "priority": 10,
        "resolved": true,
        "ipAddresses": [
          "**************"
        ]
      }
    ],
    "totalRecords": 1,
    "lowestPriority": 10,
    "issues": [
      "Only one MX record configured - consider adding backup MX records for redundancy"
    ],
    "info": "Found 1 MX record(s), 1 resolved successfully"
  },
  "dmarc": {
    "result": {
      "result": "pass",
      "comment": "p=REJECT arc=none",
      "header": {
        "from": "finovamail.org",
        "d": "finovamail.org"
      }
    },
    "domain": "finovamail.org",
    "policy": "reject",
    "alignment": {
      "spf": {
        "result": "finovamail.org",
        "strict": false
      },
      "dkim": {
        "result": "finovamail.org",
        "strict": false
      }
    },
    "info": "dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org",
    "record": "reject",
    "percentage": 100,
    "reportingEmails": {},
    "alignmentMode": {
      "spf": "r",
      "dkim": "r"
    },
    "issues": [
      "No DMARC reporting addresses configured"
    ]
  },
  "ipAddress": "**************",
  "returnPath": "<EMAIL>",
  "mailauth": {
    "dkim": {
      "headerFrom": [
        "<EMAIL>"
      ],
      "envelopeFrom": "<EMAIL>",
      "results": [
        {
          "id": "5afb98a70c195716e9aeb0326a33a8cfd9e1340330e4258fd09a57c2e49b7481",
          "signingDomain": "finovamail.org",
          "selector": "dkim",
          "signature": "TGNb9f/YJA0uliGWtpMsk4to5F8SNRl8hjU7VTbzz0V8UmnDPTCfi2IOWqK/VoqkWDfFhJjzOgd7B5P19YiI9cNFV3DSARGXBa0JWc+9Ewv7n/77FoVxKwn+UH6+AOvTRNKCFiL/o0W2HDhDTUhclDCVEoyHTcspmPATCB3wuMoa25K2xunGkfQe8HokMvGiz8pYM4U3pd75i8LHrSIKlt/bqwY2o/tfxHA3GQ3dYRfplQE0bN9B6V6mi6sca1pjUrgiTgQcQiOFe1QQ5mkRdrbQa1tY7RfSVU4XLSG8ZE5d6+aIb9Fgok1BatqN7m+PTQHuJ290xDInPHtEjea7iw==",
          "algo": "rsa-sha256",
          "format": "relaxed/relaxed",
          "bodyHash": "4qPm/nWNEENGJBmBDrdP5uoSw4qJeM7V5gLIfUTweJ8=",
          "bodyHashExpecting": "4qPm/nWNEENGJBmBDrdP5uoSw4qJeM7V5gLIfUTweJ8=",
          "signingHeaders": {
            "keys": "From: Date: Subject: Message-Id: To: MIME-Version: Content-Type",
            "headers": [
              "From: <EMAIL>",
              "Date: Sat, 24 May 2025 19:35:16 +0000",
              "Subject: SMTP test from srv.finovamail.org",
              "Message-Id: <4AH0EYXU8QU4.141M5GADA17D2@WIN-AUIR3RRGP88>",
              "To: <EMAIL>",
              "MIME-Version: 1.0",
              "Content-Type: multipart/alternative; boundary=\"=-/pprnDrJH+sMG7WCN+ge8g==\""
            ],
            "canonicalizedHeader": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"       
          },
          "status": {
            "result": "pass",
            "header": {
              "i": "@finovamail.org",
              "s": "dkim",
              "a": "rsa-sha256",
              "b": "TGNb9f/Y"
            },
            "aligned": "finovamail.org"
          },
          "sourceBodyLength": 270,
          "canonBodyLength": 270,
          "canonBodyLengthTotal": 270,
          "canonBodyLengthLimited": false,
          "mimeStructureStart": 0,
          "publicKey": "-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
          "modulusLength": 2048,
          "rr": "v=DKIM1;k=rsa;p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58F3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFTurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YLf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbHqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB",
          "info": "dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\""
        }
      ]
    },
    "spf": {
      "domain": "finovamail.org",
      "client-ip": "**************",
      "helo": "[**************]",
      "envelope-from": "<EMAIL>",
      "rr": "v=spf1 a mx ip4:************** ip4:************** ip4:*************** ip4:************** ip4:************* ~all",
      "status": {
        "result": "pass",
        "comment": "DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender",     
        "smtp": {
          "mailfrom": "<EMAIL>",
          "helo": "[**************]"
        }
      },
      "header": "Received-SPF: pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) client-ip=**************;",
      "info": "spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL> smtp.helo=\"[**************]\"",
      "lookups": {
        "limit": 10,
        "count": 2,
        "void": 1,
        "subqueries": {
          "mx": 1,
          "mx:void": 0
        }
      }
    },
    "dmarc": {
      "status": {
        "result": "pass",
        "comment": "p=REJECT arc=none",
        "header": {
          "from": "finovamail.org",
          "d": "finovamail.org"
        }
      },
      "domain": "finovamail.org",
      "policy": "reject",
      "p": "reject",
      "sp": "reject",
      "rr": "v=DMARC1; p=reject; rua=mailto:<EMAIL>",
      "alignment": {
        "spf": {
          "result": "finovamail.org",
          "strict": false
        },
        "dkim": {
          "result": "finovamail.org",
          "strict": false
        }
      },
      "info": "dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org"
    },
    "arc": {
      "status": {
        "result": "none"
      },
      "i": 0,
      "authResults": "DESKTOP-KTQ2OST;\r\n dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\";\r\n spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>\r\n smtp.helo=\"[**************]\";\r\n dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org;\r\n bimi=none"
    },
    "bimi": {
      "status": {
        "header": {},
        "result": "none"
      },
      "info": "bimi=none"
    },
    "receivedChain": [
      {
        "from": {
          "value": "**************",
          "comment": "[**************]"
        },
        "by": {
          "value": "fademail.site"
        },
        "with": {
          "value": "ESMTP"
        },
        "id": {
          "value": "<EMAIL>"
        },
        "timestamp": "Sat, 24 May 2025 19:38:55 +0000",
        "full": "Received: from ************** ([**************]) by fademail.site with <NAME_EMAIL>; Sat, 24 May 2025 19:38:55 +0000"
      }
    ],
    "headers": "Received-SPF: pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) client-ip=**************;\r\nAuthentication-Results: DESKTOP-KTQ2OST;\r\n dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\";\r\n spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>\r\n smtp.helo=\"[**************]\";\r\n dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org;\r\n bimi=none\r\n"
  }
}
Enhanced auth results: {
  "spf": {
    "result": {
      "result": "pass",
      "comment": "DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender",       
      "smtp": {
        "mailfrom": "<EMAIL>",
        "helo": "[**************]"
      }
    },
    "domain": "finovamail.org",
    "info": "spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL> smtp.helo=\"[**************]\""
  },
  "dkim": {
    "result": "pass",
    "domain": "finovamail.org",
    "selector": "dkim",
    "algorithm": "rsa-sha256",
    "canonicalization": "",
    "info": "",
    "keyStrength": 2048,
    "bodyHashMatch": true,
    "signatureValid": true,
    "publicKey": "-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
    "keyType": "rsa",
    "dnsRecord": "v=DKIM1; k=rsa; p=-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
    "issues": []
  },
  "arc": {
    "result": "none",
    "instance": 0,
    "authResults": "DESKTOP-KTQ2OST;\r\n dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\";\r\n spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>\r\n smtp.helo=\"[**************]\";\r\n dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org;\r\n bimi=none",
    "info": "ARC validation preserves authentication through email forwarding"
  },
  "bimi": {
    "result": "none",
    "location": "",
    "authority": null,
    "info": "bimi=none"
  },
  "mx": {
    "result": "warning",
    "domain": "finovamail.org",
    "records": [
      {
        "exchange": "srv1.finovamail.org",
        "priority": 10,
        "resolved": true,
        "ipAddresses": [
          "**************"
        ]
      }
    ],
    "totalRecords": 1,
    "lowestPriority": 10,
    "issues": [
      "Only one MX record configured - consider adding backup MX records for redundancy"
    ],
    "info": "Found 1 MX record(s), 1 resolved successfully"
  },
  "dmarc": {
    "result": {
      "result": "pass",
      "comment": "p=REJECT arc=none",
      "header": {
        "from": "finovamail.org",
        "d": "finovamail.org"
      }
    },
    "domain": "finovamail.org",
    "policy": "reject",
    "alignment": {
      "spf": {
        "result": "finovamail.org",
        "strict": false
      },
      "dkim": {
        "result": "finovamail.org",
        "strict": false
      }
    },
    "info": "dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org",
    "record": "reject",
    "percentage": 100,
    "reportingEmails": {},
    "alignmentMode": {
      "spf": "r",
      "dkim": "r"
    },
    "issues": [
      "No DMARC reporting addresses configured"
    ]
  },
  "ipAddress": "**************",
  "returnPath": "<EMAIL>",
  "mailauth": {
    "dkim": {
      "headerFrom": [
        "<EMAIL>"
      ],
      "envelopeFrom": "<EMAIL>",
      "results": [
        {
          "id": "5afb98a70c195716e9aeb0326a33a8cfd9e1340330e4258fd09a57c2e49b7481",
          "signingDomain": "finovamail.org",
          "selector": "dkim",
          "signature": "TGNb9f/YJA0uliGWtpMsk4to5F8SNRl8hjU7VTbzz0V8UmnDPTCfi2IOWqK/VoqkWDfFhJjzOgd7B5P19YiI9cNFV3DSARGXBa0JWc+9Ewv7n/77FoVxKwn+UH6+AOvTRNKCFiL/o0W2HDhDTUhclDCVEoyHTcspmPATCB3wuMoa25K2xunGkfQe8HokMvGiz8pYM4U3pd75i8LHrSIKlt/bqwY2o/tfxHA3GQ3dYRfplQE0bN9B6V6mi6sca1pjUrgiTgQcQiOFe1QQ5mkRdrbQa1tY7RfSVU4XLSG8ZE5d6+aIb9Fgok1BatqN7m+PTQHuJ290xDInPHtEjea7iw==",
          "algo": "rsa-sha256",
          "format": "relaxed/relaxed",
          "bodyHash": "4qPm/nWNEENGJBmBDrdP5uoSw4qJeM7V5gLIfUTweJ8=",
          "bodyHashExpecting": "4qPm/nWNEENGJBmBDrdP5uoSw4qJeM7V5gLIfUTweJ8=",
          "signingHeaders": {
            "keys": "From: Date: Subject: Message-Id: To: MIME-Version: Content-Type",
            "headers": [
              "From: <EMAIL>",
              "Date: Sat, 24 May 2025 19:35:16 +0000",
              "Subject: SMTP test from srv.finovamail.org",
              "Message-Id: <4AH0EYXU8QU4.141M5GADA17D2@WIN-AUIR3RRGP88>",
              "To: <EMAIL>",
              "MIME-Version: 1.0",
              "Content-Type: multipart/alternative; boundary=\"=-/pprnDrJH+sMG7WCN+ge8g==\""
            ],
            "canonicalizedHeader": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"       
          },
          "status": {
            "result": "pass",
            "header": {
              "i": "@finovamail.org",
              "s": "dkim",
              "a": "rsa-sha256",
              "b": "TGNb9f/Y"
            },
            "aligned": "finovamail.org"
          },
          "sourceBodyLength": 270,
          "canonBodyLength": 270,
          "canonBodyLengthTotal": 270,
          "canonBodyLengthLimited": false,
          "mimeStructureStart": 0,
          "publicKey": "-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
          "modulusLength": 2048,
          "rr": "v=DKIM1;k=rsa;p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58F3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFTurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YLf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbHqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB",
          "info": "dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\""
        }
      ]
    },
    "spf": {
      "domain": "finovamail.org",
      "client-ip": "**************",
      "helo": "[**************]",
      "envelope-from": "<EMAIL>",
      "rr": "v=spf1 a mx ip4:************** ip4:************** ip4:*************** ip4:************** ip4:************* ~all",
      "status": {
        "result": "pass",
        "comment": "DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender",     
        "smtp": {
          "mailfrom": "<EMAIL>",
          "helo": "[**************]"
        }
      },
      "header": "Received-SPF: pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) client-ip=**************;",
      "info": "spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL> smtp.helo=\"[**************]\"",
      "lookups": {
        "limit": 10,
        "count": 2,
        "void": 1,
        "subqueries": {
          "mx": 1,
          "mx:void": 0
        }
      }
    },
    "dmarc": {
      "status": {
        "result": "pass",
        "comment": "p=REJECT arc=none",
        "header": {
          "from": "finovamail.org",
          "d": "finovamail.org"
        }
      },
      "domain": "finovamail.org",
      "policy": "reject",
      "p": "reject",
      "sp": "reject",
      "rr": "v=DMARC1; p=reject; rua=mailto:<EMAIL>",
      "alignment": {
        "spf": {
          "result": "finovamail.org",
          "strict": false
        },
        "dkim": {
          "result": "finovamail.org",
          "strict": false
        }
      },
      "info": "dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org"
    },
    "arc": {
      "status": {
        "result": "none"
      },
      "i": 0,
      "authResults": "DESKTOP-KTQ2OST;\r\n dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\";\r\n spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>\r\n smtp.helo=\"[**************]\";\r\n dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org;\r\n bimi=none"
    },
    "bimi": {
      "status": {
        "header": {},
        "result": "none"
      },
      "info": "bimi=none"
    },
    "receivedChain": [
      {
        "from": {
          "value": "**************",
          "comment": "[**************]"
        },
        "by": {
          "value": "fademail.site"
        },
        "with": {
          "value": "ESMTP"
        },
        "id": {
          "value": "<EMAIL>"
        },
        "timestamp": "Sat, 24 May 2025 19:38:55 +0000",
        "full": "Received: from ************** ([**************]) by fademail.site with <NAME_EMAIL>; Sat, 24 May 2025 19:38:55 +0000"
      }
    ],
    "headers": "Received-SPF: pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) client-ip=**************;\r\nAuthentication-Results: DESKTOP-KTQ2OST;\r\n dkim=pass header.i=@finovamail.org header.s=dkim header.a=rsa-sha256 header.b=\"TGNb9f/Y\";\r\n spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>\r\n smtp.helo=\"[**************]\";\r\n dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org;\r\n bimi=none\r\n"
  },
  "fromDomain": "finovamail.org"
}
Sender domain: finovamail.org
Sender IP: **************
Analyzing email with Deepseek AI...
Analysis complete, storing results...
Auth results SPF data: {
  "result": {
    "result": "pass",
    "comment": "DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender",
    "smtp": {
      "mailfrom": "<EMAIL>",
      "helo": "[**************]"
    }
  },
  "domain": "finovamail.org",
  "info": "spf=pass (DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL> smtp.helo=\"[**************]\""
}
Auth results DKIM data: {
  "result": "pass",
  "domain": "finovamail.org",
  "selector": "dkim",
  "algorithm": "rsa-sha256",
  "canonicalization": "",
  "info": "",
  "keyStrength": 2048,
  "bodyHashMatch": true,
  "signatureValid": true,
  "publicKey": "-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
  "keyType": "rsa",
  "dnsRecord": "v=DKIM1; k=rsa; p=-----BEGIN RSA PUBLIC KEY-----\nMIIBCgKCAQEAlSAn9vSJR/YCQrC44bstrX0lRGkO4UA1/6XXvBgnARudj/cf0t58\nF3E1Y2aYujHyoKrdeth2RILq1sC4FfiwSzoj2RIb4ujdzaYwuqYG43fTKqu9szgy\n6YVqzXQrzz65N+5gN35BkVFdv7b84+6QuWmj0qQBcguD+c1V7v7jBE5ZfzzDZZFT\nurWBL/b/Z1QhX9xar5R7ApD7mf/TyICFfw4ynrm1tLR0hAuM8qJPgPdHPz3yX5YL\nf/f1+Vv8MjqgKIIBc0Xd3ZqH8alNkLxbCCnyWNol4a710C9VVthWPixXulLX2nbH\nqbjkZzVfoxnqCPoNqGginKTvlx7Yc2qrYQIDAQAB\n-----END RSA PUBLIC KEY-----\n",
  "issues": []
}
Auth results DMARC data: {
  "result": {
    "result": "pass",
    "comment": "p=REJECT arc=none",
    "header": {
      "from": "finovamail.org",
      "d": "finovamail.org"
    }
  },
  "domain": "finovamail.org",
  "policy": "reject",
  "alignment": {
    "spf": {
      "result": "finovamail.org",
      "strict": false
    },
    "dkim": {
      "result": "finovamail.org",
      "strict": false
    }
  },
  "info": "dmarc=pass (p=REJECT arc=none) header.from=finovamail.org header.d=finovamail.org",
  "record": "reject",
  "percentage": 100,
  "reportingEmails": {},
  "alignmentMode": {
    "spf": "r",
    "dkim": "r"
  },
  "issues": [
    "No DMARC reporting addresses configured"
  ]
}
Starting to store deliverability test results...
Test Address ID: 2e8b7332-583a-41e7-80af-ef8da9fba21f
Email ID: 1553
Sender Domain: finovamail.org
Sender IP: **************
Updating test address status to completed...
Test address status updated successfully
=== EXTRACTING SIMPLE RESULT FOR SPF ===
Input SPF result: object {
  result: 'pass',
  comment: 'DESKTOP-KTQ2OST: <NAME_EMAIL> designates ************** as permitted sender',
  smtp: { mailfrom: '<EMAIL>', helo: '[**************]' }
}
Object had properties: [ 'result', 'comment', 'smtp' ]
Found direct result property: pass
Final extracted SPF: pass
=== EXTRACTING SIMPLE RESULT FOR DKIM ===
Input DKIM result: string pass
Extracted string DKIM: pass
=== EXTRACTING SIMPLE RESULT FOR DMARC ===
Input DMARC result: object {
  result: 'pass',
  comment: 'p=REJECT arc=none',
  header: { from: 'finovamail.org', d: 'finovamail.org' }
}
Object had properties: [ 'result', 'comment', 'header' ]
Found direct result property: pass
Final extracted DMARC: pass
=== EXTRACTING SIMPLE RESULT FOR MX ===
Input MX result: string warning
Extracted string MX: warning
Extracted simple results: {
  spf: 'pass',
  dkim: 'pass',
  dmarc: 'pass',
  mx: 'warning',
  senderDomain: 'finovamail.org',
  senderIp: '**************',
  reverseDns: ''
}
Inserting test results with data: {
  test_address_id: '2e8b7332-583a-41e7-80af-ef8da9fba21f',
  email_id: 1553,
  sender_domain: 'finovamail.org',
  sender_ip: '**************',
  spf_result: 'pass',
  dkim_result: 'pass',
  dmarc_result: 'pass',
  mx_result: 'warning',
  reverse_dns: '',
  spam_score: 0,
  overall_score: 8,
  raw_headers: '[TRUNCATED]',
  analysis_json: '[OBJECT]'
}
Test results inserted successfully with ID: ccd97e90-72e9-4201-b039-235b8c6fcc52
Storing 2 recommendations...
Storing recommendation 1: {
  category: 'IP Reputation',
  issue: 'Unknown IP reputation...',
  priority: 3
}
Storing recommendation 2: {
  category: 'DMARC',
  issue: "DMARC policy is set to 'reject'...",
  priority: 2
}
All recommendations stored successfully
Storage completed successfully, returning result ID: ccd97e90-72e9-4201-b039-235b8c6fcc52
Results stored with ID: ccd97e90-72e9-4201-b039-235b8c6fcc52
 POST /api/tools/email-deliverability/analyze 200 in 30923ms
 GET /tools/email-deliverability/results/ccd97e90-72e9-4201-b039-235b8c6fcc52 200 in 478ms
 ○ Compiling /_not-found ...
 ✓ Compiled /api/tools/email-deliverability/results/[testId] in 1862ms (1184 modules)
 ✓ Compiled (1186 modules)
 POST /api/analytics 404 in 3675ms
 GET /api/tools/email-deliverability/results/ccd97e90-72e9-4201-b039-235b8c6fcc52 200 in 3731ms
 GET /api/tools/email-deliverability/results/ccd97e90-72e9-4201-b039-235b8c6fcc52 200 in 676ms