'use client';

import { useState, useEffect } from 'react';
import { DevicePhoneMockup, DeviceTabletMockup, DeviceDesktopMockup } from './DeviceMockups';
import { AdConfig } from '@/lib/config/types';

interface AdPreviewProps {
  adPlacement: AdConfig;
  deviceType: 'desktop' | 'tablet' | 'mobile';
}

export default function AdPreview({ adPlacement, deviceType }: AdPreviewProps) {
  const [previewHtml, setPreviewHtml] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Generate preview HTML
  useEffect(() => {
    const generatePreview = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Check if device type is supported
        if (!adPlacement.deviceTypes.includes(deviceType) && !adPlacement.deviceTypes.includes('all')) {
          setPreviewHtml('');
          setError(`This ad is not configured for ${deviceType} devices.`);
          return;
        }
        
        // Generate preview HTML based on ad unit ID
        let html = '';
        
        if (adPlacement.adUnitId.startsWith('ca-pub-')) {
          // Google AdSense
          html = `
            <div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background-color: #f3f4f6; padding: 1rem;">
              <div style="background-color: white; border-radius: 0.5rem; padding: 1rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); width: 100%; max-width: 500px;">
                <div style="font-size: 0.875rem; font-weight: 500; color: #4b5563; margin-bottom: 0.5rem;">Google AdSense Preview</div>
                <div style="width: 100%; height: 200px; background-color: #e5e7eb; display: flex; justify-content: center; align-items: center; border-radius: 0.25rem; overflow: hidden;">
                  <div style="text-align: center; padding: 1rem;">
                    <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">Ad Unit ID: ${adPlacement.adUnitId}</div>
                    <div style="font-size: 0.875rem; color: #4b5563;">This is a preview of how your Google AdSense ad would appear.</div>
                  </div>
                </div>
              </div>
            </div>
          `;
        } else if (adPlacement.adUnitId.startsWith('custom-')) {
          // Custom ad
          html = `
            <div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background-color: #f3f4f6; padding: 1rem;">
              <div style="background-color: white; border-radius: 0.5rem; padding: 1rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); width: 100%; max-width: 500px;">
                <div style="font-size: 0.875rem; font-weight: 500; color: #4b5563; margin-bottom: 0.5rem;">Custom Ad Preview</div>
                <div style="width: 100%; height: 200px; background-color: #e5e7eb; display: flex; justify-content: center; align-items: center; border-radius: 0.25rem; overflow: hidden;">
                  <div style="text-align: center; padding: 1rem;">
                    <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">Ad Unit ID: ${adPlacement.adUnitId}</div>
                    <div style="font-size: 0.875rem; color: #4b5563;">This is a preview of your custom ad content.</div>
                  </div>
                </div>
              </div>
            </div>
          `;
        } else {
          // Other ad provider
          html = `
            <div style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background-color: #f3f4f6; padding: 1rem;">
              <div style="background-color: white; border-radius: 0.5rem; padding: 1rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); width: 100%; max-width: 500px;">
                <div style="font-size: 0.875rem; font-weight: 500; color: #4b5563; margin-bottom: 0.5rem;">Ad Preview</div>
                <div style="width: 100%; height: 200px; background-color: #e5e7eb; display: flex; justify-content: center; align-items: center; border-radius: 0.25rem; overflow: hidden;">
                  <div style="text-align: center; padding: 1rem;">
                    <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">Ad Unit ID: ${adPlacement.adUnitId}</div>
                    <div style="font-size: 0.875rem; color: #4b5563;">This is a preview of your ad content.</div>
                  </div>
                </div>
              </div>
            </div>
          `;
        }
        
        setPreviewHtml(html);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while generating preview');
        console.error('Error generating preview:', err);
      } finally {
        setLoading(false);
      }
    };
    
    generatePreview();
  }, [adPlacement, deviceType]);

  // Render device mockup based on device type
  const renderDeviceMockup = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      );
    }
    
    if (error) {
      return (
        <div className="flex justify-center items-center h-full">
          <div className="text-red-500 text-center p-4">{error}</div>
        </div>
      );
    }
    
    if (!previewHtml) {
      return (
        <div className="flex justify-center items-center h-full">
          <div className="text-gray-500 text-center p-4">No preview available</div>
        </div>
      );
    }
    
    switch (deviceType) {
      case 'desktop':
        return <DeviceDesktopMockup content={previewHtml} />;
      case 'tablet':
        return <DeviceTabletMockup content={previewHtml} />;
      case 'mobile':
        return <DevicePhoneMockup content={previewHtml} />;
      default:
        return <DeviceDesktopMockup content={previewHtml} />;
    }
  };

  return (
    <div className="h-full">
      {renderDeviceMockup()}
    </div>
  );
}
