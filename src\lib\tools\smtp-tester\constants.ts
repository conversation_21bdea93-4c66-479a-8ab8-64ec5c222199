/**
 * SMTP Tester Constants
 *
 * Configuration constants and SMTP provider presets
 */

import { SmtpProvider } from '@/types/smtp';

export const SMTP_PROVIDERS: SmtpProvider[] = [
  {
    name: 'gmail',
    displayName: 'Gmail',
    server: 'smtp.gmail.com',
    port: 587,
    encryption: 'tls',
    instructions: 'Use your Gmail address and an App Password (not your regular password). Enable 2FA and generate an App Password at Google Account > Security > App Passwords.',
    setupUrl: 'https://myaccount.google.com/security'
  },
  {
    name: 'outlook',
    displayName: 'Outlook/Hotmail',
    server: 'smtp-mail.outlook.com',
    port: 587,
    encryption: 'tls',
    instructions: 'Use your Outlook.com or Hotmail address and password. If you have 2FA enabled, you may need to generate an app password.',
    setupUrl: 'https://account.microsoft.com/security'
  },
  {
    name: 'yahoo',
    displayName: 'Yahoo Mail',
    server: 'smtp.mail.yahoo.com',
    port: 587,
    encryption: 'tls',
    instructions: 'Use your Yahoo email address and an App Password. Generate one at Yahoo Account Security > Generate app password.',
    setupUrl: 'https://login.yahoo.com/account/security'
  },
  {
    name: 'custom',
    displayName: 'Custom SMTP Server',
    server: '',
    port: 587,
    encryption: 'tls',
    instructions: 'Enter your custom SMTP server details. Contact your email provider or system administrator for the correct settings.'
  }
];

export const DEFAULT_PORTS = {
  none: 25,
  tls: 587,
  ssl: 465
} as const;

export const ENCRYPTION_OPTIONS = [
  { value: 'none', label: 'None (Plain)', port: 25 },
  { value: 'tls', label: 'TLS (STARTTLS)', port: 587 },
  { value: 'ssl', label: 'SSL/TLS', port: 465 }
] as const;

export const TEST_EMAIL_DOMAIN = process.env.EMAIL_TESTER_DOMAIN || 'fademail.site';

export const SMTP_TIMEOUTS = {
  connection: 10000, // 10 seconds
  socket: 10000,     // 10 seconds
  greeting: 5000,    // 5 seconds
  response: 5000     // 5 seconds
} as const;

export const RATE_LIMITS = {
  maxRequestsPerMinute: 10,
  maxRequestsPerHour: 50
} as const;
