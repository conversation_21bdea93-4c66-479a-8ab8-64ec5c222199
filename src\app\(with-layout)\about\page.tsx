import React from 'react';
import Link from 'next/link';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'About Us | VanishPost',
  description: 'Learn about VanishPost and our mission to provide a secure temporary email service.',
};

export default function AboutPage() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-12 sm:py-16">
      <div className="text-center mb-12">
        <h1 className="text-3xl sm:text-4xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>About VanishPost</h1>
        <p className="text-lg max-w-3xl mx-auto" style={{ color: 'var(--earth-brown-medium)' }}>
          Protecting your online privacy with temporary email solutions since 2023.
        </p>
      </div>

      {/* Our Story */}
      <div className="p-8 rounded-md shadow-sm mb-10" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
        <h2 className="text-2xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Our Story</h2>
        <p className="mb-4" style={{ color: 'var(--earth-brown-medium)' }}>
          VanishPost was born from a simple observation: people needed a way to protect their primary email addresses from spam, data breaches, and unwanted marketing communications. In 2023, our small team of privacy advocates and developers set out to create a solution that would be both simple to use and effective at protecting online privacy.
        </p>
        <p className="mb-4" style={{ color: 'var(--earth-brown-medium)' }}>
          We built VanishPost with a clear mission: to provide a free, accessible service that helps people maintain control over their digital identity. Our temporary email addresses give users the freedom to engage online without compromising their personal information.
        </p>
        <p style={{ color: 'var(--earth-brown-medium)' }}>
          Today, VanishPost serves thousands of users daily, helping them protect their privacy while navigating the digital world. We remain committed to our founding principles of privacy, security, and simplicity.
        </p>
      </div>

      {/* Our Mission */}
      <div className="p-8 rounded-md mb-10" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
        <h2 className="text-2xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Our Mission</h2>
        <p className="mb-4" style={{ color: 'var(--earth-brown-medium)' }}>
          At VanishPost, we believe that privacy is a fundamental right. Our mission is to empower individuals to protect their digital identity by providing simple, effective tools that put users in control of their personal information.
        </p>
        <p style={{ color: 'var(--earth-brown-medium)' }}>
          We're committed to creating a safer internet where people can participate freely without sacrificing their privacy. Through education and accessible privacy tools, we aim to help everyone take control of their digital footprint.
        </p>
      </div>

      {/* Core Values */}
      <div className="mb-10">
        <h2 className="text-2xl font-bold mb-6 text-center" style={{ color: 'var(--earth-brown-dark)' }}>Our Core Values</h2>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
            <div className="p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--earth-brown-dark)' }}>Privacy First</h3>
            <p style={{ color: 'var(--earth-brown-medium)' }}>
              We design every aspect of our service with privacy as the top priority. We collect minimal data and delete it promptly.
            </p>
          </div>

          <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
            <div className="p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--earth-brown-dark)' }}>Simplicity</h3>
            <p style={{ color: 'var(--earth-brown-medium)' }}>
              We believe privacy tools should be accessible to everyone. Our service is designed to be intuitive and easy to use.
            </p>
          </div>

          <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
            <div className="p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--earth-brown-dark)' }}>Transparency</h3>
            <p style={{ color: 'var(--earth-brown-medium)' }}>
              We're open about how our service works, what data we collect, and how we protect your information.
            </p>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="p-8 rounded-md text-center" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
        <h2 className="text-2xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Join Us in Protecting Online Privacy</h2>
        <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
          Experience the peace of mind that comes with protecting your digital identity.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link href="/" className="inline-block bg-[#ce601c] text-white px-6 py-3 rounded-md font-medium hover:bg-[#b85518] transition-colors">
            Try VanishPost Now
          </Link>
          <Link href="/contact" className="inline-block px-6 py-3 rounded-md font-medium transition-colors contact-button"
                style={{
                  backgroundColor: 'var(--earth-beige-light)',
                  color: 'var(--earth-brown-medium)',
                  border: '1px solid var(--earth-brown-medium)'
                }}>
            Contact Us
          </Link>
        </div>
      </div>
    </div>
  );
}
