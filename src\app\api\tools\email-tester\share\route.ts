/**
 * Email Tester Share API Route
 * Generates shareable tokens for test results
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentSessionId, generateShareToken } from '@/lib/tools/email-tester/sessionManager';
import { getEmailTesterResult } from '@/lib/tools/email-tester/database';
import { logError } from '@/lib/logging';

export async function POST(request: NextRequest) {
  try {
    const { testId } = await request.json();

    if (!testId) {
      return NextResponse.json(
        { success: false, message: 'Test ID is required' },
        { status: 400 }
      );
    }

    // Get current session ID for access control
    const sessionId = await getCurrentSessionId();
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, message: 'Access denied - no valid session' },
        { status: 403 }
      );
    }

    // Verify that the test result belongs to the current session
    try {
      await getEmailTesterResult(testId, sessionId);
    } catch (error) {
      return NextResponse.json(
        { success: false, message: 'Test result not found or access denied' },
        { status: 404 }
      );
    }

    // Generate share token
    const shareToken = generateShareToken(testId, sessionId);

    return NextResponse.json({
      success: true,
      shareToken,
      shareUrl: `${request.nextUrl.origin}/tools/email-tester/results/${testId}?share=${shareToken}`
    });

  } catch (error) {
    logError('API', 'Failed to generate share token', error);
    return NextResponse.json(
      { success: false, message: 'Failed to generate share token' },
      { status: 500 }
    );
  }
}
