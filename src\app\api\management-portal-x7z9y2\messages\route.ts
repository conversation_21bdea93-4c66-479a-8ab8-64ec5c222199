import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { logInfo, logError } from '@/lib/logging';
import { MessageStatus } from '@/lib/types/contact';

/**
 * API route for fetching messages in the admin interface
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as MessageStatus || 'unread';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const pageSize = parseInt(searchParams.get('pageSize') || '50', 10);

    // Get Supabase client
    const supabase = await createServerSupabaseClient();

    // Query messages
    let query = supabase
      .from('contact_messages')
      .select('*')
      .eq('status', status)
      .eq('is_admin_reply', false)
      .order('created_at', { ascending: false })
      .range((page - 1) * pageSize, page * pageSize - 1);

    const { data: messages, error } = await query;

    if (error) {
      logError('messages', 'Failed to fetch messages', { error });
      return NextResponse.json({
        success: false,
        message: 'Failed to fetch messages'
      }, { status: 500 });
    }

    // Format dates to ensure consistency
    const formattedMessages = messages?.map(message => ({
      ...message,
      // Convert to ISO string format which is more reliable for JavaScript Date parsing
      created_at: message.created_at ? new Date(message.created_at).toISOString() : null,
      updated_at: message.updated_at ? new Date(message.updated_at).toISOString() : null
    }));

    return NextResponse.json({
      success: true,
      messages: formattedMessages
    });
  } catch (error) {
    logError('messages', 'Error fetching messages', { error });
    return NextResponse.json({
      success: false,
      message: 'An error occurred while fetching messages'
    }, { status: 500 });
  }
}
