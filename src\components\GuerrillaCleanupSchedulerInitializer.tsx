/**
 * Guerrilla Cleanup Scheduler Initializer
 * 
 * Component that automatically starts the Guerrilla cleanup scheduler on app load
 * if auto-start is enabled and the scheduler is not already running
 */

'use client';

import { useEffect } from 'react';

export default function GuerrillaCleanupSchedulerInitializer() {
  useEffect(() => {
    const initializeScheduler = async () => {
      try {
        // Check scheduler status
        const statusResponse = await fetch('/api/management-portal-x7z9y2/guerrilla-cleanup/status');

        // Check if the response is a redirect (which would indicate auth issues)
        if (statusResponse.redirected) {
          // If we're being redirected, we're likely not authenticated
          // Just log a debug message and exit gracefully
          console.debug('Guerrilla cleanup scheduler check skipped - authentication required');
          return;
        }

        // Check if the response is successful
        if (!statusResponse.ok) {
          console.debug('Guerrilla cleanup scheduler status check failed with status:', statusResponse.status);
          return;
        }

        // Try to parse JSON, but handle cases where we get HTML instead
        let statusData;
        try {
          statusData = await statusResponse.json();
        } catch (jsonError) {
          // If we can't parse JSON, it's likely because we got an HTML response (login page)
          console.debug('Guerrilla cleanup scheduler check skipped - received non-JSON response (likely authentication redirect)');
          return;
        }

        if (!statusData.success) {
          console.debug('Failed to fetch Guerrilla cleanup scheduler status:', statusData.error);
          return;
        }

        // Check if auto-start is enabled and scheduler is not running
        if (statusData.success && !statusData.status.running && statusData.status.autoStart !== false) {
          try {
            // Start the scheduler - this requires authentication - use direct admin API path
            const startResponse = await fetch('/api/admin/guerrilla-cleanup/scheduler', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                action: 'start',
                intervalMinutes: statusData.status.intervalMinutes || 60
              })
            });

            // Check if the response is a redirect (which would indicate auth issues)
            if (startResponse.redirected) {
              // If we're being redirected, we're likely not authenticated
              // Just log a debug message and exit gracefully
              console.debug('Guerrilla cleanup scheduler start skipped - authentication required');
              return;
            }

            // Check if the response is successful
            if (!startResponse.ok) {
              console.debug('Guerrilla cleanup scheduler start failed with status:', startResponse.status);
              return;
            }

            let startData;
            try {
              startData = await startResponse.json();
            } catch (jsonError) {
              // If we can't parse JSON, it's likely because we got an HTML response
              console.debug('Guerrilla cleanup scheduler start skipped - received non-JSON response');
              return;
            }

            if (startData.success) {
              console.log('Guerrilla cleanup scheduler auto-started successfully');
            } else {
              console.debug('Failed to auto-start Guerrilla cleanup scheduler:', startData.error);
            }
          } catch (startError) {
            // Only log as debug to avoid filling error logs with auth-related errors
            console.debug('Could not start Guerrilla cleanup scheduler:', startError);
          }
        }
      } catch (error) {
        // Check if this is an authentication error (typically happens after logout)
        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
          // This is likely due to a redirect after authentication failure
          // Just log a debug message instead of an error
          console.debug('Guerrilla cleanup scheduler check skipped - possible authentication issue');
        } else {
          // For other errors, log as usual but use debug level to avoid noise
          console.debug('Error initializing Guerrilla cleanup scheduler:', error);
        }
      }
    };

    // Initialize scheduler after a short delay to ensure app is fully loaded
    const timeoutId = setTimeout(initializeScheduler, 2000);

    return () => clearTimeout(timeoutId);
  }, []);

  // This component doesn't render anything
  return null;
}
