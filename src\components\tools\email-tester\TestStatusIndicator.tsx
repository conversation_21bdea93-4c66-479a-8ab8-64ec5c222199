/**
 * TestStatusIndicator Component
 *
 * This component displays the status of the email deliverability test
 * and allows the user to check for results.
 */

'use client';

interface TestStatusIndicatorProps {
  isChecking: boolean;
  onCheck: () => void;
  autoRefreshState?: {
    isPolling: boolean;
    currentAttempt: number;
    timeRemaining: number;
    status: 'idle' | 'waiting' | 'checking' | 'success' | 'timeout' | 'error';
    statusMessage: string;
  };
  onStopAutoRefresh?: () => void;
}

export default function TestStatusIndicator({
  isChecking,
  onCheck,
  autoRefreshState,
  onStopAutoRefresh
}: TestStatusIndicatorProps) {
  const isAutoRefreshActive = autoRefreshState?.isPolling;
  const isCurrentlyChecking = isChecking || autoRefreshState?.status === 'checking';

  return (
    <div className="space-y-4">
      {/* Auto-refresh status display */}
      {isAutoRefreshActive && (
        <div className="rounded-2xl p-4 sm:p-6" style={{ backgroundColor: '#f3ece8', border: '1px solid #f3ece8' }}>
          <div className="flex items-start sm:items-center justify-between mb-4 gap-3">
            <div className="flex items-start sm:items-center space-x-3 flex-1 min-w-0">
              <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-xl flex-shrink-0" style={{ backgroundColor: '#956b50' }}>
                {autoRefreshState?.status === 'checking' ? (
                  <svg className="animate-spin w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
              </div>
              <div className="min-w-0 flex-1">
                <h4 className="font-semibold text-sm sm:text-base" style={{ color: '#1b130e' }}>Auto-Refresh Active</h4>
                <p className="text-xs sm:text-sm break-words" style={{ color: '#4a3728' }}>{autoRefreshState?.statusMessage}</p>
              </div>
            </div>
            {onStopAutoRefresh && (
              <button
                onClick={onStopAutoRefresh}
                className="px-3 py-2 text-xs sm:text-sm rounded-lg transition-colors duration-200 hover:opacity-80 flex-shrink-0"
                style={{ color: '#956b50', border: '1px solid #956b50' }}
              >
                Stop
              </button>
            )}
          </div>

          {/* Progress indicator */}
          <div className="space-y-2">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 text-xs sm:text-sm" style={{ color: '#4a3728' }}>
              <span>Attempt {autoRefreshState?.currentAttempt || 0} of 25</span>
              <span>{Math.floor((autoRefreshState?.timeRemaining || 0) / 60)}m {(autoRefreshState?.timeRemaining || 0) % 60}s remaining</span>
            </div>
            <div className="w-full rounded-full h-2" style={{ backgroundColor: '#fbfaf8' }}>
              <div
                className="h-2 rounded-full transition-all duration-300"
                style={{
                  backgroundColor: '#956b50',
                  width: `${Math.min(((autoRefreshState?.currentAttempt || 0) / 25) * 100, 100)}%`
                }}
              ></div>
            </div>
          </div>
        </div>
      )}

      {/* Main status display */}
      <div className="text-center px-4">
        {/* Show icon only when auto-refresh is NOT active */}
        {!isAutoRefreshActive && (
          <div className={`inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 rounded-2xl mb-3 transition-all duration-300`} style={{
            backgroundColor: '#f3ece8'
          }}>
            {isCurrentlyChecking ? (
              <svg className="animate-spin w-6 h-6 sm:w-7 sm:h-7" fill="none" viewBox="0 0 24 24" style={{ color: '#1b130e' }}>
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className={`w-6 h-6 sm:w-7 sm:h-7`} fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#1b130e' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
          </div>
        )}
        <p className={`text-sm sm:text-base leading-normal max-w-md mx-auto mb-6 px-2 ${isAutoRefreshActive ? 'mt-4' : ''}`} style={{ color: '#4a3728' }}>
          {isAutoRefreshActive
            ? "Auto-refresh is monitoring for your test email. You can also check manually below."
            : "After sending your test email, click the button below to check if it has been received and analyze the results."
          }
        </p>
      </div>

      {/* Manual check button */}
      <div className="flex justify-center px-4">
        <button
          onClick={onCheck}
          disabled={isCurrentlyChecking}
          className="group relative px-4 sm:px-5 py-2.5 sm:py-3 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-semibold text-sm sm:text-base w-auto"
          style={{ backgroundColor: 'transparent', border: '2px solid #1b130e', color: '#1b130e' }}
        >
          <div className="flex items-center justify-center">
            {isCurrentlyChecking ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" style={{ color: '#1b130e' }}>
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Analyzing Email...</span>
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#1b130e' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Check for Results</span>
              </>
            )}
          </div>
        </button>
      </div>

      {/* Info section - only show if auto-refresh is not active */}
      {!isAutoRefreshActive && (
        <div className="rounded-2xl p-4 sm:p-6 mx-2 sm:mx-0" style={{ backgroundColor: '#f3ece8', border: '1px solid #f3ece8' }}>
          <div className="flex items-start space-x-3">
            <div className="flex items-center justify-center w-7 h-7 sm:w-8 sm:h-8 rounded-lg mt-0.5 flex-shrink-0" style={{ backgroundColor: '#956b50' }}>
              <svg className="w-3 h-3 sm:w-4 sm:h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="font-semibold mb-2 text-sm sm:text-base" style={{ color: '#1b130e' }}>Pro Tip</h4>
              <p className="text-xs sm:text-sm leading-relaxed" style={{ color: '#4a3728' }}>
                Copy the test email address above to automatically start monitoring for your email. Our system will check every 10-15 seconds and redirect you to results when your email arrives.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
