import { test, expect } from '@playwright/test';

test.describe('Admin Configuration Interface', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the admin login page
    await page.goto('/admin/login');

    // Fill in valid credentials
    await page.fill('input[name="username"]', 'test-admin');
    await page.fill('input[name="password"]', 'test-password');

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for navigation to complete
    await page.waitForURL('/admin/health');
  });

  test('should navigate to configuration page', async ({ page }) => {
    // Navigate to the configuration page
    await page.click('a[href="/admin/config"]');

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Configuration")');

    // Check that the configuration form is displayed
    const configForm = await page.isVisible('form');
    expect(configForm).toBe(true);
  });

  test('should update email expiration time', async ({ page }) => {
    // Navigate to the configuration page
    await page.click('a[href="/admin/config"]');

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Configuration")');

    // Find the email expiration input
    const expirationInput = await page.locator('input[name="emailExpirationMinutes"]');
    
    // Clear the input and set a new value
    await expirationInput.clear();
    await expirationInput.fill('45');

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for the success message
    await page.waitForSelector('div:has-text("Configuration updated successfully")');

    // Reload the page to verify the changes were saved
    await page.reload();

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Configuration")');

    // Check that the new value was saved
    const newValue = await expirationInput.inputValue();
    expect(newValue).toBe('45');
  });

  test('should update auto-refresh interval', async ({ page }) => {
    // Navigate to the configuration page
    await page.click('a[href="/admin/config"]');

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Configuration")');

    // Find the auto-refresh interval input
    const refreshInput = await page.locator('input[name="autoRefreshInterval"]');
    
    // Clear the input and set a new value
    await refreshInput.clear();
    await refreshInput.fill('20');

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for the success message
    await page.waitForSelector('div:has-text("Configuration updated successfully")');

    // Reload the page to verify the changes were saved
    await page.reload();

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Configuration")');

    // Check that the new value was saved
    const newValue = await refreshInput.inputValue();
    expect(newValue).toBe('20');
  });

  test('should toggle maintenance mode', async ({ page }) => {
    // Navigate to the configuration page
    await page.click('a[href="/admin/config"]');

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Configuration")');

    // Find the maintenance mode toggle
    const maintenanceToggle = await page.locator('input[name="maintenanceMode"]');
    
    // Get the current state
    const initialState = await maintenanceToggle.isChecked();
    
    // Toggle the state
    await maintenanceToggle.click();

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for the success message
    await page.waitForSelector('div:has-text("Configuration updated successfully")');

    // Reload the page to verify the changes were saved
    await page.reload();

    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Configuration")');

    // Check that the new state was saved
    const newState = await maintenanceToggle.isChecked();
    expect(newState).toBe(!initialState);
  });
});
