'use client'

import posthog from 'posthog-js'
import { PostHog<PERSON><PERSON>ider as <PERSON><PERSON><PERSON>ider } from 'posthog-js/react'
import { useEffect } from 'react'
import PostHogPageView from '@/components/PostHogPageView'

// Export PostHog instance for direct usage
export { posthog }

// Export PostHog provider for wrapping the app
export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Initialize PostHog only on the client side
    posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY || 'phc_placeholder', {
      api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
      // Disable automatic pageview capture, as we capture manually
      capture_pageview: false,
      // Enable pageleave capture
      capture_pageleave: true,
      // Respect Do Not Track setting
      respect_dnt: true,
      // Disable session recording to respect user privacy
      disable_session_recording: true,
      // Load preferences from localStorage
      loaded: (ph) => {
        try {
          const storedPreferences = localStorage.getItem('cookie_preferences');
          if (storedPreferences) {
            const preferences = JSON.parse(storedPreferences);
            // If user has not consented to analytics, opt out
            if (!preferences.analytics) {
              ph.opt_out_capturing();
            }
          } else {
            // No preferences set yet, opt out by default
            ph.opt_out_capturing();
          }
        } catch (error) {
          console.error('Error loading cookie preferences:', error);
          // In case of error, opt out by default
          ph.opt_out_capturing();
        }
      }
    })
  }, [])

  return (
    <PHProvider client={posthog}>
      <PostHogPageView />
      {children}
    </PHProvider>
  )
}

// Helper function to track events with consistent properties
export function trackEvent(eventName: string, properties?: Record<string, any>): Promise<void> {
  // Skip tracking for admin and management portal pages
  if (typeof window !== 'undefined') {
    const pathname = window.location.pathname
    if (pathname.startsWith('/admin') || pathname.startsWith('/management-portal-x7z9y2')) {
      return Promise.resolve()
    }
  }

  // Track the event and return a promise
  return new Promise<void>((resolve, reject) => {
    try {
      posthog.capture(eventName, properties)
      resolve()
    } catch (error) {
      reject(error)
    }
  })
}

// Helper function to track page views
export function trackPageView(url?: string): Promise<void> {
  // Skip tracking for admin and management portal pages
  if (typeof window !== 'undefined') {
    const pathname = url || window.location.pathname
    if (pathname.startsWith('/admin') || pathname.startsWith('/management-portal-x7z9y2')) {
      return Promise.resolve()
    }
  }

  // Track the page view and return a promise
  return new Promise<void>((resolve, reject) => {
    try {
      posthog.capture('$pageview', { url })
      resolve()
    } catch (error) {
      reject(error)
    }
  })
}

// Helper functions for common events
export function trackEmailGenerated(emailAddress: string): Promise<void> {
  return trackEvent('email_generated', { emailAddress })
}

export function trackEmailAddressCopied(emailAddress: string): Promise<void> {
  return trackEvent('email_address_copied', { emailAddress })
}

export function trackEmailViewed(emailId: string): Promise<void> {
  return trackEvent('email_viewed', { emailId })
}

export function trackEmailDeleted(emailId: string): Promise<void> {
  return trackEvent('email_deleted', { emailId })
}

export function trackInboxRefreshed(isManual: boolean = false): Promise<void> {
  return trackEvent('inbox_refreshed', { isManual })
}

export function trackEmailReceived(count: number = 1): Promise<void> {
  return trackEvent('email_received', { count })
}

// Function to identify a user (use sparingly and only with consent)
export function identifyUser(id: string, traits?: Record<string, any>) {
  posthog.identify(id, traits)
}

// Function to reset user identification
export function resetUser() {
  posthog.reset()
}

// Function to opt out of tracking
export function optOut() {
  posthog.opt_out_capturing()
}

// Function to opt in to tracking
export function optIn() {
  posthog.opt_in_capturing()
}

// Check if user has opted out
export function hasOptedOut(): boolean {
  return posthog.has_opted_out_capturing()
}

// Check if user has opted in
export function hasOptedIn(): boolean {
  return posthog.has_opted_in_capturing()
}
