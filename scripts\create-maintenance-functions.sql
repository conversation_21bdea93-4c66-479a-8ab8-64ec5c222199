-- Function to check if another function exists
CREATE OR REPLACE FUNCTION public.function_exists(function_name text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  func_exists boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
    AND p.proname = function_name
  ) INTO func_exists;
  
  RETURN func_exists;
END;
$$;

-- Function to create the function_exists function (meta!)
CREATE OR REPLACE FUNCTION public.create_function_exists_function()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE $FUNC$
    CREATE OR REPLACE FUNCTION public.function_exists(function_name text)
    RETURNS boolean
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $INNER$
    DECLARE
      func_exists boolean;
    BEGIN
      SELECT EXISTS (
        SELECT 1
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname = function_name
      ) INTO func_exists;
      
      RETURN func_exists;
    END;
    $INNER$;
  $FUNC$;
END;
$$;

-- Function to create the maintenance function
CREATE OR REPLACE FUNCTION public.create_maintenance_function()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE $FUNC$
    CREATE OR REPLACE FUNCTION public.perform_maintenance(table_name text)
    RETURNS text
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $INNER$
    DECLARE
      result text;
    BEGIN
      -- Analyze the table to update statistics
      EXECUTE 'ANALYZE ' || quote_ident(table_name);
      
      -- We can't run VACUUM FULL directly in a function, but we can run VACUUM
      EXECUTE 'VACUUM ' || quote_ident(table_name);
      
      -- Return success message
      result := 'Maintenance completed for ' || table_name || ' at ' || now();
      RETURN result;
    END;
    $INNER$;
  $FUNC$;
END;
$$;

-- Function to perform maintenance on a table
CREATE OR REPLACE FUNCTION public.perform_maintenance(table_name text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result text;
BEGIN
  -- Analyze the table to update statistics
  EXECUTE 'ANALYZE ' || quote_ident(table_name);
  
  -- We can't run VACUUM FULL directly in a function, but we can run VACUUM
  EXECUTE 'VACUUM ' || quote_ident(table_name);
  
  -- Return success message
  result := 'Maintenance completed for ' || table_name || ' at ' || now();
  RETURN result;
END;
$$;
