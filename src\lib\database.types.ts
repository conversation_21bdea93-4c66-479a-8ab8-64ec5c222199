/**
 * TypeScript definitions for Supabase database tables
 *
 * Auto-generated from actual database schema
 * This file defines the types for all Supabase database tables, providing
 * type safety when interacting with the database through the Supabase client.
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      abuse_patterns: {
        Row: {
          id: number
          session_id: string | null
          pattern_type: string
          confidence_score: number | null
          detected_at: string | null
          evidence: Json | null
          action_taken: string | null
          ip_address: string | null
          user_agent: string | null
          created_at: string | null
        }
        Insert: {
          id?: number
          session_id?: string | null
          pattern_type: string
          confidence_score?: number | null
          detected_at?: string | null
          evidence?: Json | null
          action_taken?: string | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string | null
        }
        Update: {
          id?: number
          session_id?: string | null
          pattern_type?: string
          confidence_score?: number | null
          detected_at?: string | null
          evidence?: Json | null
          action_taken?: string | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string | null
        }
        Relationships: []
      }
      admin_activity_log: {
        Row: {
          id: number
          user_id: number
          action_type: string
          resource_type: string | null
          resource_id: string | null
          details: Json | null
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: number
          user_id: number
          action_type: string
          resource_type?: string | null
          resource_id?: string | null
          details?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: number
          user_id?: number
          action_type?: string
          resource_type?: string | null
          resource_id?: string | null
          details?: Json | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Relationships: []
      }
      admin_users: {
        Row: {
          id: number
          username: string
          email: string
          password_hash: string
          full_name: string | null
          role: string
          is_active: boolean
          created_at: string
          updated_at: string
          last_login: string | null
        }
        Insert: {
          id?: number
          username: string
          email: string
          password_hash: string
          full_name?: string | null
          role?: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
          last_login?: string | null
        }
        Update: {
          id?: number
          username?: string
          email?: string
          password_hash?: string
          full_name?: string | null
          role?: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
          last_login?: string | null
        }
        Relationships: []
      }
      temp_emails: {
        Row: {
          id: number
          email_address: string
          creation_time: string
          expiration_date: string
        }
        Insert: {
          id?: number
          email_address: string
          creation_time?: string
          expiration_date: string
        }
        Update: {
          id?: number
          email_address?: string
          creation_time?: string
          expiration_date?: string
        }
        Relationships: []
      }
      blocked_sessions: {
        Row: {
          id: number
          session_id: string
          reason: string
          blocked_at: string | null
          blocked_by: string | null
          expires_at: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: number
          session_id: string
          reason: string
          blocked_at?: string | null
          blocked_by?: string | null
          expires_at?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: number
          session_id?: string
          reason?: string
          blocked_at?: string | null
          blocked_by?: string | null
          expires_at?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      security_events: {
        Row: {
          id: number
          event_type: string
          session_id: string | null
          ip_address: string | null
          severity: string | null
          description: string | null
          metadata: Json | null
          action_taken: string | null
          created_at: string | null
        }
        Insert: {
          id?: number
          event_type: string
          session_id?: string | null
          ip_address?: string | null
          severity?: string | null
          description?: string | null
          metadata?: Json | null
          action_taken?: string | null
          created_at?: string | null
        }
        Update: {
          id?: number
          event_type?: string
          session_id?: string | null
          ip_address?: string | null
          severity?: string | null
          description?: string | null
          metadata?: Json | null
          action_taken?: string | null
          created_at?: string | null
        }
        Relationships: []
      }
      session_rate_limits: {
        Row: {
          id: number
          session_id: string
          endpoint: string
          metadata: Json | null
          created_at: string | null
        }
        Insert: {
          id?: number
          session_id: string
          endpoint: string
          metadata?: Json | null
          created_at?: string | null
        }
        Update: {
          id?: number
          session_id?: string
          endpoint?: string
          metadata?: Json | null
          created_at?: string | null
        }
        Relationships: []
      }
      database_configurations: {
        Row: {
          id: number
          name: string
          description: string | null
          is_active: boolean
          guerrilla_config: Json
          supabase_config: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          name: string
          description?: string | null
          is_active?: boolean
          guerrilla_config: Json
          supabase_config: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          name?: string
          description?: string | null
          is_active?: boolean
          guerrilla_config?: Json
          supabase_config?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      ad_config: {
        Row: {
          placement_id: string
          domain: string
          ad_unit_id: string
          ad_client_id: string
          is_enabled: boolean
          device_types: Json
          display_options: Json | null
          schedule: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          placement_id: string
          domain: string
          ad_unit_id: string
          ad_client_id?: string
          is_enabled?: boolean
          device_types: Json
          display_options?: Json | null
          schedule?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          placement_id?: string
          domain?: string
          ad_unit_id?: string
          ad_client_id?: string
          is_enabled?: boolean
          device_types?: Json
          display_options?: Json | null
          schedule?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      rate_limit_session_config: {
        Row: {
          id: string
          endpoint: string
          session_max_requests: number
          session_window_ms: number
          session_block_duration: number
          anonymous_max_requests: number
          anonymous_window_ms: number
          strict_mode: boolean
          enabled: boolean
          created_at: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          id?: string
          endpoint: string
          session_max_requests?: number
          session_window_ms?: number
          session_block_duration?: number
          anonymous_max_requests?: number
          anonymous_window_ms?: number
          strict_mode?: boolean
          enabled?: boolean
          created_at?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          id?: string
          endpoint?: string
          session_max_requests?: number
          session_window_ms?: number
          session_block_duration?: number
          anonymous_max_requests?: number
          anonymous_window_ms?: number
          strict_mode?: boolean
          enabled?: boolean
          created_at?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      session_config: {
        Row: {
          id: string
          session_duration: number
          max_session_extensions: number
          activity_update_interval: number
          session_extension_duration: number
          enable_session_persistence: boolean
          created_at: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          id?: string
          session_duration?: number
          max_session_extensions?: number
          activity_update_interval?: number
          session_extension_duration?: number
          enable_session_persistence?: boolean
          created_at?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          id?: string
          session_duration?: number
          max_session_extensions?: number
          activity_update_interval?: number
          session_extension_duration?: number
          enable_session_persistence?: boolean
          created_at?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      analytics_events: {
        Row: {
          id: number
          event_type: string
          session_id: string | null
          user_id: string | null
          session_start_time: string | null
          session_duration: number | null
          page_path: string | null
          referrer: string | null
          country: string | null
          browser: string | null
          device_type: string | null
          timestamp: string
          additional_data: Json | null
        }
        Insert: {
          id?: number
          event_type: string
          session_id?: string | null
          user_id?: string | null
          session_start_time?: string | null
          session_duration?: number | null
          page_path?: string | null
          referrer?: string | null
          country?: string | null
          browser?: string | null
          device_type?: string | null
          timestamp?: string
          additional_data?: Json | null
        }
        Update: {
          id?: number
          event_type?: string
          session_id?: string | null
          user_id?: string | null
          session_start_time?: string | null
          session_duration?: number | null
          page_path?: string | null
          referrer?: string | null
          country?: string | null
          browser?: string | null
          device_type?: string | null
          timestamp?: string
          additional_data?: Json | null
        }
        Relationships: []
      }

      app_config: {
        Row: {
          key: string
          value: Json
          updated_at: string
        }
        Insert: {
          key: string
          value: Json
          updated_at?: string
        }
        Update: {
          key?: string
          value?: Json
          updated_at?: string
        }
        Relationships: []
      }
      domain_config: {
        Row: {
          domain: string
          is_active: boolean
          settings: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          domain: string
          is_active?: boolean
          settings: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          domain?: string
          is_active?: boolean
          settings?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      system_logs: {
        Row: {
          id: number
          level: string
          category: string
          message: string
          metadata: Json | null
          timestamp: string
        }
        Insert: {
          id?: number
          level: string
          category: string
          message: string
          metadata?: Json | null
          timestamp?: string
        }
        Update: {
          id?: number
          level?: string
          category?: string
          message?: string
          metadata?: Json | null
          timestamp?: string
        }
        Relationships: []
      }
      contact_messages: {
        Row: {
          id: number
          name: string
          email: string
          subject: string
          message: string
          status: string
          thread_id: string | null
          parent_id: number | null
          is_admin_reply: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          name: string
          email: string
          subject: string
          message: string
          status?: string
          thread_id?: string | null
          parent_id?: number | null
          is_admin_reply?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          name?: string
          email?: string
          subject?: string
          message?: string
          status?: string
          thread_id?: string | null
          parent_id?: number | null
          is_admin_reply?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contact_messages_parent_id_fkey"
            columns: ["parent_id"]
            referencedRelation: "contact_messages"
            referencedColumns: ["id"]
          }
        ]
      }
      generated_records: {
        Row: {
          id: string
          session_id: string
          user_id: string | null
          record_type: string
          domain: string
          selector: string | null
          dns_record: string
          record_name: string
          private_key_encrypted: string | null
          metadata: Json
          created_at: string
          expires_at: string
          validated_at: string | null
          validation_status: string
          validation_errors: Json
        }
        Insert: {
          id?: string
          session_id: string
          user_id?: string | null
          record_type: string
          domain: string
          selector?: string | null
          dns_record: string
          record_name: string
          private_key_encrypted?: string | null
          metadata?: Json
          created_at?: string
          expires_at?: string
          validated_at?: string | null
          validation_status?: string
          validation_errors?: Json
        }
        Update: {
          id?: string
          session_id?: string
          user_id?: string | null
          record_type?: string
          domain?: string
          selector?: string | null
          dns_record?: string
          record_name?: string
          private_key_encrypted?: string | null
          metadata?: Json
          created_at?: string
          expires_at?: string
          validated_at?: string | null
          validation_status?: string
          validation_errors?: Json
        }
        Relationships: []
      }
      dns_validation_history: {
        Row: {
          id: string
          record_id: string | null
          validation_type: string
          domain: string
          record_name: string
          expected_value: string | null
          actual_values: Json
          is_valid: boolean
          errors: Json
          validated_at: string
        }
        Insert: {
          id?: string
          record_id?: string | null
          validation_type: string
          domain: string
          record_name: string
          expected_value?: string | null
          actual_values?: Json
          is_valid: boolean
          errors?: Json
          validated_at?: string
        }
        Update: {
          id?: string
          record_id?: string | null
          validation_type?: string
          domain?: string
          record_name?: string
          expected_value?: string | null
          actual_values?: Json
          is_valid?: boolean
          errors?: Json
          validated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "dns_validation_history_record_id_fkey"
            columns: ["record_id"]
            referencedRelation: "generated_records"
            referencedColumns: ["id"]
          }
        ]
      }
      tool_audit_log: {
        Row: {
          id: string
          session_id: string | null
          user_id: string | null
          tool_name: string
          action: string
          resource_type: string | null
          resource_id: string | null
          metadata: Json
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          session_id?: string | null
          user_id?: string | null
          tool_name: string
          action: string
          resource_type?: string | null
          resource_id?: string | null
          metadata?: Json
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          session_id?: string | null
          user_id?: string | null
          tool_name?: string
          action?: string
          resource_type?: string | null
          resource_id?: string | null
          metadata?: Json
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Relationships: []
      }
      session_analytics: {
        Row: {
          id: number
          session_id: string
          user_id: string | null
          session_start_time: string
          session_end_time: string | null
          session_duration: number | null
          emails_generated_count: number | null
          emails_received_count: number | null
          emails_viewed_count: number | null
          emails_deleted_count: number | null
          manual_refresh_count: number | null
          copy_actions_count: number | null
          address_regeneration_count: number | null
          device_type: string | null
          browser: string | null
          country: string | null
          referrer: string | null
          created_at: string | null
          updated_at: string | null
          last_seen_at: string | null
          is_active: boolean | null
        }
        Insert: {
          id?: number
          session_id: string
          user_id?: string | null
          session_start_time: string
          session_end_time?: string | null
          session_duration?: number | null
          emails_generated_count?: number | null
          emails_received_count?: number | null
          emails_viewed_count?: number | null
          emails_deleted_count?: number | null
          manual_refresh_count?: number | null
          copy_actions_count?: number | null
          address_regeneration_count?: number | null
          device_type?: string | null
          browser?: string | null
          country?: string | null
          referrer?: string | null
          created_at?: string | null
          updated_at?: string | null
          last_seen_at?: string | null
          is_active?: boolean | null
        }
        Update: {
          id?: number
          session_id?: string
          user_id?: string | null
          session_start_time?: string
          session_end_time?: string | null
          session_duration?: number | null
          emails_generated_count?: number | null
          emails_received_count?: number | null
          emails_viewed_count?: number | null
          emails_deleted_count?: number | null
          manual_refresh_count?: number | null
          copy_actions_count?: number | null
          address_regeneration_count?: number | null
          device_type?: string | null
          browser?: string | null
          country?: string | null
          referrer?: string | null
          created_at?: string | null
          updated_at?: string | null
          last_seen_at?: string | null
          is_active?: boolean | null
        }
        Relationships: []
      }
      ip_whitelist: {
        Row: {
          id: string
          ip_address: string
          reason: string
          added_at: string | null
          added_by: string
          removed_at: string | null
          removed_by: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          ip_address: string
          reason: string
          added_at?: string | null
          added_by: string
          removed_at?: string | null
          removed_by?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          ip_address?: string
          reason?: string
          added_at?: string | null
          added_by?: string
          removed_at?: string | null
          removed_by?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      blocked_ips: {
        Row: {
          id: string
          ip_address: string
          reason: string
          blocked_at: string | null
          blocked_by: string
          expires_at: string | null
          unblocked_at: string | null
          unblocked_by: string | null
          is_active: boolean | null
          metadata: Json | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          ip_address: string
          reason: string
          blocked_at?: string | null
          blocked_by: string
          expires_at?: string | null
          unblocked_at?: string | null
          unblocked_by?: string | null
          is_active?: boolean | null
          metadata?: Json | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          ip_address?: string
          reason?: string
          blocked_at?: string | null
          blocked_by?: string
          expires_at?: string | null
          unblocked_at?: string | null
          unblocked_by?: string | null
          is_active?: boolean | null
          metadata?: Json | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      rate_limit_violations: {
        Row: {
          id: string
          ip_address: string
          endpoint: string
          violation_count: number
          first_violation: string
          last_violation: string
          user_agent: string | null
          is_resolved: boolean
          resolved_at: string | null
          resolved_by: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          ip_address: string
          endpoint: string
          violation_count: number
          first_violation: string
          last_violation: string
          user_agent?: string | null
          is_resolved?: boolean
          resolved_at?: string | null
          resolved_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          ip_address?: string
          endpoint?: string
          violation_count?: number
          first_violation?: string
          last_violation?: string
          user_agent?: string | null
          is_resolved?: boolean
          resolved_at?: string | null
          resolved_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      rate_limit_configs: {
        Row: {
          id: string
          endpoint: string
          name: string
          description: string | null
          window_ms: number
          max_requests: number
          skip_successful_requests: boolean | null
          skip_failed_requests: boolean | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
          updated_by: string
        }
        Insert: {
          id?: string
          endpoint: string
          name: string
          description?: string | null
          window_ms?: number
          max_requests?: number
          skip_successful_requests?: boolean | null
          skip_failed_requests?: boolean | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
          updated_by: string
        }
        Update: {
          id?: string
          endpoint?: string
          name?: string
          description?: string | null
          window_ms?: number
          max_requests?: number
          skip_successful_requests?: boolean | null
          skip_failed_requests?: boolean | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
          updated_by?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      execute_sql: {
        Args: {
          sql: string
        }
        Returns: Json
      }
      cleanup_expired_blocks: {
        Args: {}
        Returns: number
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
