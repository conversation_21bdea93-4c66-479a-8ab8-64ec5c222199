'use client';

import Link from 'next/link';
import Logo from './Logo';

/**
 * Footer Component
 *
 * A sleek and modern footer for the VanishPost application
 */
export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="py-10 mt-8"
            style={{
              color: 'var(--earth-brown-medium)',
              backgroundColor: 'rgba(255, 255, 255, 0.95)', // White glassmorphic background
              backdropFilter: 'blur(20px) saturate(180%) brightness(1.1)', // Enhanced blur with saturation and brightness
              WebkitBackdropFilter: 'blur(20px) saturate(180%) brightness(1.1)', // Safari support
              border: '1px solid rgba(206, 96, 28, 0.25)', // Brand orange border all around
              borderTop: '1px solid rgba(206, 96, 28, 0.25)', // Brand orange top border to match navbar
              borderBottom: '1px solid rgba(206, 96, 28, 0.25)', // Brand orange bottom border
            }}>
      <div className="max-w-5xl mx-auto px-4">
        {/* Improved grid with better responsive behavior */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company info - removed duplicate copyright */}
          <div className="col-span-1 sm:col-span-2 lg:col-span-1">
            <div className="mb-4">
              <Logo />
            </div>
            <p className="text-sm mb-4" style={{ color: 'var(--earth-brown-medium)' }}>Secure, temporary email addresses that expire automatically after 15 minutes.</p>
          </div>

          {/* Combined navigation section */}
          <div className="col-span-1">
            <h3 className="font-medium mb-3 text-sm uppercase tracking-wider" style={{ color: 'var(--earth-brown-dark)' }}>Features</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/features" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Temporary Email</Link></li>
              <li><Link href="/features" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Auto-Expiring</Link></li>
              <li><Link href="/features" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>No Registration</Link></li>
              <li><Link href="/features" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Secure & Private</Link></li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="font-medium mb-3 text-sm uppercase tracking-wider" style={{ color: 'var(--earth-brown-dark)' }}>Resources</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/faq" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>FAQ</Link></li>
              <li><Link href="/privacy" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Privacy Policy</Link></li>
              <li><Link href="/terms" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Terms of Service</Link></li>
              <li><Link href="/contact" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Contact Us</Link></li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="font-medium mb-3 text-sm uppercase tracking-wider" style={{ color: 'var(--earth-brown-dark)' }}>Connect</h3>
            <div className="flex justify-start sm:justify-center lg:justify-start space-x-3 mb-4">
              {/* Twitter hidden until page is created */}
              <a href="https://www.facebook.com/vanishpost" className="p-2 rounded-full shadow-sm text-[#ce601c] transition-all duration-200 transform hover:scale-105"
                 style={{ backgroundColor: 'var(--earth-beige-light)' }}
                 onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--brand-orange-hover)'}
                 onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--earth-beige-light)'}
                 aria-label="Facebook"
                 target="_blank"
                 rel="noopener noreferrer">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                </svg>
              </a>
              <a href="https://www.instagram.com/vanishpost_temporary_email/" className="p-2 rounded-full shadow-sm text-[#ce601c] transition-all duration-200 transform hover:scale-105"
                 style={{ backgroundColor: 'var(--earth-beige-light)' }}
                 onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--brand-orange-hover)'}
                 onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--earth-beige-light)'}
                 aria-label="Instagram"
                 target="_blank"
                 rel="noopener noreferrer">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
                </svg>
              </a>
              {/* LinkedIn hidden until page is created */}
            </div>
            <p className="text-xs text-center sm:text-center lg:text-left" style={{ color: 'var(--earth-brown-medium)' }}>Stay updated with our latest features and announcements.</p>
          </div>
        </div>

        <div className="mt-10 pt-6 flex flex-col md:flex-row justify-between items-center"
             style={{ borderTop: '1px solid rgba(206, 96, 28, 0.25)' }}>
          <p className="text-xs mb-4 md:mb-0 font-medium" style={{ color: 'var(--earth-brown-medium)' }}>© {currentYear} VanishPost. All rights reserved.</p>
          <div className="flex space-x-6 text-xs">
            <Link href="/privacy" className="hover:text-[#ce601c] transition-colors font-medium" style={{ color: 'var(--earth-brown-medium)' }}>Privacy Policy</Link>
            <Link href="/terms" className="hover:text-[#ce601c] transition-colors font-medium" style={{ color: 'var(--earth-brown-medium)' }}>Terms of Service</Link>
            <Link href="/cookies" className="hover:text-[#ce601c] transition-colors font-medium" style={{ color: 'var(--earth-brown-medium)' }}>Cookie Policy</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
