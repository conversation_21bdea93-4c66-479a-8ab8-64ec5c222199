/**
 * SMTP API Endpoint Tests
 *
 * Integration tests for SMTP API endpoints
 */

import { NextRequest } from 'next/server';
import { POST as smtpTesterPost } from '@/app/api/tools/smtp-tester/route';
import { POST as smtpValidatePost } from '@/app/api/tools/smtp-tester/validate/route';

// Mock the SMTP service
jest.mock('@/lib/tools/smtp-tester/smtpService', () => ({
  smtpService: {
    testSmtpConnection: jest.fn()
  }
}));

// Mock the Email Tester database functions
jest.mock('@/lib/tools/email-tester/database', () => ({
  generateEmailTesterAddress: jest.fn()
}));

// Mock nodemailer for validation endpoint
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    verify: jest.fn(),
    close: jest.fn()
  }))
}));

// Mock logging
jest.mock('@/lib/logging', () => ({
  logError: jest.fn()
}));

describe('SMTP API Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock Email Tester address generation
    const { generateEmailTesterAddress } = require('@/lib/tools/email-tester/database');
    const emailDomain = process.env.EMAIL_TESTER_DOMAIN || 'fademail.site';
    generateEmailTesterAddress.mockResolvedValue({
      id: '12345678',
      testAddress: `test-12345678@${emailDomain}`,
      createdAt: new Date(),
      expirationDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
      status: 'pending'
    });
  });

  describe('/api/tools/smtp-tester', () => {
    const validRequest = {
      config: {
        server: 'smtp.gmail.com',
        port: 587,
        encryption: 'tls',
        username: '<EMAIL>',
        password: 'password123',
        sender: '<EMAIL>'
      },
      testMode: 'auto'
    };

    it('should handle successful SMTP test', async () => {
      const { smtpService } = require('@/lib/tools/smtp-tester/smtpService');
      smtpService.testSmtpConnection.mockResolvedValue({
        success: true,
        messageId: 'test-message-id',
        logs: ['Test log 1', 'Test log 2'],
        testAddress: '<EMAIL>',
        testAddressId: '12345678',
        duration: 1500
      });

      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester', {
        method: 'POST',
        body: JSON.stringify(validRequest),
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1'
        }
      });

      const response = await smtpTesterPost(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.messageId).toBe('test-message-id');
      expect(data.logs).toBe('Test log 1\nTest log 2');
      expect(data.testAddress).toBe('<EMAIL>');
    });

    it('should handle SMTP test failure', async () => {
      const { smtpService } = require('@/lib/tools/smtp-tester/smtpService');
      smtpService.testSmtpConnection.mockResolvedValue({
        success: false,
        logs: ['Connection failed'],
        error: 'Authentication failed',
        duration: 500
      });

      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester', {
        method: 'POST',
        body: JSON.stringify(validRequest),
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1'
        }
      });

      const response = await smtpTesterPost(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication failed');
      expect(data.logs).toBe('Connection failed');
    });

    it('should validate request data', async () => {
      const invalidRequest = {
        config: {
          server: '', // Invalid server
          port: 587,
          encryption: 'tls',
          username: '<EMAIL>',
          password: 'password123',
          sender: '<EMAIL>'
        },
        testMode: 'auto'
      };

      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester', {
        method: 'POST',
        body: JSON.stringify(invalidRequest),
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1'
        }
      });

      const response = await smtpTesterPost(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid request data');
      expect(data.details).toContain('Invalid SMTP server address');
    });

    it('should require recipient for custom mode', async () => {
      const customRequest = {
        ...validRequest,
        testMode: 'custom'
        // No recipient provided
      };

      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester', {
        method: 'POST',
        body: JSON.stringify(customRequest),
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1'
        }
      });

      const response = await smtpTesterPost(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.details).toContain('Valid recipient email address is required for custom mode');
    });

    it('should handle rate limiting', async () => {
      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester', {
        method: 'POST',
        body: JSON.stringify(validRequest),
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1'
        }
      });

      // Make multiple requests to trigger rate limiting
      const responses = await Promise.all([
        smtpTesterPost(request),
        smtpTesterPost(request),
        smtpTesterPost(request),
        smtpTesterPost(request),
        smtpTesterPost(request),
        smtpTesterPost(request),
        smtpTesterPost(request),
        smtpTesterPost(request),
        smtpTesterPost(request),
        smtpTesterPost(request),
        smtpTesterPost(request), // This should be rate limited
      ]);

      const lastResponse = responses[responses.length - 1];
      const data = await lastResponse.json();

      expect(lastResponse.status).toBe(429);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Rate limit exceeded');
    });

    it('should handle malformed JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1'
        }
      });

      const response = await smtpTesterPost(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('An unexpected error occurred during SMTP testing');
    });
  });

  describe('/api/tools/smtp-tester/validate', () => {
    const validConfig = {
      config: {
        server: 'smtp.gmail.com',
        port: 587,
        encryption: 'tls',
        username: '<EMAIL>',
        password: 'password123',
        sender: '<EMAIL>'
      }
    };

    it('should handle successful validation', async () => {
      const nodemailer = require('nodemailer');
      const mockTransporter = {
        verify: jest.fn().mockResolvedValue(true),
        close: jest.fn()
      };
      nodemailer.createTransport.mockReturnValue(mockTransporter);

      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester/validate', {
        method: 'POST',
        body: JSON.stringify(validConfig),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await smtpValidatePost(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toBe('SMTP configuration is valid and connection successful');
      expect(mockTransporter.verify).toHaveBeenCalled();
    });

    it('should handle validation failure', async () => {
      const nodemailer = require('nodemailer');
      const mockTransporter = {
        verify: jest.fn().mockRejectedValue(new Error('Connection refused')),
        close: jest.fn()
      };
      nodemailer.createTransport.mockReturnValue(mockTransporter);

      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester/validate', {
        method: 'POST',
        body: JSON.stringify(validConfig),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await smtpValidatePost(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Connection refused');
    });

    it('should validate configuration before testing', async () => {
      const invalidConfig = {
        config: {
          server: '', // Invalid server
          port: 587,
          encryption: 'tls',
          username: '<EMAIL>',
          password: 'password123',
          sender: '<EMAIL>'
        }
      };

      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester/validate', {
        method: 'POST',
        body: JSON.stringify(invalidConfig),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await smtpValidatePost(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid SMTP configuration');
      expect(data.details).toContain('Invalid SMTP server address');
    });

    it('should require config in request body', async () => {
      const request = new NextRequest('http://localhost:3000/api/tools/smtp-tester/validate', {
        method: 'POST',
        body: JSON.stringify({}),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const response = await smtpValidatePost(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('SMTP configuration is required');
    });
  });
});
