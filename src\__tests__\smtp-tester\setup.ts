/**
 * SMTP Tester Test Setup
 *
 * Setup configuration for SMTP tester tests
 */

import '@testing-library/jest-dom';

// Mock environment variables
process.env.NODE_ENV = 'test';
// Use default domain for tests (EMAIL_TESTER_DOMAIN will be set per test as needed)

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Mock fetch globally
global.fetch = jest.fn();

// Mock crypto for UUID generation
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => '12345678-1234-1234-1234-123456789012')
  }
});

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn()
  }),
  useSearchParams: () => ({
    get: jest.fn(),
    has: jest.fn(),
    getAll: jest.fn(),
    keys: jest.fn(),
    values: jest.fn(),
    entries: jest.fn(),
    forEach: jest.fn(),
    toString: jest.fn()
  }),
  usePathname: () => '/tools/smtp-tester'
}));

// Mock next/headers
jest.mock('next/headers', () => ({
  headers: () => ({
    get: jest.fn(),
    has: jest.fn(),
    keys: jest.fn(),
    values: jest.fn(),
    entries: jest.fn(),
    forEach: jest.fn()
  })
}));

// Reset all mocks between tests
beforeEach(() => {
  jest.clearAllMocks();

  // Reset fetch mock
  (global.fetch as jest.Mock).mockClear();

  // Reset crypto mock
  (global.crypto.randomUUID as jest.Mock).mockReturnValue('12345678-1234-1234-1234-123456789012');
});

// Custom matchers for SMTP testing
expect.extend({
  toBeValidEmailAddress(received: string) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const pass = emailRegex.test(received);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid email address`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid email address`,
        pass: false
      };
    }
  },

  toBeValidSmtpServer(received: string) {
    const serverRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    const pass = serverRegex.test(received);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid SMTP server`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid SMTP server`,
        pass: false
      };
    }
  },

  toBeValidPort(received: number) {
    const pass = Number.isInteger(received) && received >= 1 && received <= 65535;

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid port number`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid port number (1-65535)`,
        pass: false
      };
    }
  }
});

// Declare custom matchers for TypeScript
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidEmailAddress(): R;
      toBeValidSmtpServer(): R;
      toBeValidPort(): R;
    }
  }
}

// Test utilities
export const createMockSmtpConfig = (overrides = {}) => ({
  server: 'smtp.gmail.com',
  port: 587,
  encryption: 'tls' as const,
  username: '<EMAIL>',
  password: 'password123',
  sender: '<EMAIL>',
  ...overrides
});

export const createMockSmtpTestRequest = (overrides = {}) => ({
  config: createMockSmtpConfig(),
  testMode: 'auto' as const,
  ...overrides
});

export const createMockSuccessResult = (overrides = {}) => ({
  success: true,
  messageId: 'test-message-id',
  logs: 'Test completed successfully',
  testAddress: '<EMAIL>',
  duration: 1500,
  ...overrides
});

export const createMockFailureResult = (overrides = {}) => ({
  success: false,
  error: 'Test failed',
  logs: 'Connection failed',
  duration: 500,
  ...overrides
});

export const mockFetchSuccess = (data: any) => {
  (global.fetch as jest.Mock).mockResolvedValueOnce({
    ok: true,
    json: async () => data,
    text: async () => JSON.stringify(data)
  });
};

export const mockFetchError = (error: string, status = 400) => {
  (global.fetch as jest.Mock).mockResolvedValueOnce({
    ok: false,
    status,
    json: async () => ({ success: false, error }),
    text: async () => JSON.stringify({ success: false, error })
  });
};

export const mockFetchNetworkError = () => {
  (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));
};

// Test data generators
export const generateTestEmailAddress = () => {
  const uuid = global.crypto.randomUUID();
  const emailDomain = process.env.EMAIL_TESTER_DOMAIN || 'fademail.site';
  return `test-${uuid}@${emailDomain}`;
};

export const generateRandomString = (length: number) => {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

export const generateMaliciousString = (type: 'xss' | 'sql' | 'command') => {
  switch (type) {
    case 'xss':
      return '<script>alert("xss")</script>';
    case 'sql':
      return "'; DROP TABLE users; --";
    case 'command':
      return '; rm -rf /';
    default:
      return '';
  }
};

// Performance testing utilities
export const measureExecutionTime = async (fn: () => Promise<any>) => {
  const start = Date.now();
  await fn();
  return Date.now() - start;
};

export const createLoadTest = (fn: () => Promise<any>, iterations: number) => {
  return Promise.all(Array(iterations).fill(null).map(() => fn()));
};

// Cleanup utilities
export const cleanupTestData = () => {
  // Clean up any test data if needed
  jest.clearAllMocks();
};

// Export test constants
export const TEST_CONSTANTS = {
  VALID_EMAIL: '<EMAIL>',
  INVALID_EMAIL: 'invalid-email',
  VALID_SERVER: 'smtp.gmail.com',
  INVALID_SERVER: 'invalid-server',
  VALID_PORT: 587,
  INVALID_PORT: 70000,
  TEST_TIMEOUT: 10000,
  RATE_LIMIT_REQUESTS: 10
} as const;
