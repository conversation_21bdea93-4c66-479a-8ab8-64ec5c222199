// DKIM Generator Test Suite
import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { generateDkimKeyPair, validateKeyPair } from '@/lib/tools/dkim-generator/keyGeneration';
import { formatDkimRecord, validateDkimInputs } from '@/lib/tools/dkim-generator/recordFormatting';
import { validateDkimRecord } from '@/lib/tools/shared/dnsValidation';

// Mock DNS resolution for testing
jest.mock('dns', () => ({
  promises: {
    resolveTxt: jest.fn(),
  },
}));

describe('DKIM Generator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Key Generation', () => {
    it('should generate valid RSA key pairs for 1024-bit keys', async () => {
      const keyPair = await generateDkimKeyPair(1024);
      
      expect(keyPair).toHaveProperty('privateKey');
      expect(keyPair).toHaveProperty('publicKey');
      expect(keyPair).toHaveProperty('generatedAt');
      expect(keyPair.privateKey).toContain('-----BEGIN RSA PRIVATE KEY-----');
      expect(keyPair.publicKey).toMatch(/^[A-Za-z0-9+/]+=*$/); // Base64 format
    });

    it('should generate valid RSA key pairs for 2048-bit keys', async () => {
      const keyPair = await generateDkimKeyPair(2048);
      
      expect(keyPair).toHaveProperty('privateKey');
      expect(keyPair).toHaveProperty('publicKey');
      expect(keyPair.publicKey.length).toBeGreaterThan(300); // 2048-bit keys are longer
    });

    it('should validate generated key pairs', async () => {
      const keyPair = await generateDkimKeyPair(1024);
      const isValid = await validateKeyPair(keyPair.privateKey, keyPair.publicKey);
      
      expect(isValid).toBe(true);
    });

    it('should reject invalid key strength', async () => {
      await expect(generateDkimKeyPair(512)).rejects.toThrow('Invalid key strength');
    });
  });

  describe('DNS Record Formatting', () => {
    it('should format DKIM DNS records correctly', () => {
      const mockKeyPair = {
        privateKey: 'mock-private-key',
        publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7vbqajDw',
        generatedAt: '2024-01-01T00:00:00.000Z'
      };

      const record = formatDkimRecord('example.com', 'default', mockKeyPair);
      
      expect(record.recordName).toBe('default._domainkey.example.com');
      expect(record.dnsRecord).toContain('v=DKIM1');
      expect(record.dnsRecord).toContain('k=rsa');
      expect(record.dnsRecord).toContain('p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7vbqajDw');
    });

    it('should handle long public keys with proper formatting', () => {
      const longPublicKey = 'A'.repeat(400); // Very long key
      const mockKeyPair = {
        privateKey: 'mock-private-key',
        publicKey: longPublicKey,
        generatedAt: '2024-01-01T00:00:00.000Z'
      };

      const record = formatDkimRecord('example.com', 'default', mockKeyPair);
      
      expect(record.dnsRecord).toContain(`p=${longPublicKey}`);
      expect(record.dnsRecord.length).toBeLessThan(2048); // DNS record size limit
    });
  });

  describe('Input Validation', () => {
    it('should validate correct DKIM inputs', () => {
      const validInputs = {
        domain: 'example.com',
        selector: 'default',
        keyStrength: 2048 as const,
        sessionId: 'test-session'
      };

      const result = validateDkimInputs(validInputs);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid domain formats', () => {
      const invalidInputs = {
        domain: 'invalid..domain',
        selector: 'default',
        keyStrength: 2048 as const,
        sessionId: 'test-session'
      };

      const result = validateDkimInputs(invalidInputs);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid domain format');
    });

    it('should reject invalid selectors', () => {
      const invalidInputs = {
        domain: 'example.com',
        selector: 'invalid selector with spaces',
        keyStrength: 2048 as const,
        sessionId: 'test-session'
      };

      const result = validateDkimInputs(invalidInputs);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid selector format');
    });

    it('should reject invalid key strengths', () => {
      const invalidInputs = {
        domain: 'example.com',
        selector: 'default',
        keyStrength: 512 as any,
        sessionId: 'test-session'
      };

      const result = validateDkimInputs(invalidInputs);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Key strength must be 1024 or 2048 bits');
    });
  });

  describe('DNS Validation', () => {
    it('should validate existing DKIM records', async () => {
      const mockDnsResponse = [
        ['v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7vbqajDw']
      ];
      
      const dns = require('dns');
      dns.promises.resolveTxt.mockResolvedValue(mockDnsResponse);

      const result = await validateDkimRecord('example.com', 'default', 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7vbqajDw');
      
      expect(result.exists).toBe(true);
      expect(result.isValid).toBe(true);
      expect(result.records).toEqual(['v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7vbqajDw']);
    });

    it('should handle DNS resolution failures gracefully', async () => {
      const dns = require('dns');
      dns.promises.resolveTxt.mockRejectedValue(new Error('NXDOMAIN'));

      const result = await validateDkimRecord('nonexistent.com', 'default', 'test-key');
      
      expect(result.exists).toBe(false);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('DNS record not found');
    });

    it('should detect public key mismatches', async () => {
      const mockDnsResponse = [
        ['v=DKIM1; k=rsa; p=DifferentPublicKey']
      ];
      
      const dns = require('dns');
      dns.promises.resolveTxt.mockResolvedValue(mockDnsResponse);

      const result = await validateDkimRecord('example.com', 'default', 'ExpectedPublicKey');
      
      expect(result.exists).toBe(true);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Public key mismatch');
    });
  });

  describe('Error Handling', () => {
    it('should handle key generation failures gracefully', async () => {
      // Mock a failure in the crypto module
      const originalForge = require('node-forge');
      jest.doMock('node-forge', () => ({
        ...originalForge,
        pki: {
          ...originalForge.pki,
          rsa: {
            generateKeyPair: () => {
              throw new Error('Crypto failure');
            }
          }
        }
      }));

      await expect(generateDkimKeyPair(1024)).rejects.toThrow();
    });

    it('should validate DNS record format strictly', () => {
      const invalidRecord = 'invalid-dkim-record';
      
      // This should be tested through the DNS validation function
      expect(() => {
        // Simulate parsing an invalid DKIM record
        if (!invalidRecord.includes('v=DKIM1')) {
          throw new Error('Invalid DKIM record format');
        }
      }).toThrow('Invalid DKIM record format');
    });
  });

  describe('Performance', () => {
    it('should generate keys within reasonable time limits', async () => {
      const startTime = Date.now();
      await generateDkimKeyPair(1024);
      const endTime = Date.now();
      
      // Key generation should complete within 5 seconds
      expect(endTime - startTime).toBeLessThan(5000);
    });

    it('should handle multiple concurrent key generations', async () => {
      const promises = Array(3).fill(null).map(() => generateDkimKeyPair(1024));
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result).toHaveProperty('privateKey');
        expect(result).toHaveProperty('publicKey');
      });
    });
  });
});

describe('DKIM Integration Tests', () => {
  it('should complete full DKIM generation workflow', async () => {
    // Test the complete workflow from generation to validation
    const domain = 'test.example.com';
    const selector = 'test';
    
    // 1. Generate key pair
    const keyPair = await generateDkimKeyPair(2048);
    expect(keyPair).toHaveProperty('privateKey');
    expect(keyPair).toHaveProperty('publicKey');
    
    // 2. Format DNS record
    const dnsRecord = formatDkimRecord(domain, selector, keyPair);
    expect(dnsRecord.recordName).toBe(`${selector}._domainkey.${domain}`);
    expect(dnsRecord.dnsRecord).toContain('v=DKIM1');
    
    // 3. Validate inputs
    const validation = validateDkimInputs({
      domain,
      selector,
      keyStrength: 2048,
      sessionId: 'integration-test'
    });
    expect(validation.isValid).toBe(true);
  });
});
