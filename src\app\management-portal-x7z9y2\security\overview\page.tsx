﻿'use client';

import SecurityOverview from '@/components/admin/security/SecurityOverview';

/**
 * Security Overview Page for Secure Admin Portal
 * Updated: 2025-07-25 - Production deployment trigger
 */
export default function SecurityOverviewPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Security Overview</h1>
        <p className="mt-2 text-sm text-gray-600">
          Comprehensive security monitoring and management for your VanishPost service.
        </p>
      </div>

      <SecurityOverview />
    </div>
  );
}
