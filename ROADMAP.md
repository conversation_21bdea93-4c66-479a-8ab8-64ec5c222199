# Fademail Project Roadmap

## Project Overview
Fademail is a disposable email service allowing users to generate temporary email addresses and view received emails through a clean web interface. The system automatically cleans up expired emails to maintain performance and storage efficiency.

### Tech Stack (Pre-initialized)
- **Frontend**: Next.js 15.3.0 with TypeScript 5.8.3, React, Tailwind CSS/PostCSS 4.1.3
- **Backend**: Next.js API routes
- **Databases**: MySQL (local for temporary email management, remote for email storage)
- **Email Server**: go-guerrilla (pre-configured on Digital Ocean droplet)
- **Deployment**: Digital Ocean droplet with PM2 process manager
- **DevOps**: Git, GitHub, Prettier
- **Testing**: Jest, React Testing Library

## Phase 1: Project Review & Documentation

### 1.1 Configuration Review
- [x] Review core guideline documents:
  - [x] [Clean code practices](./clean-code.md)
  - [x] [Next.js best practices](./nextjs.md)
  - [x] [React development standards](./react.md)
  - [x] [Tailwind usage guidelines](./tailwind.md)
  - [x] [UI design system](./UI_implementation.md)

### 1.2 Code Structure Review
- [x] Review existing project structure:
  ```
  /src
    /app
      /api
      /components
      /lib
    /public
  /scripts
  ```
- [x] Document any necessary structural adjustments

### 1.3 Database Configuration
> **Note:** The databases have already been configured with the following details:
>
> **Local MySQL Database** (for temporary email management):
> - DB_HOST=localhost
> - DB_USER=tempmail_web_user
> - DB_PASSWORD=yiWTpFChtFW3neWCS
> - DB_NAME=tempmail_web
>
> **Local Database Schema** (`TempEmail` table):
> ```
> +---------------+--------------+------+-----+-------------------+-------------------+
> | Field         | Type         | Null | Key | Default           | Extra             |
> +---------------+--------------+------+-----+-------------------+-------------------+
> | id            | int          | NO   | PRI | NULL              | auto_increment    |
> | emailAddress  | varchar(255) | NO   | UNI | NULL              |                   |
> | creationTime  | datetime     | NO   |     | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
> | expirationDate| datetime     | NO   |     | NULL              |                   |
> +---------------+--------------+------+-----+-------------------+-------------------+
> ```
>
> **Guerrilla Database** (on droplet):
> - GUERRILLA_DB_HOST=your-guerrilla-db-host
> - GUERRILLA_DB_USER=your-guerrilla-db-user
> - GUERRILLA_DB_PASSWORD=your-guerrilla-db-password
> - GUERRILLA_DB_NAME=your-guerrilla-db-name
>
> **Guerrilla Database Schema** (`guerrilla_mail` table):
> ```
> +-------------+------------------+------+-----+-------------------+-------------------+
> | Field       | Type             | Null | Key | Default           | Extra             |
> +-------------+------------------+------+-----+-------------------+-------------------+
> | mail_id     | bigint unsigned  | NO   | PRI | null              | auto_increment    |
> | message_id  | varchar(255)     | YES  |     | null              |                   |
> | date        | timestamp        | YES  | MUL | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
> | from        | varchar(255)     | YES  |     | null              |                   |
> | to          | varchar(255)     | YES  | MUL | null              |                   |
> | reply_to    | varchar(255)     | YES  |     | null              |                   |
> | sender      | varchar(255)     | YES  |     | null              |                   |
> | subject     | text             | YES  |     | null              |                   |
> | body        | text             | YES  |     | null              |                   |
> | mail        | mediumtext       | YES  |     | null              |                   |
> | spam_score  | float            | YES  |     | null              |                   |
> | hash        | char(32)         | YES  | MUL | null              |                   |
> | content_type| varchar(255)     | YES  |     | null              |                   |
> | recipient   | varchar(255)     | YES  |     | null              |                   |
> | has_attach  | tinyint(1)       | YES  |     | null              |                   |
> | ip_addr     | binary(16)       | YES  |     | null              |                   |
> | return_path | varchar(255)     | YES  |     | null              |                   |
> | is_tls      | tinyint(1)       | YES  |     | null              |                   |
> +-------------+------------------+------+-----+-------------------+-------------------+
> ```
>
> **Note:** Emails received by go-guerrilla are stored in the `guerrilla_mail` table, specifically in the `mail` column as `mediumtext`. Next.js must query this column to display emails.

Tasks (Days 2-3):
- [x] Create `TempEmail` table schema with appropriate indexes in local database
- [x] Create utility functions for database connections in `/lib/db.ts`
- [x] Implement secure credential management for both databases
- [x] Create test queries to verify connections to both databases

### 1.4 Core TypeScript Types and Interfaces (Day 3)
- [x] Define email interface structures and types
- [x] Create database models for both local and droplet databases
- [x] Set up TypeScript configuration with strict mode

## Phase 2: API Development (Week 2)

### 2.1 Email Generation API (Days 1-2)
- [x] Implement `/api/generate/route.ts` for creating temporary email addresses
- [x] Add UUID generation with appropriate validation
- [x] Store generated addresses in local database with expiration timestamp
- [x] Create proper error handling and response formatting
- [x] Implement domain rotation between fademail.site and flickmail.site
- [x] Generate human-like email addresses with random strings

### 2.2 Email Retrieval API (Days 3-4)
- [x] Implement `/api/emails/[address]/route.ts` to fetch emails from droplet DB
- [x] Add pagination support for large email collections
- [x] Implement sorting and filtering options
- [x] Add proper security measures to prevent unauthorized access
- [x] Implement email parsing with `simpleParser`: For each row, simpleParser processes the mail blob
  - [x] Extract `text`, `html`, sender details (`fromName`, `fromEmail`), and `attachments` Note: the fromName and fromEmail are extracted from the "from header"
  - [x] Replace CID references (inline images) with base64 data URLs
  - [x] Sanitize HTML with `DOMPurify` to prevent XSS

### 2.3 Email Parsing Performance Optimization (Days 4-5)
- [x] Implement caching layer using `node-cache` for parsed emails
- [x] Split email fetching into preview and full content modes
- [x] Add size-based processing strategy for large emails
- [x] Implement worker threads for parallel processing of multiple emails
- [x] Set up progressive loading for better user experience
- [x] Create standard parsed email response format:
  ```
  {
    mail_id: string,
    from: string,
    fromName: string,
    fromEmail: string,
    to: string,
    subject: string,
    date: string,
    text: string,
    html: string,
    attachments: Array
  }
  ```

### 2.4 Cleanup Service (Days 5-6)
- [x] Implement `/api/cleanup/route.ts` for expired address removal
- [x] Configure scheduled execution
- [x] Add logging for cleanup operations
- [x] Add manual trigger option for immediate cleanup

## Phase 3: Frontend Development (Week 3)

### 3.1 UI Planning & Design System (Day 1)
- [x] Conduct UI preference survey with stakeholder
  - [x] Color scheme preferences
  - [x] Layout style (minimal vs. feature-rich)
  - [x] Typography choices
  - [x] Component styling (rounded vs. sharp edges)
  - [x] Animation preferences
  - [x] Dark mode priority
- [x] Create design system based on survey results
- [x] Document finalized UI decisions

### 3.2 Core UI Components (Days 2-3)
- [x] Review and reference [UI guidelines document](./UI_implementation.md) for consistent design implementation
- [x] Create responsive layout with header and main content area following [UI guide](./UI_implementation.md) specifications
- [x] Implement email generation form component adhering to UI style patterns
- [x] Design email list view component with sort/filter controls based on [UI guidelines](./UI_implementation.md)
- [x] Create email detail view component with HTML sanitization matching established UI patterns

### 3.3 Main Application Page (Days 3-4)
- [x] Implement `/app/page.tsx` with server-side rendering
- [x] Ensure all UI elements conform to [UI style guide](./UI_implementation.md) (colors, typography, spacing)
- [x] Add client-side state management for email viewing
- [x] Implement real-time email checking with polling
- [x] Add copy-to-clipboard functionality for email addresses with appropriate UI feedback

### 3.4 UI Enhancements (Day 5)
- [x] Add loading states and animations as specified in [UI guidelines](./UI_implementation.md)
- [x] Implement error handling and user notifications following UI standards
- [x] Add responsive design for mobile devices (refer to mobile-specific [UI guidelines](./UI_implementation.md))
- [x] Implement theme support (light/dark mode) according to UI style guide specifications
- [x] Conduct UI review to ensure compliance with established design system

### 3.5 Auto-Refresh & Email Deletion Improvements (Day 6)
- [x] Increase auto-refresh interval from 8 to 14 seconds for better performance
- [x] Implement pause/resume functionality for auto-refresh during email deletion
- [x] Add timestamp-based mechanism to discard outdated GET requests
- [x] Add delays after operations to ensure proper synchronization
- [x] Fix email parser to handle array 'to' field

## Phase 4: Testing & Optimization (Week 4)

### 4.1 Unit Testing (Days 1-2)
- [ ] Write Jest tests for API endpoints
- [ ] Create test cases for database operations
- [ ] Test email rendering and sanitization
- [ ] Add integration tests for critical flows

### 4.2 Performance Optimization (Days 3-4)
- [ ] Implement query optimization for large email collections
- [ ] Add caching for frequently accessed data
- [ ] Optimize React component rendering
- [ ] Measure and improve Core Web Vitals

### 4.3 Security Audit (Day 5)
- [ ] Review for SQL injection vulnerabilities
- [ ] Audit HTML sanitization for XSS prevention
- [ ] Check for proper error handling and information exposure
- [ ] Verify rate limiting implementation

## Phase 5: Deployment & Monitoring (Week 5)

### 5.1 Production Deployment (Days 1-2)
- [ ] Set up PM2 for process management
- [ ] Configure production build process
- [ ] Implement environment-specific configurations
- [ ] Deploy to Digital Ocean droplet

### 5.2 Monitoring & Maintenance (Days 3-4)
- [ ] Set up logging and monitoring
- [ ] Configure database maintenance scripts
- [ ] Implement disk usage monitoring
- [ ] Create backup and restore procedures

### 5.3 Documentation & Handover (Day 5)
- [x] Complete API documentation
- [x] Create maintenance guide
- [x] Document database schemas
- [x] Finalize user guide

## Phase 6: Monetization (Week 6)

### 6.1 Ad Integration Planning (Day 1)
- [ ] Research ad networks (Google AdSense, Media.net, etc.)
- [ ] Identify optimal ad placement locations
- [ ] Determine ad formats (banner, responsive, etc.)
- [ ] Create mockups for ad placement

### 6.2 Ad Implementation (Days 2-3)
- [ ] Create reusable ad components
- [ ] Implement ad loading with performance optimization
- [ ] Add responsive ad containers
- [ ] Ensure ads don't interfere with core functionality

### 6.3 Ad Testing & Optimization (Days 4-5)
- [ ] Test ad loading performance
- [ ] Verify ad display across devices
- [ ] Implement A/B testing for ad placements
- [ ] Set up ad revenue tracking

## Technical Standards Reference

### Coding Standards
- Follow TypeScript best practices with strict type checking
- Use Conventional Commits format for all git commits
- Maintain consistent code formatting with Prettier
- Keep functions small and focused on single responsibility

### Performance Requirements
- API response times under 200ms for typical requests
- Email list loading under 1 second for up to 100 emails
- Cleanup process completion under 30 seconds
- Frontend initial load under 2 seconds on standard connections

### Database Guidelines
- Use parameterized queries for all database operations
- Implement connection pooling for efficiency
- Regular optimization of tables after bulk operations
- Monitor query performance with EXPLAIN

### UI Guidelines
- Always refer to the UI style guide document before implementing new interface elements
- Maintain consistent spacing, typography, and color schemes across all components
- Follow accessibility guidelines (WCAG 2.1 AA) for all user interface elements
- Ensure responsive design principles are applied consistently across all screen sizes
