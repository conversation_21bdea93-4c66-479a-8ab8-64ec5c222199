/**
 * Cache Initialization Utilities
 * 
 * This module provides functions to initialize and manage application caches.
 */

import { logger } from '@/lib/logging/Logger';

// Define the caches that need to be initialized
interface CacheInfo {
  name: string;
  description: string;
  initFunction: () => Promise<boolean>;
}

/**
 * Initialize all application caches
 * @returns Array of initialized cache names
 */
export async function initializeCaches(): Promise<string[]> {
  const caches: CacheInfo[] = [
    {
      name: 'config',
      description: 'Application configuration cache',
      initFunction: async () => {
        // We'll just log the initialization for now
        await logger.info('CACHE_INIT', 'Initialized config cache');
        return true;
      }
    },
    {
      name: 'domain',
      description: 'Domain settings cache',
      initFunction: async () => {
        // We'll just log the initialization for now
        await logger.info('CACHE_INIT', 'Initialized domain cache');
        return true;
      }
    },
    {
      name: 'ad',
      description: 'Advertisement configuration cache',
      initFunction: async () => {
        // We'll just log the initialization for now
        await logger.info('CACHE_INIT', 'Initialized ad configuration cache');
        return true;
      }
    }
  ];

  // Initialize each cache
  const results = await Promise.all(
    caches.map(async (cache) => {
      try {
        const success = await cache.initFunction();
        return { name: cache.name, success };
      } catch (error) {
        await logger.error('CACHE_INIT', `Failed to initialize ${cache.name} cache: ${error instanceof Error ? error.message : String(error)}`);
        return { name: cache.name, success: false };
      }
    })
  );

  // Filter for successful initializations
  const initializedCaches = results
    .filter(result => result.success)
    .map(result => result.name);

  await logger.info('CACHE_INIT', `Successfully initialized ${initializedCaches.length} of ${caches.length} caches`);
  
  return initializedCaches;
}
