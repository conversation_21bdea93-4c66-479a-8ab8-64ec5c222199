/**
 * API route for cleaning up expired emails
 *
 * This API provides functionality to clean up expired emails from the database.
 * It can be triggered manually or scheduled to run at regular intervals.
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { getGuerrillaDbConnection } from '@/lib/db';
import { logInfo, logError } from '@/lib/logging';
import { getConfig } from '@/lib/config/configService';

interface CleanupResult {
  success: boolean;
  tempEmailsDeleted: number;
  guerrillaEmailsDeleted: number;
  duration: number;
  timestamp: string;
  error?: string;
}

/**
 * GET /api/admin/cleanup
 *
 * Cleans up expired emails from the database
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Get cleanup configuration
    const cleanupBatchSize = await getConfig('cleanupBatchSize') || 1000;

    // Initialize result
    const result: CleanupResult = {
      success: true,
      tempEmailsDeleted: 0,
      guerrillaEmailsDeleted: 0,
      duration: 0,
      timestamp: new Date().toISOString()
    };

    // Get Supabase client
    const supabase = createServerSupabaseClient();

    // Step 1: Get expired email addresses
    const { data: expiredEmails, error: expiredError } = await supabase
      .from('temp_emails')
      .select('email_address')
      .lt('expiration_date', new Date().toISOString())
      .limit(cleanupBatchSize);

    if (expiredError) {
      throw new Error(`Failed to get expired emails: ${expiredError.message}`);
    }

    if (!expiredEmails || expiredEmails.length === 0) {
      // No expired emails to clean up
      result.duration = Date.now() - startTime;

      logInfo('cleanup', 'No expired emails to clean up', {
        duration: result.duration
      });

      return NextResponse.json(result);
    }

    // Extract email addresses
    const expiredAddresses = expiredEmails.map(email => email.email_address);

    // Step 2: Delete emails from Guerrilla database
    try {
      const connection = await getGuerrillaDbConnection();

      // Check if the mail table exists
      try {
        // First check if the table exists
        const [tables] = await connection.execute(
          "SHOW TABLES LIKE 'guerrilla_mail'"
        );

        // If the table exists, proceed with deletion
        if (Array.isArray(tables) && tables.length > 0) {
          // Delete in batches to avoid potential issues with large IN clauses
          const batchSize = 100;
          let guerrillaDeleted = 0;

          for (let i = 0; i < expiredAddresses.length; i += batchSize) {
            const batch = expiredAddresses.slice(i, i + batchSize);
            const placeholders = batch.map(() => '?').join(',');

            // Delete from guerrilla_mail table
            const [mailResult] = await connection.execute(
              `DELETE FROM guerrilla_mail WHERE recipient IN (${placeholders})`,
              batch
            );

            guerrillaDeleted += (mailResult as any).affectedRows || 0;
          }

          result.guerrillaEmailsDeleted = guerrillaDeleted;

          logInfo('cleanup', `Deleted ${guerrillaDeleted} emails from Guerrilla database`, {
            expiredAddressesCount: expiredAddresses.length
          });
        } else {
          // Table doesn't exist, log and continue
          logInfo('cleanup', "Guerrilla 'guerrilla_mail' table doesn't exist, skipping Guerrilla cleanup");
          result.guerrillaEmailsDeleted = 0;
        }
      } catch (tableCheckError) {
        logError('cleanup', "Error checking if 'guerrilla_mail' table exists", { tableCheckError });
        result.guerrillaEmailsDeleted = 0;
      }

      // Release the connection
      connection.release();
    } catch (guerrillaError) {
      logError('cleanup', 'Error connecting to Guerrilla database', { guerrillaError });
      // Continue with Supabase cleanup even if Guerrilla cleanup fails
      result.guerrillaEmailsDeleted = 0;
    }

    // Step 3: Delete expired email addresses from Supabase
    let tempEmailsDeleted = 0;

    if (expiredAddresses.length > 0) {
      const { error: deleteError, count } = await supabase
        .from('temp_emails')
        .delete({ count: 'exact' })
        .in('email_address', expiredAddresses);

      if (deleteError) {
        logError('cleanup', 'Error deleting expired emails from Supabase', { error: deleteError });
      } else {
        tempEmailsDeleted = count || 0;
      }
    } else {
      logInfo('cleanup', 'No expired emails to delete from Supabase');
    }

    result.tempEmailsDeleted = tempEmailsDeleted;
    result.duration = Date.now() - startTime;

    logInfo('cleanup', 'Cleanup completed successfully', {
      tempEmailsDeleted: result.tempEmailsDeleted,
      guerrillaEmailsDeleted: result.guerrillaEmailsDeleted,
      duration: result.duration
    });

    return NextResponse.json(result);
  } catch (error) {
    const duration = Date.now() - startTime;

    logError('cleanup', 'Error during cleanup', { error, duration });

    return NextResponse.json({
      success: false,
      tempEmailsDeleted: 0,
      guerrillaEmailsDeleted: 0,
      duration,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
