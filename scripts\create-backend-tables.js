/**
 * <PERSON><PERSON><PERSON> to create the Backend Management System tables in Supabase
 *
 * This script uses the Supabase API to create the tables one by one
 */
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create a Supabase client with the anon key
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Create the app_config table
 */
async function createAppConfigTable() {
  try {
    console.log('Creating app_config table...');

    // Check if the table already exists
    const { error: checkError } = await supabase
      .from('app_config')
      .select('*')
      .limit(1);

    if (!checkError) {
      console.log('app_config table already exists');
      return true;
    }

    // Create the table using the REST API
    // Note: This is a workaround since we can't execute arbitrary SQL
    // We'll need to manually create the table in the Supabase dashboard
    console.log('Please create the app_config table in the Supabase dashboard with the following SQL:');
    console.log(`
      CREATE TABLE app_config (
        key VARCHAR(50) PRIMARY KEY,
        value JSONB NOT NULL,
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      );
    `);

    return false;
  } catch (error) {
    console.error('Error creating app_config table:', error.message);
    return false;
  }
}

/**
 * Create the domain_config table
 */
async function createDomainConfigTable() {
  try {
    console.log('Creating domain_config table...');

    // Check if the table already exists
    const { error: checkError } = await supabase
      .from('domain_config')
      .select('*')
      .limit(1);

    if (!checkError) {
      console.log('domain_config table already exists');
      return true;
    }

    // Create the table using the REST API
    console.log('Please create the domain_config table in the Supabase dashboard with the following SQL:');
    console.log(`
      CREATE TABLE domain_config (
        domain VARCHAR(100) PRIMARY KEY,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        settings JSONB NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      );
    `);

    return false;
  } catch (error) {
    console.error('Error creating domain_config table:', error.message);
    return false;
  }
}

/**
 * Create the ad_config table
 */
async function createAdConfigTable() {
  try {
    console.log('Creating ad_config table...');

    // Check if the table already exists
    const { error: checkError } = await supabase
      .from('ad_config')
      .select('*')
      .limit(1);

    if (!checkError) {
      console.log('ad_config table already exists');
      return true;
    }

    // Create the table using the REST API
    console.log('Please create the ad_config table in the Supabase dashboard with the following SQL:');
    console.log(`
      CREATE TABLE ad_config (
        placement_id VARCHAR(50) NOT NULL,
        domain VARCHAR(100) NOT NULL,
        ad_unit_id VARCHAR(100) NOT NULL,
        is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
        device_types JSONB NOT NULL,
        display_options JSONB,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        PRIMARY KEY (placement_id, domain)
      );
    `);

    return false;
  } catch (error) {
    console.error('Error creating ad_config table:', error.message);
    return false;
  }
}

/**
 * Create the system_logs table
 */
async function createSystemLogsTable() {
  try {
    console.log('Creating system_logs table...');

    // Check if the table already exists
    const { error: checkError } = await supabase
      .from('system_logs')
      .select('*')
      .limit(1);

    if (!checkError) {
      console.log('system_logs table already exists');
      return true;
    }

    // Create the table using the REST API
    console.log('Please create the system_logs table in the Supabase dashboard with the following SQL:');
    console.log(`
      CREATE TABLE system_logs (
        id SERIAL PRIMARY KEY,
        level VARCHAR(20) NOT NULL,
        category VARCHAR(50) NOT NULL,
        message TEXT NOT NULL,
        metadata JSONB,
        timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
      );

      -- Create indexes for faster queries
      CREATE INDEX idx_system_logs_level ON system_logs(level);
      CREATE INDEX idx_system_logs_category ON system_logs(category);
      CREATE INDEX idx_system_logs_timestamp ON system_logs(timestamp);
    `);

    return false;
  } catch (error) {
    console.error('Error creating system_logs table:', error.message);
    return false;
  }
}

/**
 * Insert default configuration values
 */
async function insertDefaultConfig() {
  try {
    console.log('Inserting default configuration values...');

    // Check if the app_config table exists
    const { error: checkError } = await supabase
      .from('app_config')
      .select('*')
      .limit(1);

    if (checkError) {
      console.log('app_config table does not exist, skipping default values');
      return false;
    }

    // Insert default values
    const { error } = await supabase
      .from('app_config')
      .upsert([
        { key: 'emailExpirationMinutes', value: 30 },
        { key: 'autoRefreshInterval', value: 14 },
        { key: 'maxEmailsPerAddress', value: 100 },
        { key: 'maintenanceMode', value: false },
        { key: 'cleanupIntervalMinutes', value: 15 }
      ]);

    if (error) {
      console.error('Error inserting default configuration values:', error.message);
      return false;
    }

    console.log('Default configuration values inserted successfully');
    return true;
  } catch (error) {
    console.error('Error inserting default configuration values:', error.message);
    return false;
  }
}

/**
 * Insert default domain configuration
 */
async function insertDefaultDomain() {
  try {
    console.log('Inserting default domain configuration...');

    // Check if the domain_config table exists
    const { error: checkError } = await supabase
      .from('domain_config')
      .select('*')
      .limit(1);

    if (checkError) {
      console.log('domain_config table does not exist, skipping default domain');
      return false;
    }

    // Insert default domain
    const { error } = await supabase
      .from('domain_config')
      .upsert([
        {
          domain: 'fademail.site',
          is_active: true,
          settings: {
            isDefault: true,
            weight: 100,
            features: {
              adsEnabled: true,
              analyticsEnabled: true,
              autoRefreshEnabled: true
            }
          }
        }
      ]);

    if (error) {
      console.error('Error inserting default domain configuration:', error.message);
      return false;
    }

    console.log('Default domain configuration inserted successfully');
    return true;
  } catch (error) {
    console.error('Error inserting default domain configuration:', error.message);
    return false;
  }
}

/**
 * Insert default ad placements
 */
async function insertDefaultAdPlacements() {
  try {
    console.log('Inserting default ad placements...');

    // Check if the ad_config table exists
    const { error: checkError } = await supabase
      .from('ad_config')
      .select('*')
      .limit(1);

    if (checkError) {
      console.log('ad_config table does not exist, skipping default ad placements');
      return false;
    }

    // Insert default ad placements
    const { error } = await supabase
      .from('ad_config')
      .upsert([
        {
          placement_id: 'sidebar-top',
          domain: 'fademail.site',
          ad_unit_id: 'ca-pub-XXXXXXXXXXXXXXXX',
          is_enabled: true,
          device_types: ['desktop', 'tablet']
        },
        {
          placement_id: 'inbox-bottom',
          domain: 'fademail.site',
          ad_unit_id: 'ca-pub-XXXXXXXXXXXXXXXX',
          is_enabled: true,
          device_types: ['desktop', 'tablet', 'mobile']
        },
        {
          placement_id: 'email-view-right',
          domain: 'fademail.site',
          ad_unit_id: 'ca-pub-XXXXXXXXXXXXXXXX',
          is_enabled: true,
          device_types: ['desktop']
        }
      ]);

    if (error) {
      console.error('Error inserting default ad placements:', error.message);
      return false;
    }

    console.log('Default ad placements inserted successfully');
    return true;
  } catch (error) {
    console.error('Error inserting default ad placements:', error.message);
    return false;
  }
}

/**
 * Main function to create all tables and insert default values
 */
async function createBackendTables() {
  console.log('Creating Backend Management System tables in Supabase...');

  // Create tables
  const appConfigCreated = await createAppConfigTable();
  const domainConfigCreated = await createDomainConfigTable();
  const adConfigCreated = await createAdConfigTable();
  const systemLogsCreated = await createSystemLogsTable();

  // Insert default values if tables were created
  if (appConfigCreated) {
    await insertDefaultConfig();
  }

  if (domainConfigCreated) {
    await insertDefaultDomain();
  }

  if (adConfigCreated) {
    await insertDefaultAdPlacements();
  }

  console.log('\nSummary:');
  console.log(`app_config table: ${appConfigCreated ? 'Created/Exists' : 'Not Created'}`);
  console.log(`domain_config table: ${domainConfigCreated ? 'Created/Exists' : 'Not Created'}`);
  console.log(`ad_config table: ${adConfigCreated ? 'Created/Exists' : 'Not Created'}`);
  console.log(`system_logs table: ${systemLogsCreated ? 'Created/Exists' : 'Not Created'}`);

  if (!appConfigCreated || !domainConfigCreated || !adConfigCreated || !systemLogsCreated) {
    console.log('\nSome tables could not be created automatically.');
    console.log('Please create them manually in the Supabase dashboard using the SQL provided above.');
  } else {
    console.log('\nAll tables created successfully!');
  }
}

// Run the create function
createBackendTables();
