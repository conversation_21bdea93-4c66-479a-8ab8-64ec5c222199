'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import ReactIframeEmailRenderer from '@/components/ReactIframeEmailRenderer';
import SimpleIframeRenderer from '@/components/SimpleIframeRenderer';

// Sample HTML email content
const sampleHtmlEmail = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Sample Email</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #f8f9fa;
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid #e9ecef;
    }
    .content {
      padding: 20px;
    }
    .footer {
      background-color: #f8f9fa;
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #6c757d;
      border-top: 1px solid #e9ecef;
    }
    .button {
      display: inline-block;
      background-color: #007bff;
      color: white;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 4px;
      margin-top: 20px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    table, th, td {
      border: 1px solid #e9ecef;
    }
    th, td {
      padding: 10px;
      text-align: left;
    }
    th {
      background-color: #f8f9fa;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Sample Email Newsletter</h1>
    </div>
    <div class="content">
      <h2>Hello there!</h2>
      <p>This is a sample email to test different rendering approaches.</p>

      <h3>Featured Items</h3>
      <table>
        <thead>
          <tr>
            <th>Item</th>
            <th>Description</th>
            <th>Price</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Product A</td>
            <td>This is a description of Product A</td>
            <td>$19.99</td>
          </tr>
          <tr>
            <td>Product B</td>
            <td>This is a description of Product B</td>
            <td>$29.99</td>
          </tr>
          <tr>
            <td>Product C</td>
            <td>This is a description of Product C</td>
            <td>$39.99</td>
          </tr>
        </tbody>
      </table>

      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>

      <a href="#" class="button">Call to Action</a>
    </div>
    <div class="footer">
      <p>&copy; 2025 Sample Company. All rights reserved.</p>
      <p>You are receiving this email because you signed up for our newsletter.</p>
      <p><a href="#">Unsubscribe</a> | <a href="#">View in browser</a></p>
    </div>
  </div>
</body>
</html>
`;

/**
 * CompareIframeRenderers Page
 *
 * A page that compares React Letter and React-Iframe rendering approaches side by side.
 */
export default function CompareIframeRenderers() {
  const [isMounted, setIsMounted] = useState(false);

  // Set mounted state after initial render
  React.useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Iframe Renderer Comparison</h1>
        <p className="text-gray-600">
          This page compares React Letter and React-Iframe for rendering HTML emails.
        </p>
        <div className="mt-4">
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            Back to Home
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* React-Iframe */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">React-Iframe</h2>
          <p className="text-gray-600 mb-4">
            Uses react-iframe library for rendering HTML emails. This is our primary renderer.
          </p>

          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <ReactIframeEmailRenderer
              html={sampleHtmlEmail}
              style={{
                minHeight: '600px',
                width: '100%',
                display: 'block',
                maxWidth: '100%',
                borderRadius: '0.375rem'
              }}
            />
          </div>
        </div>

        {/* Simple Iframe */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">Simple Iframe</h2>
          <p className="text-gray-600 mb-4">
            Uses a custom iframe implementation without external dependencies. Available as an alternative option.
          </p>

          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <SimpleIframeRenderer
              html={sampleHtmlEmail}
              style={{
                minHeight: '600px',
                width: '100%',
                display: 'block',
                maxWidth: '100%',
                borderRadius: '0.375rem'
              }}
            />
          </div>
        </div>
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">Comparison</h2>

        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-700">React-Iframe</h3>
            <p className="text-gray-600">
              <strong>Pros:</strong> Simple API, lightweight, good isolation, easy to customize, well-maintained library.
            </p>
            <p className="text-gray-600">
              <strong>Cons:</strong> Manual height adjustment needed, external dependency.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-700">Simple Iframe</h3>
            <p className="text-gray-600">
              <strong>Pros:</strong> No external dependencies, full control over implementation, smallest bundle size.
            </p>
            <p className="text-gray-600">
              <strong>Cons:</strong> More maintenance required, less features out of the box.
            </p>
          </div>

          <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
            <h3 className="text-lg font-medium text-blue-800 mb-2">Performance Considerations</h3>
            <p className="text-blue-700">
              Both iframe-based approaches (React-Iframe and Simple Iframe) provide excellent performance for rendering emails. React Iframe offers a well-tested, maintained library with a clean API, while the Simple Iframe approach has a slightly smaller bundle size as it doesn't require external dependencies.
            </p>
          </div>

          <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-100">
            <h3 className="text-lg font-medium text-green-800 mb-2">Recommendation</h3>
            <p className="text-green-700">
              We've decided to use <strong>React Iframe</strong> as our primary renderer, with <strong>Simple Iframe</strong> as an alternative option. React Iframe provides a good balance of ease of use, performance, and features, while Simple Iframe offers a dependency-free alternative when needed.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
