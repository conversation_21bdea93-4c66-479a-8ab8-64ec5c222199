/**
 * Admin Guerrilla Email Cleanup API
 * 
 * Admin version of the Guerrilla email cleanup API
 * Mirrors the management portal functionality
 */

import { NextRequest, NextResponse } from 'next/server';
import { getGuerrillaDbConnection } from '@/lib/db';
import { getConfig } from '@/lib/config/configService';
import { logInfo, logError } from '@/lib/logging';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    logInfo('guerrilla-cleanup', 'Starting admin Guerrilla email cleanup operation');

    // Get age threshold configuration
    const ageThresholdHours = await getConfig('guerrillaCleanupAgeThresholdHours') || 24; // Default: 24 hours

    // Get MySQL connection using the same method as general cleanup
    const connection = await getGuerrillaDbConnection();

    try {
      // Calculate the cutoff timestamp
      const cutoffTime = new Date(Date.now() - (ageThresholdHours * 60 * 60 * 1000));
      const cutoffTimestamp = Math.floor(cutoffTime.getTime() / 1000); // Convert to Unix timestamp

      logInfo('guerrilla-cleanup', 'Admin Guerrilla cleanup parameters', {
        ageThresholdHours,
        cutoffTime: cutoffTime.toISOString(),
        cutoffTimestamp
      });

      // Count emails to be deleted (for logging)
      const [countRows] = await connection.execute(
        'SELECT COUNT(*) as count FROM guerrilla_mail WHERE date < FROM_UNIXTIME(?)',
        [cutoffTimestamp]
      ) as any[];

      const emailsToDelete = countRows[0]?.count || 0;

      if (emailsToDelete === 0) {
        logInfo('guerrilla-cleanup', 'No old Guerrilla emails found to delete (admin)');
        
        return NextResponse.json({
          success: true,
          guerrillaEmailsDeleted: 0,
          ageThresholdHours,
          cutoffTime: cutoffTime.toISOString(),
          duration: Date.now() - startTime,
          timestamp: new Date().toISOString(),
          message: 'No old Guerrilla emails found to delete'
        });
      }

      // Delete old Guerrilla emails
      const [deleteResult] = await connection.execute(
        'DELETE FROM guerrilla_mail WHERE date < FROM_UNIXTIME(?)',
        [cutoffTimestamp]
      ) as any[];

      const deletedCount = deleteResult.affectedRows || 0;

      logInfo('guerrilla-cleanup', 'Admin Guerrilla email cleanup completed', {
        emailsToDelete,
        deletedCount,
        ageThresholdHours,
        cutoffTime: cutoffTime.toISOString(),
        duration: Date.now() - startTime
      });

      return NextResponse.json({
        success: true,
        guerrillaEmailsDeleted: deletedCount,
        ageThresholdHours,
        cutoffTime: cutoffTime.toISOString(),
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString(),
        message: `Successfully deleted ${deletedCount} old Guerrilla emails`
      });

    } finally {
      connection.release();
    }

  } catch (error) {
    const duration = Date.now() - startTime;
    
    logError('guerrilla-cleanup', 'Admin Guerrilla email cleanup failed', { 
      error: error instanceof Error ? error.message : String(error),
      duration
    });

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      guerrillaEmailsDeleted: 0,
      duration,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
