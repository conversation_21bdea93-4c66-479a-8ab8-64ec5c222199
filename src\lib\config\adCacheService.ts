/**
 * Ad Cache Service
 *
 * This module provides functions for caching ad configurations to improve performance.
 */
import NodeCache from 'node-cache';
import { AdConfig } from './types';
import { logInfo, logError } from '@/lib/logging';

// Cache TTL in seconds
const DEFAULT_TTL = 5 * 60; // 5 minutes
const DOMAIN_TTL = 15 * 60; // 15 minutes

// Create cache instances
export const adCache = new NodeCache({
  stdTTL: DEFAULT_TTL,
  checkperiod: 60, // Check for expired keys every 60 seconds
  useClones: false // Don't clone objects (for performance)
});

export const domainCache = new NodeCache({
  stdTTL: DOMAIN_TTL,
  checkperiod: 120,
  useClones: false
});

/**
 * Cache statistics
 */
export interface CacheStats {
  hits: number;
  misses: number;
  keys: number;
  ksize: number;
  vsize: number;
}

/**
 * Get ad from cache
 *
 * @param placementId Ad placement ID
 * @param domain Domain
 * @returns AdConfig | null
 */
export function getAdFromCache(placementId: string, domain: string): AdConfig | null {
  const cacheKey = `ad:${domain}:${placementId}`;
  const cachedAd = adCache.get<AdConfig>(cacheKey);

  if (cachedAd) {
    logInfo('adCache', `Cache hit for ad ${placementId} on domain ${domain}`);
    return cachedAd;
  }

  logInfo('adCache', `Cache miss for ad ${placementId} on domain ${domain}`);
  return null;
}

/**
 * Set ad in cache
 *
 * @param ad Ad configuration
 * @param ttl Time to live in seconds (optional)
 */
export function setAdInCache(ad: AdConfig, ttl?: number): void {
  const cacheKey = `ad:${ad.domain}:${ad.placementId}`;

  try {
    adCache.set(cacheKey, ad, ttl || DEFAULT_TTL);
    logInfo('adCache', `Cached ad ${ad.placementId} on domain ${ad.domain}`);
  } catch (error) {
    logError('adCache', `Error caching ad ${ad.placementId}`, { error });
  }
}

/**
 * Get domain ads from cache
 *
 * @param domain Domain
 * @returns AdConfig[] | null
 */
export function getDomainAdsFromCache(domain: string): AdConfig[] | null {
  const cacheKey = `domain:${domain}:ads`;
  const cachedAds = domainCache.get<AdConfig[]>(cacheKey);

  if (cachedAds) {
    logInfo('adCache', `Cache hit for domain ads on ${domain}`);
    return cachedAds;
  }

  logInfo('adCache', `Cache miss for domain ads on ${domain}`);
  return null;
}

/**
 * Set domain ads in cache
 *
 * @param domain Domain
 * @param ads Ad configurations
 * @param ttl Time to live in seconds (optional)
 */
export function setDomainAdsInCache(domain: string, ads: AdConfig[], ttl?: number): void {
  const cacheKey = `domain:${domain}:ads`;

  try {
    domainCache.set(cacheKey, ads, ttl || DOMAIN_TTL);
    logInfo('adCache', `Cached ${ads.length} ads for domain ${domain}`);
  } catch (error) {
    logError('adCache', `Error caching ads for domain ${domain}`, { error });
  }
}

/**
 * Invalidate ad in cache
 *
 * @param placementId Ad placement ID
 * @param domain Domain
 */
export function invalidateAdCache(placementId: string, domain: string): void {
  const adCacheKey = `ad:${domain}:${placementId}`;
  const domainCacheKey = `domain:${domain}:ads`;

  try {
    adCache.del(adCacheKey);
    domainCache.del(domainCacheKey);
    logInfo('adCache', `Invalidated cache for ad ${placementId} on domain ${domain}`);
  } catch (error) {
    logError('adCache', `Error invalidating cache for ad ${placementId}`, { error });
  }
}

/**
 * Invalidate all ads for a domain in cache
 *
 * @param domain Domain
 */
export function invalidateDomainCache(domain: string): void {
  const domainCacheKey = `domain:${domain}:ads`;

  try {
    // Delete domain ads cache
    domainCache.del(domainCacheKey);

    // Delete individual ad caches for this domain
    const keys = adCache.keys();
    const domainPrefix = `ad:${domain}:`;

    keys.forEach(key => {
      if (key.startsWith(domainPrefix)) {
        adCache.del(key);
      }
    });

    logInfo('adCache', `Invalidated all ad caches for domain ${domain}`);
  } catch (error) {
    logError('adCache', `Error invalidating domain cache for ${domain}`, { error });
  }
}

/**
 * Flush all caches
 */
export function flushAllCaches(): void {
  try {
    adCache.flushAll();
    domainCache.flushAll();
    logInfo('adCache', 'Flushed all ad caches');
  } catch (error) {
    logError('adCache', 'Error flushing ad caches', { error });
  }
}

/**
 * Get cache statistics
 *
 * @returns CacheStats
 */
export function getCacheStats(): { adCache: CacheStats; domainCache: CacheStats } {
  return {
    adCache: adCache.getStats(),
    domainCache: domainCache.getStats()
  };
}
