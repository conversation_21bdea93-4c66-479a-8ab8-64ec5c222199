/**
 * API route for setting a domain as default (admin path)
 */
import { NextRequest, NextResponse } from 'next/server';
import { setDefaultDomain } from '@/lib/config/domainService';
import { logInfo, logError } from '@/lib/logging';

/**
 * POST /api/admin/domains/[domain]/default
 *
 * Set a domain as default
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ domain: string }> }
) {
  try {
    const resolvedParams = await params;
    const { domain } = resolvedParams;

    // Set domain as default
    const success = await setDefaultDomain(domain);

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to set domain as default' },
        { status: 500 }
      );
    }

    logInfo('api', 'Set domain as default', { domain });

    return NextResponse.json({
      success: true,
      message: 'Domain set as default successfully'
    });
  } catch (error) {
    logError('api', 'Error setting domain as default', { error });

    return NextResponse.json(
      { success: false, error: 'Failed to set domain as default' },
      { status: 500 }
    );
  }
}
