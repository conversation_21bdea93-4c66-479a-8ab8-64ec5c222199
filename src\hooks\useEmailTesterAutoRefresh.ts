/**
 * useEmailTesterAutoRefresh Hook
 *
 * Custom hook for auto-refresh functionality in the Email Tester Tool.
 * Provides polling mechanism with exponential backoff and timeout handling.
 */

'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

export interface EmailTesterAutoRefreshConfig {
  testAddress: string;
  testAddressId: string;
  onSuccess: (resultId: string) => void;
  onError: (error: string) => void;
  maxAttempts?: number;
  initialInterval?: number;
  maxInterval?: number;
  timeout?: number;
}

export interface EmailTesterAutoRefreshState {
  isPolling: boolean;
  currentAttempt: number;
  timeRemaining: number;
  status: 'idle' | 'waiting' | 'checking' | 'success' | 'timeout' | 'error';
  statusMessage: string;
}

export function useEmailTesterAutoRefresh() {
  const [state, setState] = useState<EmailTesterAutoRefreshState>({
    isPolling: false,
    currentAttempt: 0,
    timeRemaining: 0,
    status: 'idle',
    statusMessage: 'Ready to start polling'
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);
  const configRef = useRef<EmailTesterAutoRefreshConfig | null>(null);

  // Status messages for different states
  const getStatusMessage = useCallback((status: string, attempt: number, timeRemaining: number) => {
    switch (status) {
      case 'waiting':
        const minutes = Math.floor(timeRemaining / 60);
        const seconds = timeRemaining % 60;
        return `Waiting for email... (${minutes}m ${seconds}s remaining)`;
      case 'checking':
        return `Checking for new messages... (attempt ${attempt})`;
      case 'success':
        return 'Email received! Analyzing...';
      case 'timeout':
        return 'No email received within the timeout period. You can try manual refresh.';
      case 'error':
        return 'An error occurred while checking for emails.';
      default:
        return 'Ready to start polling';
    }
  }, []);

  // Update status message
  const updateStatus = useCallback((status: EmailTesterAutoRefreshState['status'], attempt?: number, timeRemaining?: number) => {
    setState(prev => ({
      ...prev,
      status,
      currentAttempt: attempt ?? prev.currentAttempt,
      timeRemaining: timeRemaining ?? prev.timeRemaining,
      statusMessage: getStatusMessage(status, attempt ?? prev.currentAttempt, timeRemaining ?? prev.timeRemaining)
    }));
  }, [getStatusMessage]);

  // Perform a single check for emails
  const checkForEmail = useCallback(async (config: EmailTesterAutoRefreshConfig): Promise<boolean> => {
    try {
      console.log(`[Email Tester Auto-Refresh] Checking for email...`);

      const response = await fetch('/api/tools/email-tester/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          testAddress: config.testAddress,
          testAddressId: config.testAddressId
        })
      });

      const data = await response.json();

      if (data.success) {
        console.log(`[Email Tester Auto-Refresh] Email found! Redirecting to results.`);
        updateStatus('success');
        config.onSuccess(data.resultId);
        return true;
      } else if (data.polling) {
        // This is expected during polling - no email has arrived yet
        console.log(`[Email Tester Auto-Refresh] No email found yet (polling), continuing...`);
        return false;
      } else {
        // This is an actual error
        console.log(`[Email Tester Auto-Refresh] Error response:`, data.message);
        return false;
      }
    } catch (error) {
      console.error('[Email Tester Auto-Refresh] Check failed:', error);
      return false;
    }
  }, [updateStatus]);

  // Stop auto-refresh polling (defined first to avoid dependency issues)
  const stopPolling = useCallback(() => {
    console.log('[Email Tester Auto-Refresh] Stopping polling');

    if (intervalRef.current) {
      clearTimeout(intervalRef.current);
      intervalRef.current = null;
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isPolling: false,
      status: prev.status === 'success' ? 'success' : 'idle',
      statusMessage: prev.status === 'success' ? prev.statusMessage : 'Polling stopped'
    }));
  }, []);

  // Start auto-refresh polling
  const startPolling = useCallback((config: EmailTesterAutoRefreshConfig) => {
    console.log('[Email Tester Auto-Refresh] Starting polling for:', config.testAddress);

    // Stop any existing polling
    stopPolling();

    configRef.current = config;
    const maxAttempts = config.maxAttempts || 25; // ~5 minutes with 12s intervals
    const initialInterval = config.initialInterval || 12000; // 12 seconds
    const maxInterval = config.maxInterval || 20000; // 20 seconds max
    const timeout = config.timeout || 300000; // 5 minutes

    setState({
      isPolling: true,
      currentAttempt: 0,
      timeRemaining: timeout / 1000,
      status: 'waiting',
      statusMessage: getStatusMessage('waiting', 0, timeout / 1000)
    });

    let attempt = 0;
    let currentInterval = initialInterval;

    // Countdown timer
    const startTime = Date.now();
    const updateCountdown = () => {
      const elapsed = Date.now() - startTime;
      const remaining = Math.max(0, Math.floor((timeout - elapsed) / 1000));

      if (remaining <= 0) {
        if (countdownRef.current) {
          clearInterval(countdownRef.current);
          countdownRef.current = null;
        }
        return;
      }

      setState(prev => ({
        ...prev,
        timeRemaining: remaining,
        statusMessage: getStatusMessage(prev.status, prev.currentAttempt, remaining)
      }));
    };

    countdownRef.current = setInterval(updateCountdown, 1000);

    // Polling function
    const poll = async () => {
      if (attempt >= maxAttempts) {
        console.log('[Email Tester Auto-Refresh] Max attempts reached, stopping polling');
        stopPolling();
        updateStatus('timeout');
        config.onError('No email received within the timeout period');
        return;
      }

      attempt++;
      updateStatus('checking', attempt);

      const emailFound = await checkForEmail(config);

      if (emailFound) {
        stopPolling();
        return;
      }

      // Exponential backoff with jitter
      currentInterval = Math.min(
        currentInterval * 1.1 + Math.random() * 1000,
        maxInterval
      );

      updateStatus('waiting', attempt);

      // Schedule next poll
      intervalRef.current = setTimeout(poll, currentInterval);
    };

    // Start first poll after a short delay
    intervalRef.current = setTimeout(poll, 2000);

    // Set overall timeout
    timeoutRef.current = setTimeout(() => {
      console.log('[Email Tester Auto-Refresh] Overall timeout reached');
      stopPolling();
      updateStatus('timeout');
      config.onError('Polling timeout reached');
    }, timeout);

  }, [checkForEmail, getStatusMessage, updateStatus, stopPolling]);

  // Manual refresh function
  const manualRefresh = useCallback(async () => {
    if (!configRef.current) return false;

    console.log('[Email Tester Auto-Refresh] Manual refresh triggered');
    updateStatus('checking');
    const emailFound = await checkForEmail(configRef.current);

    if (!emailFound) {
      updateStatus('waiting');
      configRef.current.onError('No email found for this test address yet');
    }

    return emailFound;
  }, [checkForEmail, updateStatus]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    state,
    startPolling,
    stopPolling,
    manualRefresh
  };
}
