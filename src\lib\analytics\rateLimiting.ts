/**
 * Rate Limiting System for Analytics API
 * 
 * Implements IP-based rate limiting with configurable limits and time windows.
 * Uses in-memory storage for development and can be extended for Redis in production.
 */

import { NextRequest } from 'next/server';

/**
 * Rate limit configuration for different endpoint types
 */
export interface RateLimitConfig {
  windowMs: number;     // Time window in milliseconds
  maxRequests: number;  // Maximum requests per window
  skipSuccessfulRequests?: boolean; // Whether to skip counting successful requests
  skipFailedRequests?: boolean;     // Whether to skip counting failed requests
}

/**
 * Get rate limit configuration for an endpoint (with dynamic config support)
 */
async function getRateLimitConfigForEndpoint(endpoint: string): Promise<RateLimitConfig> {
  try {
    // Try to get dynamic configuration first
    const { getRateLimitConfigWithFallback } = await import('@/lib/config/rateLimitConfig');
    const config = await getRateLimitConfigWithFallback(endpoint);
    return config;
  } catch (error) {
    console.error('Error getting dynamic rate limit config, using fallback:', error);

    // Fallback to static configurations
    const fallbackConfigs: Record<string, RateLimitConfig> = {
      emailGeneration: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: 15,          // 15 emails per hour per IP
        skipSuccessfulRequests: false,
        skipFailedRequests: true,
      },
      analytics: {
        windowMs: 60 * 1000,      // 1 minute
        maxRequests: 100,         // 100 requests per minute per IP
        skipSuccessfulRequests: false,
        skipFailedRequests: false,
      },
      adminAnalytics: {
        windowMs: 60 * 1000,      // 1 minute
        maxRequests: 60,          // 60 requests per minute per IP
        skipSuccessfulRequests: true,
        skipFailedRequests: false,
      },
      sessionAnalytics: {
        windowMs: 60 * 1000,      // 1 minute
        maxRequests: 30,          // 30 requests per minute per IP
        skipSuccessfulRequests: true,
        skipFailedRequests: false,
      },
      heartbeat: {
        windowMs: 60 * 1000,      // 1 minute
        maxRequests: 200,         // 200 requests per minute per IP
        skipSuccessfulRequests: true,
        skipFailedRequests: false,
      }
    };

    return fallbackConfigs[endpoint] || {
      windowMs: 60 * 60 * 1000, // 1 hour default
      maxRequests: 100,
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    };
  }
}

/**
 * Rate limit entry for tracking requests
 */
interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
}

/**
 * In-memory rate limit store
 * In production, this should be replaced with Redis for distributed rate limiting
 */
class InMemoryRateLimitStore {
  private store = new Map<string, RateLimitEntry>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Get rate limit entry for a key
   */
  get(key: string): RateLimitEntry | undefined {
    return this.store.get(key);
  }

  /**
   * Set rate limit entry for a key
   */
  set(key: string, entry: RateLimitEntry): void {
    this.store.set(key, entry);
  }

  /**
   * Delete rate limit entry for a key
   */
  delete(key: string): void {
    this.store.delete(key);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.store.entries()) {
      if (now > entry.resetTime) {
        this.store.delete(key);
      }
    }
  }

  /**
   * Get store statistics
   */
  getStats(): { totalKeys: number; memoryUsage: string } {
    return {
      totalKeys: this.store.size,
      memoryUsage: `${Math.round(JSON.stringify([...this.store]).length / 1024)}KB`
    };
  }

  /**
   * Clear all entries (for testing)
   */
  clear(): void {
    this.store.clear();
  }

  /**
   * Cleanup interval
   */
  destroy(): void {
    clearInterval(this.cleanupInterval);
    this.store.clear();
  }
}

// Global rate limit store instance that persists across module reloads
// This prevents Next.js development server hot reloading from clearing the rate limit store
const globalForRateLimit = globalThis as unknown as {
  rateLimitStore: InMemoryRateLimitStore | undefined;
};

const rateLimitStore = globalForRateLimit.rateLimitStore ?? new InMemoryRateLimitStore();

if (process.env.NODE_ENV === 'development') {
  globalForRateLimit.rateLimitStore = rateLimitStore;
}

/**
 * Rate limit result
 */
export interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

/**
 * Get client IP address from request
 */
export function getClientIP(request: NextRequest): string {
  // Check various headers for the real IP
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Fallback to unknown if no IP can be determined
  return 'unknown';
}

/**
 * Generate rate limit key
 */
export function generateRateLimitKey(ip: string, endpoint: string): string {
  return `ratelimit:${endpoint}:${ip}`;
}

/**
 * Check rate limit for a request (synchronous version with fallback)
 */
export function checkRateLimit(
  request: NextRequest,
  endpoint: string
): RateLimitResult {
  // Use fallback static config for synchronous operation
  const fallbackConfigs: Record<string, RateLimitConfig> = {
    emailGeneration: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 15,          // 15 emails per hour per IP
      skipSuccessfulRequests: false,
      skipFailedRequests: true,
    },
    analytics: {
      windowMs: 60 * 1000,      // 1 minute
      maxRequests: 100,         // 100 requests per minute per IP
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    },
    adminAnalytics: {
      windowMs: 60 * 1000,      // 1 minute
      maxRequests: 60,          // 60 requests per minute per IP
      skipSuccessfulRequests: true,
      skipFailedRequests: false,
    },
    sessionAnalytics: {
      windowMs: 60 * 1000,      // 1 minute
      maxRequests: 30,          // 30 requests per minute per IP
      skipSuccessfulRequests: true,
      skipFailedRequests: false,
    },
    heartbeat: {
      windowMs: 60 * 1000,      // 1 minute
      maxRequests: 200,         // 200 requests per minute per IP
      skipSuccessfulRequests: true,
      skipFailedRequests: false,
    }
  };

  const config = fallbackConfigs[endpoint] || {
    windowMs: 60 * 60 * 1000, // 1 hour default
    maxRequests: 100,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  };

  const ip = getClientIP(request);
  const key = generateRateLimitKey(ip, endpoint);
  const now = Date.now();
  
  // Get existing entry
  let entry = rateLimitStore.get(key);
  
  // If no entry exists or window has expired, create new entry
  if (!entry || now > entry.resetTime) {
    entry = {
      count: 0,  // ✅ FIX: Start with count = 0
      resetTime: now + config.windowMs,
      firstRequest: now,
    };
    rateLimitStore.set(key, entry);
  }

  // Increment count BEFORE checking limit
  entry.count++;

  // Check if limit exceeded (now uses > instead of >=)
  if (entry.count > config.maxRequests) {
    return {
      allowed: false,
      limit: config.maxRequests,
      remaining: 0,
      resetTime: entry.resetTime,
      retryAfter: Math.ceil((entry.resetTime - now) / 1000),
    };
  }

  // Update the store with incremented count
  rateLimitStore.set(key, entry);

  return {
    allowed: true,
    limit: config.maxRequests,
    remaining: config.maxRequests - entry.count,
    resetTime: entry.resetTime,
  };
}

/**
 * Check rate limit for a request (async version with dynamic config)
 */
export async function checkRateLimitAsync(
  request: NextRequest,
  endpoint: string
): Promise<RateLimitResult> {
  const config = await getRateLimitConfigForEndpoint(endpoint);
  const ip = getClientIP(request);

  if (!config) {
    console.warn(`Rate limit config not found for endpoint: ${endpoint}`);
    return {
      allowed: true,
      limit: 0,
      remaining: 0,
      resetTime: Date.now()
    };
  }

  const key = generateRateLimitKey(ip, endpoint);
  const now = Date.now();
  let entry = rateLimitStore.get(key);

  // If no entry exists or window has expired, create new entry
  if (!entry || now > entry.resetTime) {
    entry = {
      count: 0,  // ✅ FIX: Start with count = 0
      resetTime: now + config.windowMs,
      firstRequest: now,
    };
    rateLimitStore.set(key, entry);
  }

  // Increment count BEFORE checking limit
  entry.count++;

  // Check if limit exceeded (now uses > instead of >=)
  if (entry.count > config.maxRequests) {
    return {
      allowed: false,
      limit: config.maxRequests,
      remaining: 0,
      resetTime: entry.resetTime,
      retryAfter: Math.ceil((entry.resetTime - now) / 1000),
    };
  }



  // Update the store with incremented count
  rateLimitStore.set(key, entry);

  return {
    allowed: true,
    limit: config.maxRequests,
    remaining: config.maxRequests - entry.count,
    resetTime: entry.resetTime,
  };
}

/**
 * Update rate limit after request completion (for conditional counting)
 */
export function updateRateLimit(
  request: NextRequest,
  endpoint: string,
  success: boolean
): void {
  // Use fallback static config for synchronous operation
  const fallbackConfigs: Record<string, RateLimitConfig> = {
    emailGeneration: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 15,          // 15 emails per hour per IP
      skipSuccessfulRequests: false,
      skipFailedRequests: true,
    },
    analytics: {
      windowMs: 60 * 1000,      // 1 minute
      maxRequests: 100,         // 100 requests per minute per IP
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    },
    adminAnalytics: {
      windowMs: 60 * 1000,      // 1 minute
      maxRequests: 60,          // 60 requests per minute per IP
      skipSuccessfulRequests: true,
      skipFailedRequests: false,
    },
    sessionAnalytics: {
      windowMs: 60 * 1000,      // 1 minute
      maxRequests: 30,          // 30 requests per minute per IP
      skipSuccessfulRequests: true,
      skipFailedRequests: false,
    },
    heartbeat: {
      windowMs: 60 * 1000,      // 1 minute
      maxRequests: 200,         // 200 requests per minute per IP
      skipSuccessfulRequests: true,
      skipFailedRequests: false,
    }
  };

  const config = fallbackConfigs[endpoint] || {
    windowMs: 60 * 60 * 1000, // 1 hour default
    maxRequests: 100,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  };

  // If we should skip this request type, decrement the count
  if ((success && config.skipSuccessfulRequests) ||
      (!success && config.skipFailedRequests)) {
    const ip = getClientIP(request);
    const key = generateRateLimitKey(ip, endpoint);
    const entry = rateLimitStore.get(key);

    if (entry && entry.count > 0) {
      entry.count--;
      rateLimitStore.set(key, entry);
    }
  }
}

/**
 * Get rate limit headers for response
 */
export function getRateLimitHeaders(result: RateLimitResult): Record<string, string> {
  const headers: Record<string, string> = {
    'X-RateLimit-Limit': result.limit.toString(),
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': result.resetTime.toString(),
  };
  
  if (result.retryAfter) {
    headers['Retry-After'] = result.retryAfter.toString();
  }
  
  return headers;
}

/**
 * Get rate limit store statistics (for monitoring)
 */
export function getRateLimitStats(): { totalKeys: number; memoryUsage: string } {
  return rateLimitStore.getStats();
}

/**
 * Clear rate limit store (for testing)
 */
export function clearRateLimitStore(): void {
  rateLimitStore.clear();
}

/**
 * Destroy rate limit store (cleanup)
 */
export function destroyRateLimitStore(): void {
  rateLimitStore.destroy();
}
