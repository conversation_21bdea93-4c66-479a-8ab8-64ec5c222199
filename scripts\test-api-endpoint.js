/**
 * <PERSON><PERSON><PERSON> to test the API endpoint for retrieving emails
 *
 * Usage: node scripts/test-api-endpoint.js <EMAIL>
 */
const http = require('http');

async function testApiEndpoint() {
  // Get the email address from command line arguments
  const emailAddress = process.argv[2];

  if (!emailAddress) {
    console.error('Please provide an email address');
    console.error('Usage: node scripts/test-api-endpoint.js <EMAIL>');
    process.exit(1);
  }

  console.log(`Testing API endpoint for: ${emailAddress}`);

  // Create a promise to handle the HTTP request
  const fetchEmails = () => {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 3001,
        path: `/api/emails/${encodeURIComponent(emailAddress)}`,
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      };

      console.log(`Making request to: http://${options.hostname}:${options.port}${options.path}`);

      const req = http.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            resolve({ statusCode: res.statusCode, response });
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.end();
    });
  };

  try {
    const { statusCode, response } = await fetchEmails();

    console.log(`Status Code: ${statusCode}`);

    if (statusCode === 200) {
      console.log('✅ API endpoint returned success');
      console.log(`Total emails: ${response.totalCount}`);

      if (response.emails.length > 0) {
        console.log('\nEmails:');
        response.emails.forEach((email, index) => {
          console.log(`\nEmail #${index + 1}:`);
          console.log(`  - ID: ${email.mail_id}`);
          console.log(`  - From: ${email.from}`);
          console.log(`  - From Name: ${email.fromName}`);
          console.log(`  - From Email: ${email.fromEmail}`);
          console.log(`  - To: ${email.to}`);
          console.log(`  - Subject: ${email.subject}`);
          console.log(`  - Date: ${email.date}`);
          console.log(`  - Has HTML: ${email.html ? 'Yes' : 'No'}`);
          console.log(`  - Has Text: ${email.text ? 'Yes' : 'No'}`);
          console.log(`  - Attachments: ${email.attachments.length}`);
        });
      } else {
        console.log('No emails found');
      }
    } else {
      console.log('❌ API endpoint returned an error');
      console.log('Response:', response);
    }
  } catch (error) {
    console.error('Error testing API endpoint:', error);
  }
}

// Run the function
testApiEndpoint().catch(console.error);
