# Database Maintenance System

This document describes the database maintenance system implemented in Fademail to optimize database performance after cleanup operations.

## Overview

The Database Maintenance System is designed to keep the databases in optimal condition by performing regular maintenance operations. This is particularly important after cleanup operations that delete expired email addresses and their associated emails, which can lead to database fragmentation and inefficient storage usage over time.

## Components

The maintenance system consists of the following components:

1. **Maintenance Library** (`src/lib/maintenance/dbMaintenance.ts`)
   - Provides functions for optimizing the Guerrilla MySQL database
   - Provides functions for vacuuming and analyzing the Supabase PostgreSQL database
   - Handles error reporting and logging

2. **Maintenance Scheduler** (`src/lib/maintenance/maintenanceScheduler.ts`)
   - Manages the scheduling of maintenance operations
   - Starts and stops the maintenance process
   - Configurable interval (default: 24 hours)

3. **API Endpoints**
   - `/api/management-portal-x7z9y2/maintenance` - Run maintenance manually
   - `/api/management-portal-x7z9y2/maintenance/scheduler` - Manage the maintenance scheduler

4. **Admin UI**
   - Maintenance Manager component for controlling maintenance operations
   - Status display showing the results of maintenance operations
   - Configuration options for the maintenance interval

5. **SQL Functions**
   - Stored procedures in PostgreSQL for performing maintenance operations
   - Functions for checking and creating maintenance functions

## Maintenance Operations

### MySQL (Guerrilla Database)

The system performs the following operations on the MySQL database:

```sql
OPTIMIZE TABLE guerrilla_mail
```

This operation:
- Reclaims unused space
- Defragments the data file
- Sorts the index
- Updates statistics

### PostgreSQL (Supabase)

The system performs the following operations on the PostgreSQL database:

```sql
ANALYZE temp_emails
VACUUM temp_emails
```

These operations:
- Update statistics used by the query planner
- Remove dead tuples
- Reclaim space for reuse
- Improve query performance

## Configuration

The maintenance system is configurable through the admin interface:

- **Maintenance Interval**: How often maintenance runs (default: 24 hours)
- **Automatic Maintenance**: Enable/disable scheduled maintenance
- **Manual Maintenance**: Run maintenance on demand

## Implementation Details

### Initialization

The maintenance system is initialized when the application starts:

1. The `MaintenanceSchedulerInitializer` component checks if the scheduler is running
2. If not, it starts the scheduler with the configured interval
3. The scheduler runs in the background, independent of user sessions

### Scheduler

The scheduler uses JavaScript's `setInterval` to run maintenance at regular intervals:

```typescript
maintenanceIntervalId = setInterval(async () => {
  try {
    await runMaintenance();
  } catch (error) {
    logError('maintenance', 'Error running scheduled maintenance', { error });
  }
}, intervalMs);
```

### Error Handling

The system includes comprehensive error handling:

- Each operation is wrapped in try/catch blocks
- Errors are logged with detailed information
- The UI displays error messages when operations fail
- The system continues to function even if individual operations fail

## Best Practices

For optimal database performance:

1. **Regular Maintenance**: Run maintenance at least once a day
2. **After Large Cleanups**: Consider running manual maintenance after large cleanup operations
3. **Monitoring**: Check the maintenance results regularly to ensure operations are successful
4. **Interval Tuning**: Adjust the maintenance interval based on your application's usage patterns

## Troubleshooting

If you encounter issues with the maintenance system:

1. Check the logs for error messages
2. Verify database connectivity
3. Ensure the necessary permissions are granted to the database users
4. Try running maintenance manually to see detailed error messages

## Future Improvements

Potential enhancements to the maintenance system:

1. **Adaptive Scheduling**: Adjust maintenance frequency based on database size and activity
2. **Detailed Metrics**: Collect and display more detailed performance metrics
3. **Partial Maintenance**: Implement more granular maintenance operations for large databases
4. **Maintenance History**: Store and display a history of maintenance operations and results
