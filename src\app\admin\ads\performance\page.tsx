'use client';

import { useState, useEffect } from 'react';
import { ArrowPathIcon } from '@heroicons/react/24/solid';
import AdPerformance from '@/components/admin/AdPerformance';
import { AdConfig } from '@/lib/config/types';

/**
 * Ad Performance Dashboard Page
 */
export default function AdPerformancePage() {
  const [adPlacements, setAdPlacements] = useState<AdConfig[]>([]);
  const [selectedAdId, setSelectedAdId] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch ad placements
  const fetchAdPlacements = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/ads');

      if (!response.ok) {
        throw new Error(`Failed to fetch ad placements: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setAdPlacements(data.data);

        // Select the first ad placement by default
        if (data.data.length > 0 && !selectedAdId) {
          setSelectedAdId(`${data.data[0].placementId}-${data.data[0].domain}`);
        }
      } else {
        setError(data.error || 'Failed to fetch ad placements');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching ad placements');
      console.error('Error fetching ad placements:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchAdPlacements();
  }, []);

  // Get the selected ad placement
  const selectedAd = adPlacements.find(ad => `${ad.placementId}-${ad.domain}` === selectedAdId);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex items-center mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
          <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
        </svg>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Ad Performance</h1>
          <p className="mt-1 text-sm text-gray-600">
            View detailed performance metrics for your ad placements.
          </p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow overflow-hidden rounded-lg mb-8">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">Select Ad Placement</h3>
          <button
            type="button"
            onClick={fetchAdPlacements}
            disabled={loading}
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-1.5 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
        <div className="px-4 py-5 sm:p-6">
          {loading && !adPlacements.length ? (
            <div className="flex justify-center items-center h-32">
              <ArrowPathIcon className="h-8 w-8 text-indigo-500 animate-spin" />
            </div>
          ) : adPlacements.length > 0 ? (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {adPlacements.map((ad) => (
                <div
                  key={`${ad.placementId}-${ad.domain}`}
                  className={`relative rounded-lg border ${
                    `${ad.placementId}-${ad.domain}` === selectedAdId
                      ? 'border-indigo-500 ring-2 ring-indigo-500'
                      : 'border-gray-300 hover:border-indigo-400'
                  } bg-white px-6 py-5 shadow-sm flex items-center space-x-3 cursor-pointer`}
                  onClick={() => setSelectedAdId(`${ad.placementId}-${ad.domain}`)}
                >
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{ad.placementId}</p>
                    <p className="text-sm text-gray-500 truncate">{ad.domain}</p>
                    <div className="mt-2">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          ad.isEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {ad.isEnabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No ad placements found. Create ad placements to view performance metrics.</p>
            </div>
          )}
        </div>
      </div>

      <AdPerformance adPlacement={selectedAd} />
    </div>
  );
}
