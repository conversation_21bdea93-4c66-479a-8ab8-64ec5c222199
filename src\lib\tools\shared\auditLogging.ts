// Audit Logging Utilities for Email Tools
import { createServerSupabaseClient } from '@/lib/supabase/server';

export interface AuditLogEntry {
  sessionId?: string;
  userId?: string;
  toolName: string;
  action: string;
  resourceType?: string;
  resourceId?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

export interface AuditLogFilter {
  toolName?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
}

/**
 * Log an audit event for tool usage
 */
export async function logAuditEvent(entry: AuditLogEntry): Promise<void> {
  try {
    const supabase = await createServerSupabaseClient();

    const { error } = await supabase
      .from('tool_audit_log')
      .insert({
        session_id: entry.sessionId,
        user_id: entry.userId,
        tool_name: entry.toolName,
        action: entry.action,
        resource_type: entry.resourceType,
        resource_id: entry.resourceId,
        metadata: entry.metadata || {},
        ip_address: entry.ipAddress,
        user_agent: entry.userAgent,
      });

    if (error) {
      console.error('Failed to log audit event:', error);
      // Don't throw error to avoid breaking the main functionality
    }
  } catch (error) {
    console.error('Audit logging error:', error);
    // Silently fail to avoid breaking the main functionality
  }
}

/**
 * Retrieve audit logs with filtering
 */
export async function getAuditLogs(filter: AuditLogFilter = {}) {
  try {
    const supabase = await createServerSupabaseClient();

    let query = supabase
      .from('tool_audit_log')
      .select('*')
      .order('created_at', { ascending: false });

    // Apply filters
    if (filter.toolName) {
      query = query.eq('tool_name', filter.toolName);
    }

    if (filter.action) {
      query = query.eq('action', filter.action);
    }

    if (filter.userId) {
      query = query.eq('user_id', filter.userId);
    }

    if (filter.sessionId) {
      query = query.eq('session_id', filter.sessionId);
    }

    if (filter.startDate) {
      query = query.gte('created_at', filter.startDate.toISOString());
    }

    if (filter.endDate) {
      query = query.lte('created_at', filter.endDate.toISOString());
    }

    if (filter.limit) {
      query = query.limit(filter.limit);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Failed to retrieve audit logs:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Audit log retrieval error:', error);
    return [];
  }
}

/**
 * Get audit statistics for a tool
 */
export async function getToolAuditStats(toolName: string, days: number = 30) {
  try {
    const supabase = await createServerSupabaseClient();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data, error } = await supabase
      .from('tool_audit_log')
      .select('action, created_at')
      .eq('tool_name', toolName)
      .gte('created_at', startDate.toISOString());

    if (error) {
      console.error('Failed to get audit stats:', error);
      return {
        totalEvents: 0,
        actionCounts: {},
        dailyUsage: {},
      };
    }

    const actionCounts: Record<string, number> = {};
    const dailyUsage: Record<string, number> = {};

    data?.forEach(log => {
      // Count actions
      actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;

      // Count daily usage
      const date = new Date(log.created_at).toISOString().split('T')[0];
      dailyUsage[date] = (dailyUsage[date] || 0) + 1;
    });

    return {
      totalEvents: data?.length || 0,
      actionCounts,
      dailyUsage,
    };
  } catch (error) {
    console.error('Audit stats error:', error);
    return {
      totalEvents: 0,
      actionCounts: {},
      dailyUsage: {},
    };
  }
}

/**
 * Clean up old audit logs (for maintenance)
 */
export async function cleanupOldAuditLogs(daysToKeep: number = 90): Promise<number> {
  try {
    const supabase = await createServerSupabaseClient();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const { data, error } = await supabase
      .from('tool_audit_log')
      .delete()
      .lt('created_at', cutoffDate.toISOString())
      .select('id');

    if (error) {
      console.error('Failed to cleanup audit logs:', error);
      return 0;
    }

    return data?.length || 0;
  } catch (error) {
    console.error('Audit cleanup error:', error);
    return 0;
  }
}

// Predefined audit actions for consistency
export const AUDIT_ACTIONS = {
  // DKIM Generator actions
  DKIM_GENERATE: 'dkim_generate',
  DKIM_VALIDATE: 'dkim_validate',
  DKIM_DOWNLOAD_KEY: 'dkim_download_key',

  // DMARC Generator actions
  DMARC_GENERATE: 'dmarc_generate',
  DMARC_VALIDATE: 'dmarc_validate',

  // Email Tester actions
  EMAIL_TEST_START: 'email_test_start',
  EMAIL_TEST_COMPLETE: 'email_test_complete',
  EMAIL_TEST_VIEW_RESULTS: 'email_test_view_results',

  // General actions
  TOOL_ACCESS: 'tool_access',
  DNS_VALIDATION: 'dns_validation',
  RECORD_DOWNLOAD: 'record_download',
  RECORD_COPY: 'record_copy',
} as const;

// Tool names for consistency
export const TOOL_NAMES = {
  DKIM_GENERATOR: 'dkim-generator',
  DMARC_GENERATOR: 'dmarc-generator',
  EMAIL_TESTER: 'email-tester',
  DNS_VALIDATOR: 'dns-validator',
} as const;

/**
 * Helper function to log tool access
 */
export async function logToolAccess(
  toolName: string,
  sessionId?: string,
  userId?: string,
  ipAddress?: string,
  userAgent?: string
) {
  await logAuditEvent({
    sessionId,
    userId,
    toolName,
    action: AUDIT_ACTIONS.TOOL_ACCESS,
    metadata: {
      timestamp: new Date().toISOString(),
    },
    ipAddress,
    userAgent,
  });
}

/**
 * Helper function to log record generation
 */
export async function logRecordGeneration(
  toolName: string,
  recordType: string,
  domain: string,
  sessionId?: string,
  userId?: string,
  metadata?: Record<string, any>
) {
  const action = toolName === TOOL_NAMES.DKIM_GENERATOR
    ? AUDIT_ACTIONS.DKIM_GENERATE
    : AUDIT_ACTIONS.DMARC_GENERATE;

  await logAuditEvent({
    sessionId,
    userId,
    toolName,
    action,
    resourceType: recordType,
    metadata: {
      domain,
      ...metadata,
    },
  });
}

/**
 * Helper function to log DNS validation
 */
export async function logDnsValidation(
  toolName: string,
  domain: string,
  isValid: boolean,
  sessionId?: string,
  userId?: string,
  errors?: string[]
) {
  const action = toolName === TOOL_NAMES.DKIM_GENERATOR
    ? AUDIT_ACTIONS.DKIM_VALIDATE
    : AUDIT_ACTIONS.DMARC_VALIDATE;

  await logAuditEvent({
    sessionId,
    userId,
    toolName,
    action,
    resourceType: 'dns_validation',
    metadata: {
      domain,
      isValid,
      errors: errors || [],
    },
  });
}
