-- Migration: Create DKIM and DMARC Generator Tables
-- Description: Creates isolated database tables for DKIM and DMARC generator tools
-- Safety: This migration creates NEW tables only, no modifications to existing tables

-- Create generated_records table for storing DKIM and DMARC records
CREATE TABLE IF NOT EXISTS generated_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id VARCHAR(255) NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  record_type VARCHAR(20) NOT NULL CHECK (record_type IN ('dkim', 'dmarc')),
  domain VARCHAR(255) NOT NULL,
  selector VARCHAR(255), -- For DKIM only, NULL for DMARC
  dns_record TEXT NOT NULL,
  record_name VARCHAR(255) NOT NULL,
  private_key_encrypted TEXT, -- For DKIM only, NULL for DMARC
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  validated_at TIMESTAMP WITH TIME ZONE,
  validation_status VARCHAR(20) DEFAULT 'pending' CHECK (validation_status IN ('pending', 'valid', 'invalid', 'error')),
  validation_errors JSONB DEFAULT '[]'
);

-- Create DNS validation history table
CREATE TABLE IF NOT EXISTS dns_validation_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  record_id UUID REFERENCES generated_records(id) ON DELETE CASCADE,
  validation_type VARCHAR(20) NOT NULL,
  domain VARCHAR(255) NOT NULL,
  record_name VARCHAR(255) NOT NULL,
  expected_value TEXT,
  actual_values JSONB DEFAULT '[]',
  is_valid BOOLEAN NOT NULL,
  errors JSONB DEFAULT '[]',
  validated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create audit log table for tool usage tracking
CREATE TABLE IF NOT EXISTS tool_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id VARCHAR(255),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  tool_name VARCHAR(50) NOT NULL CHECK (tool_name IN ('dkim-generator', 'dmarc-generator', 'dns-validator')),
  action VARCHAR(50) NOT NULL,
  resource_type VARCHAR(50),
  resource_id UUID,
  metadata JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create performance indexes for the new tables
CREATE INDEX IF NOT EXISTS idx_generated_records_session_id ON generated_records(session_id);
CREATE INDEX IF NOT EXISTS idx_generated_records_user_id ON generated_records(user_id);
CREATE INDEX IF NOT EXISTS idx_generated_records_type ON generated_records(record_type);
CREATE INDEX IF NOT EXISTS idx_generated_records_domain ON generated_records(domain);
CREATE INDEX IF NOT EXISTS idx_generated_records_expires_at ON generated_records(expires_at);
CREATE INDEX IF NOT EXISTS idx_generated_records_validation_status ON generated_records(validation_status);

CREATE INDEX IF NOT EXISTS idx_dns_validation_history_record_id ON dns_validation_history(record_id);
CREATE INDEX IF NOT EXISTS idx_dns_validation_history_domain ON dns_validation_history(domain);
CREATE INDEX IF NOT EXISTS idx_dns_validation_history_validated_at ON dns_validation_history(validated_at);

CREATE INDEX IF NOT EXISTS idx_tool_audit_log_user_id ON tool_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_tool_audit_log_session_id ON tool_audit_log(session_id);
CREATE INDEX IF NOT EXISTS idx_tool_audit_log_tool_name ON tool_audit_log(tool_name);
CREATE INDEX IF NOT EXISTS idx_tool_audit_log_created_at ON tool_audit_log(created_at);

-- Enable Row Level Security on all new tables
ALTER TABLE generated_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE dns_validation_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE tool_audit_log ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for generated_records table
CREATE POLICY "Users can view their own generated records" ON generated_records
  FOR SELECT USING (
    auth.uid() = user_id OR 
    session_id = current_setting('app.session_id', true)
  );

CREATE POLICY "Users can insert their own generated records" ON generated_records
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR 
    session_id = current_setting('app.session_id', true)
  );

CREATE POLICY "Users can update their own generated records" ON generated_records
  FOR UPDATE USING (
    auth.uid() = user_id OR 
    session_id = current_setting('app.session_id', true)
  );

CREATE POLICY "Users can delete their own generated records" ON generated_records
  FOR DELETE USING (
    auth.uid() = user_id OR 
    session_id = current_setting('app.session_id', true)
  );

-- Create RLS policies for dns_validation_history table
CREATE POLICY "Users can view validation history for their records" ON dns_validation_history
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM generated_records gr 
      WHERE gr.id = record_id 
      AND (gr.user_id = auth.uid() OR gr.session_id = current_setting('app.session_id', true))
    )
  );

CREATE POLICY "Users can insert validation history for their records" ON dns_validation_history
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM generated_records gr 
      WHERE gr.id = record_id 
      AND (gr.user_id = auth.uid() OR gr.session_id = current_setting('app.session_id', true))
    )
  );

-- Create RLS policies for tool_audit_log table
CREATE POLICY "Users can view their own audit logs" ON tool_audit_log
  FOR SELECT USING (
    auth.uid() = user_id OR 
    session_id = current_setting('app.session_id', true)
  );

CREATE POLICY "Users can insert their own audit logs" ON tool_audit_log
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR 
    session_id = current_setting('app.session_id', true)
  );

-- Add comments for documentation
COMMENT ON TABLE generated_records IS 'Stores DKIM and DMARC records generated by the tools';
COMMENT ON TABLE dns_validation_history IS 'Tracks DNS validation attempts and results';
COMMENT ON TABLE tool_audit_log IS 'Audit log for DKIM/DMARC generator tool usage';

COMMENT ON COLUMN generated_records.record_type IS 'Type of record: dkim or dmarc';
COMMENT ON COLUMN generated_records.selector IS 'DKIM selector (only used for DKIM records)';
COMMENT ON COLUMN generated_records.private_key_encrypted IS 'Encrypted private key (only used for DKIM records)';
COMMENT ON COLUMN generated_records.metadata IS 'Additional record metadata in JSON format';
COMMENT ON COLUMN generated_records.validation_status IS 'DNS validation status: pending, valid, invalid, or error';
