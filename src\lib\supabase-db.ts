/**
 * Supabase database functions for Fademail
 *
 * This file contains functions for interacting with the Supabase PostgreSQL database.
 * It replaces the MySQL-based implementation in db.ts for the local database operations.
 * The Guerrilla database operations remain unchanged.
 */
import { createServerSupabaseClient } from './supabase';
import { TempEmail } from './types';
import { logInfo, logError } from './logging';
import { DatabaseConnectionError, DatabaseQueryError, EmailNotFoundError } from './db';

/**
 * Create a temporary email in the Supabase database
 */
export async function createTempEmail(emailAddress: string, expirationDate: Date): Promise<TempEmail> {
  try {
    logInfo('DB', `Creating temporary email: ${emailAddress}`);

    const supabase = createServerSupabaseClient();

    const { data, error } = await supabase
      .from('temp_emails')
      .insert([
        {
          email_address: emailAddress,
          expiration_date: expirationDate.toISOString()
        }
      ])
      .select()
      .single();

    if (error) {
      throw new DatabaseQueryError(
        `Failed to create temporary email: ${emailAddress}`,
        'INSERT INTO temp_emails',
        new Error(error.message)
      );
    }

    if (!data) {
      throw new DatabaseQueryError(
        `Failed to retrieve created email: ${emailAddress}`,
        'SELECT FROM temp_emails',
        new Error('No data returned after insert')
      );
    }

    return {
      id: data.id,
      emailAddress: data.email_address,
      creationTime: new Date(data.creation_time),
      expirationDate: new Date(data.expiration_date)
    };
  } catch (error) {
    logError('DB', `Error creating temporary email: ${error instanceof Error ? error.message : String(error)}`);

    if (error instanceof DatabaseQueryError) {
      throw error;
    }

    throw new DatabaseQueryError(
      `Failed to create temporary email: ${emailAddress}`,
      'INSERT INTO temp_emails',
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * Get a temporary email by address from the Supabase database
 */
export async function getTempEmailByAddress(emailAddress: string): Promise<TempEmail | null> {
  try {
    logInfo('DB', `Getting temporary email by address: ${emailAddress}`);

    const supabase = createServerSupabaseClient();

    const { data, error } = await supabase
      .from('temp_emails')
      .select('*')
      .eq('email_address', emailAddress)
      .single();

    if (error) {
      // If the error is PGRST116 (not found), return null
      if (error.code === 'PGRST116') {
        return null;
      }

      throw new DatabaseQueryError(
        `Failed to get temporary email by address: ${emailAddress}`,
        'SELECT FROM temp_emails',
        new Error(error.message)
      );
    }

    if (!data) {
      return null;
    }

    return {
      id: data.id,
      emailAddress: data.email_address,
      creationTime: new Date(data.creation_time),
      expirationDate: new Date(data.expiration_date)
    };
  } catch (error) {
    logError('DB', `Error getting temporary email by address: ${error instanceof Error ? error.message : String(error)}`);

    if (error instanceof DatabaseQueryError) {
      throw error;
    }

    throw new DatabaseQueryError(
      `Failed to get temporary email by address: ${emailAddress}`,
      'SELECT FROM temp_emails',
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * Delete expired temporary emails from the Supabase database
 */
export async function deleteExpiredEmails(): Promise<number> {
  try {
    logInfo('DB', 'Deleting expired emails');

    const supabase = createServerSupabaseClient();

    const now = new Date().toISOString();

    const { data, error } = await supabase
      .from('temp_emails')
      .delete()
      .lt('expiration_date', now)
      .select('id');

    if (error) {
      throw new DatabaseQueryError(
        'Failed to delete expired emails',
        'DELETE FROM temp_emails',
        new Error(error.message)
      );
    }

    const deletedCount = data?.length || 0;
    logInfo('DB', `Deleted ${deletedCount} expired email(s)`);

    return deletedCount;
  } catch (error) {
    logError('DB', `Error deleting expired emails: ${error instanceof Error ? error.message : String(error)}`);

    if (error instanceof DatabaseQueryError) {
      throw error;
    }

    throw new DatabaseQueryError(
      'Failed to delete expired emails',
      'DELETE FROM temp_emails',
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * Test the connection to the Supabase database
 */
export async function testSupabaseConnection(): Promise<boolean> {
  try {
    logInfo('DB', 'Testing connection to Supabase database');

    const supabase = createServerSupabaseClient();

    const { data, error } = await supabase
      .from('temp_emails')
      .select('id')
      .limit(1);

    if (error) {
      throw new DatabaseConnectionError(
        'Failed to connect to Supabase database',
        new Error(error.message)
      );
    }

    logInfo('DB', 'Successfully connected to Supabase database');
    return true;
  } catch (error) {
    logError('DB', `Error testing Supabase connection: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
}
