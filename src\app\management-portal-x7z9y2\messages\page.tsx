'use client';

import { useState, useEffect } from 'react';
import { ContactMessage, MessageStatus } from '@/lib/types/contact';
import MessageThread from '@/components/admin/MessageThread';
import { formatRelativeTime, formatLocaleTime } from '@/lib/helpers/dateFormatter';
import { AdminCard, AdminCardHeader, AdminCardContent, AdminCardTitle } from '@/components/admin/ui/AdminCard';
import { AdminButton } from '@/components/admin/ui/AdminButton';
import { AdminBadge } from '@/components/admin/ui/AdminBadge';
import { AdminTextArea } from '@/components/admin/ui/AdminTextArea';
import { AdminSkeleton, AdminSkeletonText } from '@/components/admin/ui/AdminSkeleton';
import { AdminStatusIndicator } from '@/components/admin/ui/AdminStatusIndicator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content, AdminTabsTrigger } from '@/components/ui/Tabs';

/**
 * Admin message management page
 */
export default function MessagesPage() {
  const [messages, setMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<MessageStatus>('unread');
  const [selectedMessage, setSelectedMessage] = useState<ContactMessage | null>(null);
  const [replyText, setReplyText] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [emailStatus, setEmailStatus] = useState<{success: boolean; message: string} | null>(null);
  const [fadeIn, setFadeIn] = useState(false);

  // Fetch messages
  const fetchMessages = async (status: MessageStatus = 'unread') => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/management-portal-x7z9y2/messages?status=${status}`);
      const data = await response.json();

      if (data.success) {
        setMessages(data.messages);
      } else {
        setError(data.message || 'Failed to fetch messages');
      }
    } catch (err) {
      setError('An error occurred while fetching messages');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    const status = value as MessageStatus;
    setActiveTab(status);
    fetchMessages(status);
    setSelectedMessage(null);
  };

  // Handle message selection
  const handleSelectMessage = async (message: ContactMessage) => {
    // Reset fade-in animation
    setFadeIn(false);

    // Set selected message after a brief delay for animation
    setTimeout(() => {
      setSelectedMessage(message);
      setFadeIn(true);
    }, 50);

    // If message is unread, mark as read
    if (message.status === 'unread') {
      try {
        const response = await fetch(`/api/management-portal-x7z9y2/messages/${message.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ status: 'read' })
        });

        const data = await response.json();

        if (data.success) {
          // Update message status in the list
          setMessages(prev =>
            prev.map(msg =>
              msg.id === message.id ? { ...msg, status: 'read' } : msg
            )
          );
        }
      } catch (err) {
        console.error('Error marking message as read:', err);
      }
    }
  };

  // Handle reply submission
  const handleSendReply = async () => {
    if (!selectedMessage || !replyText.trim()) return;

    try {
      setIsSending(true);
      setError(null);
      setSuccess(null);
      setEmailStatus(null);

      const response = await fetch('/api/management-portal-x7z9y2/messages/reply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          parentId: selectedMessage.id,
          threadId: selectedMessage.threadId || null, // Ensure threadId is never undefined
          message: replyText
        })
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Reply sent successfully');
        setReplyText('');

        // Set email status if available
        if (data.emailSent !== undefined) {
          setEmailStatus({
            success: data.emailSent,
            message: data.emailStatus || (data.emailSent ? 'Email notification sent' : 'Email notification failed')
          });
        }

        // Update message status in the list
        setMessages(prev =>
          prev.map(msg =>
            msg.id === selectedMessage.id ? { ...msg, status: 'replied' } : msg
          )
        );

        // Refresh messages after a short delay
        setTimeout(() => {
          fetchMessages(activeTab);
          setSelectedMessage(null);
          setEmailStatus(null);
        }, 3000);
      } else {
        setError(data.message || 'Failed to send reply');
      }
    } catch (err) {
      setError('An error occurred while sending reply');
      console.error(err);
    } finally {
      setIsSending(false);
    }
  };

  // Handle message archiving
  const handleArchiveMessage = async () => {
    if (!selectedMessage) return;

    try {
      const response = await fetch(`/api/management-portal-x7z9y2/messages/${selectedMessage.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: 'archived' })
      });

      const data = await response.json();

      if (data.success) {
        // Remove message from the list
        setMessages(prev => prev.filter(msg => msg.id !== selectedMessage.id));
        setSelectedMessage(null);
        setSuccess('Message archived successfully');

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(data.message || 'Failed to archive message');
      }
    } catch (err) {
      setError('An error occurred while archiving message');
      console.error(err);
    }
  };

  // Load messages on initial render
  useEffect(() => {
    fetchMessages('unread');
  }, []);

  // Set fade-in animation when component mounts
  useEffect(() => {
    setFadeIn(true);
  }, []);

  return (
    <div className="container mx-auto py-6 px-4 sm:px-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h1 className="text-2xl font-bold text-gray-900">Message Management</h1>
        <AdminButton
          onClick={() => fetchMessages(activeTab)}
          variant="outline"
          leftIcon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
            </svg>
          }
        >
          Refresh
        </AdminButton>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg shadow-sm mb-6 animate-fadeIn">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg shadow-sm mb-6 animate-fadeIn">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              {success}
            </div>
            {emailStatus && (
              <div className="flex items-center">
                <AdminStatusIndicator
                  status={emailStatus.success ? 'success' : 'error'}
                  label={emailStatus.success ? 'Email notification sent' : 'Email notification failed'}
                  pulseAnimation={!emailStatus.success}
                />
              </div>
            )}
          </div>
          {emailStatus && !emailStatus.success && (
            <div className="mt-2 ml-7 text-xs text-amber-600">
              {emailStatus.message}
            </div>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <AdminCard>
            <AdminCardHeader className="p-4">
              <Tabs value={activeTab} onValueChange={handleTabChange}>
                <TabsList>
                  <AdminTabsTrigger
                    value="unread"
                    badge={messages.filter(m => m.status === 'unread').length || undefined}
                    icon={
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                      </svg>
                    }
                  >
                    Unread
                  </AdminTabsTrigger>
                  <AdminTabsTrigger
                    value="read"
                    badge={messages.filter(m => m.status === 'read').length || undefined}
                    icon={
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M2.94 6.412A2 2 0 002 8.108V16a2 2 0 002 2h12a2 2 0 002-2V8.108a2 2 0 00-.94-1.696l-6-3.75a2 2 0 00-2.12 0l-6 3.75zm2.615 2.423a1 1 0 10-1.11 1.664l5 3.333a1 1 0 001.11 0l5-3.333a1 1 0 00-1.11-1.664L10 12.027l-4.445-2.192z" clipRule="evenodd" />
                      </svg>
                    }
                  >
                    Read
                  </AdminTabsTrigger>
                  <AdminTabsTrigger
                    value="replied"
                    badge={messages.filter(m => m.status === 'replied').length || undefined}
                    icon={
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M7.707 3.293a1 1 0 010 1.414L5.414 7H11a7 7 0 017 7v2a1 1 0 11-2 0v-2a5 5 0 00-5-5H5.414l2.293 2.293a1 1 0 11-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    }
                  >
                    Replied
                  </AdminTabsTrigger>
                </TabsList>
              </Tabs>
            </AdminCardHeader>
            <div className="max-h-[600px] overflow-y-auto">
              {loading ? (
                <div className="p-4 space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="p-3 border-b border-gray-100">
                      <AdminSkeletonText lines={3} />
                    </div>
                  ))}
                </div>
              ) : messages.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mx-auto text-gray-300 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <p>No messages found</p>
                </div>
              ) : (
                <ul className="divide-y divide-gray-200">
                  {messages.map((message) => (
                    <li
                      key={message.id}
                      className={`p-4 cursor-pointer hover:bg-gray-50 transition-all duration-200 border-l-4 ${
                        selectedMessage?.id === message.id
                          ? 'bg-indigo-50 border-l-indigo-500 shadow-sm'
                          : 'border-l-transparent'
                      }`}
                      onClick={() => handleSelectMessage(message)}
                    >
                      <div className="flex justify-between">
                        <h3 className="font-medium text-gray-900 truncate max-w-[200px]">
                          {message.subject}
                        </h3>
                        {message.status === 'unread' && (
                          <AdminBadge variant="primary" size="sm">
                            New
                          </AdminBadge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500 truncate">{message.name}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        {formatRelativeTime(message.createdAt)}
                      </p>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </AdminCard>
        </div>

        <div className="lg:col-span-2">
          {selectedMessage ? (
            <AdminCard className={`transition-opacity duration-300 ${fadeIn ? 'opacity-100' : 'opacity-0'}`}>
              <AdminCardHeader className="flex justify-between items-center">
                <AdminCardTitle>{selectedMessage.subject}</AdminCardTitle>
                <AdminButton
                  variant="outline"
                  size="sm"
                  onClick={handleArchiveMessage}
                >
                  Archive
                </AdminButton>
              </AdminCardHeader>
              <AdminCardContent className="border-b border-gray-200">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <p className="font-medium text-gray-900">{selectedMessage.name}</p>
                    <p className="text-sm text-gray-500">{selectedMessage.email}</p>
                  </div>
                  <p className="text-sm text-gray-500">
                    {formatLocaleTime(selectedMessage.createdAt)}
                  </p>
                </div>
                <div className="prose max-w-none">
                  <p className="whitespace-pre-wrap">{selectedMessage.message}</p>
                </div>
              </AdminCardContent>

              {selectedMessage.threadId && (
                <AdminCardContent className="border-b border-gray-200">
                  <h3 className="font-medium text-gray-900 mb-3">Conversation History</h3>
                  <MessageThread threadId={selectedMessage.threadId} />
                </AdminCardContent>
              )}

              <AdminCardContent>
                <h3 className="font-medium text-gray-900 mb-2">Reply</h3>
                <AdminTextArea
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  rows={5}
                  placeholder="Type your reply here..."
                  disabled={isSending}
                  autoResize
                  maxHeight={300}
                />
                <div className="mt-4 flex justify-end">
                  <AdminButton
                    variant="primary"
                    onClick={handleSendReply}
                    disabled={!replyText.trim() || isSending}
                    isLoading={isSending}
                  >
                    Send Reply
                  </AdminButton>
                </div>
              </AdminCardContent>
            </AdminCard>
          ) : (
            <AdminCard className={`transition-opacity duration-300 ${fadeIn ? 'opacity-100' : 'opacity-0'}`}>
              <AdminCardContent className="p-8 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No message selected</h3>
                <p className="text-gray-500">Select a message from the list to view its details and reply.</p>
              </AdminCardContent>
            </AdminCard>
          )}
        </div>
      </div>
    </div>
  );
}
