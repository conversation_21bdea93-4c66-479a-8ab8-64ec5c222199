/**
 * Performance Monitoring Utilities
 * 
 * Provides comprehensive performance monitoring for the email authentication tools
 * including timing, memory usage, and error tracking.
 */

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: Date;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

interface MemoryUsage {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 1000; // Keep last 1000 metrics
  private timers: Map<string, number> = new Map();

  /**
   * Start timing an operation
   */
  startTimer(operationId: string): void {
    this.timers.set(operationId, Date.now());
  }

  /**
   * End timing an operation and record the metric
   */
  endTimer(
    operationId: string, 
    operation: string, 
    success: boolean = true, 
    error?: string,
    metadata?: Record<string, any>
  ): number {
    const startTime = this.timers.get(operationId);
    if (!startTime) {
      console.warn(`No start time found for operation: ${operationId}`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(operationId);

    const metric: PerformanceMetric = {
      operation,
      duration,
      timestamp: new Date(),
      success,
      error,
      metadata
    };

    this.addMetric(metric);
    return duration;
  }

  /**
   * Record a metric directly
   */
  recordMetric(
    operation: string,
    duration: number,
    success: boolean = true,
    error?: string,
    metadata?: Record<string, any>
  ): void {
    const metric: PerformanceMetric = {
      operation,
      duration,
      timestamp: new Date(),
      success,
      error,
      metadata
    };

    this.addMetric(metric);
  }

  /**
   * Add a metric to the collection
   */
  private addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  /**
   * Get performance statistics for an operation
   */
  getStats(operation?: string, timeWindow?: number): {
    count: number;
    averageDuration: number;
    minDuration: number;
    maxDuration: number;
    successRate: number;
    errorRate: number;
    recentErrors: string[];
  } {
    let filteredMetrics = this.metrics;

    // Filter by operation if specified
    if (operation) {
      filteredMetrics = filteredMetrics.filter(m => m.operation === operation);
    }

    // Filter by time window if specified (in minutes)
    if (timeWindow) {
      const cutoff = new Date(Date.now() - timeWindow * 60 * 1000);
      filteredMetrics = filteredMetrics.filter(m => m.timestamp >= cutoff);
    }

    if (filteredMetrics.length === 0) {
      return {
        count: 0,
        averageDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        successRate: 0,
        errorRate: 0,
        recentErrors: []
      };
    }

    const durations = filteredMetrics.map(m => m.duration);
    const successCount = filteredMetrics.filter(m => m.success).length;
    const errorCount = filteredMetrics.length - successCount;
    const recentErrors = filteredMetrics
      .filter(m => !m.success && m.error)
      .slice(-5)
      .map(m => m.error!);

    return {
      count: filteredMetrics.length,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      successRate: (successCount / filteredMetrics.length) * 100,
      errorRate: (errorCount / filteredMetrics.length) * 100,
      recentErrors
    };
  }

  /**
   * Get current memory usage
   */
  getMemoryUsage(): MemoryUsage {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage();
      return {
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
        external: Math.round(usage.external / 1024 / 1024), // MB
        rss: Math.round(usage.rss / 1024 / 1024) // MB
      };
    }

    // Fallback for browser environment
    return {
      heapUsed: 0,
      heapTotal: 0,
      external: 0,
      rss: 0
    };
  }

  /**
   * Get all metrics for a specific operation
   */
  getMetrics(operation?: string, limit: number = 100): PerformanceMetric[] {
    let filteredMetrics = this.metrics;

    if (operation) {
      filteredMetrics = filteredMetrics.filter(m => m.operation === operation);
    }

    return filteredMetrics.slice(-limit);
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
    this.timers.clear();
  }

  /**
   * Get a summary of all operations
   */
  getSummary(): Record<string, any> {
    const operations = [...new Set(this.metrics.map(m => m.operation))];
    const summary: Record<string, any> = {};

    operations.forEach(operation => {
      summary[operation] = this.getStats(operation, 60); // Last 60 minutes
    });

    return {
      operations: summary,
      totalMetrics: this.metrics.length,
      memoryUsage: this.getMemoryUsage(),
      uptime: typeof process !== 'undefined' ? process.uptime() : 0
    };
  }

  /**
   * Check if performance is within acceptable thresholds
   */
  checkHealth(): {
    healthy: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check error rates
    const recentStats = this.getStats(undefined, 30); // Last 30 minutes
    if (recentStats.errorRate > 5) {
      issues.push(`High error rate: ${recentStats.errorRate.toFixed(1)}%`);
      recommendations.push('Investigate recent errors and implement fixes');
    }

    // Check average response times
    const dkimStats = this.getStats('dkim_generation', 30);
    if (dkimStats.averageDuration > 5000) {
      issues.push(`Slow DKIM generation: ${dkimStats.averageDuration.toFixed(0)}ms average`);
      recommendations.push('Consider optimizing key generation algorithm');
    }

    const dmarcStats = this.getStats('dmarc_generation', 30);
    if (dmarcStats.averageDuration > 1000) {
      issues.push(`Slow DMARC generation: ${dmarcStats.averageDuration.toFixed(0)}ms average`);
      recommendations.push('Optimize DMARC record generation process');
    }

    // Check memory usage
    const memory = this.getMemoryUsage();
    if (memory.heapUsed > 512) { // 512MB threshold
      issues.push(`High memory usage: ${memory.heapUsed}MB`);
      recommendations.push('Consider implementing memory cleanup routines');
    }

    return {
      healthy: issues.length === 0,
      issues,
      recommendations
    };
  }
}

// Global performance monitor instance
const performanceMonitor = new PerformanceMonitor();

/**
 * Decorator function to automatically monitor function performance
 */
export function monitorPerformance(operationName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const operationId = `${operationName}_${Date.now()}_${Math.random()}`;
      performanceMonitor.startTimer(operationId);

      try {
        const result = await method.apply(this, args);
        performanceMonitor.endTimer(operationId, operationName, true, undefined, {
          args: args.length,
          resultType: typeof result
        });
        return result;
      } catch (error) {
        performanceMonitor.endTimer(
          operationId, 
          operationName, 
          false, 
          error instanceof Error ? error.message : 'Unknown error',
          { args: args.length }
        );
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Utility function to measure execution time of a function
 */
export async function measureTime<T>(
  operation: string,
  fn: () => Promise<T> | T,
  metadata?: Record<string, any>
): Promise<{ result: T; duration: number }> {
  const operationId = `${operation}_${Date.now()}_${Math.random()}`;
  performanceMonitor.startTimer(operationId);

  try {
    const result = await fn();
    const duration = performanceMonitor.endTimer(operationId, operation, true, undefined, metadata);
    return { result, duration };
  } catch (error) {
    const duration = performanceMonitor.endTimer(
      operationId, 
      operation, 
      false, 
      error instanceof Error ? error.message : 'Unknown error',
      metadata
    );
    throw error;
  }
}

/**
 * Log performance metrics to console (development only)
 */
export function logPerformanceStats(operation?: string): void {
  if (process.env.NODE_ENV === 'development') {
    const stats = performanceMonitor.getStats(operation, 60);
    console.log(`Performance Stats${operation ? ` for ${operation}` : ''}:`, {
      count: stats.count,
      avgDuration: `${stats.averageDuration.toFixed(2)}ms`,
      minDuration: `${stats.minDuration}ms`,
      maxDuration: `${stats.maxDuration}ms`,
      successRate: `${stats.successRate.toFixed(1)}%`,
      errorRate: `${stats.errorRate.toFixed(1)}%`
    });
  }
}

export { performanceMonitor };
export type { PerformanceMetric, MemoryUsage };
