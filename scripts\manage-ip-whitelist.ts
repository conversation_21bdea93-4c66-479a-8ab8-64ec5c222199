#!/usr/bin/env tsx

/**
 * IP Whitelist Management Script
 * 
 * Command-line utility for managing IP whitelist
 * Usage: npm run manage-whitelist -- add *********** "Trusted office IP"
 *        npm run manage-whitelist -- remove ***********
 *        npm run manage-whitelist -- list
 */

import { createServerSupabaseClient } from '../src/lib/supabase/server';

interface WhitelistEntry {
  id: string;
  ip_address: string;
  reason: string;
  added_at: string | null;
  added_by: string;
  removed_at: string | null;
  removed_by: string | null;
  is_active: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

async function addToWhitelist(ipAddress: string, reason: string, addedBy: string = 'cli-admin') {
  try {
    const supabase = await createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ip_whitelist')
      .insert({
        ip_address: ipAddress,
        reason,
        added_by: addedBy,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        console.error(`❌ IP ${ipAddress} is already whitelisted`);
        return false;
      }
      throw error;
    }

    console.log(`✅ Successfully added ${ipAddress} to whitelist`);
    console.log(`   Reason: ${reason}`);
    console.log(`   Added by: ${addedBy}`);
    return true;
  } catch (error) {
    console.error(`❌ Error adding IP to whitelist:`, error);
    return false;
  }
}

async function removeFromWhitelist(ipAddress: string, removedBy: string = 'cli-admin') {
  try {
    const supabase = await createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ip_whitelist')
      .update({ 
        is_active: false,
        removed_at: new Date().toISOString(),
        removed_by: removedBy
      })
      .eq('ip_address', ipAddress)
      .eq('is_active', true)
      .select();

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      console.error(`❌ IP ${ipAddress} not found in whitelist or already inactive`);
      return false;
    }

    console.log(`✅ Successfully removed ${ipAddress} from whitelist`);
    console.log(`   Removed by: ${removedBy}`);
    return true;
  } catch (error) {
    console.error(`❌ Error removing IP from whitelist:`, error);
    return false;
  }
}

async function listWhitelist() {
  try {
    const supabase = await createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ip_whitelist')
      .select('*')
      .eq('is_active', true)
      .order('added_at', { ascending: false });

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      console.log('📝 No IP addresses in whitelist');
      return;
    }

    console.log(`📝 Whitelisted IP Addresses (${data.length} total):`);
    console.log('');
    
    data.forEach((entry: WhitelistEntry, index: number) => {
      console.log(`${index + 1}. ${entry.ip_address}`);
      console.log(`   Reason: ${entry.reason}`);
      console.log(`   Added: ${entry.added_at ? new Date(entry.added_at).toLocaleString() : 'Unknown'}`);
      console.log(`   Added by: ${entry.added_by}`);
      console.log('');
    });
  } catch (error) {
    console.error(`❌ Error listing whitelist:`, error);
  }
}

async function checkIP(ipAddress: string) {
  try {
    const supabase = await createServerSupabaseClient();
    const { data, error } = await supabase
      .from('ip_whitelist')
      .select('*')
      .eq('ip_address', ipAddress)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw error;
    }

    if (data) {
      console.log(`✅ ${ipAddress} is whitelisted`);
      console.log(`   Reason: ${data.reason}`);
      console.log(`   Added: ${data.added_at ? new Date(data.added_at).toLocaleString() : 'Unknown'}`);
      console.log(`   Added by: ${data.added_by}`);
    } else {
      console.log(`❌ ${ipAddress} is not whitelisted`);
    }
  } catch (error) {
    console.error(`❌ Error checking IP:`, error);
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('IP Whitelist Management Tool');
    console.log('');
    console.log('Usage:');
    console.log('  npm run manage-whitelist -- add <ip> "<reason>" [added_by]');
    console.log('  npm run manage-whitelist -- remove <ip> [removed_by]');
    console.log('  npm run manage-whitelist -- list');
    console.log('  npm run manage-whitelist -- check <ip>');
    console.log('');
    console.log('Examples:');
    console.log('  npm run manage-whitelist -- add *********** "Office network"');
    console.log('  npm run manage-whitelist -- remove ***********');
    console.log('  npm run manage-whitelist -- list');
    console.log('  npm run manage-whitelist -- check 127.0.0.1');
    return;
  }

  const command = args[0];

  switch (command) {
    case 'add':
      if (args.length < 3) {
        console.error('❌ Usage: add <ip> "<reason>" [added_by]');
        process.exit(1);
      }
      await addToWhitelist(args[1], args[2], args[3]);
      break;

    case 'remove':
      if (args.length < 2) {
        console.error('❌ Usage: remove <ip> [removed_by]');
        process.exit(1);
      }
      await removeFromWhitelist(args[1], args[2]);
      break;

    case 'list':
      await listWhitelist();
      break;

    case 'check':
      if (args.length < 2) {
        console.error('❌ Usage: check <ip>');
        process.exit(1);
      }
      await checkIP(args[1]);
      break;

    default:
      console.error(`❌ Unknown command: ${command}`);
      console.error('Available commands: add, remove, list, check');
      process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
