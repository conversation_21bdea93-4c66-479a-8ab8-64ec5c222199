'use client';

import React, { useRef, useEffect, useState, useMemo } from 'react';

interface SimpleResponsiveIframeRendererProps {
  /**
   * The HTML content to render in the iframe
   */
  html: string;

  /**
   * Optional className for the iframe container
   */
  className?: string;

  /**
   * Optional array of web fonts to load in the iframe
   * Each font should be a Google Fonts URL or similar
   */
  webFonts?: string[];
}

// Helper function to generate a simple content hash for memoization
function generateContentHash(content: string): string {
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString(36);
}

/**
 * SimpleResponsiveIframeRenderer Component
 *
 * A simpler implementation of responsive iframe email rendering that doesn't rely on external libraries.
 * Uses CSS transforms to scale down fixed-width emails on mobile devices.
 */
export default function SimpleResponsiveIframeRenderer({
  html,
  className = '',
  webFonts = []
}: SimpleResponsiveIframeRendererProps) {
  // Create a ref for the iframe
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // State to track if the component is mounted
  const [isMounted, setIsMounted] = useState(false);

  // Generate a content hash for memoization
  const contentHash = useMemo(() => generateContentHash(html + webFonts.join('')), [html, webFonts]);

  // Create the HTML content with viewport scaling for mobile
  const htmlContent = useMemo(() => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, minimum-scale=1">
          <meta name="format-detection" content="telephone=no">
          ${webFonts.map(font => `<link href="${font}" rel="stylesheet">`).join('')}
          <style>
            /* Reset styles */
            html, body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              line-height: 1.4;
              overflow: hidden; /* Prevent both horizontal and vertical scrolling */
              -webkit-text-size-adjust: 100%;
              -webkit-font-smoothing: antialiased;
              height: 100%; /* Ensure full height */
              width: 100%; /* Ensure full width */
            }

            /* Mobile-specific wrapper for scaling */
            .email-wrapper {
              max-width: 100%;
              overflow: hidden; /* Prevent both horizontal and vertical scrolling */
              position: relative;
              min-height: 100%; /* Ensure full height */
            }

            /* Ensure images don't overflow */
            img {
              max-width: 100%;
              height: auto !important;
            }

            /* Ensure tables don't overflow */
            table {
              max-width: 100%;
            }

            /* Default link styling */
            a:not([style]) {
              color: #0000EE;
              text-decoration: underline;
            }

            /* Gmail-like mobile scaling */
            @media (max-width: 640px) {
              /* Add mobile-specific styles */
              .email-mobile-scale {
                transform-origin: top left;
                transition: transform 0.2s ease-in-out;
              }

              /* Handle fixed-width tables */
              table[width], table[style*="width"] {
                max-width: 100% !important;
              }
            }
          </style>
          <script>
            // This script handles responsive scaling for fixed-width emails
            window.addEventListener('load', function() {
              // Function to handle email scaling
              function handleEmailScaling() {
                var wrapper = document.querySelector('.email-wrapper');
                var content = wrapper.firstElementChild;
                var viewportWidth = window.innerWidth;

                // Find all fixed-width elements (enhanced selector)
                var fixedWidthElements = document.querySelectorAll(
                  'table[width], table[style*="width"], div[style*="width"], td[width], ' +
                  'img[width], center > table, .container, [class*="container"], ' +
                  '[style*="margin-left: auto"][style*="margin-right: auto"]'
                );
                var maxContentWidth = 0;

                // Find the maximum content width
                fixedWidthElements.forEach(function(el) {
                  var elWidth = el.getAttribute('width') ||
                               (el.style.width ? parseInt(el.style.width, 10) : 0) ||
                               el.offsetWidth;

                  // Convert to number
                  elWidth = parseInt(elWidth, 10);

                  if (elWidth > maxContentWidth) {
                    maxContentWidth = elWidth;
                  }
                });

                // If no fixed width elements found, use the content width
                if (maxContentWidth === 0) {
                  maxContentWidth = content.offsetWidth;
                }

                // Only scale if content is wider than viewport and we're on mobile
                if (maxContentWidth > viewportWidth && viewportWidth < 640) {
                  var scale = viewportWidth / maxContentWidth;

                  // Dynamic minimum scale based on device width
                  var minScale = 0.6; // Default in optimal range (0.5-0.7)

                  // For very small devices, allow more aggressive scaling
                  if (viewportWidth < 375) {
                    minScale = 0.5; // More aggressive scaling for small phones
                  } else if (viewportWidth >= 375 && viewportWidth < 480) {
                    minScale = 0.55; // Medium scaling for medium phones
                  }

                  // Check if email has small text and adjust scaling if needed
                  var hasSmallText = document.querySelectorAll('font[size="1"], [style*="font-size: 10px"], [style*="font-size: 11px"], [style*="font-size:10px"], [style*="font-size:11px"]').length > 0;
                  if (hasSmallText) {
                    // Less aggressive scaling for emails with already small text
                    minScale = Math.min(Math.max(minScale + 0.05, 0.55), 0.7);
                  }

                  // Apply minimum scale to prevent too small content
                  scale = Math.max(scale, minScale);

                  // Apply the scale transform
                  content.classList.add('email-mobile-scale');
                  content.style.transform = 'scale(' + scale + ')';
                  content.style.width = (maxContentWidth) + 'px';
                  content.style.transformOrigin = 'top left';

                  // Calculate the scaled height with a small buffer to prevent scrollbars
                  const scaledHeight = (content.offsetHeight * scale) + 5;

                  // Set wrapper height to match scaled content
                  wrapper.style.height = scaledHeight + 'px';

                  // Ensure the body and html elements don't scroll
                  document.body.style.overflow = 'hidden';
                  document.documentElement.style.overflow = 'hidden';

                  // Notify parent iframe about height change
                  window.parent.postMessage({
                    type: 'resize',
                    height: scaledHeight
                  }, '*');
                } else {
                  // Reset scaling if not needed
                  if (content.classList.contains('email-mobile-scale')) {
                    content.classList.remove('email-mobile-scale');
                    content.style.transform = '';
                    content.style.width = '';

                    // Calculate height with a small buffer to prevent scrollbars
                    const contentHeight = content.offsetHeight + 5;
                    wrapper.style.height = contentHeight + 'px';

                    // Ensure the body and html elements don't scroll
                    document.body.style.overflow = 'hidden';
                    document.documentElement.style.overflow = 'hidden';

                    // Notify parent iframe about height change
                    window.parent.postMessage({
                      type: 'resize',
                      height: contentHeight
                    }, '*');
                  }
                }
              }

              // Initial scaling
              handleEmailScaling();

              // Re-scale on resize
              window.addEventListener('resize', handleEmailScaling);

              // Handle links to make them work properly
              document.querySelectorAll('a[href]').forEach(function(link) {
                link.addEventListener('click', function(e) {
                  e.preventDefault();

                  var href = this.getAttribute('href');

                  // Open links in new tab/window
                  if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
                    window.open(href, '_blank');
                  }
                });
              });
            });
          </script>
        </head>
        <body>
          <div class="email-wrapper">
            ${html}
          </div>
        </body>
      </html>
    `;
  }, [html, webFonts, contentHash]);

  // Effect to handle client-side rendering
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Effect to handle iframe content and resizing
  useEffect(() => {
    if (!isMounted || !iframeRef.current) return;

    const iframe = iframeRef.current;

    try {
      // Set initial height to prevent scrollbar flash
      iframe.style.height = '500px'; // Temporary generous height during loading

      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) return;

      // Write the HTML content
      iframeDoc.open();
      iframeDoc.write(htmlContent);
      iframeDoc.close();

      // Improved height adjustment function with debounce
      let adjustHeightTimeout: NodeJS.Timeout;

      const adjustHeight = () => {
        if (!iframe.contentWindow) return;

        // Clear any pending adjustment
        clearTimeout(adjustHeightTimeout);

        // Set a timeout to debounce multiple rapid adjustments
        adjustHeightTimeout = setTimeout(() => {
          try {
            const body = iframeDoc.body;
            const html = iframeDoc.documentElement;

            // Get all direct children of the email wrapper
            const emailWrapper = iframeDoc.querySelector('.email-wrapper');
            const wrapperContent = emailWrapper?.children || [];

            // Calculate the total height of all content
            let contentHeight = 0;
            for (let i = 0; i < wrapperContent.length; i++) {
              const element = wrapperContent[i] as HTMLElement;
              contentHeight += element.offsetHeight;
            }

            // Use the maximum of various height calculations
            const height = Math.max(
              body.scrollHeight,
              body.offsetHeight,
              html.clientHeight,
              html.scrollHeight,
              html.offsetHeight,
              contentHeight || 0
            );

            // Add a small buffer to prevent any potential scrollbar
            const heightWithBuffer = height + 5;

            // Apply the height
            iframe.style.height = `${heightWithBuffer}px`;
          } catch (e) {
            console.error('Error adjusting iframe height:', e);
          }
        }, 10); // Short delay to batch DOM measurements
      };

      // Listen for resize messages from the iframe content
      const handleMessage = (event: MessageEvent) => {
        if (event.data && event.data.type === 'resize' && typeof event.data.height === 'number') {
          // Add a small buffer to prevent any potential scrollbar
          const heightWithBuffer = event.data.height + 5;
          iframe.style.height = `${heightWithBuffer}px`;
        }
      };

      // Adjust height at multiple points to ensure proper sizing

      // 1. Initial adjustment
      adjustHeight();

      // 2. Adjustment after iframe load event
      if (iframe.contentWindow) {
        iframe.contentWindow.addEventListener('load', adjustHeight);
      }

      // 3. Adjustment after all images and resources load
      if (iframe.contentWindow) {
        iframe.contentWindow.addEventListener('load', () => {
          // Additional adjustment after a short delay to catch late layout changes
          setTimeout(adjustHeight, 100);
          // And another adjustment after a longer delay for any async content
          setTimeout(adjustHeight, 500);
        });
      }

      // 4. Listen for resize messages from the content
      window.addEventListener('message', handleMessage);

      // 5. Adjust on window resize
      window.addEventListener('resize', adjustHeight);

      // Clean up
      return () => {
        clearTimeout(adjustHeightTimeout);
        iframe.contentWindow?.removeEventListener('load', adjustHeight);
        window.removeEventListener('message', handleMessage);
        window.removeEventListener('resize', adjustHeight);
      };
    } catch (error) {
      console.error('Error rendering email in iframe:', error);
    }
  }, [isMounted, htmlContent]);

  // Loading state for server-side rendering and initial client render
  const loadingState = (
    <div className="animate-pulse flex flex-col space-y-4 p-4" style={{ minHeight: '200px' }}>
      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      <div className="h-4 bg-gray-200 rounded w-full"></div>
      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
    </div>
  );

  return (
    <div className={`iframe-email-container ${className}`} style={{
      width: '100%',
      overflow: 'hidden',
      position: 'relative'
    }}>
      {!isMounted ? (
        // Server-side and initial client render - show loading state
        loadingState
      ) : (
        // Client-side render after hydration - use iframe
        <iframe
          ref={iframeRef}
          title="Email content"
          style={{
            width: '100%',
            border: 'none',
            overflow: 'hidden',
            minHeight: '200px',
            display: 'block' // Prevent inline display issues
          }}
          scrolling="no" // HTML attribute for older browsers
          sandbox="allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-scripts"
        />
      )}
    </div>
  );
}
