'use client';

import { useEffect } from 'react';
import { ADMIN } from '@/lib/constants';

/**
 * Component that initializes all caches in the application
 * This is a client component that runs once when the app starts
 */
export default function CacheInitializer() {
  useEffect(() => {
    // Log initialization
    console.log('Cache initialization triggered');

    // Use direct admin API path instead of relying on rewrites
    console.log('Using direct admin API path for cache initialization');

    // Call the API to initialize caches server-side (using direct admin path)
    fetch('/api/admin/cache/init', { method: 'POST' })
      .then(response => {
        if (!response.ok) {
          // Enhanced error logging with status code and text
          console.error(`Cache initialization failed with status: ${response.status} ${response.statusText}`);
          // Try to get more details from the response
          response.text().then(text => {
            try {
              // Try to parse as JSON
              const data = JSON.parse(text);
              console.error('Error details:', data);
            } catch (e) {
              // If not JSON, log as text
              console.error('Error response:', text);
            }
          }).catch(e => {
            console.error('Could not read error response:', e);
          });
          return;
        }

        console.log('Cache initialization complete');
        return response.json();
      })
      .then(data => {
        if (data) {
          console.log('Cache initialization result:', data);
        }
      })
      .catch(error => {
        console.error('Cache initialization error:', error);
      });
  }, []);

  // This component doesn't render anything
  return null;
}
