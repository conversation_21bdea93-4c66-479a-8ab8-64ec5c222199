/**
 * Tests for Email Tester Analyze API Route
 */
import { NextRequest } from 'next/server';
import { POST } from '../route';

// Helper function to create a proper NextRequest mock
function createMockRequest(body: any) {
  const request = {
    json: jest.fn().mockResolvedValue(body),
    method: 'POST',
    url: 'http://localhost:3000/api/tools/email-tester/analyze',
    headers: new Headers({ 'Content-Type': 'application/json' })
  } as unknown as NextRequest;

  return request;
}

// Mock the database functions
jest.mock('@/lib/tools/email-tester/database', () => ({
  getEmailTesterEmail: jest.fn(),
  storeEmailTesterResults: jest.fn(),
}));

// Mock other dependencies
jest.mock('@/lib/tools/email-tester/headerParser', () => ({
  analyzeEmailAuthentication: jest.fn(),
  parseAuthenticationHeaders: jest.fn(),
  extractSenderDomain: jest.fn(),
  extractSenderIp: jest.fn(),
}));

jest.mock('@/lib/tools/email-tester/deepseekService', () => ({
  analyzeEmailDeliverability: jest.fn(),
}));

jest.mock('@/lib/logging', () => ({
  logError: jest.fn(),
}));

describe('/api/tools/email-tester/analyze', () => {
  const mockGetEmailTesterEmail = require('@/lib/tools/email-tester/database').getEmailTesterEmail;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Polling behavior', () => {
    it('should return 200 status when no email is found (polling state)', async () => {
      // Mock no email found
      mockGetEmailTesterEmail.mockResolvedValue(null);

      const request = createMockRequest({
        testAddress: '<EMAIL>',
        testAddressId: 'test-id-123'
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200); // Should be 200, not 404
      expect(data.success).toBe(false);
      expect(data.polling).toBe(true);
      expect(data.status).toBe('waiting');
      expect(data.message).toBe('No email found for this test address');
    });

    it('should return 400 when test address is missing', async () => {
      const request = createMockRequest({});

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.message).toBe('Test address is required');
    });

    it('should call getEmailTesterEmail with correct test address', async () => {
      mockGetEmailTesterEmail.mockResolvedValue(null);

      const testAddress = '<EMAIL>';
      const request = createMockRequest({
        testAddress,
        testAddressId: 'test-id-456'
      });

      await POST(request);

      expect(mockGetEmailTesterEmail).toHaveBeenCalledWith(testAddress);
    });
  });

  describe('Error handling', () => {
    it('should return 500 when database throws an error', async () => {
      mockGetEmailTesterEmail.mockRejectedValue(new Error('Database connection failed'));

      const request = createMockRequest({
        testAddress: '<EMAIL>',
        testAddressId: 'test-id-error'
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.message).toContain('Failed to analyze email deliverability');
    });
  });
});
