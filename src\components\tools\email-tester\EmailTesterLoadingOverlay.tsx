/**
 * EmailTesterLoadingOverlay Component
 *
 * Displays a branded loading overlay specifically for the Email Tester Tool
 * that matches the main VanishPost application's loading experience while maintaining
 * the earth-tone design system.
 */

import React from 'react';

interface EmailTesterLoadingOverlayProps {
  /**
   * Whether the overlay is visible
   */
  isVisible: boolean;
  /**
   * Custom loading message (optional)
   */
  message?: string;
  /**
   * Whether to show the full overlay or inline loading
   */
  variant?: 'overlay' | 'inline';
}

export default function EmailTesterLoadingOverlay({
  isVisible,
  message = "Loading Email Tester Tool...",
  variant = 'overlay'
}: EmailTesterLoadingOverlayProps) {
  if (!isVisible) return null;

  // Inline variant for smaller loading states
  if (variant === 'inline') {
    return (
      <div className="flex flex-col items-center justify-center py-16 px-4">
        <div className="flex flex-col items-center">
          {/* Email Tester Icon */}
          <div className="mb-6 flex items-center justify-center w-16 h-16 rounded-2xl shadow-lg animate-pulse" style={{ backgroundColor: '#66b077' }}>
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6z" />
              <path d="M22 6l-10 7L2 6" />
              <path d="M9 12l2 2 4-4" />
            </svg>
          </div>

          {/* Loading animation with earth-tone colors */}
          <div className="flex space-x-2 mb-4">
            <div className="w-3 h-3 rounded-full animate-bounce" style={{ backgroundColor: '#66b077', animationDelay: '0ms' }}></div>
            <div className="w-3 h-3 rounded-full animate-bounce" style={{ backgroundColor: '#956b50', animationDelay: '150ms' }}></div>
            <div className="w-3 h-3 rounded-full animate-bounce" style={{ backgroundColor: '#f59e0b', animationDelay: '300ms' }}></div>
          </div>

          <p className="text-sm text-center" style={{ color: '#4a3728' }}>{message}</p>
        </div>
      </div>
    );
  }

  // Full overlay variant
  return (
    <div
      className="fixed inset-0 z-50 flex flex-col items-center justify-center transition-opacity duration-300"
      style={{
        backgroundColor: '#fbfaf8',
        opacity: isVisible ? 1 : 0,
        pointerEvents: isVisible ? 'auto' : 'none'
      }}
    >
      <div className="flex flex-col items-center max-w-md mx-4">
        {/* Email Tester Tool Branding */}
        <div className="mb-6 flex flex-col items-center">
          {/* Email Tester Tool Icon */}
          <div className="mb-4 flex items-center justify-center w-16 h-16 rounded-2xl" style={{ backgroundColor: '#66b077' }}>
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6z" />
              <path d="M22 6l-10 7L2 6" />
              <path d="M9 12l2 2 4-4" />
            </svg>
          </div>

          {/* Tool Title */}
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2" style={{ color: '#1b130e' }}>Email Tester Tool</h2>
            <p className="text-sm" style={{ color: '#4a3728' }}>Professional email authentication analysis</p>
          </div>
        </div>

        {/* Enhanced Loading Animation */}
        <div className="mb-6">
          {/* Loading Bar */}
          <div className="relative w-64 h-2 rounded-full overflow-hidden mb-4" style={{ backgroundColor: '#f3ece8' }}>
            <div
              className="absolute top-0 left-0 h-full rounded-full animate-pulse"
              style={{
                backgroundColor: '#66b077',
                width: '40%',
                animation: 'loadingBar 2s ease-in-out infinite'
              }}
            ></div>
          </div>

          {/* Loading Dots */}
          <div className="flex space-x-2 justify-center">
            <div className="w-3 h-3 rounded-full animate-bounce" style={{ backgroundColor: '#66b077', animationDelay: '0ms' }}></div>
            <div className="w-3 h-3 rounded-full animate-bounce" style={{ backgroundColor: '#956b50', animationDelay: '150ms' }}></div>
            <div className="w-3 h-3 rounded-full animate-bounce" style={{ backgroundColor: '#f59e0b', animationDelay: '300ms' }}></div>
          </div>
        </div>

        {/* Loading Message */}
        <div className="text-center">
          <p className="text-lg font-medium mb-2" style={{ color: '#1b130e' }}>{message}</p>
          <p className="text-sm" style={{ color: '#4a3728' }}>Preparing your email authentication analysis...</p>
        </div>

        {/* Feature Highlights */}
        <div className="mt-8 grid grid-cols-3 gap-4 text-center">
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 rounded-lg mb-2 flex items-center justify-center" style={{ backgroundColor: '#f3ece8' }}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#66b077' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.586-3.414A2 2 0 0018 7.414V6a2 2 0 00-2-2H8a2 2 0 00-2 2v1.414a2 2 0 00.586 1.414L9 11.414V15a2 2 0 002 2h2a2 2 0 002-2v-3.586l2.414-2.414z" />
              </svg>
            </div>
            <span className="text-xs" style={{ color: '#4a3728' }}>SPF Analysis</span>
          </div>

          <div className="flex flex-col items-center">
            <div className="w-8 h-8 rounded-lg mb-2 flex items-center justify-center" style={{ backgroundColor: '#f3ece8' }}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#956b50' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 1 21 9z" />
              </svg>
            </div>
            <span className="text-xs" style={{ color: '#4a3728' }}>DKIM Check</span>
          </div>

          <div className="flex flex-col items-center">
            <div className="w-8 h-8 rounded-lg mb-2 flex items-center justify-center" style={{ backgroundColor: '#f3ece8' }}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ color: '#f59e0b' }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <span className="text-xs" style={{ color: '#4a3728' }}>DMARC Policy</span>
          </div>
        </div>
      </div>
    </div>
  );
}
