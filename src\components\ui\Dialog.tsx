'use client';

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { DialogPortal } from './DialogPortal';

// Define dialog variants using class-variance-authority
const dialogOverlayVariants = cva(
  "fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] transition-opacity",
  {
    variants: {
      state: {
        open: "opacity-100",
        closed: "opacity-0"
      }
    },
    defaultVariants: {
      state: "closed"
    }
  }
);

const dialogContentVariants = cva(
  "bg-white rounded-lg shadow-lg border border-gray-200 p-6 focus:outline-none transition-all",
  {
    variants: {
      state: {
        open: "opacity-100 scale-100",
        closed: "opacity-0 scale-95"
      },
      size: {
        sm: "w-full max-w-sm",
        md: "w-full max-w-md",
        lg: "w-full max-w-lg",
        xl: "w-full max-w-xl",
        "2xl": "w-full max-w-2xl",
        "3xl": "w-full max-w-3xl",
        "4xl": "w-full max-w-4xl",
        "5xl": "w-full max-w-5xl",
        full: "w-[calc(100%-2rem)] max-h-[calc(100%-2rem)]"
      }
    },
    defaultVariants: {
      state: "closed",
      size: "md"
    }
  }
);

// Dialog component
interface DialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children: React.ReactNode;
}

function Dialog({ open = false, onOpenChange, children }: DialogProps) {
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && onOpenChange) {
      onOpenChange(false);
    }
  };

  // If dialog is not open, don't render anything
  if (!open) return null;

  return (
    <DialogPortal>
      {/* Backdrop */}
      <div
        className={dialogOverlayVariants({ state: open ? "open" : "closed" })}
        onClick={handleOverlayClick}
        aria-hidden="true"
      />

      {/* Dialog content */}
      {children}
    </DialogPortal>
  );
}

// Dialog Content component
interface DialogContentProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof dialogContentVariants> {
  size?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl" | "full";
}

function DialogContent({ className, size, children, ...props }: DialogContentProps) {
  return (
    <div
      role="dialog"
      aria-modal="true"
      className={cn(
        dialogContentVariants({ size, state: "open" }),
        "fixed left-1/2 top-1/2 z-[10000] -translate-x-1/2 -translate-y-1/2",
        className
      )}
      style={{
        maxHeight: 'calc(100vh - 40px)',
        overflowY: 'auto'
      }}
      {...props}
    >
      {children}
    </div>
  );
}

// Dialog Header component
interface DialogHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}

function DialogHeader({ className, children, ...props }: DialogHeaderProps) {
  return (
    <div
      className={cn("mb-4", className)}
      {...props}
    >
      {children}
    </div>
  );
}

// Dialog Title component
interface DialogTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {}

function DialogTitle({ className, children, ...props }: DialogTitleProps) {
  return (
    <h2
      className={cn("text-lg font-semibold text-gray-900", className)}
      {...props}
    >
      {children}
    </h2>
  );
}

// Dialog Description component
interface DialogDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

function DialogDescription({ className, children, ...props }: DialogDescriptionProps) {
  return (
    <p
      className={cn("text-sm text-gray-500", className)}
      {...props}
    >
      {children}
    </p>
  );
}

// Dialog Footer component
interface DialogFooterProps extends React.HTMLAttributes<HTMLDivElement> {}

function DialogFooter({ className, children, ...props }: DialogFooterProps) {
  return (
    <div
      className={cn("mt-6 flex justify-end space-x-2", className)}
      {...props}
    >
      {children}
    </div>
  );
}

export {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
};
