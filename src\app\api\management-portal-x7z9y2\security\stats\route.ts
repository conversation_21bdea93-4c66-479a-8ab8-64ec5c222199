import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { verifyAdminAuth } from '@/lib/auth/adminAuth';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * GET /api/management-portal-x7z9y2/security/stats
 * Get real-time security statistics (updated)
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get current time and time windows
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // 1. Get Blocked IPs (from blocked_ips table - individual IPs)
    const { count: blockedIPs } = await supabase
      .from('blocked_ips')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    // Get blocked IPs from last week for comparison
    const { count: blockedIPsLastWeek } = await supabase
      .from('blocked_ips')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .gte('blocked_at', oneWeekAgo.toISOString())
      .lt('blocked_at', now.toISOString());

    // 2. Get Blocked IP Ranges (from blocked_ip_ranges table)
    const { count: blockedIPRanges } = await supabase
      .from('blocked_ip_ranges')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    // Get blocked IP ranges from last week for comparison
    const { count: blockedIPRangesLastWeek } = await supabase
      .from('blocked_ip_ranges')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .gte('blocked_at', oneWeekAgo.toISOString())
      .lt('blocked_at', now.toISOString());

    // 3. Get Blocked Sessions (from blocked_sessions table)
    const { count: blockedSessions } = await supabase
      .from('blocked_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    // Get blocked sessions from last week for comparison
    const { count: blockedSessionsLastWeek } = await supabase
      .from('blocked_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .gte('blocked_at', oneWeekAgo.toISOString())
      .lt('blocked_at', now.toISOString());

    // 4. Calculate Security Score
    // Base score starts at 100, deduct points for security issues
    let securityScore = 100;
    
    // Deduct points for recent violations
    const recentViolations = await supabase
      .from('security_events')
      .select('*', { count: 'exact', head: true })
      .in('event_type', ['rate_limit_exceeded', 'suspicious_activity', 'threat_detected'])
      .gte('created_at', oneHourAgo.toISOString());

    securityScore -= Math.min(30, (recentViolations.count || 0) * 2);

    // Deduct points for active blocks (both individual IPs and ranges)
    securityScore -= Math.min(15, (blockedIPs || 0) * 0.5);
    securityScore -= Math.min(15, (blockedIPRanges || 0) * 1.0);

    // Ensure score doesn't go below 0
    securityScore = Math.max(0, Math.round(securityScore));

    // Get security score from last week for comparison
    const lastWeekEnd = oneWeekAgo;
    const lastWeekStart = new Date(oneWeekAgo.getTime() - 7 * 24 * 60 * 60 * 1000);

    const { count: lastWeekViolations } = await supabase
      .from('security_events')
      .select('*', { count: 'exact', head: true })
      .in('event_type', ['session_blocked', 'suspicious_activity', 'threat_detected'])
      .gte('created_at', lastWeekStart.toISOString())
      .lt('created_at', lastWeekEnd.toISOString());

    let lastWeekSecurityScore = 100;
    lastWeekSecurityScore -= Math.min(30, (lastWeekViolations || 0) * 2);
    lastWeekSecurityScore -= Math.min(15, (blockedIPsLastWeek || 0) * 0.5);
    lastWeekSecurityScore -= Math.min(15, (blockedIPRangesLastWeek || 0) * 1.0);
    lastWeekSecurityScore = Math.max(0, Math.round(lastWeekSecurityScore));

    // Calculate percentage changes with better messaging
    const calculateChange = (current: number, previous: number) => {
      if (previous === 0 && current === 0) return 'No data';
      if (previous === 0) return 'New data';
      const change = ((current - previous) / previous) * 100;
      if (Math.abs(change) < 1) return 'No change';
      return change > 0 ? `+${Math.round(change)}%` : `${Math.round(change)}%`;
    };

    // Helper function to determine change type
    const getChangeType = (current: number, previous: number, change: string) => {
      if (change === 'No data' || change === 'New data' || change === 'No change') {
        return 'neutral' as const;
      }
      return current >= previous ? 'increase' as const : 'decrease' as const;
    };

    const stats = {
      blockedIPs: {
        value: blockedIPs || 0,
        change: calculateChange(blockedIPs || 0, blockedIPsLastWeek || 0),
        changeType: getChangeType(blockedIPs || 0, blockedIPsLastWeek || 0, calculateChange(blockedIPs || 0, blockedIPsLastWeek || 0))
      },
      blockedIPRanges: {
        value: blockedIPRanges || 0,
        change: calculateChange(blockedIPRanges || 0, blockedIPRangesLastWeek || 0),
        changeType: getChangeType(blockedIPRanges || 0, blockedIPRangesLastWeek || 0, calculateChange(blockedIPRanges || 0, blockedIPRangesLastWeek || 0))
      },
      blockedSessions: {
        value: blockedSessions || 0,
        change: calculateChange(blockedSessions || 0, blockedSessionsLastWeek || 0),
        changeType: getChangeType(blockedSessions || 0, blockedSessionsLastWeek || 0, calculateChange(blockedSessions || 0, blockedSessionsLastWeek || 0))
      },
      securityScore: {
        value: securityScore,
        change: calculateChange(securityScore, lastWeekSecurityScore),
        changeType: getChangeType(securityScore, lastWeekSecurityScore, calculateChange(securityScore, lastWeekSecurityScore))
      }
    };

    return NextResponse.json({
      success: true,
      data: stats,
      timestamp: now.toISOString()
    });

  } catch (error) {
    console.error('Error fetching security stats:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch security statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
