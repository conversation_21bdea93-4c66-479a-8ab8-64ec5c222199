'use client';

// DMARC Generator Form Component - Full implementation
import React, { useState } from 'react';
import { DmarcFormData, DmarcFormErrors, DmarcPolicy, DmarcAlignment } from '@/types/dmarc';
import PolicySelector from './PolicySelector';

interface DmarcGeneratorFormProps {
  onSubmit: (data: DmarcFormData) => void;
  loading?: boolean;
}

export default function DmarcGeneratorForm({ onSubmit, loading = false }: DmarcGeneratorFormProps) {
  const [formData, setFormData] = useState<DmarcFormData>({
    domain: '',
    policy: 'none',
    rua: '',
    pct: 100,
    sp: undefined,
    adkim: 'r',
    aspf: 'r',
  });

  const [errors, setErrors] = useState<DmarcFormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [showAdvanced, setShowAdvanced] = useState(false);

  const validateField = (name: keyof DmarcFormData, value: string | number | undefined): string | undefined => {
    switch (name) {
      case 'domain':
        if (!value) return 'Domain is required';
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
        if (!domainRegex.test(value as string)) return 'Invalid domain format';
        break;
      case 'policy':
        if (!value || !['none', 'quarantine', 'reject'].includes(value as string)) {
          return 'Policy must be one of: none, quarantine, reject';
        }
        break;
      case 'rua':
        if (!value) return 'Reporting email address is required';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value as string)) return 'Invalid email address format';
        break;
      case 'pct':
        if (value !== undefined) {
          const pct = Number(value);
          if (isNaN(pct) || pct < 0 || pct > 100) return 'Percentage must be between 0 and 100';
        }
        break;
      case 'sp':
        if (value && !['none', 'quarantine', 'reject'].includes(value as string)) {
          return 'Subdomain policy must be one of: none, quarantine, reject';
        }
        break;
      case 'adkim':
      case 'aspf':
        if (value && !['r', 's'].includes(value as string)) {
          return 'Alignment must be "r" (relaxed) or "s" (strict)';
        }
        break;
    }
    return undefined;
  };

  const handleInputChange = (name: keyof DmarcFormData, value: string | number | undefined) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleBlur = (name: keyof DmarcFormData) => {
    setTouched(prev => ({ ...prev, [name]: true }));
    const error = validateField(name, formData[name]);
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const newErrors: DmarcFormErrors = {};
    Object.keys(formData).forEach(key => {
      const fieldName = key as keyof DmarcFormData;
      const error = validateField(fieldName, formData[fieldName]);
      if (error) newErrors[fieldName] = error;
    });

    setErrors(newErrors);
    setTouched({
      domain: true,
      policy: true,
      rua: true,
      pct: true,
    });

    // If no errors, submit the form
    if (Object.keys(newErrors).length === 0) {
      onSubmit(formData);
    }
  };

  return (
    <div className="bg-white/80 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-8 shadow-sm">
      <div className="flex items-center space-x-4 mb-6">
        <div className="w-12 h-12 bg-gradient-to-br from-[#956b50] to-[#4a3728] rounded-xl flex items-center justify-center shadow-lg flex-shrink-0" style={{ aspectRatio: '1' }}>
          <svg className="w-6 h-6 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        </div>
        <div>
          <h2 className="text-2xl font-bold text-[#1b130e]">
            Generate DMARC Policy
          </h2>
          <p className="text-[#4a3728] mt-1">
            Create comprehensive DMARC policy records for email authentication and reporting
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-5">
        <div className="space-y-3">
          {/* Domain */}
          <div>
            <label className="block text-sm font-semibold text-[#1b130e] mb-2">
              Domain *
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="example.com"
                value={formData.domain}
                onChange={(e) => handleInputChange('domain', e.target.value)}
                onBlur={() => handleBlur('domain')}
                className={`w-full px-4 py-2.5 border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${
                  errors.domain && touched.domain
                    ? 'border-red-500 focus:ring-red-500 bg-red-50'
                    : 'border-[#4a3728]/20 focus:ring-[#956b50] focus:border-[#956b50] bg-white/60'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
                disabled={loading}
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-4">
                <svg className="w-5 h-5 text-[#4a3728]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                </svg>
              </div>
            </div>
            {errors.domain && touched.domain && (
              <div className="flex items-center mt-2 text-red-600">
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <p className="text-sm">{errors.domain}</p>
              </div>
            )}
            <p className="text-xs text-[#4a3728] mt-2 flex items-center">
              <svg className="w-4 h-4 mr-1 text-[#956b50]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              The domain you want to configure DMARC for
            </p>
          </div>

          {/* Policy Selector */}
          <div>
            <PolicySelector
              value={formData.policy}
              onChange={(value) => handleInputChange('policy', value)}
              disabled={loading}
            />
            {errors.policy && touched.policy && (
              <p className="text-red-600 text-sm mt-1">{errors.policy}</p>
            )}
          </div>
        </div>

        {/* Reporting Email */}
        <div>
          <label className="block text-sm font-medium text-[#1b130e] mb-2">
            Reporting Email Address (RUA) *
          </label>
          <input
            type="email"
            placeholder="<EMAIL>"
            value={formData.rua}
            onChange={(e) => handleInputChange('rua', e.target.value)}
            onBlur={() => handleBlur('rua')}
            className={`w-full px-4 py-2.5 border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${
              errors.rua && touched.rua
                ? 'border-red-500 focus:ring-red-500 bg-red-50'
                : 'border-[#4a3728]/20 focus:ring-[#956b50] focus:border-[#956b50] bg-white/60'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
            disabled={loading}
          />
          {errors.rua && touched.rua && (
            <p className="text-red-600 text-sm mt-1">{errors.rua}</p>
          )}
          <p className="text-xs text-[#4a3728] mt-1">
            Email address where DMARC reports will be sent
          </p>
        </div>

        {/* Policy Percentage */}
        <div>
          <label className="block text-sm font-medium text-[#1b130e] mb-2">
            Policy Percentage
          </label>
          <div className="flex items-center space-x-3">
            <input
              type="range"
              min="0"
              max="100"
              step="5"
              value={formData.pct}
              onChange={(e) => handleInputChange('pct', parseInt(e.target.value))}
              className="flex-1"
              disabled={loading}
            />
            <div className="flex items-center space-x-2">
              <input
                type="number"
                min="0"
                max="100"
                value={formData.pct}
                onChange={(e) => handleInputChange('pct', parseInt(e.target.value) || 0)}
                onBlur={() => handleBlur('pct')}
                className={`w-16 px-2 py-1 border rounded text-center text-sm ${
                  errors.pct && touched.pct
                    ? 'border-red-500'
                    : 'border-[#4a3728]'
                }`}
                disabled={loading}
              />
              <span className="text-sm text-[#1b130e]">%</span>
            </div>
          </div>
          {errors.pct && touched.pct && (
            <p className="text-red-600 text-sm mt-1">{errors.pct}</p>
          )}
          <p className="text-xs text-[#4a3728] mt-1">
            Percentage of emails to apply the policy to (start with 100% for monitoring)
          </p>
        </div>

        {/* Advanced Options Toggle */}
        <div>
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center text-[#956b50] hover:text-[#4a3728] transition-all duration-200 font-medium"
            disabled={loading}
          >
            <span className="mr-2">Advanced Options</span>
            <svg
              className={`h-4 w-4 transition-transform duration-200 ${showAdvanced ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>

        {/* Advanced Options */}
        {showAdvanced && (
          <div className="space-y-3 bg-white/60 backdrop-blur-sm border border-[#4a3728]/20 rounded-2xl p-4 shadow-sm transition-all duration-300 hover:bg-white/80 hover:shadow-md">
            <h3 className="font-medium text-[#1b130e] mb-2">Advanced Configuration</h3>

            {/* Subdomain Policy */}
            <div>
              <label className="block text-sm font-medium text-[#1b130e] mb-2">
                Subdomain Policy (sp)
              </label>
              <select
                value={formData.sp || ''}
                onChange={(e) => handleInputChange('sp', e.target.value || undefined)}
                className="w-full px-3 py-2 border border-[#4a3728] rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077]"
                disabled={loading}
              >
                <option value="">Same as main policy</option>
                <option value="none">None</option>
                <option value="quarantine">Quarantine</option>
                <option value="reject">Reject</option>
              </select>
              <p className="text-xs text-[#4a3728] mt-1">
                Policy for subdomains (leave empty to use main policy)
              </p>
            </div>

            {/* DKIM Alignment */}
            <div>
              <label className="block text-sm font-medium text-[#1b130e] mb-2">
                DKIM Alignment (adkim)
              </label>
              <select
                value={formData.adkim || 'r'}
                onChange={(e) => handleInputChange('adkim', e.target.value as DmarcAlignment)}
                className="w-full px-3 py-2 border border-[#4a3728] rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077]"
                disabled={loading}
              >
                <option value="r">Relaxed (r) - Recommended</option>
                <option value="s">Strict (s)</option>
              </select>
              <p className="text-xs text-[#4a3728] mt-1">
                How strictly DKIM signatures are validated
              </p>
            </div>

            {/* SPF Alignment */}
            <div>
              <label className="block text-sm font-medium text-[#1b130e] mb-2">
                SPF Alignment (aspf)
              </label>
              <select
                value={formData.aspf || 'r'}
                onChange={(e) => handleInputChange('aspf', e.target.value as DmarcAlignment)}
                className="w-full px-3 py-2 border border-[#4a3728] rounded-md focus:outline-none focus:ring-2 focus:ring-[#66b077]"
                disabled={loading}
              >
                <option value="r">Relaxed (r) - Recommended</option>
                <option value="s">Strict (s)</option>
              </select>
              <p className="text-xs text-[#4a3728] mt-1">
                How strictly SPF records are validated
              </p>
            </div>
          </div>
        )}

        {/* Information Box */}
        <div className="bg-gradient-to-r from-[#956b50]/10 to-[#4a3728]/10 border border-[#956b50]/30 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-[#956b50]/10 rounded-lg flex items-center justify-center flex-shrink-0" style={{ aspectRatio: '1' }}>
              <svg className="w-5 h-5 text-[#956b50] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div>
              <h4 className="font-semibold text-[#1b130e] mb-1">What happens next?</h4>
              <p className="text-sm text-[#4a3728]">
                We'll generate a comprehensive DMARC policy record, provide DNS setup instructions, and guide you through implementation best practices.
              </p>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-center">
          <button
            type="submit"
            className="border border-[#ce601c] text-[#ce601c] bg-transparent py-3 px-6 rounded-xl hover:bg-[#ce601c] hover:text-white hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center font-semibold text-base"
            disabled={loading}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current mr-3"></div>
                Generating DMARC Record...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Generate DMARC Record
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
