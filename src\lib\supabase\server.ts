/**
 * Supabase client for server-side operations
 *
 * This module provides a Supabase client for server-side operations
 * using the active database configuration.
 */

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { getSupabaseClient } from '@/lib/db/connectionManager';
import { Database } from '@/lib/database.types';

// Singleton instance for server client
let serverClientInstance: ReturnType<typeof createServerClient<Database>> | null = null;

/**
 * Create a Supabase client for use in server components and server actions
 * Uses a singleton pattern to prevent multiple instances in the same request context
 */
export async function createServerSupabaseClient() {
  try {
    // For server components, we can use the connection manager
    // which will use the active configuration from the database
    const client = await getSupabaseClient();
    return client;
  } catch (error) {
    console.error('Error creating server Supabase client:', error);

    // Fall back to environment variables if connection manager fails
    if (!serverClientInstance) {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
      const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

      serverClientInstance = createServerClient<Database>(
        supabaseUrl,
        supabaseAnonKey,
        {
          cookies: {
            async get(name: string) {
              const cookieStore = await cookies();
              return cookieStore.get(name)?.value;
            },
            async set(name: string, value: string, options) {
              // We don't need to set cookies here as we're handling this in the route handlers
              console.log(`Setting cookie ${name} (this is handled in route handlers)`);
            },
            async remove(name: string, options) {
              // We don't need to remove cookies here as we're handling this in the route handlers
              console.log(`Removing cookie ${name} (this is handled in route handlers)`);
            },
          },
          global: {
            headers: {
              apikey: supabaseAnonKey,
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          }
        }
      );
    }

    return serverClientInstance;
  }
}
