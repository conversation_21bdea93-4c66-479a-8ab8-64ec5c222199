'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface PageHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  icon?: React.ReactNode;
}

/**
 * PageHeader Component
 *
 * A consistent header for admin pages with title, description, icon, and optional action buttons
 */
const PageHeader = React.forwardRef<HTMLDivElement, PageHeaderProps>(
  ({ className, title, description, actions, icon, ...props }, ref) => {
    return (
      <div
        className={cn("flex flex-col md:flex-row md:justify-between mb-6", className)}
        ref={ref}
        {...props}
      >
        <div className="flex items-center">
          {icon && (
            <div className="mr-3 text-indigo-600 flex-shrink-0">
              {icon}
            </div>
          )}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
            {description && (
              <p className="mt-1 text-sm text-gray-600">{description}</p>
            )}
          </div>
        </div>
        {actions && (
          <div className="mt-4 md:mt-0 flex justify-start md:justify-end items-center">{actions}</div>
        )}
      </div>
    );
  }
);

PageHeader.displayName = "PageHeader";

export { PageHeader };
