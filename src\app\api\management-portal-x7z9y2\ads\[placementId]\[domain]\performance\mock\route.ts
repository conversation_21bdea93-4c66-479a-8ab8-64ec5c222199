/**
 * Mock API route for ad performance metrics
 * This is used for testing the ad performance dashboard
 */
import { NextRequest, NextResponse } from 'next/server';
import { format, subDays, parseISO } from 'date-fns';
import { logInfo } from '@/lib/logging';

/**
 * GET /api/management-portal-x7z9y2/ads/[placementId]/[domain]/performance/mock
 *
 * Get mock performance metrics for a specific ad placement
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ placementId: string; domain: string }> }
) {
  try {
    const { placementId, domain } = await params;

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') || format(subDays(new Date(), 7), 'yyyy-MM-dd');
    const endDate = searchParams.get('endDate') || format(new Date(), 'yyyy-MM-dd');

    // Generate mock metrics
    const metrics = generateMockMetrics(startDate, endDate);

    logInfo('api', 'Retrieved mock ad performance metrics', { placementId, domain, startDate, endDate });

    return NextResponse.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Failed to get mock ad performance metrics' },
      { status: 500 }
    );
  }
}

/**
 * Generate mock metrics for the given date range
 *
 * @param startDate Start date (YYYY-MM-DD)
 * @param endDate End date (YYYY-MM-DD)
 * @returns Array of mock metrics
 */
function generateMockMetrics(startDate: string, endDate: string): any[] {
  const start = parseISO(startDate);
  const end = parseISO(endDate);
  const metrics = [];

  let currentDate = start;

  // Base values for the metrics
  let baseImpressions = Math.floor(Math.random() * 500) + 100;
  let baseClicks = Math.floor(Math.random() * 50) + 5;

  // Generate metrics for each day in the range
  while (currentDate <= end) {
    // Add some randomness to the metrics
    const randomFactor = Math.random() * 0.5 + 0.75; // 0.75 to 1.25
    const impressions = Math.floor(baseImpressions * randomFactor);
    const clicks = Math.floor(baseClicks * randomFactor);
    const ctr = (clicks / impressions) * 100;

    // Add some revenue for some days
    const hasRevenue = Math.random() > 0.3;
    const revenue = hasRevenue ? parseFloat((clicks * (Math.random() * 0.5 + 0.1)).toFixed(2)) : undefined;

    metrics.push({
      date: format(currentDate, 'yyyy-MM-dd'),
      impressions,
      clicks,
      ctr,
      revenue
    });

    // Move to the next day
    currentDate = subDays(currentDate, -1);

    // Slightly adjust the base values for the next day
    baseImpressions = Math.max(50, baseImpressions + Math.floor(Math.random() * 21) - 10);
    baseClicks = Math.max(3, baseClicks + Math.floor(Math.random() * 5) - 2);
  }

  return metrics;
}
