/**
 * <PERSON><PERSON><PERSON> to set up the Backend Management System tables in Supabase
 *
 * This script creates the necessary tables for configuration, domain management, and logging
 */
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
// Using the anon key which seems to work with the test script
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

console.log('Using Supabase URL:', supabaseUrl);

// Create a Supabase client with the anon key
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Execute SQL statements from a file
 */
async function executeSqlFile(filePath) {
  try {
    console.log(`Reading SQL file: ${filePath}`);
    const sqlContent = fs.readFileSync(filePath, 'utf8');

    // Split the SQL content into individual statements
    const statements = sqlContent
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);

    console.log(`Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}/${statements.length}...`);

      try {
        // Try using the REST API directly since execute_sql function might not be available
        const { error } = await supabase.rpc('execute_sql', { query: statement + ';' });

        if (error) {
          console.warn(`Warning: Could not execute statement using execute_sql RPC`);
          console.warn(`Error: ${error.message}`);
          console.warn(`Trying alternative method...`);

          // For table creation and other DDL statements, we need to use the SQL API
          // This is a workaround and may not work for all statements
          if (statement.toLowerCase().includes('create table') ||
              statement.toLowerCase().includes('create index') ||
              statement.toLowerCase().includes('insert into')) {

            // For CREATE TABLE statements, we can check if the table exists
            const tableName = statement.match(/create\s+table\s+if\s+not\s+exists\s+(\w+)/i)?.[1];
            if (tableName) {
              const { data, error: checkError } = await supabase.from(tableName).select('*').limit(1);
              if (checkError && checkError.code === '42P01') {
                console.log(`Table ${tableName} does not exist yet, which is expected`);
              } else {
                console.log(`Table ${tableName} might already exist or was created successfully`);
              }
            } else if (statement.toLowerCase().includes('insert into')) {
              // For INSERT statements, we can try to execute them directly
              const tableName = statement.match(/insert\s+into\s+(\w+)/i)?.[1];
              if (tableName) {
                console.log(`Attempting direct insert into ${tableName}...`);
                // We can't easily execute the insert directly, but we can check if the table exists
                const { data, error: checkError } = await supabase.from(tableName).select('*').limit(1);
                if (checkError) {
                  console.warn(`Warning: Could not check table ${tableName}: ${checkError.message}`);
                } else {
                  console.log(`Table ${tableName} exists, insert might have succeeded`);
                }
              }
            }
          }
        } else {
          console.log(`Statement executed successfully`);
        }
      } catch (error) {
        console.error(`Error executing statement: ${error.message}`);
        console.error(`Statement: ${statement}`);
      }
    }

    console.log('SQL file execution completed');
    return true;
  } catch (error) {
    console.error(`Error executing SQL file: ${error.message}`);
    return false;
  }
}

/**
 * Main function to set up the backend management system
 */
async function setupBackendManagement() {
  try {
    console.log('Setting up Backend Management System in Supabase...');

    // Execute the SQL file
    const sqlFilePath = path.join(__dirname, 'setup-backend-management.sql');
    const success = await executeSqlFile(sqlFilePath);

    if (success) {
      console.log('Backend Management System setup completed successfully');
    } else {
      console.error('Backend Management System setup failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error setting up Backend Management System:', error);
    process.exit(1);
  }
}

// Run the setup function
setupBackendManagement();
