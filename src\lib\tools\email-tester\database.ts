/**
 * Database utilities for the Email Tester Tool
 *
 * This module provides functions for interacting with the database
 * for the Email Tester Tool.
 */

import { createServerSupabaseClient } from '@/lib/supabase';
import { getGuerrillaDbConnection } from '@/lib/db';
import { logError } from '@/lib/logging';
import { generateShortId } from '@/lib/utils/uuid';

/**
 * Interface for an email tester address
 */
export interface EmailTesterAddress {
  id: string;
  testAddress: string;
  createdAt: Date;
  expirationDate: Date;
  status: 'pending' | 'completed' | 'expired';
}

/**
 * Interface for email tester results
 */
export interface EmailTesterResult {
  id: string;
  testAddressId: string;
  emailId: string;
  senderDomain: string;
  senderIp: string;
  spfResult: string;
  dkimResult: string;
  dmarcResult: string;
  reverseDns: string;
  spamScore: number;
  overallScore: number;
  rawHeaders: string;
  analysisJson: any;
  createdAt: Date;
}

/**
 * Interface for an email tester recommendation
 */
export interface EmailTesterRecommendation {
  id: string;
  testResultId: string;
  category: string;
  issueDescription: string;
  recommendation: string;
  dnsRecordTemplate?: string;
  implementationSteps?: string;
  priority: number;
}

/**
 * Generate a unique test email address for email testing
 * @param expirationHours Number of hours until the test address expires
 * @returns The generated test address information
 */
export async function generateEmailTesterAddress(
  expirationHours = 24
): Promise<EmailTesterAddress> {
  const supabase = createServerSupabaseClient();
  const uniqueId = generateShortId();
  const emailDomain = process.env.EMAIL_TESTER_DOMAIN || 'fademail.site';
  const testAddress = `test-${uniqueId}@${emailDomain}`;

  // Calculate expiration date
  const expirationDate = new Date();
  expirationDate.setHours(expirationDate.getHours() + expirationHours);

  try {
    // Insert the test address into Supabase
    // Note: Using type assertion since the table is not in the generated types yet
    const { data, error } = await (supabase as any)
      .from('email_tester_addresses')
      .insert({
        test_address: testAddress,
        expires_at: expirationDate.toISOString(),
        status: 'pending'
      })
      .select('id, test_address, created_at, expires_at, status')
      .single();

    if (error) throw error;

    return {
      id: data.id,
      testAddress: data.test_address,
      createdAt: new Date(data.created_at),
      expirationDate: new Date(data.expires_at),
      status: data.status
    };
  } catch (error) {
    logError('DB', 'Failed to generate deliverability test address', error);
    throw new Error('Failed to generate test email address');
  }
}

/**
 * Enhanced interface for email data from Guerrilla database
 */
export interface GuerrillaEmailData {
  mail_id: string;
  mail: string; // Raw email content
  ip_addr: Buffer; // Binary IP address
  return_path: string;
  recipient: string;
  spam_score: number;
  date: Date;
  // Converted/processed fields
  clientIp?: string;
}

/**
 * Get email data from Guerrilla database for a specific test address (enhanced)
 * @param testAddress The test email address
 * @returns The email data from Guerrilla with processed IP address
 */
export async function getEmailTesterEmail(testAddress: string): Promise<GuerrillaEmailData | null> {
  try {
    console.log('Getting Guerrilla DB connection for test address:', testAddress);
    const connection = await getGuerrillaDbConnection();

    console.log('Connection obtained, executing query...');
    // Query the Guerrilla database for emails sent to this test address
    // Select specific fields we need for analysis
    const [rows] = await connection.execute(
      'SELECT mail_id, mail, ip_addr, return_path, recipient, spam_score, date FROM guerrilla_mail WHERE recipient = ? ORDER BY date DESC LIMIT 1',
      [testAddress]
    );

    console.log('Query executed, releasing connection...');
    connection.release();

    console.log('Query results:', Array.isArray(rows) ? `Found ${rows.length} rows` : 'No rows found');
    if (!Array.isArray(rows) || rows.length === 0) {
      return null;
    }

    const emailData = rows[0] as any;
    console.log('Processing email data with mail_id:', emailData.mail_id);

    // Extract sender IP directly from email headers (optimized approach)
    let clientIp = '';
    console.log('Extracting IP address from email headers...');

    try {
      // Multiple regex patterns to handle different Received header formats
      const ipPatterns = [
        // Pattern 1: Received: from ************** ([**************])
        /Received: from ([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}) \(\[([^\]]+)\]\)/,
        // Pattern 2: Received: from hostname ([IP])
        /Received: from [^\s]+ \(\[([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})\]\)/,
        // Pattern 3: Received: from hostname (hostname [IP])
        /Received: from [^\s]+ \([^\s]+ \[([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})\]\)/,
        // Pattern 4: More general pattern for any IP in brackets
        /\[([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})\]/
      ];

      for (const pattern of ipPatterns) {
        const match = emailData.mail.match(pattern);
        if (match) {
          // For patterns with multiple capture groups, use the last one (the IP in brackets)
          clientIp = match[match.length - 1];
          console.log(`Extracted IP using pattern ${ipPatterns.indexOf(pattern) + 1}:`, clientIp);
          break;
        }
      }

      // Validate the extracted IP
      if (clientIp && /^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$/.test(clientIp)) {
        console.log('Successfully extracted valid IP address:', clientIp);
      } else {
        console.warn('No valid IP address found in email headers');
        clientIp = '';
      }

    } catch (ipError) {
      console.warn('Failed to extract IP from email headers:', ipError);
      logError('DB', 'Failed to extract IP from email headers', ipError);
      clientIp = '';
    }

    // Verify email content integrity for DKIM analysis
    console.log('=== EMAIL CONTENT INTEGRITY CHECK ===');
    console.log('Raw email length:', emailData.mail.length);
    console.log('Email starts with:', emailData.mail.substring(0, 100));
    console.log('Contains DKIM-Signature:', emailData.mail.includes('DKIM-Signature'));

    // Check for potential content corruption
    const hasProperHeaders = emailData.mail.includes('Received:') && emailData.mail.includes('From:');
    console.log('Has proper email headers:', hasProperHeaders);

    if (!hasProperHeaders) {
      console.warn('⚠️ Email content may be corrupted - missing standard headers');
    }

    const result: GuerrillaEmailData = {
      mail_id: emailData.mail_id,
      mail: emailData.mail, // Unmodified raw email content
      ip_addr: emailData.ip_addr,
      return_path: emailData.return_path,
      recipient: emailData.recipient,
      spam_score: emailData.spam_score || 0,
      date: emailData.date,
      clientIp: clientIp // Only the extracted IP is added, mail content unchanged
    };

    console.log('Returning processed email data with integrity preserved');
    return result;
  } catch (error) {
    console.error('Error getting deliverability test email:', error);
    logError('DB', 'Failed to retrieve test email data', error);
    throw new Error(`Failed to retrieve test email data: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Store email tester results in Supabase
 * @param testAddressId The ID of the test address
 * @param emailId The ID of the email in Guerrilla
 * @param analysisData The analysis data
 * @param sessionId The session ID for access control
 * @returns The ID of the stored test result
 */
export async function storeEmailTesterResults(
  testAddressId: string,
  emailId: string,
  senderDomain: string,
  senderIp: string,
  spfResult: string,
  dkimResult: string,
  dmarcResult: string,
  reverseDns: string,
  spamScore: number,
  overallScore: number,
  rawHeaders: string,
  analysisJson: any,
  sessionId: string
): Promise<string> {
  const supabase = createServerSupabaseClient();

  try {
    console.log('Starting to store deliverability test results...');
    console.log('Test Address ID:', testAddressId);
    console.log('Email ID:', emailId);
    console.log('Sender Domain:', senderDomain);
    console.log('Sender IP:', senderIp);

    // Verify test address exists before attempting to store results
    console.log('Verifying test address exists...');
    const { data: testAddress, error: testAddressError } = await (supabase as any)
      .from('email_tester_addresses')
      .select('id, status')
      .eq('id', testAddressId)
      .single();

    if (testAddressError) {
      console.error('Test address verification failed:', testAddressError);
      throw new Error(`Test address not found: ${testAddressError.message}`);
    }

    if (!testAddress) {
      throw new Error(`Test address with ID ${testAddressId} does not exist`);
    }

    console.log('Test address verified:', testAddress);

    // Update the test address status
    console.log('Updating test address status to completed...');
    const { error: updateError } = await (supabase as any)
      .from('email_tester_addresses')
      .update({ status: 'completed' })
      .eq('id', testAddressId);

    if (updateError) {
      console.error('Error updating test address status:', updateError);
      throw new Error(`Failed to update test address status: ${updateError.message}`);
    }
    console.log('Test address status updated successfully');

    // Extract simple result strings from complex objects for database fields
    const extractSimpleResult = (result: any, fieldName: string): string => {
      console.log(`=== EXTRACTING SIMPLE RESULT FOR ${fieldName.toUpperCase()} ===`);
      console.log(`Input ${fieldName} result:`, typeof result, result);

      if (typeof result === 'string') {
        const extracted = result.toLowerCase().substring(0, 50);
        console.log(`Extracted string ${fieldName}:`, extracted);
        return extracted;
      }

      if (typeof result === 'object' && result !== null) {
        console.log(`Object had properties:`, Object.keys(result));

        // Handle different Mailauth result structures
        let simpleResult = 'none';

        // Check for direct result/status properties
        if (result.result) {
          simpleResult = result.result;
          console.log(`Found direct result property: ${simpleResult}`);
        } else if (result.status) {
          // Handle nested status object (like DKIM)
          if (typeof result.status === 'object' && result.status.result) {
            simpleResult = result.status.result;
            console.log(`Found nested status.result: ${simpleResult}`);
          } else if (typeof result.status === 'string') {
            simpleResult = result.status;
            console.log(`Found status string: ${simpleResult}`);
          }
        }

        const extracted = simpleResult.toLowerCase().substring(0, 50);
        console.log(`Final extracted ${fieldName}:`, extracted);
        return extracted;
      }

      console.log(`Defaulting ${fieldName} to 'none'`);
      return 'none';
    };

    // Ensure simple string results for database constraints
    const simpleSpfResult = extractSimpleResult(spfResult, 'SPF');
    const simpleDkimResult = extractSimpleResult(dkimResult, 'DKIM');
    const simpleDmarcResult = extractSimpleResult(dmarcResult, 'DMARC');
    // MX result can be in analysisJson.mx or analysisJson.enhancedAuthResults.mx
    const mxResult = analysisJson.mx?.result || analysisJson.enhancedAuthResults?.mx?.result || 'none';
    const simpleMxResult = extractSimpleResult(mxResult, 'MX');

    // Ensure other string fields don't exceed database limits
    const safeSenderDomain = senderDomain ? senderDomain.substring(0, 255) : '';
    const safeSenderIp = senderIp ? senderIp.substring(0, 45) : ''; // IPv6 max length
    const safeReverseDns = reverseDns ? reverseDns.substring(0, 255) : '';

    console.log('Extracted simple results:', {
      spf: simpleSpfResult,
      dkim: simpleDkimResult,
      dmarc: simpleDmarcResult,
      mx: simpleMxResult,
      senderDomain: safeSenderDomain,
      senderIp: safeSenderIp,
      reverseDns: safeReverseDns
    });

    // Validate data types before insertion
    const validateAndSanitize = (value: any, fieldName: string, maxLength?: number): any => {
      if (value === null || value === undefined) {
        console.log(`Field ${fieldName} is null/undefined, setting to null`);
        return null;
      }

      if (typeof value === 'string') {
        const sanitized = maxLength ? value.substring(0, maxLength) : value;
        console.log(`Field ${fieldName} validated as string (length: ${sanitized.length})`);
        return sanitized;
      }

      if (typeof value === 'number') {
        console.log(`Field ${fieldName} validated as number: ${value}`);
        return value;
      }

      if (typeof value === 'object') {
        // Validate that the object can be serialized to JSON
        try {
          const jsonString = JSON.stringify(value);
          const parsed = JSON.parse(jsonString);
          console.log(`Field ${fieldName} validated as serializable object (size: ${jsonString.length} chars)`);
          return parsed;
        } catch (jsonError) {
          console.error(`Field ${fieldName} contains non-serializable object:`, jsonError);
          console.log(`Converting ${fieldName} to string representation`);
          return String(value);
        }
      }

      console.log(`Field ${fieldName} converted to string: ${String(value)}`);
      return String(value);
    };

    // Prepare the data for insertion with validation
    const insertData = {
      test_address_id: validateAndSanitize(testAddressId, 'test_address_id'),
      email_id: validateAndSanitize(emailId, 'email_id', 255),
      sender_domain: validateAndSanitize(safeSenderDomain, 'sender_domain', 255),
      sender_ip: validateAndSanitize(safeSenderIp, 'sender_ip', 45),
      spf_result: validateAndSanitize(simpleSpfResult, 'spf_result', 50),
      dkim_result: validateAndSanitize(simpleDkimResult, 'dkim_result', 50),
      dmarc_result: validateAndSanitize(simpleDmarcResult, 'dmarc_result', 50),
      mx_result: validateAndSanitize(simpleMxResult, 'mx_result', 50),
      reverse_dns: validateAndSanitize(safeReverseDns, 'reverse_dns', 255),
      spam_score: validateAndSanitize(spamScore, 'spam_score'),
      overall_score: validateAndSanitize(overallScore, 'overall_score'),
      raw_headers: validateAndSanitize(rawHeaders, 'raw_headers'),
      analysis_json: validateAndSanitize(analysisJson, 'analysis_json'),
      session_id: validateAndSanitize(sessionId, 'session_id', 255)
    };

    console.log('Inserting test results with data:', {
      ...insertData,
      raw_headers: '[TRUNCATED]',
      analysis_json: '[OBJECT]'
    });

    // Store the test results
    const { data, error } = await (supabase as any)
      .from('email_tester_results')
      .insert(insertData)
      .select('id')
      .single();

    if (error) {
      console.error('=== DATABASE INSERT ERROR ===');
      console.error('Error details:', error);
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);
      console.error('Error hint:', error.hint);
      console.error('Insert data structure:', {
        ...insertData,
        raw_headers: '[TRUNCATED]',
        analysis_json: typeof insertData.analysis_json
      });
      throw new Error(`Failed to insert test results: ${error.message} (Code: ${error.code})`);
    }

    console.log('Test results inserted successfully with ID:', data.id);

    // Store the recommendations
    if (analysisJson.recommendations && Array.isArray(analysisJson.recommendations)) {
      console.log(`Storing ${analysisJson.recommendations.length} recommendations...`);

      for (let i = 0; i < analysisJson.recommendations.length; i++) {
        const rec = analysisJson.recommendations[i];
        console.log(`Storing recommendation ${i + 1}:`, {
          category: rec.category,
          issue: rec.issue?.substring(0, 50) + '...',
          priority: rec.priority
        });

        const { error: recError } = await (supabase as any)
          .from('email_tester_recommendations')
          .insert({
            test_result_id: data.id,
            category: rec.category,
            issue_description: rec.issue,
            recommendation: rec.recommendation,
            dns_record_template: rec.dnsTemplate || null,
            implementation_steps: rec.implementationSteps || null,
            priority: rec.priority || 1
          });

        if (recError) {
          console.error(`Error storing recommendation ${i + 1}:`, recError);
          throw new Error(`Failed to store recommendation: ${recError.message}`);
        }
      }
      console.log('All recommendations stored successfully');
    } else {
      console.log('No recommendations to store');
    }

    console.log('Storage completed successfully, returning result ID:', data.id);
    return data.id;
  } catch (error) {
    logError('DB', 'Failed to store deliverability test results', error);
    throw new Error('Failed to store test results');
  }
}

/**
 * Get test history
 * @param limit Maximum number of results to return
 * @param sessionId Optional session ID to filter results by user session
 * @returns Array of test history items
 */
export async function getEmailTesterHistory(limit = 20, sessionId?: string) {
  const supabase = createServerSupabaseClient();

  try {
    // Note: Using type assertion since the table is not in the generated types yet
    let query = (supabase as any)
      .from('email_tester_results')
      .select(`
        id,
        created_at,
        sender_domain,
        overall_score,
        spf_result,
        dkim_result,
        dmarc_result,
        test_address_id,
        session_id,
        email_tester_addresses (
          test_address
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Filter by session ID if provided
    if (sessionId) {
      query = query.eq('session_id', sessionId);
    }

    const { data, error } = await query;

    if (error) throw error;

    return data;
  } catch (error) {
    logError('DB', 'Failed to retrieve deliverability test history', error);
    throw new Error('Failed to retrieve test history');
  }
}

/**
 * Get test result by ID
 * @param testId The test result ID
 * @param sessionId Optional session ID for access control
 * @param shareToken Optional share token for public access
 * @returns The test result and recommendations
 */
export async function getEmailTesterResult(testId: string, sessionId?: string, shareToken?: string) {
  const supabase = createServerSupabaseClient();

  try {
    // Get the test result
    // Note: Using type assertion since the table is not in the generated types yet
    let query = (supabase as any)
      .from('email_tester_results')
      .select(`
        *,
        email_tester_addresses (
          test_address,
          created_at,
          expires_at
        )
      `)
      .eq('id', testId);

    // Apply session filter if provided (for access control)
    if (sessionId && !shareToken) {
      query = query.eq('session_id', sessionId);
    }

    const { data: result, error: resultError } = await query.single();

    if (resultError) {
      if (resultError.code === 'PGRST116') {
        // No rows returned - either doesn't exist or access denied
        throw new Error('Test result not found or access denied');
      }
      throw resultError;
    }

    // If using share token, validate it
    if (shareToken) {
      const { parseShareToken } = await import('./sessionManager');
      const tokenData = parseShareToken(shareToken);

      if (!tokenData || tokenData.testId !== testId) {
        throw new Error('Invalid or expired share token');
      }
    }

    // Get the recommendations for this test
    // Note: Using type assertion since the table is not in the generated types yet
    const { data: recommendations, error: recError } = await (supabase as any)
      .from('email_tester_recommendations')
      .select('*')
      .eq('test_result_id', testId)
      .order('priority', { ascending: true });

    if (recError) throw recError;

    return { result, recommendations };
  } catch (error) {
    logError('DB', 'Failed to retrieve deliverability test result', error);
    throw new Error('Failed to retrieve test result');
  }
}
