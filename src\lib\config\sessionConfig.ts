/**
 * Session Configuration Management System
 *
 * This module provides centralized configuration for session management
 * with backend controls for dynamic adjustment of session settings.
 * Configuration is persisted to the database for consistency.
 */

import { createClient } from '@supabase/supabase-js';

// Create a simple Supabase client for configuration management
function getConfigSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');
  }

  if (!supabaseKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is required');
  }

  return createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

export interface SessionConfig {
  // Session Duration Settings
  sessionDuration: number; // in milliseconds
  sessionExtensionDuration: number; // in milliseconds
  activityUpdateInterval: number; // in milliseconds
  
  // Storage Settings
  storageKeys: {
    sessionId: string;
    sessionMetadata: string;
  };
  
  // Security Settings
  enableSessionPersistence: boolean;
  enableCrossBrowserSessions: boolean;
  enableSessionExtension: boolean;
  maxSessionExtensions: number;
  
  // Cleanup Settings
  enableAutomaticCleanup: boolean;
  cleanupInterval: number; // in milliseconds
  
  // Development Settings
  enableDebugLogging: boolean;
  enableSessionAnalytics: boolean;
}

export interface RateLimitConfig {
  // Email Generation Limits
  emailGeneration: {
    ipLimits: {
      windowMs: number;
      maxRequests: number;
    };
    sessionLimits: {
      windowMs: number;
      maxRequests: number;
      blockDuration: number;
    };
    anonymousSessionLimits?: {
      windowMs: number;
      maxRequests: number;
    };
    strictMode: boolean;
    enabled: boolean;
  };



  // Emergency Settings
  emergencyMode: {
    enabled: boolean;
    ipLimits: {
      windowMs: number;
      maxRequests: number;
    };
    sessionLimits: {
      windowMs: number;
      maxRequests: number;
    };
  };
}

// Default Configuration
const DEFAULT_SESSION_CONFIG: SessionConfig = {
  // Session Duration Settings (24 hours default)
  sessionDuration: 24 * 60 * 60 * 1000, // 24 hours
  sessionExtensionDuration: 24 * 60 * 60 * 1000, // 24 hours
  activityUpdateInterval: 60 * 1000, // 1 minute
  
  // Storage Settings
  storageKeys: {
    sessionId: 'vanishpost_session_id',
    sessionMetadata: 'vanishpost_session_metadata'
  },
  
  // Security Settings
  enableSessionPersistence: true,
  enableCrossBrowserSessions: false, // Each browser gets its own session
  enableSessionExtension: true,
  maxSessionExtensions: 3, // Max 3 extensions (72 hours total)
  
  // Cleanup Settings
  enableAutomaticCleanup: true,
  cleanupInterval: 60 * 60 * 1000, // 1 hour
  
  // Development Settings
  enableDebugLogging: process.env.NODE_ENV === 'development',
  enableSessionAnalytics: true
};

// Default Rate Limiting Configuration
const DEFAULT_RATE_LIMIT_CONFIG: RateLimitConfig = {
  emailGeneration: {
    ipLimits: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 5 // 5 emails per IP per hour
    },
    sessionLimits: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 8, // 8 emails per session per hour
      blockDuration: 60 * 60 * 1000 // 1 hour block
    },

    strictMode: true,
    enabled: true
  },


  
  emergencyMode: {
    enabled: false,
    ipLimits: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 1 // Emergency: 1 email per IP per hour
    },
    sessionLimits: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 1 // Emergency: 1 email per session per hour
    }
  }
};

// Configuration Presets for Different Scenarios
export const SESSION_PRESETS = {
  // Standard production settings
  PRODUCTION: {
    ...DEFAULT_SESSION_CONFIG,
    enableDebugLogging: false,
    sessionDuration: 24 * 60 * 60 * 1000, // 24 hours
    maxSessionExtensions: 2
  },
  
  // Development settings with more logging
  DEVELOPMENT: {
    ...DEFAULT_SESSION_CONFIG,
    enableDebugLogging: true,
    sessionDuration: 2 * 60 * 60 * 1000, // 2 hours for testing
    activityUpdateInterval: 30 * 1000 // 30 seconds
  },
  
  // High security settings
  HIGH_SECURITY: {
    ...DEFAULT_SESSION_CONFIG,
    sessionDuration: 4 * 60 * 60 * 1000, // 4 hours
    enableSessionExtension: false,
    maxSessionExtensions: 0,
    enableCrossBrowserSessions: false
  },
  
  // Relaxed settings for special events
  RELAXED: {
    ...DEFAULT_SESSION_CONFIG,
    sessionDuration: 48 * 60 * 60 * 1000, // 48 hours
    maxSessionExtensions: 5
  }
};

export const RATE_LIMIT_PRESETS = {
  // Standard production rate limits
  PRODUCTION: DEFAULT_RATE_LIMIT_CONFIG,
  
  // Development with higher limits
  DEVELOPMENT: {
    ...DEFAULT_RATE_LIMIT_CONFIG,
    emailGeneration: {
      ...DEFAULT_RATE_LIMIT_CONFIG.emailGeneration,
      ipLimits: { windowMs: 60 * 60 * 1000, maxRequests: 20 },
      sessionLimits: { 
        windowMs: 60 * 60 * 1000, 
        maxRequests: 25,
        blockDuration: 10 * 60 * 1000 // 10 minutes
      }
    }
  },
  
  // High security with strict limits
  HIGH_SECURITY: {
    ...DEFAULT_RATE_LIMIT_CONFIG,
    emailGeneration: {
      ...DEFAULT_RATE_LIMIT_CONFIG.emailGeneration,
      ipLimits: { windowMs: 60 * 60 * 1000, maxRequests: 2 },
      sessionLimits: { 
        windowMs: 60 * 60 * 1000, 
        maxRequests: 3,
        blockDuration: 2 * 60 * 60 * 1000 // 2 hours
      },

    }
  },
  
  // Emergency mode - very restrictive
  EMERGENCY: {
    ...DEFAULT_RATE_LIMIT_CONFIG,
    emergencyMode: {
      enabled: true,
      ipLimits: { windowMs: 60 * 60 * 1000, maxRequests: 1 },
      sessionLimits: { windowMs: 60 * 60 * 1000, maxRequests: 1 }
    },
    emailGeneration: {
      ...DEFAULT_RATE_LIMIT_CONFIG.emailGeneration,
      ipLimits: { windowMs: 60 * 60 * 1000, maxRequests: 1 },
      sessionLimits: { 
        windowMs: 60 * 60 * 1000, 
        maxRequests: 1,
        blockDuration: 4 * 60 * 60 * 1000 // 4 hours
      }
    }
  }
};

// Current active configuration (can be changed at runtime)
let currentSessionConfig: SessionConfig = { ...DEFAULT_SESSION_CONFIG };
let currentRateLimitConfig: RateLimitConfig = { ...DEFAULT_RATE_LIMIT_CONFIG };

/**
 * Get current session configuration from database
 */
export async function getSessionConfig(): Promise<SessionConfig> {
  try {
    const supabase = getConfigSupabaseClient();
    const { data, error } = await supabase
      .from('session_config')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.warn('Failed to load session config from database, using defaults:', error);
      return { ...currentSessionConfig };
    }

    return {
      ...currentSessionConfig,
      sessionDuration: data.session_duration,
      maxSessionExtensions: data.max_session_extensions,
      activityUpdateInterval: data.activity_update_interval,
      sessionExtensionDuration: data.session_extension_duration,
      enableSessionPersistence: data.enable_session_persistence
    };
  } catch (error) {
    console.error('Error loading session config:', error);
    return { ...currentSessionConfig };
  }
}

/**
 * Get current rate limit configuration from database
 */
export async function getRateLimitConfig(): Promise<RateLimitConfig> {
  try {
    const supabase = getConfigSupabaseClient();
    const { data, error } = await supabase
      .from('rate_limit_session_config')
      .select('*');

    if (error) {
      console.warn('Failed to load rate limit config from database, using defaults:', error);
      return { ...currentRateLimitConfig };
    }

    // Convert database records to config format
    const config: RateLimitConfig = {
      emailGeneration: {
        ipLimits: currentRateLimitConfig.emailGeneration.ipLimits, // Keep IP limits from defaults
        sessionLimits: { windowMs: 3600000, maxRequests: 8, blockDuration: 3600000 },
        strictMode: true,
        enabled: true // Default to enabled
      },

      emergencyMode: currentRateLimitConfig.emergencyMode
    };

    // Update with database values
    data.forEach(record => {
      if (record.endpoint === 'emailGeneration') {
        config.emailGeneration.sessionLimits = {
          windowMs: record.session_window_ms,
          maxRequests: record.session_max_requests,
          blockDuration: record.session_block_duration
        };
        config.emailGeneration.strictMode = record.strict_mode;
        config.emailGeneration.enabled = record.enabled;
      }
    });

    return config;
  } catch (error) {
    console.error('Error loading rate limit config:', error);
    return { ...currentRateLimitConfig };
  }
}

/**
 * Update session configuration in database
 */
export async function updateSessionConfig(newConfig: Partial<SessionConfig>): Promise<void> {
  try {
    const supabase = getConfigSupabaseClient();

    // Get current config to merge with updates
    const current = await getSessionConfig();
    const updated = { ...current, ...newConfig };

    // Update or insert session config
    const { error } = await supabase
      .from('session_config')
      .upsert({
        session_duration: updated.sessionDuration,
        max_session_extensions: updated.maxSessionExtensions,
        activity_update_interval: updated.activityUpdateInterval,
        session_extension_duration: updated.sessionExtensionDuration,
        enable_session_persistence: updated.enableSessionPersistence,
        updated_at: new Date().toISOString(),
        updated_by: 'admin'
      });

    if (error) {
      console.error('Failed to update session config in database:', error);
      throw error;
    }

    // Update in-memory cache
    currentSessionConfig = { ...currentSessionConfig, ...newConfig };
    console.log('Session configuration updated in database:', newConfig);
  } catch (error) {
    console.error('Error updating session config:', error);
    throw error;
  }
}

/**
 * Update rate limit configuration in database
 */
export async function updateRateLimitConfig(newConfig: Partial<RateLimitConfig>): Promise<void> {
  try {
    const supabase = getConfigSupabaseClient();

    // Update email generation config if provided
    if (newConfig.emailGeneration) {
      const config = newConfig.emailGeneration;
      const updateData = {
        endpoint: 'emailGeneration',
        session_max_requests: config.sessionLimits?.maxRequests ?? 8,
        session_window_ms: config.sessionLimits?.windowMs ?? 3600000,
        session_block_duration: config.sessionLimits?.blockDuration ?? 3600000,
        anonymous_max_requests: 2, // Fixed value since we removed the UI
        anonymous_window_ms: 3600000, // Fixed value since we removed the UI
        strict_mode: config.strictMode ?? true,
        enabled: config.enabled ?? true, // Use explicit enabled field
        updated_at: new Date().toISOString(),
        updated_by: 'admin'
      };

      const { data, error } = await supabase
        .from('rate_limit_session_config')
        .upsert(updateData, {
          onConflict: 'endpoint',
          ignoreDuplicates: false
        });

      if (error) {
        throw error;
      }
    }



    // Update in-memory cache
    currentRateLimitConfig = { ...currentRateLimitConfig, ...newConfig };
    console.log('Rate limit configuration updated in database:', newConfig);
  } catch (error) {
    console.error('Error updating rate limit config:', error);
    throw error;
  }
}

/**
 * Apply a session preset
 */
export function applySessionPreset(preset: keyof typeof SESSION_PRESETS): void {
  currentSessionConfig = { ...SESSION_PRESETS[preset] };
  console.log(`Applied session preset: ${preset}`);
}

/**
 * Apply a rate limit preset
 */
export function applyRateLimitPreset(preset: keyof typeof RATE_LIMIT_PRESETS): void {
  currentRateLimitConfig = { ...RATE_LIMIT_PRESETS[preset] };
  console.log(`Applied rate limit preset: ${preset}`);
}

/**
 * Reset to default configuration
 */
export function resetToDefaults(): void {
  currentSessionConfig = { ...DEFAULT_SESSION_CONFIG };
  currentRateLimitConfig = { ...DEFAULT_RATE_LIMIT_CONFIG };
  console.log('Configuration reset to defaults');
}

/**
 * Get configuration summary for admin dashboard
 */
export function getConfigurationSummary() {
  return {
    session: {
      duration: `${currentSessionConfig.sessionDuration / (60 * 60 * 1000)} hours`,
      persistence: currentSessionConfig.enableSessionPersistence,
      extensions: `${currentSessionConfig.maxSessionExtensions} max`,
      activityInterval: `${currentSessionConfig.activityUpdateInterval / 1000} seconds`
    },
    rateLimits: {
      emailGeneration: {
        ipLimit: `${currentRateLimitConfig.emailGeneration?.ipLimits?.maxRequests || 0}/hour`,
        sessionLimit: `${currentRateLimitConfig.emailGeneration?.sessionLimits?.maxRequests || 0}/hour`,

        strictMode: currentRateLimitConfig.emailGeneration?.strictMode || false
      },

      emergencyMode: currentRateLimitConfig.emergencyMode?.enabled || false
    }
  };
}

export default {
  getSessionConfig,
  getRateLimitConfig,
  updateSessionConfig,
  updateRateLimitConfig,
  applySessionPreset,
  applyRateLimitPreset,
  resetToDefaults,
  getConfigurationSummary,
  SESSION_PRESETS,
  RATE_LIMIT_PRESETS
};
