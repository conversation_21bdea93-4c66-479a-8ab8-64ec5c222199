import { NextRequest, NextResponse } from 'next/server';
import { generateDkimKeyPair, validateKeyPair } from '@/lib/tools/dkim-generator/keyGeneration';
import { formatDkimRecord, generateDkimInstructions, validateDkimInputs } from '@/lib/tools/dkim-generator/recordFormatting';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { DkimGenerateRequest, DkimGenerateResponse } from '@/types/dkim';
import { logRecordGeneration, TOOL_NAMES } from '@/lib/tools/shared/auditLogging';
import crypto from 'crypto';

// Rate limiting - simple in-memory store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = 10; // requests per hour
const RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour in milliseconds

function checkRateLimit(identifier: string): boolean {
  const now = Date.now();
  const userLimit = rateLimitStore.get(identifier);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitStore.set(identifier, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT) {
    return false;
  }

  userLimit.count++;
  return true;
}

function encryptPrivateKey(privateKey: string): string {
  // Simple encryption for demo - in production, use proper key management
  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'default-key', 'salt', 32);
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(algorithm, key, iv);

  let encrypted = cipher.update(privateKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  return `${iv.toString('hex')}:${encrypted}`;
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body: DkimGenerateRequest = await request.json();
    const { domain, selector, keyStrength, sessionId } = body;

    // Validate inputs
    if (!domain || !selector || !keyStrength) {
      return NextResponse.json(
        {
          success: false,
          message: 'Missing required fields: domain, selector, and keyStrength are required',
        },
        { status: 400 }
      );
    }

    // Validate input format
    const validation = validateDkimInputs(domain, selector, keyStrength);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid input format',
          error: validation.errors.join(', '),
        },
        { status: 400 }
      );
    }

    // Rate limiting
    const clientIp = request.headers.get('x-forwarded-for') || 'unknown';
    const rateLimitKey = sessionId || clientIp;

    if (!checkRateLimit(rateLimitKey)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Rate limit exceeded. Please try again later.',
        },
        { status: 429 }
      );
    }

    // Generate DKIM key pair
    console.log(`Generating DKIM keys for ${domain} with selector ${selector} (${keyStrength} bits)`);
    const keyPair = await generateDkimKeyPair(keyStrength);

    // Validate generated key pair
    if (!validateKeyPair(keyPair)) {
      throw new Error('Generated key pair validation failed');
    }

    // Format DNS record
    const dkimRecord = formatDkimRecord(domain, selector, keyPair.publicKey, keyStrength);
    const instructions = generateDkimInstructions(dkimRecord);

    // Encrypt private key for storage
    const encryptedPrivateKey = encryptPrivateKey(keyPair.privateKey);

    // Store in database
    const supabase = await createServerSupabaseClient();
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

    const { data: dbRecord, error: dbError } = await supabase
      .from('generated_records')
      .insert({
        session_id: sessionId || crypto.randomUUID(),
        record_type: 'dkim',
        domain,
        selector,
        dns_record: dkimRecord.dnsRecord,
        record_name: dkimRecord.recordName,
        private_key_encrypted: encryptedPrivateKey,
        metadata: {
          keyStrength,
          publicKey: keyPair.publicKey,
          instructions,
        } as any,
        expires_at: expiresAt.toISOString(),
        validation_status: 'pending',
      })
      .select()
      .single();

    if (dbError) {
      console.error('Database error:', dbError);
      throw new Error('Failed to save DKIM record to database');
    }

    // Log audit event
    const userAgent = request.headers.get('user-agent') || 'unknown';

    await logRecordGeneration(
      TOOL_NAMES.DKIM_GENERATOR,
      'dkim',
      domain,
      sessionId,
      undefined, // userId - not implemented yet
      {
        selector,
        keyStrength,
        recordId: dbRecord.id,
      }
    );

    // Prepare response
    const response: DkimGenerateResponse = {
      success: true,
      data: {
        privateKey: keyPair.privateKey,
        publicKey: keyPair.publicKey,
        dnsRecord: dkimRecord.dnsRecord,
        recordName: dkimRecord.recordName,
        selector,
        domain,
        keyStrength,
        instructions,
        generatedAt: keyPair.generatedAt,
        expiresAt: expiresAt.toISOString(),
      },
    };

    console.log(`DKIM keys generated successfully for ${domain}`);
    return NextResponse.json(response);

  } catch (error) {
    console.error('DKIM Generator API Error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Failed to generate DKIM keys',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    {
      success: false,
      message: 'Method not allowed. Use POST to generate DKIM records.',
    },
    { status: 405 }
  );
}
