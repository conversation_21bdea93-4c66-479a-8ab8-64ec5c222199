'use server';

import { createServerSupabaseClient } from '@/lib/supabase/server';
import { DatabaseConfig, GuerrillaDbConfig, SupabaseConfig } from '@/lib/types/databaseConfig';
import { revalidatePath } from 'next/cache';

/**
 * Get all database configurations
 */
export async function getAllDatabaseConfigurations(): Promise<DatabaseConfig[]> {
  try {
    const supabase = await createServerSupabaseClient();

    const { data, error } = await supabase
      .from('database_configurations')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching database configurations:', error);
      return [];
    }

    return data.map(mapDatabaseConfigFromDb);
  } catch (error) {
    console.error('Error in getAllDatabaseConfigurations:', error);
    return [];
  }
}

/**
 * Get the active database configuration
 */
export async function getActiveConfiguration(): Promise<DatabaseConfig | null> {
  try {
    const supabase = await createServerSupabaseClient();

    const { data, error } = await supabase
      .from('database_configurations')
      .select('*')
      .eq('is_active', true)
      .single();

    if (error || !data) {
      console.error('Error fetching active database configuration:', error);
      return null;
    }

    return mapDatabaseConfigFromDb(data);
  } catch (error) {
    console.error('Error in getActiveConfiguration:', error);
    return null;
  }
}

/**
 * Get a database configuration by ID
 */
export async function getDatabaseConfigurationById(id: number): Promise<DatabaseConfig | null> {
  try {
    const supabase = await createServerSupabaseClient();

    const { data, error } = await supabase
      .from('database_configurations')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) {
      console.error(`Error fetching database configuration with ID ${id}:`, error);
      return null;
    }

    return mapDatabaseConfigFromDb(data);
  } catch (error) {
    console.error('Error in getDatabaseConfigurationById:', error);
    return null;
  }
}

/**
 * Create a new database configuration
 */
export async function createDatabaseConfiguration(config: Omit<DatabaseConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    const supabase = await createServerSupabaseClient();

    const { data, error } = await supabase
      .from('database_configurations')
      .insert([mapDatabaseConfigToDb(config)])
      .select('id')
      .single();

    if (error) {
      console.error('Error creating database configuration:', error);
      return { success: false, error: error.message };
    }

    revalidatePath('/management-portal-x7z9y2/database-config');
    return { success: true, id: data.id };
  } catch (error) {
    console.error('Error in createDatabaseConfiguration:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Update an existing database configuration
 */
export async function updateDatabaseConfiguration(config: DatabaseConfig): Promise<{ success: boolean; error?: string }> {
  try {
    if (!config.id) {
      return { success: false, error: 'Configuration ID is required' };
    }

    const supabase = await createServerSupabaseClient();

    const { error } = await supabase
      .from('database_configurations')
      .update(mapDatabaseConfigToDb(config))
      .eq('id', config.id);

    if (error) {
      console.error('Error updating database configuration:', error);
      return { success: false, error: error.message };
    }

    revalidatePath('/management-portal-x7z9y2/database-config');
    return { success: true };
  } catch (error) {
    console.error('Error in updateDatabaseConfiguration:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Delete a database configuration
 */
export async function deleteDatabaseConfiguration(id: number): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createServerSupabaseClient();

    // Check if this is the active configuration
    const { data: activeConfig } = await supabase
      .from('database_configurations')
      .select('is_active')
      .eq('id', id)
      .single();

    if (activeConfig?.is_active) {
      return { success: false, error: 'Cannot delete the active configuration' };
    }

    const { error } = await supabase
      .from('database_configurations')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting database configuration:', error);
      return { success: false, error: error.message };
    }

    revalidatePath('/management-portal-x7z9y2/database-config');
    return { success: true };
  } catch (error) {
    console.error('Error in deleteDatabaseConfiguration:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Set a configuration as active
 */
export async function setActiveConfiguration(id: number): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createServerSupabaseClient();

    // Check if the configuration exists
    const { data: configExists, error: checkError } = await supabase
      .from('database_configurations')
      .select('id')
      .eq('id', id)
      .single();

    if (checkError || !configExists) {
      return { success: false, error: 'Configuration not found' };
    }

    // Update the configuration to be active
    // The trigger will automatically deactivate other configurations
    const { error: updateError } = await supabase
      .from('database_configurations')
      .update({ is_active: true })
      .eq('id', id);

    if (updateError) {
      console.error('Error setting active configuration:', updateError);
      return { success: false, error: updateError.message };
    }

    revalidatePath('/management-portal-x7z9y2/database-config');
    return { success: true };
  } catch (error) {
    console.error('Error in setActiveConfiguration:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Helper function to map database configuration from database format to application format
 */
function mapDatabaseConfigFromDb(dbConfig: any): DatabaseConfig {
  return {
    id: dbConfig.id,
    name: dbConfig.name,
    description: dbConfig.description,
    isActive: dbConfig.is_active,
    guerrillaConfig: dbConfig.guerrilla_config,
    supabaseConfig: dbConfig.supabase_config,
    createdAt: dbConfig.created_at,
    updatedAt: dbConfig.updated_at
  };
}

/**
 * Helper function to map database configuration from application format to database format
 */
function mapDatabaseConfigToDb(config: Omit<DatabaseConfig, 'createdAt' | 'updatedAt'>): any {
  return {
    id: config.id,
    name: config.name,
    description: config.description,
    is_active: config.isActive,
    guerrilla_config: config.guerrillaConfig,
    supabase_config: config.supabaseConfig
  };
}

/**
 * Get default configuration from environment variables
 */
export async function getDefaultConfiguration(): Promise<DatabaseConfig> {
  return {
    name: 'Default Configuration',
    description: 'Default configuration from environment variables',
    isActive: true,
    guerrillaConfig: {
      host: process.env.GUERRILLA_DB_HOST || 'localhost',
      port: parseInt(process.env.GUERRILLA_DB_PORT || '3306'),
      database: process.env.GUERRILLA_DB_NAME || 'guerrilla_db',
      user: process.env.GUERRILLA_DB_USER || 'root',
      password: process.env.GUERRILLA_DB_PASSWORD || '',
      connectionLimit: parseInt(process.env.GUERRILLA_DB_CONNECTION_LIMIT || '10'),
      ssl: process.env.GUERRILLA_DB_SSL === 'true'
    },
    supabaseConfig: {
      url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      apiKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY
    }
  };
}
