/**
 * Supabase client for Fademail with SSR support
 *
 * This file creates and exports Supabase client functions that can be used
 * throughout the application to interact with the Supabase PostgreSQL database.
 * It uses the @supabase/ssr package for better server-side rendering support.
 */
import { createBrowserClient } from '@supabase/ssr';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { type CookieOptions } from '@supabase/ssr';
import { Database } from './database.types';

// Get Supabase URL and anon key from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Singleton instances for browser and server clients
let browserClientInstance: ReturnType<typeof createBrowserClient<Database>> | null = null;
let serverClientInstance: ReturnType<typeof createServerClient<Database>> | null = null;

/**
 * Create a Supabase client for use in browser environments
 * Uses a singleton pattern to prevent multiple instances
 */
export function createClient() {
  if (typeof window !== 'undefined' && browserClientInstance) {
    return browserClientInstance;
  }

  const client = createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: {
        apikey: supabaseAnonKey,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }
  });

  if (typeof window !== 'undefined') {
    browserClientInstance = client;
  }

  return client;
}

/**
 * Create a Supabase client for use in server components and server actions
 * Uses a singleton pattern to prevent multiple instances in the same request context
 *
 * This implementation avoids the cookies().get() warning by using a simpler approach
 * that doesn't rely on cookies for server-side operations.
 */
export function createServerSupabaseClient() {
  // For server components, we don't need to use cookies
  // We can use the anon key directly

  // In server context, we can't maintain a true singleton across requests
  // But we can at least reuse the client within the same request context
  if (serverClientInstance) {
    return serverClientInstance;
  }

  const client = createServerClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      // Use a simple no-op cookies implementation for server components
      cookies: {
        get(name: string) {
          // Return null for all cookie requests on the server
          // This avoids the cookies().get() warning
          return null;
        },
        set(name: string, value: string, options: CookieOptions) {
          // No-op for server components
        },
        remove(name: string, options: CookieOptions) {
          // No-op for server components
        },
      },
      global: {
        headers: {
          apikey: supabaseAnonKey,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    }
  );

  // Store the instance for reuse within this request context
  serverClientInstance = client;

  return client;
}
