/**
 * API route for getting the maintenance scheduler status
 * 
 * This API provides information about the current status of the maintenance scheduler.
 */
import { NextRequest, NextResponse } from 'next/server';
import { logError } from '@/lib/logging';
import { isMaintenanceSchedulerRunning } from '@/lib/maintenance/maintenanceScheduler';
import { getConfig } from '@/lib/config/configService';

/**
 * GET /api/admin-portal/maintenance/status
 * 
 * Get the status of the maintenance scheduler
 */
export async function GET(request: NextRequest) {
  try {
    const running = isMaintenanceSchedulerRunning();
    const intervalHours = await getConfig('maintenanceIntervalHours');
    
    // Calculate next run time if running
    let nextRunAt = null;
    if (running && intervalHours) {
      nextRunAt = new Date(Date.now() + intervalHours * 60 * 60 * 1000).toISOString();
    }
    
    return NextResponse.json({
      success: true,
      status: {
        running,
        intervalHours,
        nextRunAt
      }
    });
  } catch (error) {
    logError('maintenance', 'Error getting maintenance scheduler status', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
