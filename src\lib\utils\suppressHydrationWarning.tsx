'use client';

import React, { useEffect, useState } from 'react';

/**
 * A utility component that suppresses React hydration warnings for content
 * that's expected to differ between server and client renders.
 * 
 * This is particularly useful for third-party scripts like Google AdSense
 * that inject content into the DOM after the initial render.
 * 
 * @param children The content to render
 * @returns The content wrapped in a div with suppressHydrationWarning
 */
export function SuppressHydrationWarning({ children }: { children: React.ReactNode }) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <div suppressHydrationWarning>
      {isMounted ? children : null}
    </div>
  );
}

/**
 * A utility component that only renders its children on the client side.
 * This completely avoids hydration mismatches by not rendering anything during SSR.
 * 
 * @param children The content to render on the client
 * @returns The content only when running on the client
 */
export function ClientOnly({ children }: { children: React.ReactNode }) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null;
  }

  return <>{children}</>;
}
