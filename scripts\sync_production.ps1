# PowerShell script to manually sync the development repository with the production repository

# Clone the production repository
git clone https://github.com/johnshawa1/js_production.git temp_production

# Copy all files from the development repository to the production repository
Copy-Item -Path "./*" -Destination "temp_production/" -Recurse -Force

# Remove the .git directory from the production repository
Remove-Item -Path "temp_production/.git" -Recurse -Force

# Navigate to the production repository
Set-Location -Path "temp_production"

# Initialize a new git repository
git init

# Add all files
git add .

# Commit the changes
git commit -m "Sync with development repository"

# Add the remote
git remote add origin https://github.com/johnshawa1/js_production.git

# Push to the production repository
git push -f origin main

# Navigate back to the development repository
Set-Location -Path ".."

# Remove the temporary production repository
Remove-Item -Path "temp_production" -Recurse -Force

Write-Host "Sync completed successfully!"
