import { NextRequest, NextResponse } from 'next/server';
import { generateDmarcRecord, generateDmarcInstructions, validateDmarcRecordFormat } from '@/lib/tools/dmarc-generator/recordGeneration';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { DmarcGenerateRequest, DmarcGenerateResponse, DmarcPolicyConfig } from '@/types/dmarc';
import { validateDomainFormat, sanitizeDomain } from '@/lib/tools/shared/dnsValidation';
import { logRecordGeneration, TOOL_NAMES } from '@/lib/tools/shared/auditLogging';
import crypto from 'crypto';

// Rate limiting - simple in-memory store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = 20; // requests per hour (higher than DKIM since DMARC is simpler)
const RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour in milliseconds

function checkRateLimit(identifier: string): boolean {
  const now = Date.now();
  const userLimit = rateLimitStore.get(identifier);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitStore.set(identifier, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT) {
    return false;
  }

  userLimit.count++;
  return true;
}

function validateEmailAddress(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function validateDmarcInputs(request: DmarcGenerateRequest): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate domain
  if (!request.domain) {
    errors.push('Domain is required');
  } else if (!validateDomainFormat(sanitizeDomain(request.domain))) {
    errors.push('Invalid domain format');
  }

  // Validate policy
  if (!request.policy || !['none', 'quarantine', 'reject'].includes(request.policy)) {
    errors.push('Policy must be one of: none, quarantine, reject');
  }

  // Validate reporting email
  if (!request.rua) {
    errors.push('Reporting email address (rua) is required');
  } else if (!validateEmailAddress(request.rua)) {
    errors.push('Invalid reporting email address format');
  }

  // Validate percentage
  if (request.pct !== undefined) {
    if (typeof request.pct !== 'number' || request.pct < 0 || request.pct > 100) {
      errors.push('Percentage must be a number between 0 and 100');
    }
  }

  // Validate subdomain policy if provided
  if (request.sp && !['none', 'quarantine', 'reject'].includes(request.sp)) {
    errors.push('Subdomain policy must be one of: none, quarantine, reject');
  }

  // Validate alignment modes if provided
  if (request.adkim && !['r', 's'].includes(request.adkim)) {
    errors.push('DKIM alignment must be "r" (relaxed) or "s" (strict)');
  }

  if (request.aspf && !['r', 's'].includes(request.aspf)) {
    errors.push('SPF alignment must be "r" (relaxed) or "s" (strict)');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body: DmarcGenerateRequest = await request.json();
    const { domain, policy, rua, ruf, pct = 100, sp, adkim, aspf, sessionId } = body;

    // Validate inputs
    const validation = validateDmarcInputs(body);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid input data',
          error: validation.errors.join(', '),
        },
        { status: 400 }
      );
    }

    // Rate limiting
    const clientIp = request.headers.get('x-forwarded-for') || 'unknown';
    const rateLimitKey = sessionId || clientIp;

    if (!checkRateLimit(rateLimitKey)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Rate limit exceeded. Please try again later.',
        },
        { status: 429 }
      );
    }

    // Sanitize domain
    const cleanDomain = sanitizeDomain(domain);

    // Create DMARC policy configuration
    const dmarcConfig: DmarcPolicyConfig = {
      domain: cleanDomain,
      policy,
      rua,
      ruf,
      pct,
      sp,
      adkim,
      aspf,
    };

    console.log(`Generating DMARC record for ${cleanDomain} with policy ${policy}`);

    // Generate DMARC record
    const dmarcRecord = generateDmarcRecord(dmarcConfig);
    const instructions = generateDmarcInstructions(dmarcRecord);

    // Validate generated record format
    if (!validateDmarcRecordFormat(dmarcRecord.dnsRecord)) {
      throw new Error('Generated DMARC record format validation failed');
    }

    // Store in database
    const supabase = await createServerSupabaseClient();
    const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days for DMARC

    const { data: dbRecord, error: dbError } = await supabase
      .from('generated_records')
      .insert({
        session_id: sessionId || crypto.randomUUID(),
        record_type: 'dmarc',
        domain: cleanDomain,
        selector: null, // DMARC doesn't use selectors
        dns_record: dmarcRecord.dnsRecord,
        record_name: dmarcRecord.recordName,
        private_key_encrypted: null, // DMARC doesn't use private keys
        metadata: {
          policy: dmarcConfig,
          instructions,
        } as any,
        expires_at: expiresAt.toISOString(),
        validation_status: 'pending',
      })
      .select()
      .single();

    if (dbError) {
      console.error('Database error:', dbError);
      throw new Error('Failed to save DMARC record to database');
    }

    // Log audit event
    await logRecordGeneration(
      TOOL_NAMES.DMARC_GENERATOR,
      'dmarc',
      cleanDomain,
      sessionId,
      undefined, // userId - not implemented yet
      {
        policy: dmarcConfig.policy,
        pct: dmarcConfig.pct,
        recordId: dbRecord.id,
      }
    );

    // Prepare response
    const response: DmarcGenerateResponse = {
      success: true,
      data: {
        dnsRecord: dmarcRecord.dnsRecord,
        recordName: dmarcRecord.recordName,
        domain: cleanDomain,
        policy: dmarcConfig,
        instructions,
        generatedAt: new Date().toISOString(),
      },
    };

    console.log(`DMARC record generated successfully for ${cleanDomain}`);
    return NextResponse.json(response);

  } catch (error) {
    console.error('DMARC Generator API Error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Failed to generate DMARC record',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    {
      success: false,
      message: 'Method not allowed. Use POST to generate DMARC records.',
    },
    { status: 405 }
  );
}
