/**
 * useSmtpTester Hook Tests
 * 
 * Tests for the SMTP tester React hook
 */

import { renderHook, act } from '@testing-library/react';
import { useSmtpTester } from '@/hooks/useSmtpTester';

// Mock fetch
global.fetch = jest.fn();

describe('useSmtpTester Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useSmtpTester());

    expect(result.current.isLoading).toBe(false);
    expect(result.current.result).toBe(null);
    expect(result.current.error).toBe(null);
    expect(result.current.formData).toEqual({
      server: '',
      port: '587',
      encryption: 'tls',
      username: '',
      password: '',
      sender: '',
      testMode: 'auto',
      recipient: ''
    });
    expect(result.current.validationErrors).toEqual({});
  });

  it('should update form data correctly', () => {
    const { result } = renderHook(() => useSmtpTester());

    act(() => {
      result.current.updateFormData({
        server: 'smtp.gmail.com',
        username: '<EMAIL>'
      });
    });

    expect(result.current.formData.server).toBe('smtp.gmail.com');
    expect(result.current.formData.username).toBe('<EMAIL>');
    expect(result.current.formData.port).toBe('587'); // Should remain unchanged
    expect(result.current.validationErrors).toEqual({}); // Should clear validation errors
  });

  it('should validate form data correctly', () => {
    const { result } = renderHook(() => useSmtpTester());

    const invalidFormData = {
      server: '',
      port: 'invalid',
      encryption: 'tls' as const,
      username: '',
      password: '',
      sender: 'invalid-email',
      testMode: 'auto' as const,
      recipient: ''
    };

    const errors = result.current.validateForm(invalidFormData);

    expect(errors.server).toBe('SMTP server is required');
    expect(errors.port).toBe('Valid port number (1-65535) is required');
    expect(errors.username).toBe('Username is required');
    expect(errors.password).toBe('Password is required');
    expect(errors.sender).toBe('Valid sender email is required');
  });

  it('should validate custom mode recipient requirement', () => {
    const { result } = renderHook(() => useSmtpTester());

    const customModeData = {
      server: 'smtp.gmail.com',
      port: '587',
      encryption: 'tls' as const,
      username: '<EMAIL>',
      password: 'password123',
      sender: '<EMAIL>',
      testMode: 'custom' as const,
      recipient: '' // Missing recipient for custom mode
    };

    const errors = result.current.validateForm(customModeData);
    expect(errors.recipient).toBe('Recipient email is required for custom mode');
  });

  it('should handle successful SMTP validation', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, message: 'Valid configuration' })
    });

    const { result } = renderHook(() => useSmtpTester());

    const validFormData = {
      server: 'smtp.gmail.com',
      port: '587',
      encryption: 'tls' as const,
      username: '<EMAIL>',
      password: 'password123',
      sender: '<EMAIL>',
      testMode: 'auto' as const,
      recipient: ''
    };

    let validationResult: boolean;
    await act(async () => {
      validationResult = await result.current.validateSmtpConfig(validFormData);
    });

    expect(validationResult!).toBe(true);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(fetch).toHaveBeenCalledWith('/api/tools/smtp-tester/validate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        config: {
          server: 'smtp.gmail.com',
          port: 587,
          encryption: 'tls',
          username: '<EMAIL>',
          password: 'password123',
          sender: '<EMAIL>'
        }
      })
    });
  });

  it('should handle SMTP validation failure', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      json: async () => ({ success: false, error: 'Invalid configuration' })
    });

    const { result } = renderHook(() => useSmtpTester());

    const invalidFormData = {
      server: 'invalid-server',
      port: '587',
      encryption: 'tls' as const,
      username: '<EMAIL>',
      password: 'password123',
      sender: '<EMAIL>',
      testMode: 'auto' as const,
      recipient: ''
    };

    let validationResult: boolean;
    await act(async () => {
      validationResult = await result.current.validateSmtpConfig(invalidFormData);
    });

    expect(validationResult!).toBe(false);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe('Invalid configuration');
  });

  it('should handle successful SMTP test', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        messageId: 'test-message-id',
        logs: 'Test completed successfully',
        testAddress: '<EMAIL>'
      })
    });

    const { result } = renderHook(() => useSmtpTester());

    const validFormData = {
      server: 'smtp.gmail.com',
      port: '587',
      encryption: 'tls' as const,
      username: '<EMAIL>',
      password: 'password123',
      sender: '<EMAIL>',
      testMode: 'auto' as const,
      recipient: ''
    };

    await act(async () => {
      await result.current.testSmtpConnection(validFormData);
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.result).toEqual({
      success: true,
      messageId: 'test-message-id',
      logs: 'Test completed successfully',
      testAddress: '<EMAIL>'
    });
  });

  it('should handle SMTP test failure', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      json: async () => ({
        success: false,
        error: 'Authentication failed',
        logs: 'Connection failed'
      })
    });

    const { result } = renderHook(() => useSmtpTester());

    const validFormData = {
      server: 'smtp.gmail.com',
      port: '587',
      encryption: 'tls' as const,
      username: '<EMAIL>',
      password: 'wrongpassword',
      sender: '<EMAIL>',
      testMode: 'auto' as const,
      recipient: ''
    };

    await act(async () => {
      await result.current.testSmtpConnection(validFormData);
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe('Authentication failed');
    expect(result.current.result).toBe(null);
  });

  it('should not submit with validation errors', async () => {
    const { result } = renderHook(() => useSmtpTester());

    const invalidFormData = {
      server: '', // Invalid
      port: '587',
      encryption: 'tls' as const,
      username: '',
      password: '',
      sender: '',
      testMode: 'auto' as const,
      recipient: ''
    };

    await act(async () => {
      await result.current.testSmtpConnection(invalidFormData);
    });

    expect(result.current.validationErrors.server).toBe('SMTP server is required');
    expect(result.current.validationErrors.username).toBe('Username is required');
    expect(result.current.validationErrors.password).toBe('Password is required');
    expect(result.current.validationErrors.sender).toBe('Valid sender email is required');
    expect(fetch).not.toHaveBeenCalled();
  });

  it('should reset state correctly', () => {
    const { result } = renderHook(() => useSmtpTester());

    // Set some state
    act(() => {
      result.current.updateFormData({ server: 'smtp.gmail.com' });
    });

    // Reset
    act(() => {
      result.current.reset();
    });

    expect(result.current.formData.server).toBe('');
    expect(result.current.isLoading).toBe(false);
    expect(result.current.result).toBe(null);
    expect(result.current.error).toBe(null);
    expect(result.current.validationErrors).toEqual({});
  });

  it('should clear error correctly', () => {
    const { result } = renderHook(() => useSmtpTester());

    // Simulate an error state
    act(() => {
      result.current.updateFormData({ server: 'test' });
      // Manually set error for testing
      (result.current as any).error = 'Test error';
    });

    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBe(null);
  });

  it('should handle network errors gracefully', async () => {
    (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    const { result } = renderHook(() => useSmtpTester());

    const validFormData = {
      server: 'smtp.gmail.com',
      port: '587',
      encryption: 'tls' as const,
      username: '<EMAIL>',
      password: 'password123',
      sender: '<EMAIL>',
      testMode: 'auto' as const,
      recipient: ''
    };

    await act(async () => {
      await result.current.testSmtpConnection(validFormData);
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe('Network error');
    expect(result.current.result).toBe(null);
  });

  it('should set loading state during operations', async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise(resolve => {
      resolvePromise = resolve;
    });

    (fetch as jest.Mock).mockReturnValueOnce(promise);

    const { result } = renderHook(() => useSmtpTester());

    const validFormData = {
      server: 'smtp.gmail.com',
      port: '587',
      encryption: 'tls' as const,
      username: '<EMAIL>',
      password: 'password123',
      sender: '<EMAIL>',
      testMode: 'auto' as const,
      recipient: ''
    };

    // Start the operation
    act(() => {
      result.current.testSmtpConnection(validFormData);
    });

    // Should be loading
    expect(result.current.isLoading).toBe(true);

    // Resolve the promise
    await act(async () => {
      resolvePromise!({
        ok: true,
        json: async () => ({ success: true, messageId: 'test' })
      });
    });

    // Should no longer be loading
    expect(result.current.isLoading).toBe(false);
  });
});
