import { NextRequest, NextResponse } from 'next/server';
import { validateDmarcRecord, validateDomainFormat, sanitizeDomain } from '@/lib/tools/shared/dnsValidation';
import { parseDmarcRecord } from '@/lib/tools/dmarc-generator/recordGeneration';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { DmarcValidationRequest, DmarcValidationResponse, DmarcPolicy } from '@/types/dmarc';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body: DmarcValidationRequest = await request.json();
    const { domain, expectedPolicy } = body;

    // Validate inputs
    if (!domain) {
      return NextResponse.json(
        {
          success: false,
          message: 'Missing required field: domain is required',
        },
        { status: 400 }
      );
    }

    // Sanitize and validate domain
    const cleanDomain = sanitizeDomain(domain);
    if (!validateDomainFormat(cleanDomain)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid domain format',
        },
        { status: 400 }
      );
    }

    // Validate expected policy if provided
    if (expectedPolicy && !['none', 'quarantine', 'reject'].includes(expectedPolicy)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Expected policy must be one of: none, quarantine, reject',
        },
        { status: 400 }
      );
    }

    console.log(`Validating DMARC record for _dmarc.${cleanDomain}`);

    // Perform DNS validation
    const validationResult = await validateDmarcRecord(cleanDomain, expectedPolicy);

    // Parse DMARC record if it exists
    let parsedPolicy = undefined;
    let policyMatch = undefined;

    if (validationResult.exists && validationResult.records.length > 0) {
      const dmarcRecord = validationResult.records[0];
      parsedPolicy = parseDmarcRecord(dmarcRecord);

      // Check if policy matches expected value
      if (expectedPolicy && parsedPolicy.policy) {
        policyMatch = parsedPolicy.policy === expectedPolicy;
        if (!policyMatch) {
          validationResult.errors.push(`Policy mismatch: expected "${expectedPolicy}", found "${parsedPolicy.policy}"`);
          validationResult.isValid = false;
        }
      }
    }

    // Store validation history in database
    const supabase = await createServerSupabaseClient();

    // Try to find the corresponding generated record
    const { data: generatedRecord } = await supabase
      .from('generated_records')
      .select('id')
      .eq('domain', cleanDomain)
      .eq('record_type', 'dmarc')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    // Store validation history
    const { error: historyError } = await supabase
      .from('dns_validation_history')
      .insert({
        record_id: generatedRecord?.id || null,
        validation_type: 'dmarc',
        domain: cleanDomain,
        record_name: validationResult.recordName,
        expected_value: expectedPolicy || null,
        actual_values: validationResult.records,
        is_valid: validationResult.isValid,
        errors: validationResult.errors,
      });

    if (historyError) {
      console.warn('Failed to store validation history:', historyError);
      // Don't fail the request if history storage fails
    }

    // Update the generated record validation status if found
    if (generatedRecord?.id) {
      const { error: updateError } = await supabase
        .from('generated_records')
        .update({
          validated_at: new Date().toISOString(),
          validation_status: validationResult.isValid ? 'valid' : 'invalid',
          validation_errors: validationResult.errors,
        })
        .eq('id', generatedRecord.id);

      if (updateError) {
        console.warn('Failed to update record validation status:', updateError);
      }
    }

    // Prepare response
    const response: DmarcValidationResponse = {
      success: true,
      data: {
        exists: validationResult.exists,
        records: validationResult.records,
        isValid: validationResult.isValid,
        errors: validationResult.errors,
        lastChecked: validationResult.lastChecked,
        parsedPolicy,
        policyMatch,
      },
    };

    console.log(`DMARC validation completed for ${cleanDomain}: ${validationResult.isValid ? 'VALID' : 'INVALID'}`);
    return NextResponse.json(response);

  } catch (error) {
    console.error('DMARC Validation API Error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Failed to validate DMARC record',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    {
      success: false,
      message: 'Method not allowed. Use POST to validate DMARC records.',
    },
    { status: 405 }
  );
}
