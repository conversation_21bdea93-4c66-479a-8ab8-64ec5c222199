/**
 * Email Tester Results Page
 *
 * This page displays the results of an email test.
 */

'use client';

import { useEffect, useState, useRef } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import ResultsSummary from '@/components/tools/email-tester/ResultsSummary';
import DetailedResults from '@/components/tools/email-tester/DetailedResults';
import RecommendationCard from '@/components/tools/email-tester/RecommendationCard';
import EmailTesterLoadingOverlay from '@/components/tools/email-tester/EmailTesterLoadingOverlay';
import Breadcrumbs from '@/components/seo/Breadcrumbs';
import { WebApplicationSchema } from '@/components/seo/StructuredData';

interface TestResult {
  id: string;
  sender_domain: string;
  sender_ip: string;
  spf_result: string;
  dkim_result: string;
  dmarc_result: string;
  mx_result: string;
  reverse_dns: string;
  spam_score: number;
  overall_score: number;
  created_at: string;
  analysis_json: any;
  email_tester_addresses: {
    test_address: string;
    created_at: string;
    expires_at: string;
  };
}

interface Recommendation {
  id: string;
  category: string;
  issue_description: string;
  recommendation: string;
  dns_record_template?: string;
  implementation_steps?: string;
  priority: number;
}

export default function ResultsPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const testId = params?.testId as string;
  const shareToken = searchParams?.get('share');
  const resultsSummaryRef = useRef<HTMLDivElement>(null);

  const [result, setResult] = useState<TestResult | null>(null);
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [shareUrl, setShareUrl] = useState<string | null>(null);
  const [isGeneratingShare, setIsGeneratingShare] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  useEffect(() => {
    const fetchResults = async () => {
      try {
        // Include share token in the request if present
        const url = shareToken
          ? `/api/tools/email-tester/results/${testId}?share=${shareToken}`
          : `/api/tools/email-tester/results/${testId}`;

        const response = await fetch(url);
        const data = await response.json();

        if (data.success) {
          setResult(data.result);
          setRecommendations(data.recommendations);
        } else {
          setError(data.message || 'Failed to load test results');
        }
      } catch (error) {
        setError('An error occurred while loading the test results');
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, [testId, shareToken]);

  // Scroll to results summary when results are loaded
  useEffect(() => {
    if (result && resultsSummaryRef.current) {
      // Add a small delay to ensure the component is fully rendered
      const timer = setTimeout(() => {
        // Calculate optimal scroll position to show complete target area
        const element = resultsSummaryRef.current;
        if (!element) return;

        const elementRect = element.getBoundingClientRect();
        const viewportHeight = window.innerHeight;

        // Get the bottom of the ResultsSummary component (which comes after the hero section)
        const resultsSummaryElement = element.nextElementSibling;
        const resultsSummaryRect = resultsSummaryElement?.getBoundingClientRect();

        // Calculate the total height needed to show hero + score card
        const totalContentHeight = resultsSummaryRect ?
          (resultsSummaryRect.bottom - elementRect.top) :
          elementRect.height + 400; // fallback estimate

        // If content fits in viewport, scroll to show it all; otherwise prioritize the hero section
        const scrollOptions: ScrollIntoViewOptions = {
          behavior: 'smooth',
          block: totalContentHeight <= viewportHeight * 0.9 ? 'start' : 'start',
          inline: 'nearest'
        };

        element.scrollIntoView(scrollOptions);
      }, 150);

      return () => clearTimeout(timer);
    }
  }, [result]);

  // Generate shareable URL
  const generateShareUrl = async () => {
    if (!result || shareToken) return; // Don't generate share for already shared results

    setIsGeneratingShare(true);
    try {
      const response = await fetch('/api/tools/email-tester/share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ testId }),
      });

      const data = await response.json();
      if (data.success) {
        const fullShareUrl = `${window.location.origin}/tools/email-tester/results/${testId}?share=${data.shareToken}`;
        setShareUrl(fullShareUrl);
      } else {
        console.error('Failed to generate share URL:', data.message);
      }
    } catch (error) {
      console.error('Error generating share URL:', error);
    } finally {
      setIsGeneratingShare(false);
    }
  };

  // Copy share URL to clipboard
  const copyShareUrl = async () => {
    if (!shareUrl) return;

    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  if (isLoading) {
    return <EmailTesterLoadingOverlay isVisible={true} message="Loading test results..." />;
  }

  if (error || !result) {
    return (
      <div className="max-w-5xl mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error || 'Test results not found'}
        </div>
        <div className="mt-4">
          <Link href="/tools/email-tester" className="text-[#ce601c] hover:underline">
            ← Back to Email Tester
          </Link>
        </div>
      </div>
    );
  }

  // Breadcrumb items
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Tools', url: '/tools' },
    { name: 'Email Tester', url: '/tools/email-tester' },
    { name: 'Test Results', url: `/tools/email-tester/results/${testId}`, current: true },
  ];

  return (
    <>
      {/* Structured Data */}
      <WebApplicationSchema
        name="Email Test Results"
        description={`Detailed email deliverability analysis results for ${result?.sender_domain || 'domain'} including SPF, DKIM, DMARC validation and AI-powered recommendations`}
        url={`https://vanishpost.com/tools/email-tester/results/${testId}`}
        applicationCategory="BusinessApplication"
      />

      <div className="min-h-screen" style={{ backgroundColor: '#fbfaf8' }}>
        <div className="max-w-5xl mx-auto px-4 py-6 sm:py-8 lg:py-12">
          {/* Breadcrumb Navigation */}
          <Breadcrumbs items={breadcrumbItems} />

          {/* Navigation */}
          <div className="mb-4 sm:mb-6 lg:mb-8">
            <Link
              href="/tools/email-tester"
              className="group inline-flex items-center space-x-2 transition-colors duration-200"
              style={{ color: '#956b50' }}
              onMouseEnter={(e) => e.currentTarget.style.color = '#1b130e'}
              onMouseLeave={(e) => e.currentTarget.style.color = '#956b50'}
            >
              <svg className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span className="font-medium">Back to Email Tester</span>
            </Link>
          </div>

          {/* Hero Section */}
          <div ref={resultsSummaryRef} className="text-center mb-6 sm:mb-8 lg:mb-10">
            <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 rounded-2xl mb-4 sm:mb-6 shadow-lg" style={{ backgroundColor: '#66b077' }}>
              <svg className="w-8 h-8 sm:w-10 sm:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-3 sm:mb-4" style={{ color: '#1b130e' }}>
              Email Deliverability Test Results - {result?.sender_domain}
            </h1>
            <div className="max-w-2xl mx-auto">
              <p className="text-base sm:text-lg leading-relaxed" style={{ color: '#4a3728' }}>
                Comprehensive SPF, DKIM, DMARC analysis for <span className="font-mono font-semibold" style={{ color: '#956b50' }}>{result.sender_domain}</span>
              </p>
              <p className="mt-1 sm:mt-2 text-sm sm:text-base" style={{ color: '#4a3728' }}>
                Test email sent to <span className="font-mono" style={{ color: '#1b130e' }}>{result.email_tester_addresses.test_address}</span>
              </p>
            </div>
          </div>

        {/* Results Summary */}
        <div className="mb-8 sm:mb-10 lg:mb-12">
          <ResultsSummary result={result} />
        </div>

        {/* Detailed Analysis */}
        <div className="mb-8 sm:mb-10 lg:mb-12">
          <DetailedResults result={result} />
        </div>

        {/* Recommendations Section */}
        <div className="mb-8 sm:mb-10 lg:mb-12">
          <div className="flex items-center space-x-4 mb-8">
            <div className="flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 rounded-2xl shadow-lg flex-shrink-0" style={{ backgroundColor: '#f59e0b', aspectRatio: '1' }}>
              <svg className="w-6 h-6 sm:w-7 sm:h-7 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div>
              <h2 className="text-2xl font-bold" style={{ color: '#1b130e' }}>AI-Powered Recommendations</h2>
              <p style={{ color: '#4a3728' }}>Actionable insights to improve your email deliverability</p>
            </div>
          </div>

          {recommendations.filter(rec => rec.category.toLowerCase() !== 'ip reputation').length > 0 ? (
            <div className="space-y-6">
              {recommendations
                .filter(rec => rec.category.toLowerCase() !== 'ip reputation')
                .map(rec => (
                  <RecommendationCard key={rec.id} recommendation={rec} />
                ))}
            </div>
          ) : (
            <div className="rounded-2xl p-8 text-center" style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8' }}>
              <div className="inline-flex items-center justify-center w-14 h-14 sm:w-16 sm:h-16 rounded-2xl mb-4 flex-shrink-0" style={{ backgroundColor: '#66b077', aspectRatio: '1' }}>
                <svg className="w-7 h-7 sm:w-8 sm:h-8 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-lg" style={{ color: '#1b130e' }}>No specific recommendations available for this test.</p>
              <p className="text-sm mt-2" style={{ color: '#4a3728' }}>Your email configuration appears to be well-optimized!</p>
            </div>
          )}
        </div>

        {/* Next Steps Section */}
        <div className="rounded-2xl p-8 transition-all duration-300" style={{ backgroundColor: '#fbfaf8', border: '1px solid #f3ece8' }}>
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-xl flex-shrink-0" style={{ backgroundColor: '#66b077', aspectRatio: '1' }}>
              <svg className="w-5 h-5 sm:w-6 sm:h-6 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" preserveAspectRatio="xMidYMid meet">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-bold" style={{ color: '#1b130e' }}>Next Steps</h2>
              <p style={{ color: '#4a3728' }}>Continue optimizing your email deliverability</p>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <p className="leading-relaxed" style={{ color: '#4a3728' }}>
                Implement the recommendations above to improve your email deliverability. After making changes, run another test to verify the improvements and track your progress.
              </p>

              {/* Action Buttons */}
              <div className="space-y-4">
                {/* Primary Actions */}
                <div className="flex flex-wrap gap-3">
                  <Link
                    href="/tools/email-tester"
                    className="group px-6 py-3 text-white rounded-xl hover:scale-105 transition-all duration-300 font-semibold"
                    style={{ backgroundColor: '#1b130e' }}
                  >
                    <div className="flex items-center space-x-2">
                      <svg className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      <span>Run Another Test</span>
                    </div>
                  </Link>
                </div>

                {/* Share Functionality */}
                {!shareToken && (
                  <div className="pt-2 border-t" style={{ borderColor: '#f3ece8' }}>
                    <p className="text-sm mb-3" style={{ color: '#4a3728' }}>
                      Share these results with your team or save for future reference
                    </p>
                    {!shareUrl ? (
                      <button
                        onClick={generateShareUrl}
                        disabled={isGeneratingShare}
                        className="inline-flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                        style={{
                          backgroundColor: '#fbfaf8',
                          borderColor: '#956b50',
                          color: '#956b50'
                        }}
                      >
                        {isGeneratingShare ? (
                          <>
                            <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span>Generating...</span>
                          </>
                        ) : (
                          <>
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                            </svg>
                            <span>Generate Share Link</span>
                          </>
                        )}
                      </button>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2 px-3 py-2 rounded-lg" style={{ backgroundColor: '#f3ece8' }}>
                          <svg className="w-4 h-4" style={{ color: '#66b077' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="text-sm font-medium" style={{ color: '#1b130e' }}>Shareable link generated successfully</span>
                        </div>
                        <button
                          onClick={copyShareUrl}
                          className="inline-flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200 hover:scale-105 font-medium"
                          style={{
                            backgroundColor: copySuccess ? '#66b077' : '#956b50',
                            color: '#fbfaf8'
                          }}
                        >
                          {copySuccess ? (
                            <>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <span>Copied to Clipboard!</span>
                            </>
                          ) : (
                            <>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                              <span>Copy Share Link</span>
                            </>
                          )}
                        </button>
                      </div>
                    )}
                  </div>
                )}

                {/* Shared Result Indicator */}
                {shareToken && (
                  <div className="pt-2 border-t" style={{ borderColor: '#f3ece8' }}>
                    <div className="flex items-center space-x-2 px-3 py-2 rounded-lg" style={{ backgroundColor: '#f3ece8' }}>
                      <svg className="w-4 h-4" style={{ color: '#f59e0b' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                      </svg>
                      <span className="text-sm font-medium" style={{ color: '#1b130e' }}>You're viewing a shared test result</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="rounded-xl p-6" style={{ backgroundColor: '#f3ece8', border: '1px solid #f3ece8' }}>
              <h3 className="font-semibold mb-3" style={{ color: '#1b130e' }}>Pro Tips</h3>
              <ul className="space-y-2 text-sm" style={{ color: '#4a3728' }}>
                <li className="flex items-start space-x-2">
                  <span className="mt-0.5" style={{ color: '#956b50' }}>•</span>
                  <span>Test regularly to monitor your email reputation</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="mt-0.5" style={{ color: '#956b50' }}>•</span>
                  <span>Implement changes gradually and test each modification</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="mt-0.5" style={{ color: '#956b50' }}>•</span>
                  <span>Monitor your email metrics after implementing changes</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
