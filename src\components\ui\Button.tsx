'use client';

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Define button variants using class-variance-authority with earth-tone styling
const buttonVariants = cva(
  // Base styles applied to all buttons
  "inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-70 active:scale-98 relative overflow-hidden",
  {
    variants: {
      variant: {
        primary: "bg-[#4a3728] text-white border border-[#4a3728] hover:bg-[#3a2b1f] hover:border-[#3a2b1f] hover:shadow-md focus-visible:ring-[#4a3728]",
        secondary: "bg-white text-[#4a3728] border border-[#4a3728]/20 hover:bg-[#f3ece8] hover:shadow-md focus-visible:ring-[#4a3728]",
        danger: "bg-red-600 text-white border border-red-600 hover:bg-red-700 hover:border-red-700 hover:shadow-md focus-visible:ring-red-600",
        ghost: "text-[#4a3728] hover:bg-[#f3ece8] hover:text-[#3a2b1f] focus-visible:ring-[#4a3728]",
        link: "text-[#4a3728] underline-offset-4 hover:underline focus-visible:ring-[#4a3728]",
        outline: "border border-[#4a3728]/20 bg-white hover:bg-[#f3ece8] text-[#4a3728]",
        default: "bg-[#956b50] text-white hover:bg-[#4a3728]",
      },
      size: {
        sm: "h-8 px-3 text-xs",
        md: "h-10 px-4 py-2",
        lg: "h-12 px-8 text-base",
        icon: "h-10 w-10",
      },
      fullWidth: {
        true: "w-full",
        false: "",
      },
      withAnimation: {
        true: "transition-all duration-300 hover:scale-105",
        false: "",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
      fullWidth: false,
      withAnimation: false,
    },
  }
);

// Define the props for the Button component
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  children: React.ReactNode;
  asChild?: boolean;
  as?: React.ElementType; // Support for polymorphic component
  href?: string; // Support for link functionality
  loading?: boolean;
  isLoading?: boolean; // Alternative prop name for loading
  icon?: React.ReactNode;
  leftIcon?: React.ReactNode; // Alternative prop name for left icon
  rightIcon?: React.ReactNode; // Alternative prop name for right icon
  iconPosition?: 'left' | 'right';
}

/**
 * Button Component
 *
 * A customizable button component with various styles and sizes.
 */
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant,
    size,
    fullWidth,
    withAnimation,
    loading = false,
    isLoading = false,
    icon,
    leftIcon,
    rightIcon,
    iconPosition = 'left',
    children,
    disabled,
    asChild,
    as,
    href,
    ...props
  }, ref) => {
    // Normalize loading state
    const isButtonLoading = loading || isLoading;

    // Determine the icon to use
    const leftIconToUse = leftIcon || (iconPosition === 'left' ? icon : null);
    const rightIconToUse = rightIcon || (iconPosition === 'right' ? icon : null);

    // Determine if the button has an icon
    const hasIcon = leftIconToUse || rightIconToUse || isButtonLoading;

    // Determine the component to render
    const Component = as || 'button';
    
    // Loading spinner component
    const LoadingSpinner = () => (
      <svg
        className="animate-spin h-4 w-4"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    );

    return (
      <Component
        className={cn(buttonVariants({ variant, size, fullWidth, withAnimation, className }))}
        ref={ref}
        disabled={disabled || isButtonLoading}
        href={href}
        {...props}
      >
        {/* Left icon or loading spinner */}
        {leftIconToUse && (
          <span className={cn("flex items-center", children && "mr-2")}>
            {isButtonLoading ? <LoadingSpinner /> : leftIconToUse}
          </span>
        )}

        {/* Button content */}
        {children}

        {/* Right icon */}
        {rightIconToUse && !isButtonLoading && (
          <span className={cn("flex items-center", children && "ml-2")}>
            {rightIconToUse}
          </span>
        )}
      </Component>
    );
  }
);

Button.displayName = "Button";

export { Button, buttonVariants };
