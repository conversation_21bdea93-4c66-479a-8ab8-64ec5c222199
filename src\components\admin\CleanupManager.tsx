'use client';

import { useState, useEffect, useCallback } from 'react';
import { ArrowPathIcon, PlayIcon, StopIcon, TrashIcon, ClockIcon, CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/solid';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { ProgressWithLabel } from '../ui/Progress';

interface CleanupResult {
  success: boolean;
  tempEmailsDeleted: number;
  guerrillaEmailsDeleted: number;
  duration: number;
  timestamp: string;
  error?: string;
}

interface CleanupSchedulerStatus {
  running: boolean;
  intervalMinutes: number;
  nextRunAt?: string;
  autoStart?: boolean;
}

interface CleanupHistoryItem {
  id: string;
  timestamp: string;
  success: boolean;
  tempEmailsDeleted: number;
  guerrillaEmailsDeleted: number;
  duration: number;
  error?: string;
}

export default function CleanupManager() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastResult, setLastResult] = useState<CleanupResult | null>(null);
  const [schedulerStatus, setSchedulerStatus] = useState<CleanupSchedulerStatus | null>(null);
  const [cleanupInterval, setCleanupInterval] = useState(15); // Default: 15 minutes
  const [cleanupHistory, setCleanupHistory] = useState<CleanupHistoryItem[]>([]);
  const [countdown, setCountdown] = useState<number | null>(null);
  const [autoStart, setAutoStart] = useState(true);

  // Progress tracking for cleanup operations
  const [cleanupProgress, setCleanupProgress] = useState(0);
  const [cleanupStage, setCleanupStage] = useState<string>('');

  // Countdown timer effect
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (schedulerStatus?.running && schedulerStatus.nextRunAt) {
      const updateCountdown = () => {
        const nextRun = new Date(schedulerStatus.nextRunAt!).getTime();
        const now = Date.now();
        const timeLeft = Math.max(0, Math.floor((nextRun - now) / 1000));
        setCountdown(timeLeft);

        // If countdown reaches 0, refresh scheduler status
        if (timeLeft === 0) {
          fetchSchedulerStatus();
        }
      };

      updateCountdown();
      intervalId = setInterval(updateCountdown, 1000);
    } else {
      setCountdown(null);
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [schedulerStatus?.running, schedulerStatus?.nextRunAt]);

  // Format countdown display
  const formatCountdown = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Fetch scheduler status
  const fetchSchedulerStatus = async () => {
    try {
      const response = await fetch('/api/management-portal-x7z9y2/cleanup/status');

      if (!response.ok) {
        throw new Error(`Failed to fetch scheduler status: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setSchedulerStatus(data.status);
        setCleanupInterval(data.status.intervalMinutes);
        setAutoStart(data.status.autoStart !== false);
      } else {
        setError(data.error || 'Failed to fetch scheduler status');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching scheduler status');
      console.error('Error fetching scheduler status:', err);
    }
  };

  // Run cleanup manually with progress tracking
  const runCleanup = async () => {
    try {
      setLoading(true);
      setError(null);
      setCleanupProgress(0);
      setCleanupStage('Initializing cleanup...');

      // Simulate progress stages for better UX
      const progressStages = [
        { progress: 10, stage: 'Scanning temporary emails...' },
        { progress: 30, stage: 'Scanning guerrilla emails...' },
        { progress: 50, stage: 'Processing deletions...' },
        { progress: 80, stage: 'Cleaning up resources...' },
        { progress: 95, stage: 'Finalizing...' }
      ];

      // Update progress through stages
      for (const { progress, stage } of progressStages) {
        setCleanupProgress(progress);
        setCleanupStage(stage);
        await new Promise(resolve => setTimeout(resolve, 300)); // Small delay for UX
      }

      const response = await fetch('/api/management-portal-x7z9y2/cleanup');

      if (!response.ok) {
        throw new Error(`Failed to run cleanup: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Complete progress
      setCleanupProgress(100);
      setCleanupStage('Cleanup completed successfully!');

      setLastResult(data);

      // Add to history
      const historyItem: CleanupHistoryItem = {
        id: Date.now().toString(),
        timestamp: data.timestamp,
        success: data.success,
        tempEmailsDeleted: data.tempEmailsDeleted,
        guerrillaEmailsDeleted: data.guerrillaEmailsDeleted,
        duration: data.duration,
        error: data.error
      };

      setCleanupHistory(prev => [historyItem, ...prev.slice(0, 2)]); // Keep last 3 operations

      // Refresh scheduler status
      fetchSchedulerStatus();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while running cleanup');
      setCleanupStage('Cleanup failed');
      console.error('Error running cleanup:', err);
    } finally {
      setLoading(false);
      // Reset progress after a delay
      setTimeout(() => {
        setCleanupProgress(0);
        setCleanupStage('');
      }, 3000);
    }
  };

  // Start scheduler
  const startScheduler = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/cleanup/scheduler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'start',
          intervalMinutes: cleanupInterval,
          autoStart: autoStart
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to start scheduler: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        fetchSchedulerStatus();
      } else {
        setError(data.error || 'Failed to start scheduler');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while starting scheduler');
      console.error('Error starting scheduler:', err);
    } finally {
      setLoading(false);
    }
  };

  // Stop scheduler
  const stopScheduler = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/management-portal-x7z9y2/cleanup/scheduler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'stop' })
      });

      if (!response.ok) {
        throw new Error(`Failed to stop scheduler: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        fetchSchedulerStatus();
      } else {
        setError(data.error || 'Failed to stop scheduler');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while stopping scheduler');
      console.error('Error stopping scheduler:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchSchedulerStatus();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
          Scheduler Configuration
        </h2>
        <Button
          onClick={fetchSchedulerStatus}
          isLoading={loading}
          leftIcon={<ArrowPathIcon className="h-4 w-4" />}
          size="sm"
        >
          Refresh
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <Button
                  onClick={() => setError(null)}
                  variant="ghost"
                  size="sm"
                  className="text-red-700 hover:bg-red-100"
                >
                  Dismiss
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            Scheduler Status
          </h3>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
          {schedulerStatus ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`h-4 w-4 rounded-full ${schedulerStatus.running ? 'bg-green-500' : 'bg-red-500'} mr-2`}></div>
                  <span className="text-sm font-medium text-gray-900">
                    {schedulerStatus.running ? 'Running' : 'Stopped'}
                  </span>
                </div>
                <div>
                  {schedulerStatus.running ? (
                    <Button
                      onClick={stopScheduler}
                      isLoading={loading}
                      leftIcon={<StopIcon className="h-4 w-4" />}
                      variant="danger"
                    >
                      Stop Scheduler
                    </Button>
                  ) : (
                    <Button
                      onClick={startScheduler}
                      isLoading={loading}
                      leftIcon={<PlayIcon className="h-4 w-4" />}
                      variant="primary"
                      className="bg-green-600 hover:bg-green-700 focus-visible:ring-green-500"
                    >
                      Start Scheduler
                    </Button>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-3">
                <div>
                  <label htmlFor="cleanup-interval" className="block text-sm font-medium text-gray-700">
                    Cleanup Interval (minutes)
                  </label>
                  <div className="mt-1">
                    <Input
                      type="number"
                      name="cleanup-interval"
                      id="cleanup-interval"
                      min="1"
                      max="1440"
                      value={cleanupInterval}
                      onChange={(e) => setCleanupInterval(Number(e.target.value))}
                      disabled={schedulerStatus.running || loading}
                      leftIcon={<ClockIcon className="h-4 w-4" />}
                    />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    {schedulerStatus.running
                      ? `Current interval: ${schedulerStatus.intervalMinutes} minutes`
                      : 'Set the interval between cleanup runs'}
                  </p>
                </div>

                <div>
                  <label htmlFor="auto-start" className="block text-sm font-medium text-gray-700">
                    Auto-Start on App Load
                  </label>
                  <div className="mt-3">
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        checked={autoStart}
                        onChange={(e) => setAutoStart(e.target.checked)}
                        disabled={loading}
                        className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        Start scheduler automatically when application loads
                      </span>
                    </label>
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    When enabled, the scheduler will start automatically on app startup
                  </p>
                </div>

                {schedulerStatus.running && schedulerStatus.nextRunAt && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      Next Run
                    </h4>
                    <div className="mt-2 space-y-1">
                      <p className="text-sm text-gray-900">
                        {new Date(schedulerStatus.nextRunAt).toLocaleString()}
                      </p>
                      {countdown !== null && (
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                          <span className="text-lg font-mono font-bold text-indigo-600">
                            {formatCountdown(countdown)}
                          </span>
                        </div>
                      )}
                      <p className="text-xs text-gray-500">
                        {formatDistanceToNow(new Date(schedulerStatus.nextRunAt), { addSuffix: true })}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex justify-center items-center h-24">
              <ArrowPathIcon className="h-6 w-6 text-indigo-500 animate-spin" />
            </div>
          )}
        </div>
      </div>

      <h2 className="text-xl font-semibold text-gray-800 mt-8 mb-4 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        Manual Cleanup Operations
      </h2>
      <div className="bg-white shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            Manual Cleanup
          </h3>
          <Button
            onClick={runCleanup}
            isLoading={loading}
            leftIcon={<TrashIcon className="h-4 w-4" />}
          >
            Run Cleanup Now
          </Button>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
          {/* Progress indicator during cleanup */}
          {loading && cleanupProgress > 0 && (
            <div className="mb-6">
              <ProgressWithLabel
                value={cleanupProgress}
                label={cleanupStage}
                variant={cleanupProgress === 100 ? 'success' : 'default'}
                className="mb-2"
              />
            </div>
          )}

          {lastResult ? (
            <div className="space-y-4">
              <div className={`rounded-md ${lastResult.success ? 'bg-green-50' : 'bg-red-50'} p-4`}>
                <div className="flex">
                  <div className="flex-shrink-0">
                    {lastResult.success ? (
                      <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  <div className="ml-3">
                    <h3 className={`text-sm font-medium ${lastResult.success ? 'text-green-800' : 'text-red-800'}`}>
                      {lastResult.success ? 'Cleanup completed successfully' : 'Cleanup failed'}
                    </h3>
                    {!lastResult.success && lastResult.error && (
                      <div className="mt-2 text-sm text-red-700">
                        <p>{lastResult.error}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 lg:grid-cols-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                    </svg>
                    Temp Emails Deleted
                  </h4>
                  <p className="mt-2 text-2xl font-semibold text-gray-900">
                    {lastResult.tempEmailsDeleted.toLocaleString()}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                    Guerrilla Emails Deleted
                  </h4>
                  <p className="mt-2 text-2xl font-semibold text-gray-900">
                    {lastResult.guerrillaEmailsDeleted.toLocaleString()}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                    Duration
                  </h4>
                  <p className="mt-2 text-2xl font-semibold text-gray-900">
                    {(lastResult.duration / 1000).toFixed(2)}s
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                    Timestamp
                  </h4>
                  <p className="mt-2 text-sm text-gray-900">
                    {new Date(lastResult.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-sm text-gray-500">
              No cleanup has been run yet. Click the "Run Cleanup Now" button to manually clean up expired emails.
            </p>
          )}
        </div>
      </div>

      {/* Operation History */}
      {cleanupHistory.length > 0 && (
        <div className="mt-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
            Recent Operations
          </h2>
          <div className="bg-white shadow overflow-hidden rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg font-medium text-gray-900">
                Last {cleanupHistory.length} Cleanup Operations
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                History of recent cleanup operations with their results
              </p>
            </div>
            <div className="border-t border-gray-200">
              <div className="space-y-0">
                {cleanupHistory.map((item, index) => (
                  <div key={item.id} className={`px-4 py-4 ${index !== cleanupHistory.length - 1 ? 'border-b border-gray-200' : ''}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {item.success ? (
                          <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
                        ) : (
                          <XCircleIcon className="h-5 w-5 text-red-500 mr-3" />
                        )}
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {item.success ? 'Cleanup Successful' : 'Cleanup Failed'}
                          </p>
                          <p className="text-sm text-gray-500">
                            {new Date(item.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>Temp: {item.tempEmailsDeleted}</span>
                          <span>Guerrilla: {item.guerrillaEmailsDeleted}</span>
                          <span>{(item.duration / 1000).toFixed(2)}s</span>
                        </div>
                        {item.error && (
                          <div className="mt-1 flex items-center text-sm text-red-600">
                            <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                            <span className="truncate max-w-xs">{item.error}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
