import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '../../../../../../lib/auth';
import productionSafeguards from '../../../../../../lib/config/productionSafeguards';
import { logger } from '../../../../../../lib/logging/dbLogger';

/**
 * POST /api/admin/safeguards/validate/domain
 * Validate domain changes
 */
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { domain, isActive, settings } = body;

    if (!domain) {
      return NextResponse.json({ error: 'Domain name is required' }, { status: 400 });
    }

    if (isActive === undefined) {
      return NextResponse.json({ error: 'Active status is required' }, { status: 400 });
    }

    if (!settings) {
      return NextResponse.json({ error: 'Domain settings are required' }, { status: 400 });
    }

    // Validate domain change
    const validationResult = await productionSafeguards.validateDomainChange(domain, isActive, settings);

    return NextResponse.json(validationResult);
  } catch (error) {
    await logger.error('API_SAFEGUARDS_VALIDATE_DOMAIN', `Error validating domain: ${error}`);
    return NextResponse.json({ success: false, message: 'Failed to validate domain' }, { status: 500 });
  }
}
