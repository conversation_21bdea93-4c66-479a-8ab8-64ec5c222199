'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Admin<PERSON>ard, AdminCardHeader, AdminCardContent, AdminCardTitle } from '../ui/AdminCard';
import { AdminButton } from '../ui/AdminButton';
import {
  BarChart3,
  Shield,
  Ban,
  Zap,
  TrendingUp,
  Target,
  RefreshCw,
  Search,
  Activity
} from 'lucide-react';

// Simple wrapper component for AdminCard with title support
function AdminCardWithTitle({ title, children, className }: { title: string | React.ReactNode; children: React.ReactNode; className?: string }) {
  return (
    <AdminCard className={className}>
      <AdminCardHeader>
        <AdminCardTitle>{title}</AdminCardTitle>
      </AdminCardHeader>
      <AdminCardContent>
        {children}
      </AdminCardContent>
    </AdminCard>
  );
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

interface SecurityMetrics {
  totalThreats: number;
  blockedSessions: number;
  blockedIPs: number;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  hourlyBlocks: number;
  falsePositives: number;
  systemHealth: number; // 0-100
}

interface ThreatTrend {
  hour: string;
  threats: number;
  blocks: number;
  type: string;
}

interface TopThreat {
  identifier: string;
  type: 'ip' | 'session' | 'pattern';
  count: number;
  lastSeen: string;
  riskLevel: string;
}

export function SecurityMetrics() {
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalThreats: 0,
    blockedSessions: 0,
    blockedIPs: 0,
    threatLevel: 'low',
    hourlyBlocks: 0,
    falsePositives: 0,
    systemHealth: 100
  });
  const [trends, setTrends] = useState<ThreatTrend[]>([]);
  const [topThreats, setTopThreats] = useState<TopThreat[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('24h');

  // Fetch security metrics
  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const timeWindow = getTimeWindow(timeRange);

      // Get total threats detected
      const { count: totalThreats } = await supabase
        .from('security_events')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', timeWindow.toISOString())
        .in('severity', ['high', 'critical']);

      // Get blocked sessions
      const { count: blockedSessions } = await supabase
        .from('blocked_sessions')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      // Get blocked IP ranges
      const { count: blockedIPs } = await supabase
        .from('blocked_ip_ranges')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      // Get hourly blocks (last hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const { count: hourlyBlocks } = await supabase
        .from('security_events')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', oneHourAgo.toISOString())
        .eq('action_taken', 'request_blocked');

      // Calculate threat level
      const threatLevel = calculateThreatLevel(totalThreats || 0, hourlyBlocks || 0);

      // Calculate system health (simplified)
      const systemHealth = Math.max(0, 100 - (hourlyBlocks || 0) * 5);

      setMetrics({
        totalThreats: totalThreats || 0,
        blockedSessions: blockedSessions || 0,
        blockedIPs: blockedIPs || 0,
        threatLevel,
        hourlyBlocks: hourlyBlocks || 0,
        falsePositives: 0, // TODO: Implement false positive tracking
        systemHealth
      });

    } catch (error) {
      console.error('Error fetching metrics:', error);
    }
  };

  // Fetch threat trends
  const fetchTrends = async () => {
    try {
      const timeWindow = getTimeWindow(timeRange);
      
      const { data: events } = await supabase
        .from('security_events')
        .select('created_at, event_type, action_taken')
        .gte('created_at', timeWindow.toISOString())
        .order('created_at', { ascending: true });

      if (!events) return;

      // Group by hour
      const hourlyData: Record<string, { threats: number; blocks: number }> = {};
      
      events.forEach(event => {
        const hour = new Date(event.created_at).toISOString().slice(0, 13) + ':00:00.000Z';
        if (!hourlyData[hour]) {
          hourlyData[hour] = { threats: 0, blocks: 0 };
        }
        
        if (event.event_type.includes('detected')) {
          hourlyData[hour].threats++;
        }
        if (event.action_taken === 'request_blocked') {
          hourlyData[hour].blocks++;
        }
      });

      const trendData: ThreatTrend[] = Object.entries(hourlyData).map(([hour, data]) => ({
        hour: new Date(hour).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        threats: data.threats,
        blocks: data.blocks,
        type: 'security'
      }));

      setTrends(trendData.slice(-24)); // Last 24 hours
    } catch (error) {
      console.error('Error fetching trends:', error);
    }
  };

  // Fetch top threats
  const fetchTopThreats = async () => {
    try {
      const timeWindow = getTimeWindow(timeRange);

      // Get abuse patterns
      const { data: patterns } = await supabase
        .from('abuse_patterns')
        .select('session_id, pattern_type, confidence_score, detected_at, ip_address')
        .gte('detected_at', timeWindow.toISOString())
        .order('confidence_score', { ascending: false })
        .limit(10);

      if (!patterns) return;

      // Process into top threats
      const threats: TopThreat[] = patterns.map(pattern => ({
        identifier: pattern.session_id || pattern.ip_address || 'Unknown',
        type: pattern.ip_address ? 'ip' : 'session',
        count: 1, // TODO: Aggregate counts
        lastSeen: pattern.detected_at,
        riskLevel: pattern.confidence_score > 0.8 ? 'critical' : 
                  pattern.confidence_score > 0.6 ? 'high' : 'medium'
      }));

      setTopThreats(threats);
    } catch (error) {
      console.error('Error fetching top threats:', error);
    }
  };

  // Helper functions
  const getTimeWindow = (range: string): Date => {
    const now = new Date();
    switch (range) {
      case '1h': return new Date(now.getTime() - 60 * 60 * 1000);
      case '6h': return new Date(now.getTime() - 6 * 60 * 60 * 1000);
      case '24h': return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case '7d': return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      default: return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }
  };

  const calculateThreatLevel = (totalThreats: number, hourlyBlocks: number): 'low' | 'medium' | 'high' | 'critical' => {
    if (hourlyBlocks > 20 || totalThreats > 100) return 'critical';
    if (hourlyBlocks > 10 || totalThreats > 50) return 'high';
    if (hourlyBlocks > 5 || totalThreats > 20) return 'medium';
    return 'low';
  };

  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-green-600 bg-green-100';
    }
  };

  const getHealthColor = (health: number) => {
    if (health >= 90) return 'text-green-600';
    if (health >= 70) return 'text-yellow-600';
    if (health >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  // Auto-refresh effect
  useEffect(() => {
    const fetchData = async () => {
      await Promise.all([fetchMetrics(), fetchTrends(), fetchTopThreats()]);
      setLoading(false);
    };

    fetchData();
    const interval = setInterval(fetchData, 60000); // Refresh every minute
    return () => clearInterval(interval);
  }, [timeRange]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center">
          <BarChart3 className="h-6 w-6 mr-2 text-indigo-600" />
          Security Metrics Dashboard
        </h2>
        <div className="flex gap-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="1h">Last Hour</option>
            <option value="6h">Last 6 Hours</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
          </select>
          <AdminButton
            onClick={() => {
              fetchMetrics();
              fetchTrends();
              fetchTopThreats();
            }}
            variant="secondary"
            size="sm"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </AdminButton>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <AdminCardWithTitle title={<><Target className="h-4 w-4 mr-1 inline text-red-600" />Threat Level</>} className="text-center">
          <div className={`text-3xl font-bold px-4 py-2 rounded-lg ${getThreatLevelColor(metrics.threatLevel)}`}>
            {metrics.threatLevel.toUpperCase()}
          </div>
          <p className="text-sm text-gray-600 mt-2">Current system threat level</p>
        </AdminCardWithTitle>

        <AdminCardWithTitle title={<><Shield className="h-4 w-4 mr-1 inline text-blue-600" />System Health</>} className="text-center">
          <div className={`text-3xl font-bold ${getHealthColor(metrics.systemHealth)}`}>
            {metrics.systemHealth}%
          </div>
          <p className="text-sm text-gray-600 mt-2">Overall system health</p>
        </AdminCardWithTitle>

        <AdminCardWithTitle title={<><Ban className="h-4 w-4 mr-1 inline text-red-600" />Active Blocks</>} className="text-center">
          <div className="text-3xl font-bold text-blue-600">
            {metrics.blockedSessions + metrics.blockedIPs}
          </div>
          <p className="text-sm text-gray-600 mt-2">
            {metrics.blockedSessions} sessions, {metrics.blockedIPs} IPs
          </p>
        </AdminCardWithTitle>

        <AdminCardWithTitle title={<><Activity className="h-4 w-4 mr-1 inline text-purple-600" />Hourly Activity</>} className="text-center">
          <div className="text-3xl font-bold text-purple-600">
            {metrics.hourlyBlocks}
          </div>
          <p className="text-sm text-gray-600 mt-2">Blocks in last hour</p>
        </AdminCardWithTitle>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Threat Trends */}
        <AdminCardWithTitle title={<><TrendingUp className="h-4 w-4 mr-1 inline text-blue-600" />Threat Trends</>} className="border-blue-200">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            </div>
          ) : (
            <div className="space-y-2">
              {trends.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No trend data available</p>
              ) : (
                <div className="space-y-1">
                  {trends.slice(-10).map((trend, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <span className="text-sm font-mono">{trend.hour}</span>
                      <div className="flex gap-4 text-sm">
                        <span className="text-orange-600 flex items-center">
                          <Search className="h-3 w-3 mr-1" />
                          {trend.threats} threats
                        </span>
                        <span className="text-red-600 flex items-center">
                          <Ban className="h-3 w-3 mr-1" />
                          {trend.blocks} blocks
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </AdminCardWithTitle>

        {/* Top Threats */}
        <AdminCardWithTitle title={<><Target className="h-4 w-4 mr-1 inline text-red-600" />Top Threats</>} className="border-red-200">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
            </div>
          ) : (
            <div className="space-y-2">
              {topThreats.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No threats detected</p>
              ) : (
                topThreats.slice(0, 8).map((threat, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getThreatLevelColor(threat.riskLevel)}`}>
                          {threat.riskLevel.toUpperCase()}
                        </span>
                        <span className="font-mono text-sm">
                          {threat.identifier.slice(0, 20)}...
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {threat.type.toUpperCase()} • Last seen: {new Date(threat.lastSeen).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </AdminCardWithTitle>
      </div>

      {/* Performance Metrics */}
      <AdminCardWithTitle title={<><Zap className="h-4 w-4 mr-1 inline text-green-600" />Performance Metrics</>} className="border-green-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {metrics.totalThreats}
            </div>
            <p className="text-sm text-gray-600">Total Threats Detected</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {metrics.falsePositives}
            </div>
            <p className="text-sm text-gray-600">False Positives</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {metrics.totalThreats > 0 ? ((metrics.totalThreats - metrics.falsePositives) / metrics.totalThreats * 100).toFixed(1) : 100}%
            </div>
            <p className="text-sm text-gray-600">Detection Accuracy</p>
          </div>
        </div>
      </AdminCardWithTitle>
    </div>
  );
}
