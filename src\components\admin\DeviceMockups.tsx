'use client';

interface DeviceMockupProps {
  content: string;
}

export function DevicePhoneMockup({ content }: DeviceMockupProps) {
  return (
    <div className="relative mx-auto border-gray-800 dark:border-gray-800 bg-gray-800 border-[14px] rounded-[2.5rem] h-[600px] w-[300px]">
      <div className="h-[32px] w-[3px] bg-gray-800 dark:bg-gray-800 absolute -left-[17px] top-[72px] rounded-l-lg"></div>
      <div className="h-[46px] w-[3px] bg-gray-800 dark:bg-gray-800 absolute -left-[17px] top-[124px] rounded-l-lg"></div>
      <div className="h-[46px] w-[3px] bg-gray-800 dark:bg-gray-800 absolute -left-[17px] top-[178px] rounded-l-lg"></div>
      <div className="h-[64px] w-[3px] bg-gray-800 dark:bg-gray-800 absolute -right-[17px] top-[142px] rounded-r-lg"></div>
      <div className="rounded-[2rem] overflow-hidden w-[272px] h-[572px] bg-white dark:bg-gray-800">
        <div className="w-[272px] h-[572px] overflow-hidden">
          <iframe
            srcDoc={content}
            className="w-full h-full border-0"
            title="Mobile Preview"
            sandbox="allow-scripts"
          ></iframe>
        </div>
      </div>
    </div>
  );
}

export function DeviceTabletMockup({ content }: DeviceMockupProps) {
  return (
    <div className="relative mx-auto border-gray-800 dark:border-gray-800 bg-gray-800 border-[14px] rounded-[2.5rem] h-[454px] max-w-[341px] md:h-[682px] md:max-w-[512px]">
      <div className="h-[32px] w-[3px] bg-gray-800 dark:bg-gray-800 absolute -right-[17px] top-[72px] rounded-r-lg"></div>
      <div className="h-[46px] w-[3px] bg-gray-800 dark:bg-gray-800 absolute -right-[17px] top-[124px] rounded-r-lg"></div>
      <div className="h-[46px] w-[3px] bg-gray-800 dark:bg-gray-800 absolute -right-[17px] top-[178px] rounded-r-lg"></div>
      <div className="h-[64px] w-[3px] bg-gray-800 dark:bg-gray-800 absolute -left-[17px] top-[142px] rounded-l-lg"></div>
      <div className="rounded-[2rem] overflow-hidden h-[426px] md:h-[654px] bg-white dark:bg-gray-800">
        <iframe
          srcDoc={content}
          className="w-full h-full border-0"
          title="Tablet Preview"
          sandbox="allow-scripts"
        ></iframe>
      </div>
    </div>
  );
}

export function DeviceDesktopMockup({ content }: DeviceMockupProps) {
  return (
    <div className="relative mx-auto border-gray-800 dark:border-gray-800 bg-gray-800 border-[8px] rounded-t-xl h-[172px] max-w-[301px] md:h-[294px] md:max-w-[512px]">
      <div className="rounded-lg overflow-hidden h-[156px] md:h-[278px] bg-white dark:bg-gray-800">
        <iframe
          srcDoc={content}
          className="w-full h-full border-0"
          title="Desktop Preview"
          sandbox="allow-scripts"
        ></iframe>
      </div>
      <div className="absolute left-1/2 top-[0px] -translate-x-1/2 rounded-b-xl w-[56px] h-[5px] md:w-[96px] md:h-[8px] bg-gray-800"></div>
      <div className="w-[48px] md:w-[96px] h-[5px] md:h-[8px] bg-gray-800 absolute -bottom-[5px] md:-bottom-[8px] rounded-b-xl left-1/2 -translate-x-1/2"></div>
      <div className="h-[3px] w-[96px] md:h-[5px] md:w-[128px] bg-gray-800 absolute -bottom-[8px] md:-bottom-[13px] left-1/2 -translate-x-1/2"></div>
      <div className="h-[10px] md:h-[16px] w-[128px] md:w-[224px] bg-gray-800 absolute -bottom-[18px] md:-bottom-[29px] rounded-b-xl left-1/2 -translate-x-1/2"></div>
    </div>
  );
}
