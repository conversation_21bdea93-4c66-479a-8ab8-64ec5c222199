'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  label?: string;
  description?: string;
  error?: string;
  onCheckedChange?: (checked: boolean) => void;
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, label, description, error, onCheckedChange, ...props }, ref) => {
    // Handle the onCheckedChange callback
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      // Call the onCheckedChange handler if provided
      if (onCheckedChange) {
        onCheckedChange(e.target.checked);
      }
    };

    return (
      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            type="checkbox"
            className={cn(
              "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 transition-all duration-300 ease-in-out",
              error && "border-red-500 focus:ring-red-500",
              className
            )}
            ref={ref}
            onChange={handleChange}
            {...props}
          />
        </div>
        {(label || description) && (
          <div className="ml-3 text-sm">
            {label && (
              <label
                htmlFor={props.id}
                className={cn(
                  "font-medium text-gray-700",
                  props.disabled && "opacity-50 cursor-not-allowed",
                  error && "text-red-500"
                )}
              >
                {label}
              </label>
            )}
            {description && (
              <p className={cn("text-gray-500", props.disabled && "opacity-50")}>
                {description}
              </p>
            )}
            {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
          </div>
        )}
      </div>
    );
  }
);

Checkbox.displayName = "Checkbox";

export { Checkbox };
