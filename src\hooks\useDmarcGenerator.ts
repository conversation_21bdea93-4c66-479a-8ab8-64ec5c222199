// DMARC Generator Hook - Full implementation
'use client';

import { useState } from 'react';
import { DmarcGenerateRequest, DmarcGenerateResponse, DmarcValidationRequest, DmarcValidationResponse, DmarcPolicy } from '@/types/dmarc';

interface UseDmarcGeneratorReturn {
  generateRecord: (params: DmarcGenerateRequest) => Promise<void>;
  validateDns: (domain: string, expectedPolicy?: DmarcPolicy) => Promise<void>;
  result: DmarcGenerateResponse['data'] | null;
  validation: DmarcValidationResponse['data'] | null;
  loading: boolean;
  error: string | null;
  clearResult: () => void;
  clearError: () => void;
}

export function useDmarcGenerator(): UseDmarcGeneratorReturn {
  const [result, setResult] = useState<DmarcGenerateResponse['data'] | null>(null);
  const [validation, setValidation] = useState<DmarcValidationResponse['data'] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateRecord = async (params: DmarcGenerateRequest) => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Generating DMARC record:', params);

      const response = await fetch('/api/tools/dmarc-generator/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      const data: DmarcGenerateResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || data.message || 'Failed to generate DMARC record');
      }

      setResult(data.data || null);
      console.log('DMARC record generated successfully');

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate DMARC record';
      console.error('DMARC generation error:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const validateDns = async (domain: string, expectedPolicy?: DmarcPolicy) => {
    setLoading(true);
    setError(null);
    setValidation(null);

    try {
      console.log('Validating DMARC DNS record:', { domain, expectedPolicy });

      const requestBody: DmarcValidationRequest = {
        domain,
        expectedPolicy,
      };

      const response = await fetch('/api/tools/dmarc-generator/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data: DmarcValidationResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      if (!data.success) {
        throw new Error(data.error || data.message || 'Failed to validate DMARC record');
      }

      setValidation(data.data || null);
      console.log('DMARC validation completed:', data.data?.isValid ? 'VALID' : 'INVALID');

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to validate DMARC record';
      console.error('DMARC validation error:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const clearResult = () => {
    setResult(null);
    setValidation(null);
  };

  const clearError = () => {
    setError(null);
  };

  return {
    generateRecord,
    validateDns,
    result,
    validation,
    loading,
    error,
    clearResult,
    clearError,
  };
}
