/**
 * API route for seeding side rail ad configurations using SQL
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logInfo, logError } from '@/lib/logging';

/**
 * GET /api/management-portal-x7z9y2/ads/seed-rail-sql
 * 
 * Seeds the database with side rail ad configurations using SQL
 */
export async function GET(request: NextRequest) {
  try {
    // Get Supabase client
    const supabase = createServerSupabaseClient();

    // Check if entries already exist
    const { data: existingData, error: checkError } = await supabase
      .from('ad_config')
      .select('placement_id, domain')
      .in('placement_id', ['left-rail', 'right-rail']);

    if (checkError) {
      throw new Error(`Failed to check existing side rail ads: ${checkError.message}`);
    }

    // Log existing entries
    logInfo('ads', `Found ${existingData?.length || 0} existing side rail ad entries`);

    // If entries already exist, don't add more
    if (existingData && existingData.length > 0) {
      return NextResponse.json({
        success: true,
        message: 'Side rail ads already exist',
        existingEntries: existingData
      });
    }

    // SQL to insert side rail ads
    const sql = `
      INSERT INTO ad_config (
        placement_id, 
        domain, 
        ad_unit_id, 
        ad_client_id, 
        is_enabled, 
        device_types, 
        display_options, 
        created_at, 
        updated_at
      ) VALUES 
      (
        'left-rail', 
        'localhost', 
        '1234567890', 
        'ca-pub-8397529755029714', 
        true, 
        '["desktop"]'::jsonb, 
        '{"position": "fixed", "margin": "10px 0", "padding": "10px", "backgroundColor": "#f9f9f9", "borderRadius": "4px", "maxWidth": "100%", "overflow": "hidden", "showBorder": true, "borderColor": "#e0e0e0", "labelText": "Advertisement", "showLabel": true}'::jsonb, 
        NOW(), 
        NOW()
      ),
      (
        'right-rail', 
        'localhost', 
        '0987654321', 
        'ca-pub-8397529755029714', 
        true, 
        '["desktop"]'::jsonb, 
        '{"position": "fixed", "margin": "10px 0", "padding": "10px", "backgroundColor": "#f9f9f9", "borderRadius": "4px", "maxWidth": "100%", "overflow": "hidden", "showBorder": true, "borderColor": "#e0e0e0", "labelText": "Advertisement", "showLabel": true}'::jsonb, 
        NOW(), 
        NOW()
      ),
      (
        'left-rail', 
        '*', 
        '1234567890', 
        'ca-pub-8397529755029714', 
        true, 
        '["desktop"]'::jsonb, 
        '{"position": "fixed", "margin": "10px 0", "padding": "10px", "backgroundColor": "#f9f9f9", "borderRadius": "4px", "maxWidth": "100%", "overflow": "hidden", "showBorder": true, "borderColor": "#e0e0e0", "labelText": "Advertisement", "showLabel": true}'::jsonb, 
        NOW(), 
        NOW()
      ),
      (
        'right-rail', 
        '*', 
        '0987654321', 
        'ca-pub-8397529755029714', 
        true, 
        '["desktop"]'::jsonb, 
        '{"position": "fixed", "margin": "10px 0", "padding": "10px", "backgroundColor": "#f9f9f9", "borderRadius": "4px", "maxWidth": "100%", "overflow": "hidden", "showBorder": true, "borderColor": "#e0e0e0", "labelText": "Advertisement", "showLabel": true}'::jsonb, 
        NOW(), 
        NOW()
      )
    `;

    // Execute SQL
    const { error: sqlError } = await supabase.rpc('execute_sql', { sql });

    if (sqlError) {
      throw new Error(`Failed to execute SQL: ${sqlError.message}`);
    }

    // Verify the entries were added
    const { data: verifyData, error: verifyError } = await supabase
      .from('ad_config')
      .select('placement_id, domain')
      .in('placement_id', ['left-rail', 'right-rail']);

    if (verifyError) {
      throw new Error(`Failed to verify side rail ads: ${verifyError.message}`);
    }

    return NextResponse.json({
      success: true,
      message: 'Side rail ads seeded successfully',
      entries: verifyData
    });
  } catch (error) {
    logError('ads', 'Error seeding side rail ads', { error });
    
    return NextResponse.json(
      { success: false, error: 'Failed to seed side rail ads', message: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
