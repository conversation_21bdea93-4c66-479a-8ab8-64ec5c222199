/**
 * SMTP Service
 *
 * Core SMTP testing functionality using nodemailer
 */

import nodemailer from 'nodemailer';
import { SmtpConfig, SmtpTestResult } from '@/types/smtp';
import { createTestEmailContent, formatTestDuration } from './utils';
import { generateEmailTesterAddress } from '@/lib/tools/email-tester/database';
import { SMTP_TIMEOUTS } from './constants';

interface SmtpTestOptions {
  config: SmtpConfig;
  testMode: 'auto' | 'custom';
  recipient?: string;
}

interface SmtpConnectionResult {
  success: boolean;
  messageId?: string;
  logs: string[];
  error?: string;
  testAddress?: string;
  testAddressId?: string;
  duration: number;
}

export class SmtpService {
  private logs: string[] = [];
  private startTime: Date = new Date();

  private log(message: string): void {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    this.logs.push(logEntry);
    console.log(`SMTP Test: ${logEntry}`);
  }

  private createTransporter(config: SmtpConfig): nodemailer.Transporter {
    this.log(`Creating SMTP transporter for ${config.server}:${config.port}`);

    const transportConfig: any = {
      host: config.server,
      port: config.port,
      connectionTimeout: SMTP_TIMEOUTS.connection,
      greetingTimeout: SMTP_TIMEOUTS.greeting,
      socketTimeout: SMTP_TIMEOUTS.socket,
      auth: {
        user: config.username,
        pass: config.password
      }
    };

    // Configure encryption
    if (config.encryption === 'ssl') {
      transportConfig.secure = true;
      this.log('Using SSL/TLS encryption');
    } else if (config.encryption === 'tls') {
      transportConfig.secure = false;
      transportConfig.requireTLS = true;
      this.log('Using STARTTLS encryption');
    } else {
      transportConfig.secure = false;
      transportConfig.ignoreTLS = true;
      this.log('Using no encryption (plain text)');
    }

    return nodemailer.createTransport(transportConfig);
  }

  private async verifyConnection(transporter: nodemailer.Transporter): Promise<void> {
    this.log('Verifying SMTP connection...');

    try {
      const verified = await transporter.verify();
      if (verified) {
        this.log('✓ SMTP connection verified successfully');
      } else {
        throw new Error('SMTP connection verification failed');
      }
    } catch (error) {
      this.log(`✗ SMTP connection verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  private async sendTestEmail(
    transporter: nodemailer.Transporter,
    config: SmtpConfig,
    recipient: string
  ): Promise<string> {
    this.log(`Sending test email to ${recipient}...`);

    const emailContent = createTestEmailContent(recipient);

    const mailOptions = {
      from: config.sender,
      to: recipient,
      subject: emailContent.subject,
      text: emailContent.text,
      html: emailContent.html
    };

    try {
      const info = await transporter.sendMail(mailOptions);
      this.log(`✓ Email sent successfully. Message ID: ${info.messageId}`);
      return info.messageId;
    } catch (error) {
      this.log(`✗ Failed to send email: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  public async testSmtpConnection(options: SmtpTestOptions): Promise<SmtpConnectionResult> {
    this.logs = [];
    this.startTime = new Date();

    this.log('Starting SMTP connection test');
    this.log(`Test mode: ${options.testMode}`);
    this.log(`SMTP Server: ${options.config.server}:${options.config.port}`);
    this.log(`Encryption: ${options.config.encryption}`);
    this.log(`Username: ${options.config.username}`);
    this.log(`Sender: ${options.config.sender}`);

    let transporter: nodemailer.Transporter | null = null;
    let testAddress: string | undefined;
    let testAddressId: string | undefined;
    let messageId: string | undefined;

    try {
      // Determine recipient
      if (options.testMode === 'auto') {
        this.log('Generating test address using Email Tester system...');
        const emailTesterAddress = await generateEmailTesterAddress(24); // 24 hour expiration
        testAddress = emailTesterAddress.testAddress;
        testAddressId = emailTesterAddress.id;
        this.log(`Generated test address: ${testAddress}`);
        this.log(`Test address ID: ${testAddressId}`);
      } else {
        testAddress = options.recipient;
        this.log(`Using custom recipient: ${testAddress}`);
      }

      if (!testAddress) {
        throw new Error('No recipient address specified');
      }

      // Create transporter
      transporter = this.createTransporter(options.config);

      // Verify connection
      await this.verifyConnection(transporter);

      // Send test email
      messageId = await this.sendTestEmail(transporter, options.config, testAddress);

      const endTime = new Date();
      const duration = endTime.getTime() - this.startTime.getTime();

      this.log(`✓ SMTP test completed successfully in ${duration}ms`);

      return {
        success: true,
        messageId,
        logs: [...this.logs],
        testAddress: options.testMode === 'auto' ? testAddress : undefined,
        testAddressId: options.testMode === 'auto' ? testAddressId : undefined,
        duration
      };

    } catch (error) {
      const endTime = new Date();
      const duration = endTime.getTime() - this.startTime.getTime();

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      this.log(`✗ SMTP test failed after ${duration}ms: ${errorMessage}`);

      return {
        success: false,
        logs: [...this.logs],
        error: errorMessage,
        testAddress: options.testMode === 'auto' ? testAddress : undefined,
        testAddressId: options.testMode === 'auto' ? testAddressId : undefined,
        duration
      };

    } finally {
      // Clean up transporter (connection will be closed automatically)
      if (transporter) {
        this.log('SMTP connection will be closed automatically');
      }
    }
  }
}

// Export singleton instance
export const smtpService = new SmtpService();
