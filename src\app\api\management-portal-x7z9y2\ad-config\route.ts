import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';
import { getDeviceType } from '@/lib/deviceDetection';

/**
 * GET /api/management-portal-x7z9y2/ad-config
 * 
 * Get ad configurations
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const placementId = searchParams.get('placementId');
    const domain = searchParams.get('domain');
    
    const supabase = createClient();
    let query = supabase.from('ad_config').select('*');
    
    if (placementId) {
      query = query.eq('placement_id', placementId);
    }
    
    if (domain) {
      query = query.eq('domain', domain);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching ad configurations:', error);
      return NextResponse.json(
        { success: false, message: 'Failed to fetch ad configurations', error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Error in ad-config GET route:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}

/**
 * POST /api/management-portal-x7z9y2/ad-config
 * 
 * Create a new ad configuration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { placementId, domain, adUnitId, adClientId, isEnabled, deviceTypes, format, displayOptions } = body;
    
    // Validate required fields
    if (!placementId || !domain || !adUnitId || !adClientId) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('ad_config')
      .insert([
        {
          placement_id: placementId,
          domain,
          ad_unit_id: adUnitId,
          ad_client_id: adClientId,
          is_enabled: isEnabled !== undefined ? isEnabled : true,
          device_types: deviceTypes || ['desktop', 'tablet', 'mobile'],
          format: format || 'auto',
          display_options: displayOptions || {}
        }
      ])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating ad configuration:', error);
      return NextResponse.json(
        { success: false, message: 'Failed to create ad configuration', error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Error in ad-config POST route:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/management-portal-x7z9y2/ad-config
 * 
 * Update an existing ad configuration
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { placementId, domain, updates } = body;
    
    // Validate required fields
    if (!placementId || !domain || !updates) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('ad_config')
      .update(updates)
      .eq('placement_id', placementId)
      .eq('domain', domain)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating ad configuration:', error);
      return NextResponse.json(
        { success: false, message: 'Failed to update ad configuration', error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Error in ad-config PUT route:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/management-portal-x7z9y2/ad-config
 * 
 * Delete an ad configuration
 */
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const placementId = searchParams.get('placementId');
    const domain = searchParams.get('domain');
    
    if (!placementId || !domain) {
      return NextResponse.json(
        { success: false, message: 'Missing required parameters' },
        { status: 400 }
      );
    }
    
    const supabase = createClient();
    
    const { error } = await supabase
      .from('ad_config')
      .delete()
      .eq('placement_id', placementId)
      .eq('domain', domain);
    
    if (error) {
      console.error('Error deleting ad configuration:', error);
      return NextResponse.json(
        { success: false, message: 'Failed to delete ad configuration', error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in ad-config DELETE route:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}
