'use client';

import { useState, useEffect } from 'react';
import { ContactMessage } from '@/lib/types/contact';
import { formatRelativeTime } from '@/lib/helpers/dateFormatter';
import { AdminSkeleton, AdminSkeletonText } from '@/components/admin/ui/AdminSkeleton';
import { AdminBadge } from '@/components/admin/ui/AdminBadge';

interface MessageThreadProps {
  threadId: string;
}

/**
 * Component for displaying a thread of messages
 */
export default function MessageThread({ threadId }: MessageThreadProps) {
  const [messages, setMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchThreadMessages = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/management-portal-x7z9y2/messages/thread/${threadId}`);
        const data = await response.json();

        if (data.success) {
          setMessages(data.messages);
        } else {
          setError(data.message || 'Failed to fetch thread messages');
        }
      } catch (err) {
        setError('An error occurred while fetching thread messages');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    if (threadId) {
      fetchThreadMessages();
    }
  }, [threadId]);

  if (loading) {
    return (
      <div className="space-y-4 animate-pulse">
        {[1, 2].map((i) => (
          <div key={i} className="p-4 rounded-lg bg-gray-50 border border-gray-100">
            <div className="flex justify-between items-start mb-2">
              <AdminSkeleton width="120px" height="20px" className="mb-1" />
              <AdminSkeleton width="80px" height="16px" />
            </div>
            <AdminSkeletonText lines={2} spacing="mb-1" />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-red-500 bg-red-50 rounded-lg border border-red-100">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Error: {error}
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className="p-6 text-center text-gray-500 bg-gray-50 rounded-lg border border-gray-100">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mx-auto mb-2 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
        No messages found in this thread.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {messages.map((message, index) => (
        <div
          key={message.id}
          className={`p-4 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md ${
            message.isAdminReply
              ? 'bg-indigo-50 border border-indigo-100 ml-6 relative'
              : 'bg-gray-50 border border-gray-100'
          }`}
          style={{
            animationDelay: `${index * 100}ms`,
            animationFillMode: 'both',
            animationName: 'fadeInUp',
            animationDuration: '0.5s'
          }}
        >
          {message.isAdminReply && (
            <div className="absolute -left-6 top-1/2 transform -translate-y-1/2 w-6 h-0.5 bg-indigo-200"></div>
          )}

          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                message.isAdminReply ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-600'
              }`}>
                {message.isAdminReply ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div>
                <p className="font-medium text-gray-900">
                  {message.isAdminReply ? 'VanishPost Support' : message.name}
                </p>
                <p className="text-xs text-gray-500">
                  {message.isAdminReply ? '<EMAIL>' : message.email}
                </p>
              </div>
            </div>
            <div className="flex flex-col items-end">
              <p className="text-xs text-gray-500 mb-1">
                {formatRelativeTime(message.createdAt)}
              </p>
              {message.isAdminReply && (
                <AdminBadge variant="primary" size="sm" icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                }>
                  Admin
                </AdminBadge>
              )}
            </div>
          </div>

          <div className="prose max-w-none ml-11">
            <p className="whitespace-pre-wrap text-gray-800">{message.message}</p>
          </div>
        </div>
      ))}
    </div>
  );
}
