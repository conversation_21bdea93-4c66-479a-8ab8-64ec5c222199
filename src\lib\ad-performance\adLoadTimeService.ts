/**
 * Service for tracking ad load times
 */

// Store load times in memory (would be in a database in production)
const adLoadTimes: number[] = [];

/**
 * Add a load time measurement
 * 
 * @param loadTime Load time in milliseconds
 */
export function addAdLoadTime(loadTime: number): void {
  adLoadTimes.push(loadTime);
  
  // Keep only the last 1000 measurements
  if (adLoadTimes.length > 1000) {
    adLoadTimes.shift();
  }
}

/**
 * Get all ad load times
 */
export function getAdLoadTimes(): number[] {
  return [...adLoadTimes];
}
