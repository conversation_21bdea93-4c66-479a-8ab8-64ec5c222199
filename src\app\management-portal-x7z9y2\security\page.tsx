'use client';

import { SecurityMetrics } from '@/components/admin/security/SecurityMetrics';

/**
 * Security Dashboard Page for Secure Admin Portal
 */
export default function SecurityDashboardPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fadeIn">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Security Dashboard</h1>
        <p className="mt-2 text-sm text-gray-600">
          Monitor security metrics, threat levels, and system protection status.
        </p>
      </div>

      <SecurityMetrics />
    </div>
  );
}
