import { NextRequest, NextResponse } from 'next/server';
import { deleteExpiredEmails } from '@/lib/supabase-db';
import { CleanupResponse } from '@/lib/types';

/**
 * Clean up expired email addresses
 *
 * @route POST /api/cleanup
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check for API key if provided in environment variables
    const apiKey = process.env.CLEANUP_API_KEY;
    if (apiKey) {
      const authHeader = request.headers.get('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.substring(7) !== apiKey) {
        return NextResponse.json({
          success: false,
          message: 'Unauthorized'
        }, { status: 401 });
      }
    }

    // Delete expired email addresses
    const deletedCount = await deleteExpiredEmails();

    // Return the number of deleted email addresses
    const response: CleanupResponse = {
      deletedCount,
      success: true
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error cleaning up expired email addresses:', error);

    const errorResponse: CleanupResponse = {
      deletedCount: 0,
      success: false,
      message: 'Failed to clean up expired email addresses'
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}

/**
 * Get cleanup status
 *
 * @route GET /api/cleanup
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // This endpoint doesn't actually perform any cleanup
    // It just returns a status message

    return NextResponse.json({
      success: true,
      message: 'Cleanup service is running. Use POST to trigger cleanup.'
    });
  } catch (error) {
    console.error('Error getting cleanup status:', error);

    return NextResponse.json({
      success: false,
      message: 'Failed to get cleanup status'
    }, { status: 500 });
  }
}
